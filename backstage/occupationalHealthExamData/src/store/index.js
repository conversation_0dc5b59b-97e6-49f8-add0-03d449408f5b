import Vue from 'vue';
import Vuex from 'vuex';
import { getUserSession } from '@/api/index';
Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    userInfo: {},
  },
  mutations: {
    setUserInfo(state, userInfo) {
      state.userInfo = userInfo;
    },
  },
  actions: {
    setUserInfo({ commit }) {
      getUserSession().then(res => {
        if (res && res.data) {
          const userInfo = res.data.userInfo || {};
          console.log('setUserInfo', userInfo);
          commit('setUserInfo', userInfo);
        }
      }).catch(error => {
        console.error('获取用户信息失败:', error);
      });
    },
  },
  modules: {
  },
});
