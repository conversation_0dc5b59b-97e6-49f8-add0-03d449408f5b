/**
 * 返回比率
 * @param {number} num 分子数
 * @param {number} total 分母数
 * @param {number} len 小数精确数，默认为2
 * @return {number} 返回不带%符号的数字
 */
export const returnRate = function(num, total, len = 2) {
  const numeratorBackup = Number(num);
  const denominatorBackup = Number(total);
  if (denominatorBackup === 0 || numeratorBackup === 0) {
    return 0;
  }
  const result = numeratorBackup / denominatorBackup;
  let val = 0;
  const divisor = result.toString().indexOf('.') !== -1 ? result.toString().split('.')[1] : result;
  if (numeratorBackup % denominatorBackup === 0 || divisor.length <= len) {
    val = ((numeratorBackup / denominatorBackup) * 100).toFixed(len);
  } else {
    val = ((numeratorBackup / denominatorBackup) * 100).toFixed(len);
  }

  return Number(val);
};
