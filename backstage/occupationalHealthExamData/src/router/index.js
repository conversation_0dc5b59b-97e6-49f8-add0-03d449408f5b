import Vue from 'vue';
import VueRouter from 'vue-router';
import settings from '@root/publicMethods/settings';
import Index from '../views/Index.vue';// 检查
import JCData from '../views/JCData.vue';// 检测

Vue.use(VueRouter);

// 添加路由调试信息
console.log('Router base path:', settings.admin_base_path + '/occupationalHealthExamData');

const createRouter = () =>
  new VueRouter({
    mode: 'history',
    base: process.env.BASE_URL,
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: [
      // 检查
      {
        path: settings.admin_base_path + '/occupationalHealthExamData',
        name: 'index',
        component: Index,
        beforeEnter: (to, from, next) => {
          console.log('Route entered:', to.path);
          next();
        },
      },
      // 检测
      {
        path: settings.admin_base_path + '/occupationalHealthExamDataJCData',
        name: 'occupationalHealthExamDataJCData',
        component: JCData,
        beforeEnter: (to, from, next) => {
          console.log('Route entered:', to.path);
          next();
        },
      },
    ],
  });

const router = createRouter();

// 全局路由钩子，用于调试
router.beforeEach((to, from, next) => {
  console.log('Global router beforeEach hook, to:', to.path);
  next();
});

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
