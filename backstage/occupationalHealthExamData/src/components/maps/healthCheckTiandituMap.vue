<template>
  <div class="tianditu-map-container">
    <div id="tiandituMap" class="map-element"></div>
  </div>
</template>

<script>
import { getServiceOrgList } from '@/api/index';
import { getFactorDistributionMap } from '@/api/index'; // 导入危害因素分布接口
// import { getHazardInspectionDataMapMarkers } from '@/api/healthCheck'// 危害因素分布接口
export default {
  name: 'TiandituMap',
  props: {
    // 天地图API密钥
    apiKey: {
      type: String,
      default: 'c12f57a65df6ea3e27dcb1355bdc05d9' // 请替换为实际的API密钥
    },
    // 初始化中心点经度
    initLng: {
      type: Number,
      default: 87.6177
    },
    // 初始化中心点纬度
    initLat: {
      type: Number,
      default: 43.7928
    },
    // 初始化缩放级别
    initZoom: {
      type: Number,
      default: 5
    },
    // 企业数据，可选
    enterpriseData: {
      type: Object,
      default: null
    },
    // 地图筛选数据
    filterForm: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tdtMap: null,
      isInitialized: false,
      markerObjects: [],
      infoWindowObjects: [],
      serviceOrgList: [], // 服务机构列表
      factorDistributionData: [], // 危害因素分布数据
      cloudMarkerLayer: null, // 海量点图层
      loading: false // 加载状态
    };
  },
  mounted() {
    this.loadTiandituScript();
  },
  methods: {
    // 加载天地图API脚本
    loadTiandituScript() {
      return new Promise((resolve, reject) => {
        if (window.T) {
          this.initTiandituMap();
          resolve(window.T);
          return;
        }
        
        // 加载天地图主API
        const tdtScript = document.createElement('script');
        tdtScript.type = 'text/javascript';
        tdtScript.src = `https://api.tianditu.gov.cn/api?v=4.0&tk=${this.apiKey}`;
        tdtScript.onerror = (e) => {
          console.error('天地图API加载失败', e);
          this.$emit('api-load-error', e);
          reject(e);
        };
        
        tdtScript.onload = () => {
          console.log('天地图API加载成功');
          this.initTiandituMap();
          resolve(window.T);
        };
        
        document.head.appendChild(tdtScript);
      });
    },

    // 初始化天地图
    initTiandituMap() {
      if (this.tdtMap) return;
      
      try {
        console.log('初始化天地图...');
        
        // 创建地图实例
        this.tdtMap = new T.Map('tiandituMap');
        
        // 设置显示地图的中心点和级别
        this.tdtMap.centerAndZoom(new T.LngLat(this.initLng, this.initLat), this.initZoom);
        
        // 添加地图类型控件
        this.tdtMap.addControl(new T.Control.MapType());
        
        // 添加缩放控件
        this.tdtMap.addControl(new T.Control.Zoom());
        
        // 添加比例尺控件
        this.tdtMap.addControl(new T.Control.Scale());
        
        // 添加地图点击事件
        this.tdtMap.addEventListener('click', this.handleMapClick);
        
        this.isInitialized = true;
        console.log('天地图初始化完成');
        this.$emit('map-initialized', this.tdtMap);
        
        // 如果有企业数据，优先处理企业数据
        if (this.enterpriseData) {
          this.processEnterpriseData();
        } else {
          // 否则加载企业数据到地图
          this.loadFactorDistributionData();
        }
      } catch (error) {
        console.error("天地图初始化失败:", error);
        this.$emit('map-init-error', error);
      }
    },

    // 处理地图点击事件
    handleMapClick(e) {
      console.log('地图点击位置:', e.lnglat.lng, e.lnglat.lat);
      this.$emit('map-click', {
        lng: e.lnglat.lng,
        lat: e.lnglat.lat
      });
    },

    // 添加标记点
    addMarker(longitude, latitude, title, options = {}) {
      // console.log('添加标记点:', longitude, latitude, title);
      if (!this.tdtMap) return null;
      
      // 创建标记点
      const marker = new T.Marker(new T.LngLat(longitude, latitude), options);
      
      // 将标记点添加到地图
      this.tdtMap.addOverLay(marker);
      
      // 保存标记点对象，以便后续管理
      this.markerObjects.push(marker);
      
      return marker;
    },

    // 清除地图覆盖物
    clearMapOverlays() {
      // 清除标记点
      this.markerObjects.forEach(marker => {
        if (marker && this.tdtMap) {
          this.tdtMap.removeOverLay(marker);
        }
      });
      this.markerObjects = [];
      
      // 清除信息窗口
      this.infoWindowObjects = [];
      
      // 清除海量点图层
      if (this.cloudMarkerLayer && this.tdtMap) {
        this.tdtMap.removeOverLay(this.cloudMarkerLayer);
        this.cloudMarkerLayer = null;
      }
    },

    // 更新企业数据到地图
    // 注意：父组件有直接通过ref调用此函数
    async updateEnterpriseData() {
      if (!this.tdtMap) return;
      try {
        // 清除之前的标记点
        this.clearMapOverlays();
        
        // 设置加载状态
        this.loading = true;
        
        // 保留原有点位信息（不清除覆盖物）

        // 1. 获取服务机构列表（原有逻辑）
        const params = { pageSize: 9999 };
        const response = await getServiceOrgList(params);
        if (response && response.data) {
          this.serviceOrgList = response.data.list || [];
          // 用于存储有效坐标的第一个机构
          let firstValidCoordinates = null;
          
          // 检查并处理坐标数据
          function processCoordinates(org) {
            if (org.lon && org.lat) {
              return {
                longitude: parseFloat(org.lon),
                latitude: parseFloat(org.lat)
              };
            }
            
            // 使用直接的经纬度字段
            if (org.longitude && org.latitude) {
              return {
                longitude: parseFloat(org.longitude),
                latitude: parseFloat(org.latitude)
              };
            }
            
            // 没有任何坐标信息
            return null;
          }
          
          // 遍历服务机构，添加标记点
          this.serviceOrgList.forEach((org, index) => {
            // 处理坐标数据
            const coordinates = processCoordinates(org);
            
            // 如果有经纬度数据，添加标记点
            if (coordinates) {
              // 保存第一个有效坐标
              if (index === 0 || !firstValidCoordinates) {
                firstValidCoordinates = coordinates;
              }
              
              const { longitude, latitude } = coordinates;
              
              // 创建标记点 - 使用自定义图标
              const icon = new T.Icon({
                iconUrl: require('./marker3.png'),
                iconSize: new T.Point(36, 36), // 图标可视区域的大小
                iconAnchor: new T.Point(30, 30), // 图标的定位锚点
              });
              const marker = this.addMarker(longitude, latitude, org.name, {
                icon
              });
              
              // 创建信息窗口
              const infoWindow = new T.InfoWindow();
              infoWindow.setContent(this.createInfoWindowContent(org));
              
              // 添加点击事件
              marker.addEventListener('click', () => {
                this.$emit('marker-click', org);
                marker.openInfoWindow(infoWindow);
              });
              
              // 添加悬停事件显示信息窗口
              marker.addEventListener('mouseover', () => {
                marker.openInfoWindow(infoWindow);
              });
              
              // 保存信息窗口对象
              this.infoWindowObjects.push(infoWindow);
            }
          });
          
          // 设置地图中心点为第一个有效坐标的机构位置
          if (firstValidCoordinates) {
            this.tdtMap.centerAndZoom(
              new T.LngLat(firstValidCoordinates.longitude, firstValidCoordinates.latitude), 
              7
            );
          }
          // 如果没有找到有效坐标，但有标记点，则调整地图视图以包含所有标记点
          else if (this.markerObjects.length > 0) {
            this.fitMapToMarkers();
          }
        }
        // 2. 获取危害因素点位信息并添加到地图
        this.addEnterpriseFactorMarkers()
        // 3.城市点位
        this.addCityMarkers();
      } catch (error) {
        console.error('获取服务机构或危害因素点位信息失败:', error);
        this.$message.error('获取服务机构或危害因素点位信息失败');
      } finally {
        this.loading = false;
      }
    },
    
    // 加载职业病危害因素分布数据
    async loadFactorDistributionData() {
      if (!this.tdtMap) return;
      
      try {
        // 清除之前的标记点
        this.clearMapOverlays();
        
        // 设置加载状态
        this.loading = true;
        
        // 构建查询参数 - 可以根据需要添加时间范围等参数
        const params = {};
        
        // 获取危害因素分布数据
        // const response = await getFactorDistributionMap(params);
        
        // if (response && response.data) {
        //   this.factorDistributionData = response.data || [];
          
        //   // 创建海量点标记
        //   this.createCloudMarkers();
          
        //   // 调整地图视图以包含所有点
        //   if (this.factorDistributionData.length > 0) {
        //     this.fitMapToFactorDistribution();
        //   }
        // }
      } catch (error) {
        console.error('获取职业病危害因素分布数据失败:', error);
        this.$message.error('获取职业病危害因素分布数据失败');
      } finally {
        this.loading = false;
      }
    },
    
    // 添加单个企业的所有危害因素标记点
    async addEnterpriseFactorMarkers() {
      const filterForm = structuredClone(this.filterForm)
      console.log('添加企业危害因素标记点，筛选条件:', filterForm);
      if(filterForm.hazardFactors.length){
        filterForm.hazardFactors = this.filterForm.hazardFactors.join(',')
      }
      const res = await getFactorDistributionMap(filterForm);
        if (res && res.data && Array.isArray(res.data)) {
          let filterData = res.data
          if(this.filterForm.hazardFactors.length){
            filterData = filterData.filter(item => item.factors.length>1)
          }
          filterData.forEach((item, idx) => {
            const { latitude,longitude,factors, name, address, industryName, employeeCount } = item;
              const infoHtml = `
                <div class="map-info-window">
                  <h3>${name || '未知企业'}</h3>
                  <div class="info-item"><label>地址：</label><span>${address || '无'}</span></div>
                  <div class="info-item"><label>行业：</label><span>${(industryName && industryName.join(',')) || '无'}</span></div>
                  <div class="info-item"><label>危害因素：</label><span>${(factors && factors.map(i=>i.name).join(',')) || '无'}</span></div>
                  <div class="info-item"><label>员工数：</label><span>${employeeCount != undefined ? employeeCount:''}</span></div>
                </div>
              `;
              const TDT_companyIcon = new T.Icon({
                iconUrl: require('./marker1.png'),
                iconSize: new T.Point(36, 36), // 图标可视区域的大小
                iconAnchor: new T.Point(30, 30), // 图标的定位锚点
              });
              const marker = this.addMarker(longitude, latitude, name, {
                icon: TDT_companyIcon
              });
              const infoWindow = new T.InfoWindow();
              infoWindow.setContent(infoHtml);
              marker.addEventListener('click', () => {
                marker.openInfoWindow(infoWindow);
              });
              marker.addEventListener('mouseover', () => {
                marker.openInfoWindow(infoWindow);
              });
              this.infoWindowObjects.push(infoWindow);
          });
          // 不自动调整地图中心，保持原有点位视图逻辑
        }
    },

    async addCityMarkers() { 
        try {
            const cities = [
              { name: '第一师（阿拉尔市）', lng: 81.29, lat: 40.55 },
              { name: '第二师（铁门关市）', lng: 85.69, lat: 41.86 },
              { name: '第三师（图木舒克市）', lng: 79.08, lat: 39.87 },
              { name: '第四师（可达拉克市）', lng: 81.03, lat: 43.94 },
              { name: '第五师（双河市）', lng: 82.38, lat: 44.85 },
              { name: '第六师（五家渠市）', lng: 87.55, lat: 44.17 },
              { name: '第七师（胡杨河师）', lng: 84.83, lat: 44.70 },
              { name: '第八师（石河子市）', lng: 86.09, lat: 44.31 },
              { name: '第九师（白杨市）', lng: 82.99, lat: 46.76 },
              { name: '第十师（北屯市）', lng: 87.84, lat: 47.34 },
              { name: '第十一师（建工师、乌鲁木齐）', lng: 87.65, lat: 43.75 },
              { name: '第十二师（乌鲁木齐五一农场）', lng: 87.38, lat: 43.99 },
              { name: '第十三师（新星市）', lng: 93.75, lat: 42.80 },
              { name: '兵地融合发展草湖项目区', lng: 87.38, lat: 43.99 }
            ];
            cities.forEach(city => {
              const label = new T.Label({
                text: `<b>${city.name}<b>`,
                position: new T.LngLat(city.lng, city.lat),
                offset: new T.Point(0, -10)
              });
              this.tdtMap.addOverLay(label);
            });
        } catch (error) {
            console.error('添加城市标记点时出错:', error);
        }
    },
   
    // 创建海量点标记
    createCloudMarkers() {
      console.log('createCloudMarkers')
      if (!this.tdtMap || !this.factorDistributionData.length) return;
      
      try {
        // 判断浏览器是否支持Canvas
        if (!document.createElement('canvas').getContext) {
          this.$message.warning('当前浏览器不支持海量点展示，请使用Chrome、Safari或IE9及以上浏览器');
          return;
        }
        
        // 创建合格和不合格点位的数组
        const qualifiedPoints = [];
        const unqualifiedPoints = [];
        
        
        // 分类数据点
        this.factorDistributionData.forEach(enterprise => {
          const lnglat = new T.LngLat(enterprise.longitude, enterprise.latitude);
          
          enterprise.factors.forEach(factor => {
            if (!factor.qualified) {
              unqualifiedPoints.push(lnglat);
            } else {
              qualifiedPoints.push(lnglat);
            }
          });
         
        });
        
        // 创建合格点的海量点图层（绿色）
        if (qualifiedPoints.length > 0) {
          const qualifiedLayer = new T.CloudMarkerCollection(qualifiedPoints, {
            color: 'green',
            SizeType: TDT_POINT_SIZE_SMALL
          });
          this.tdtMap.addOverLay(qualifiedLayer);
        }
        
        // 创建不合格点的海量点图层（红色）
        if (unqualifiedPoints.length > 0) {
          const unqualifiedLayer = new T.CloudMarkerCollection(unqualifiedPoints, {
            color: 'red',
            SizeType: TDT_POINT_SIZE_SMALL
          });
          this.tdtMap.addOverLay(unqualifiedLayer);
        }

      } catch (error) {
        console.error('创建海量点标记失败:', error);
      }

    },
    
   
    // 调整地图视图以包含所有危害因素分布点
    fitMapToFactorDistribution() {
      if (!this.tdtMap || !this.factorDistributionData.length) return;
      
      try {
        // 如果只有一个点，直接中心定位到该点
        if (this.factorDistributionData.length === 1) {
          const enterprise = this.factorDistributionData[0];
          this.tdtMap.centerAndZoom(
            new T.LngLat(enterprise.longitude, enterprise.latitude), 
            15
          );
          return;
        }
        
        // 创建边界对象
        const bounds = new T.LngLatBounds();
        
        // 将所有点加入边界计算
        this.factorDistributionData.forEach(enterprise => {
          bounds.extend(new T.LngLat(enterprise.longitude, enterprise.latitude));
        });
        
        // 设置地图视图以包含所有点
        this.tdtMap.setViewport(bounds);
      } catch (error) {
        console.error('调整地图视图失败:', error);
      }
    },
    
    // 创建信息窗口内容
    createInfoWindowContent(org) {
      return `
        <div class="map-info-window">
          <h3>${org.name || '未知机构'}</h3>
          <div class="info-item">
            <label>统一社会信用代码：</label>
            <span>${org.organization || '无'}</span>
          </div>
          <div class="info-item">
            <label>地址：</label>
            <span>${org.address || '无'}</span>
          </div>
          <div class="info-item">
            <label>法人代表：</label>
            <span>${org.corp || '无'}</span>
          </div>
        </div>
      `;
    },
    
    // 调整地图视图以包含所有标记点
    fitMapToMarkers() {
      if (!this.tdtMap || this.markerObjects.length === 0) return;
      
      try {
        // 如果只有一个标记点，直接中心定位到该点
        if (this.markerObjects.length === 1) {
          const position = this.markerObjects[0].getLngLat();
          this.tdtMap.centerAndZoom(position, 15);
          return;
        }
        
        // 创建边界对象
        const bounds = new T.LngLatBounds();
        
        // 将所有标记点加入边界计算
        this.markerObjects.forEach(marker => {
          bounds.extend(marker.getLngLat());
        });
        
        // 设置地图视图以包含所有标记点
        this.tdtMap.setViewport(bounds);
      } catch (error) {
        console.error('调整地图视图失败:', error);
      }
    },

    // 放大地图
    zoomIn() {
      if (this.tdtMap) {
        this.tdtMap.zoomIn();
      }
    },

    // 缩小地图
    zoomOut() {
      if (this.tdtMap) {
        this.tdtMap.zoomOut();
      }
    },

    // 重置地图
    resetMap() {
      if (this.tdtMap) {
        this.tdtMap.centerAndZoom(new T.LngLat(this.initLng, this.initLat), this.initZoom);
      }
    },

    // 处理企业数据
    processEnterpriseData() {
      if (this.enterpriseData && this.tdtMap) {
        console.log('处理企业数据:', this.enterpriseData);
        // 清除之前的覆盖物
        this.clearMapOverlays();
        
        // 添加企业危害因素标记
        this.addEnterpriseFactorMarkers(this.enterpriseData);
        
        // 居中显示企业位置
        this.tdtMap.centerAndZoom(
          new T.LngLat(this.enterpriseData.longitude, this.enterpriseData.latitude),
          15
        );
      }
    },

  },
  beforeDestroy() {
    // 清理资源
    if (this.tdtMap) {
      // 移除事件监听
      this.tdtMap.removeEventListener('click', this.handleMapClick);
      
      // 清除覆盖物
      this.clearMapOverlays();
      
      // 清空地图实例引用
      this.tdtMap = null;
    }
  },
  watch: {
    // 监听企业数据变化
    enterpriseData: {
      handler(newVal) {
        if (newVal && this.tdtMap) {
          this.processEnterpriseData();
        }
      },
      deep: true
    }
  }
};
</script>

<style lang="scss" scoped>
.tianditu-map-container {
  width: 100%;
  height: 100%;
  position: relative;
  
  .map-element {
    width: 100%;
    height: 100%;
  }
}
</style>

<!-- 非scoped全局样式 -->
<style lang="scss">
.map-info-window {
  padding: 8px 12px;
  min-width: 200px;
  font-size: 14px;
  
  h3 {
    margin: 0 0 8px 0;
    padding-bottom: 8px;
    font-size: 16px;
    border-bottom: 1px solid #eee;
    color: #409EFF;
  }
  
  .info-item {
    margin-bottom: 6px;
    line-height: 1.5;
    
    label {
      font-weight: bold;
      color: #606266;
    }
    
    span {
      margin-left: 4px;
      color: #303133;
      
      &.qualified {
        color: #67C23A;
        font-weight: bold;
      }
      
      &.unqualified {
        color: #F56C6C;
        font-weight: bold;
      }
    }
  }
  
  &.factor-info-window {
    min-width: 250px;
    
    .factor-group {
      margin-top: 10px;
      
      h4 {
        margin: 8px 0 4px 0;
        font-size: 14px;
      }
      
      ul {
        margin: 5px 0;
        padding-left: 20px;
      }
      
      &.unqualified h4 {
        color: #F56C6C;
      }
      
      &.qualified h4 {
        color: #67C23A;
      }
    }
  }
  
  &.factor-marker-window {
    min-width: 220px;
  }
}
</style>