<template>
  <el-card shadow="hover" class="chart-card">
    <div slot="header" class="clearfix">
      <span>职业健康检查开展用人单位数</span>
    </div>
    <div class="chart-container" v-loading="loading">
      <div ref="chartRef" class="chart"></div>
    </div>
  </el-card>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'DetectionRatePieChart',
  props: {
    statsData: {
      type: Object,
      default: () => ({
        totalEnterpriseCount: 0,
        fullyCheckedCount: 0,
        partiallyCheckedCount: 0,
        notCheckedCount: 0,
        checkRate: '0%'
      })
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    statsData: {
      handler(newVal) {
        this.$nextTick(() => {
          this.initChart();
        });
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    window.addEventListener('resize', this.resizeChart);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeChart);
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  },
  methods: {
    initChart() {
      // 确保DOM元素已经渲染
      if (!this.$refs.chartRef) return;

      // 如果已经有chart实例，则销毁重建
      if (this.chart) {
        this.chart.dispose();
      }
      
      // 创建新的图表实例
      this.chart = echarts.init(this.$refs.chartRef);
      
      const { fullyCheckedCount, partiallyCheckedCount, notCheckedCount, totalEnterpriseCount } = this.statsData;
      
      // 处理空数据情况
      if (totalEnterpriseCount === 0) {
        this.chart.setOption({
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: 16,
              color: '#999'
            }
          }
        });
        return;
      }
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          data: ['全部检测', '部分检测', '未检测']
        },
        color: ['#67C23A', '#E6A23C', '#F56C6C'],
        series: [
          {
            name: '检测情况',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: fullyCheckedCount, name: '全部检测' },
              { value: partiallyCheckedCount, name: '部分检测' },
              { value: notCheckedCount, name: '未检测' }
            ]
          }
        ]
      };

      this.chart.setOption(option);
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.chart-card {
  margin-bottom: 20px;

  .chart-container {
    height: 400px;
    width: 100%;
    
    .chart {
      height: 100%;
      width: 100%;
    }
  }
}
</style> 