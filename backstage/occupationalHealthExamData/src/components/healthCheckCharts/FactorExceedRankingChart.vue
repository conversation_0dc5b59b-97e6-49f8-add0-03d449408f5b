<template>
  <el-card shadow="hover" class="chart-card">
    <div slot="header" class="clearfix">
      <span>职业病危害因素检测超标排名</span>
    </div>
    <div class="chart-container" v-loading="loading">
      <div ref="chartRef" class="chart"></div>
    </div>
  </el-card>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'FactorExceedRankingChart',
  props: {
    factorExceedData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    factorExceedData: {
      handler(newVal) {
        this.$nextTick(() => {
          this.initChart();
        });
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    window.addEventListener('resize', this.resizeChart);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeChart);
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  },
  methods: {
    initChart() {
      // 确保DOM元素已经渲染
      if (!this.$refs.chartRef) return;

      // 如果已经有chart实例，则销毁重建
      if (this.chart) {
        this.chart.dispose();
      }
      
      // 创建新的图表实例
      this.chart = echarts.init(this.$refs.chartRef);

      // 处理空数据情况
      if (!this.factorExceedData || this.factorExceedData.length === 0) {
        this.chart.setOption({
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: 16,
              color: '#999'
            }
          }
        });
        return;
      }

      // 准备数据 - 按照从大到小排序
      const sortedData = [...this.factorExceedData]
        .sort((a, b) => (b.exceedPointCount || 0) - (a.exceedPointCount || 0));
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params) => {
            const item = params[0];
            // 由于y轴数据已经reverse，需要相应调整dataIndex
            const dataIndex = sortedData.length - 1 - item.dataIndex;
            const dataItem = sortedData[dataIndex];
            return `
              <div>
                <p><strong>${dataItem.factor}</strong></p>
                <p>超标企业数: ${dataItem.exceedEnterpriseCount}</p>
                <p>超标点数: ${dataItem.exceedPointCount || 0}</p>
              </div>
            `;
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          name: '超标点数',
          axisLabel: {
            formatter: '{value}'
          }
        },
        yAxis: {
          type: 'category',
          data: sortedData.map(item => item.factor).reverse(),
          axisLabel: {
            interval: 0,
            textStyle: {
              fontSize: 12
            }
          }
        },
        series: [
          {
            name: '超标点数',
            type: 'bar',
            barWidth: '60%',
            data: sortedData.map(item => item.exceedPointCount || 0).reverse(),
            itemStyle: {
              color: (params) => {
                const value = params.value;
                if (value >= 10) {
                  return '#F56C6C'; // 高超标 - 红色
                } else if (value >= 5) {
                  return '#E6A23C'; // 中超标 - 黄色
                } else {
                  return '#409EFF'; // 低超标 - 蓝色
                }
              }
            },
            label: {
              show: true,
              position: 'right',
              formatter: '{c}'
            }
          }
        ]
      };

      this.chart.setOption(option);
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.chart-card {
  margin-bottom: 20px;

  .chart-container {
    height: 400px;
    width: 100%;
    
    .chart {
      height: 100%;
      width: 100%;
    }
  }
}
</style> 