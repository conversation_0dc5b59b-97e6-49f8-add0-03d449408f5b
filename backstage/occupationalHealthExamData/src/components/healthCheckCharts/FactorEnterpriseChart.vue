<template>
  <el-card shadow="hover" class="chart-card">
    <div slot="header" class="clearfix">
      <span>各职业病危害因素检测企业数</span>
    </div>
    <div class="chart-container" v-loading="loading">
      <div ref="chartRef" class="chart"></div>
    </div>
  </el-card>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'FactorEnterpriseChart',
  props: {
    factorData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    factorData: {
      handler(newVal) {
        this.$nextTick(() => {
          this.initChart();
        });
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    window.addEventListener('resize', this.resizeChart);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeChart);
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  },
  methods: {
    initChart() {
      console.log('Initializing FactorEnterpriseChart with data:', this.factorData);
      // 确保DOM元素已经渲染
      if (!this.$refs.chartRef) return;

      // 如果已经有chart实例，则销毁重建
      if (this.chart) {
        this.chart.dispose();
      }
      
      // 创建新的图表实例
      this.chart = echarts.init(this.$refs.chartRef);

      // 处理空数据情况
      if (!this.factorData || this.factorData.length === 0) {
        this.chart.setOption({
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: 16,
              color: '#999'
            }
          }
        });
        return;
      }

      // 获取前10个危害因素数据
      const topFactors = this.factorData.slice(0, 10);
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: topFactors.map(item => item.factor),
          axisLabel: {
            interval: 0,
            rotate: 30,
            textStyle: {
              fontSize: 12
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '企业数'
        },
        series: [
          {
            name: '检测企业数',
            type: 'bar',
            barWidth: '60%',
            data: topFactors.map(item => item.enterpriseCount),
            itemStyle: {
              color: '#409EFF'
            },
            label: {
              show: true,
              position: 'top'
            }
          }
        ]
      };

      this.chart.setOption(option);
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.chart-card {
  margin-bottom: 20px;

  .chart-container {
    height: 400px;
    width: 100%;
    
    .chart {
      height: 100%;
      width: 100%;
    }
  }
}
</style> 