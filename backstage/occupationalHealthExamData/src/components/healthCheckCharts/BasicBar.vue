<template>
  <el-card shadow="hover" class="chart-card">
    <div slot="header" class="clearfix">
      <span>{{ title }}</span>
    </div>
    <div class="chart-container" v-loading="loading">
      <div ref="chartRef" class="chart"></div>
    </div>
  </el-card>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'EnterpriseRankingChart',
  props: {
    title:'',
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    description: {
      type: String,
      default: ''
    }
  },
  
  data() {
    return {
      chart: null
    };
  },
  watch: {
    data: {
      handler(newVal) {
        this.$nextTick(() => {
          this.initChart();
        });
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    window.addEventListener('resize', this.resizeChart);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeChart);
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  },
  methods: {
    initChart() {
      // 确保DOM元素已经渲染
      if (!this.$refs.chartRef) return;

      // 如果已经有chart实例，则销毁重建
      if (this.chart) {
        this.chart.dispose();
      }
      
      // 创建新的图表实例
      this.chart = echarts.init(this.$refs.chartRef);

      // 处理空数据情况
      if (!this.data || this.data.length === 0) {
        this.chart.setOption({
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              fontSize: 16,
              color: '#999'
            }
          }
        });
        return;
      }

      const rankData = [...this.data];
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          confine:true,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
        },
        yAxis: {
          type: 'category',
          data: rankData.map(item => item.name),
          axisLabel: {
            width: 200,
            interval: 0,
            textStyle: {
              fontSize: 12
            },
            formatter: function(value) {
              return value;
            },
            rich: {
              tooltip: {
                backgroundColor: '#fff',
                color: '#333',
                padding: [5, 10],
                borderRadius: 3,
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
                valueFormatter:(value) => value,  
              }
            }
          },
          tooltip: {
            show: true
          }
        },
        series: [
          {
            name: this.description,
            type: 'bar',
            barWidth: '60%',
            data: rankData.map(item => item.value).reverse(),
            // itemStyle: {
            //   color: (params) => {
            //     // 根据完成率设置不同颜色
            //     const value = params.value;
            //     if (value >= 80) {
            //       return '#67C23A'; // 成功色（绿色）
            //     } else if (value >= 50) {
            //       return '#409EFF'; // 主题色（蓝色）
            //     } else if (value >= 20) {
            //       return '#E6A23C'; // 警告色（黄色）
            //     } else {
            //       return '#F56C6C'; // 危险色（红色）
            //     }
            //   }
            // },
            label: {
              show: true,
              position: 'right',
            }
          }
        ]
      };

      this.chart.setOption(option);
      
      // 添加Y轴标签的鼠标悬停事件
      this.addAxisLabelTooltip();
    },
    addAxisLabelTooltip() {
      if (!this.chart || !this.data || this.data.length === 0) return;
      
      const self = this;
      // 获取图表DOM元素
      const chartDom = this.$refs.chartRef;
      
      // 给Y轴标签区域添加mouseover事件
      chartDom.addEventListener('mousemove', function(event) {
        const chartOffset = self.chart.getDom().getBoundingClientRect();
        const mouseX = event.clientX - chartOffset.left;
        const mouseY = event.clientY - chartOffset.top;
        
        // 判断鼠标是否在Y轴标签区域
        const yAxisLabelWidth = chartOffset.width * 0.1; // 左侧10%区域
        if (mouseX <= yAxisLabelWidth) {
          // 计算当前鼠标位置对应的企业索引
          const pixelY = self.chart.convertFromPixel({yAxisIndex: 0}, [0, mouseY]);
          const index = Math.floor(pixelY);
          
          // 确保索引有效
          if (index >= 0 && index < self.data.length) {
            const rawName = self.data.reverse()[index].name;
            // 如果原始名称和显示名称不同，则显示tooltip
              self.chart.dispatchAction({
                type: 'showTip',
                seriesIndex: 0,
                dataIndex: index,
                position: ['5%', '50%'],
                content: rawName,
              });
              return;

          }
        }
        
        // 不在Y轴标签区域时，不显示特殊tooltip
        // self.chart.dispatchAction({
        //   type: 'hideTip'
        // });
      });
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.chart-card {
  margin-bottom: 20px;

  .chart-container {
    height: 400px;
    width: 100%;
    
    .chart {
      height: 100%;
      width: 100%;
    }
  }
}
</style> 