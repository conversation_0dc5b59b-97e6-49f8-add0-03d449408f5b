import request from '@root/publicMethods/request';

/**
 * 获取用人单位检测统计数据
 * @param {Object} params
 * @return {Promise}
 */
export function getEnterpriseExamStatsByTime(params) {
  return request({
    url: '/manage/healthCheckStatistics/getEnterpriseExamStatsByTime',
    method: 'get',
    params,
  });
}

export function healthCheckFactorEmployees(params) {
  return request({
    url: '/manage/healthCheckStatistics/healthCheckFactorEmployees',
    method: 'get',
    params,
  });
}
export function getFullyCheckedEnterpriseCountByCity(params) {
  return request({
    url: '/manage/healthCheckStatistics/getFullyCheckedEnterpriseCountByCity',
    method: 'get',
    params,
  });
}
export function countJobConclusionTypes(params) {
  return request({
    url: '/manage/healthCheckStatistics/countJobConclusionTypes',
    method: 'get',
    params,
  });
}
export function calcJobHealthAbnormalRate(params) {
  return request({
    url: '/manage/healthCheckStatistics/calcJobHealthAbnormalRate',
    method: 'get',
    params,
  });
}
export function getAbnormalEnterpriseCountByCity(params) {
  return request({
    url: '/manage/healthCheckStatistics/getAbnormalEnterpriseCountByCity',
    method: 'get',
    params,
  });
}

// 获取地图标记点数据
export function getHazardInspectionDataMapMarkers(params) {
  return request({
    url: '/api/hazardInspectionData/mapMarkers',
    method: 'get',
    params,
  });
}
