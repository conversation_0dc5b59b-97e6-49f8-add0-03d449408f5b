import request from '@root/publicMethods/request';

/**
 * 获取用人单位检测统计数据
 * @param {Object} params
 * @return {Promise}
 */
export function getEnterpriseStatsByTime(params) {
  return request({
    url: '/manage/jobHealthStatistics/getEnterpriseStatsByTime',
    method: 'get',
    params,
  });
}

/**
 * 获取各职业病危害因素检测的用人单位数
 * @param {Object} params
 * @return {Promise}
 */
export function getFactorEnterpriseStats(params) {
  return request({
    url: '/manage/jobHealthStatistics/getFactorEnterpriseStats',
    method: 'get',
    params,
  });
}

/**
 * 获取职业健康检测完成情况排名
 * @param {Object} params
 * @return {Promise}
 */
export function getEnterpriseRanking(params) {
  return request({
    url: '/manage/jobHealthStatistics/getEnterpriseRanking',
    method: 'get',
    params,
  });
}

/**
 * 获取检测超标统计数据
 * @param {Object} params
 * @return {Promise}
 */
export function getExceedStandardStats(params) {
  return request({
    url: '/manage/jobHealthStatistics/getExceedStandardStats',
    method: 'get',
    params,
  });
}

/**
 * 获取危害因素检测超标排名
 * @param {Object} params
 * @return {Promise}
 */
export function getFactorExceedRanking(params) {
  return request({
    url: '/manage/jobHealthStatistics/getFactorExceedRanking',
    method: 'get',
    params,
  });
}

// 获取地区列表
export function getDistrictList(params) {
  return request({
    url: '/api/adminorgGov/address/list',
    params,
    method: 'get',
  });
}

/**
 * 获取服务机构列表
 * @param {Object} params
 * @return {Promise}
 */
export function getServiceOrgList(params) {
  return request({
    url: '/manage/jobHealthStatistics/getServiceOrgList',
    method: 'get',
    params,
  });
}

/**
 * 获取危害因素检测分布图
 * @param {Object} params
 * @return {Promise}
 */
export function getFactorDistributionMap(params) {
  return request({
    url: '/manage/jobHealthStatistics/getFactorDistributionMap',
    method: 'get',
    params,
  });
}

// 获取用户信息
export function getUserSession() {
  return request({
    url: '/manage/getUserSession',
    method: 'get',
  });
}
