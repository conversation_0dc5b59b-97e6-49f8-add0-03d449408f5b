import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import '@/styles/index.scss'; // global css
import singleSpaVue from 'single-spa-vue';
import './set-public-path';
import * as Sentry from '@sentry/browser';
import { Vue as VueIntegration } from '@sentry/integrations';

Sentry.init({
  dsn: 'https://<EMAIL>/5337371',
  integrations: [ new VueIntegration({ Vue, attachProps: true }) ],
});

// 使用Element UI
Vue.use(ElementUI);

// 打印调试信息
console.log('已加载Element UI组件库');

Vue.config.productionTip = false;

const vueLifecycles = singleSpaVue({
  Vue,
  appOptions: {
    render: h => h(App),
    router,
    store,
  },
});

export const bootstrap = [
  vueLifecycles.bootstrap,
];
// 获取用户信息
store.dispatch('setUserInfo');
export function mount(props) {
  console.log('occupationalHealthExamData props', props);
  return vueLifecycles.mount(props);
}

export const unmount = [
  vueLifecycles.unmount,
];

