<template>
  <div class="dashboard-container">
    <!-- 头部区域：标题、筛选器和统计卡片 -->
    <div class="dashboard-header">
      <div class="header-title-filter">
        <h1>职业健康检查分布情况、检查机构信息</h1>
        <div class="date-filter">
          <el-radio-group v-model="timeFilterType" size="small" style="margin-right: 10px;">
            <el-radio-button label="timePoint">时间点</el-radio-button>
            <el-radio-button label="timeRange">时间段</el-radio-button>
          </el-radio-group>
          
          <el-date-picker
            v-if="timeFilterType === 'timePoint'"
            v-model="timePoint"
            type="date"
            :clearable="false"
            @change="fetchAllData"
            placeholder="选择日期"
          />
          <el-date-picker
            v-else
            v-model="timeRange"
            type="daterange"
            :clearable="false"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="fetchAllData"
          />
          
          <el-cascader
            style="width: 200px; margin-left: 10px;"
            :props="districtListProps"
            @change="handleRegionChange"
            v-model="regionData"
            clearable
            ref="regionCascader"
            size="small"
            placeholder="请选择地区"
          >
            <template slot-scope="{ data }">
              <span>{{ data.label }}</span>
            </template>
          </el-cascader>
          
          <el-button type="primary" icon="el-icon-refresh" circle @click="fetchAllData"></el-button>
        </div>
      </div>
      
      <!-- 统计卡片移至头部 -->
    </div>

    <!-- 主体三栏布局 -->
    <div class="dashboard-main">
      <!-- 左侧栏 (20%) -->
      <div class="dashboard-left">
        <detection-rate-pie-chart
          :stats-data="enterpriseStatsData"
          :loading="loading.enterpriseStats"
          class="chart-item"
        />
        <el-card shadow="hover" class="chart-item">
          <div>
            <div slot="header" class="clearfix">
              <!-- <span>职业健康检查率(全部检查+部分检查/用人单位总数)</span> -->
              <span>职业健康检查率</span>
            </div>
            <div class="stats-info">
              <div class="stats-title" style="color:rgba( 80,214,124);font-size: large;font-weight: bold;">{{checkEnterpriseRate}}</div>
            </div>
          </div>
        </el-card>
        <BasicPie
          title="各职业病危害因素（大类）职业健康检查劳动者数"
          description="劳动者数"
          :data="factorEmployeesData"
          class="chart-item"
        />
        <BasicBar
          title="各区域职业健康检查完成情况排名"
          :data="fullyCheckedEnterpriseCountByCityData"
          class="chart-item"
        />
      </div>
      
      <!-- 中间部分 (60%) - 地图 -->
      <div class="dashboard-center">
        <div>
          <span>筛选危害因素：</span>
          <el-select v-model="filterForm.hazardFactors" multiple placeholder="选择危害因素" size="small" :collapse-tags="true">
            <el-option v-for="item in [
                { value: 'dust', label: '粉尘类' },
                { value: 'chemical', label: '化学物质类' },
                { value: 'physical', label: '物理因素类' },
                { value: 'radiation', label: '放射性因素类' },
                { value: 'biological', label: '生物因素类' }
              ]" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <span>
            <el-button type="primary" icon="el-icon-search" size="small" @click="updateMapData">筛选</el-button>
          </span>
        </div>
        <div class="map-container">
          <tianditu-map
            ref="tiandituMap"
            @map-click="handleMapClick"
            @map-initialized="handleMapInitialized"
            @marker-click="handleMarkerClick"
            :filterForm="filterForm"
          />
        
        </div>
      </div>
      
      <!-- 右侧栏 (20%) -->
      <div class="dashboard-right">
        <BasicPie
          title="职业健康检查异常人数分布情况"
          description="劳动者数"
          :data="countJobConclusionTypesData"
          class="chart-item"
        />
        <el-card shadow="hover" class="chart-item">
          <div>
            <div slot="header" class="clearfix">
              <!-- <span>职业健康检查异常率(异常人数/全部体检人数)</span> -->
              <span>职业健康检查异常率</span>
            </div>
            <div class="stats-info">
              <div class="stats-title" style="color:rgba(228, 61, 48,0.5);font-size: large;font-weight: bold;">{{calcJobHealthAbnormalRateData.abnormalRate}}</div>
            </div>
          </div>
        </el-card>

         <BasicBar
          title="职业健康检查异常率用人单位数排名"
          :data="abnormalEnterpriseCountByCityData"
          class="chart-item"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {
  getDistrictList,
} from '@/api/index';
import {
  getEnterpriseExamStatsByTime,
  healthCheckFactorEmployees,
  getFullyCheckedEnterpriseCountByCity,
  countJobConclusionTypes,
  calcJobHealthAbnormalRate,
  getAbnormalEnterpriseCountByCity
} from '@/api/healthCheck';
import {
  DetectionRatePieChart,
  FactorEnterpriseChart,
  EnterpriseRankingChart,
  FactorExceedRankingChart
} from '@/components/healthCheckCharts'
import TiandituMap from '@/components/maps/healthCheckTiandituMap';
import moment from 'moment';
import BasicPie from '@/components/healthCheckCharts/BasicPie'
import BasicBar from '@/components/healthCheckCharts/BasicBar'
import {returnRate} from '@/utils/utils'
export default {
  name: 'JobHealthStatistics',
  components: {
    DetectionRatePieChart,
    FactorEnterpriseChart,
    EnterpriseRankingChart,
    FactorExceedRankingChart,
    TiandituMap,
    BasicPie,
    BasicBar
  },
  data() {
    return {
      timeFilterType: 'timePoint',
      timePoint: '',
      timeRange: [],
      loading: {
        enterpriseStats: false,
        factorStats: false,
        enterpriseRanking: false,
        exceedStandard: false,
        factorExceed: false
      },
      enterpriseStatsData: {
        totalEnterpriseCount: 0,
        fullyCheckedCount: 0,
        partiallyCheckedCount: 0,
        notCheckedCount: 0,
        checkRate: '0%'
      },
      factorEmployeesData: [],
      fullyCheckedEnterpriseCountByCityData:[],
      countJobConclusionTypesData:[],
      calcJobHealthAbnormalRateData:'',
      abnormalEnterpriseCountByCityData:[],
      districtListProps: {
        checkStrictly: true,
        lazy: true,
        lazyLoad(node, resolve) {
          const { level } = node;
          const params = {
            level,
          };
          if (level) {
            params.parent_code = node.value.area_code;
          }
          getDistrictList(params).then(({ data }) => {
            try {
              const nodes = Array.from(data.docs).map((item) => ({
                value: item,
                label: item.name,
                leaf: item.level >= 3,
                disabled: item.name === "市辖区" ? true : false,
              }));
              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          });
        },
      },
      regionData: [],
      mapInstance: null,
      filterForm:{
        hazardFactors:[],
      }
    };
  },
  mounted() {
    // 组件挂载后获取数据
    this.$nextTick(() => {
      this.fetchAllData();
    });
  },
  methods: {
    handleTimeFilterChange() {
      this.fetchAllData();
    },
    // 获取用于API请求的参数
    getTimeParams() {
      const params = {};
      if (this.timeFilterType === 'timePoint') {
        params.timePoint = this.timePoint ? moment(this.timePoint).format('YYYY-MM-DD') : null;
      } else {
        params.startTime = this.timeRange[0] ? moment(this.timeRange[0]).format('YYYY-MM-DD') : null;
        params.endTime = this.timeRange[1] ? moment(this.timeRange[1]).format('YYYY-MM-DD') : null;
      }

      // 添加地区参数
      if (this.regionData && this.regionData.length > 0) {
        params.regionCode = this.regionData[this.regionData.length - 1].area_code;
      }else{
        // ----250807----hwj-----数据隔离
        //没有选择的地区参数，使用用户信息的地区参数
        params.regionCode = this.$store.state.userInfo.area_code;
      }

      return params;
    },
    // 获取所有数据
    fetchAllData() {
      this.fetchEnterpriseStats();
      this.fetchFactorEmployeeData();
      this.getFullyCheckedEnterpriseCountByCity()
      this.countJobConclusionTypes()
      this.calcJobHealthAbnormalRate()
      this.getAbnormalEnterpriseCountByCity()
    },
    // 获取职业健康检查开展用人单位数统计数据
    async fetchEnterpriseStats() {
      this.loading.enterpriseStats = true;
      try {
        const params = this.getTimeParams();
        const res = await getEnterpriseExamStatsByTime(params);
        if (res && res.data) {
          this.enterpriseStatsData = res.data;
        }
      } catch (error) {
        console.error('获取用人单位检测统计数据失败:', error);
        this.$message.error('获取用人单位检测统计数据失败');
      } finally {
        this.loading.enterpriseStats = false;
      }
    },

    async fetchFactorEmployeeData(){
      try {
        const params = this.getTimeParams();
        const res = await healthCheckFactorEmployees(params);
        if (res && res.data) {
          this.factorEmployeesData = res.data.map(i=>({name:i.category,value:i.employeeCount}))
        }
      } catch (error) {
        console.error('获取危害因素种类员工数量失败:', error);
        this.$message.error('获取危害因素种类员工数量失败');
      }
    },

    async getFullyCheckedEnterpriseCountByCity(){
      try {
        const params = this.getTimeParams();
        const res = await getFullyCheckedEnterpriseCountByCity(params);
        if (res && res.data) {
          this.fullyCheckedEnterpriseCountByCityData = res.data.map(i=>({
            name: i.cityName.match(/^建设兵团.+/)? i.cityName.replace('建设兵团', ''): i.cityName,
            value: i.fullyCheckedCount
          }));
        }
      } catch (error) {
        console.error('获取各区域职业健康检查完成情况排名失败:', error);
        this.$message.error('获取各区域职业健康检查完成情况排名失败');
      }
    },
    async countJobConclusionTypes(){
      try {
        const params = this.getTimeParams();
        const res = await countJobConclusionTypes(params);
        if (res && res.data) {
          this.countJobConclusionTypesData = res.data.map(i => {
            return {
              name: i.type,
              value: i.count
            };
          });
        }
      } catch (error) {
        console.error('统计各类体检结论类型人数失败:', error);
        this.$message.error('统计各类体检结论类型人数失败');
      }
    },
    async calcJobHealthAbnormalRate(){
      try {
        const params = this.getTimeParams();
        const res = await calcJobHealthAbnormalRate(params);
        if (res && res.data) {
          this.calcJobHealthAbnormalRateData = res.data
        }
      } catch (error) {
        console.error('计算健康体检异常率失败:', error);
        this.$message.error('计算健康体检异常率失败');
      }
    },
    async getAbnormalEnterpriseCountByCity(){
      try {
        const params = this.getTimeParams();
        const res = await getAbnormalEnterpriseCountByCity(params);
        if (res && res.data) {
          this.abnormalEnterpriseCountByCityData = res.data.map(i=>({
            name: i.cityName.match(/^建设兵团.+/)? i.cityName.replace('建设兵团', ''): i.cityName,
            value: i.abnormalEnterpriseCount
          }));
        }
      } catch (error) {
        console.error('获取各区域职业健康体检异常用人单位数排名失败:', error);
        this.$message.error('获取各区域职业健康体检异常用人单位数排名失败');
      }
    },
    // 处理地区变化
    handleRegionChange(value) {
      this.regionData = value;
      if (this.$refs.regionCascader) {
        this.$refs.regionCascader.dropDownVisible = false;
      }
      this.fetchAllData();
    },
    // 控制地图
    controlMap(action) {
      try {
        if (this.$refs.tiandituMap && this.$refs.tiandituMap[action]) {
          this.$refs.tiandituMap[action]();
        }
      } catch (error) {
        console.error('地图控制失败:', error);
      }
    },
    // 处理地图点击
    handleMapClick(location) {
      console.log('地图点击位置:', location.lng, location.lat);
      // 可以在这里添加对地图点击的处理逻辑
    },
    // 处理地图初始化完成
    handleMapInitialized(mapInstance) {
      console.log('地图初始化完成');
      this.mapInstance = mapInstance;
      // 手动触发一次地图数据刷新
      if (this.$refs.tiandituMap) {
        this.$refs.tiandituMap.updateEnterpriseData();
      }
    },
    updateMapData() {
      if (this.$refs.tiandituMap) {
        this.$refs.tiandituMap.updateEnterpriseData();
      }
    },
    // 处理标记点点击
    handleMarkerClick(org) {
      console.log('服务机构被点击:', org);
      // 在这里可以添加服务机构点击后的处理逻辑
      // 例如显示详情面板或跳转到详情页
    }
  },
  computed:{
    checkEnterpriseRate(){
      if(!this.enterpriseStatsData.totalEnterpriseCount){
        return '/'
      }
      return returnRate(this.enterpriseStatsData.fullyCheckedCount + this.enterpriseStatsData.partiallyCheckedCount, this.enterpriseStatsData.totalEnterpriseCount) + '%';
    }
  }
};
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 40px); // 考虑到页面padding的高度
  
  .dashboard-header {
    margin-bottom: 20px;
    
    .header-title-filter {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h1 {
        margin: 0;
        font-size: 24px;
        color: #303133;
      }
      
      .date-filter {
        display: flex;
        align-items: center;
      }
    }
    
    .header-stats-card {
      margin-bottom: 20px;
    }
  }
  
  .dashboard-main {
    display: flex;
    flex: 1;
    min-height: 0; // 解决Flex布局中的溢出问题
    
    .dashboard-left {
      width: 30%;
      padding-right: 10px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
    }
    
    .dashboard-center {
      width: 40%;
      padding: 0 10px;
      
      .map-container {
        height: calc(100% - 40px);
        position: relative;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        
        ::v-deep .tianditu-map-container {
          width: 100%;
          height: 100%;
        }
        
        .map-controls {
          position: absolute;
          bottom: 20px;
          right: 20px;
          z-index: 10;
          background-color: rgba(255, 255, 255, 0.8);
          border-radius: 4px;
          padding: 4px;
        }
      }
    }
    
    .dashboard-right {
      width: 30%;
      padding-left: 10px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
    }
    
    .chart-item {
      margin-bottom: 20px;
      flex: 0 0 auto;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
      border: 1px solid #ebeef5;
    }
  }
}
</style> 