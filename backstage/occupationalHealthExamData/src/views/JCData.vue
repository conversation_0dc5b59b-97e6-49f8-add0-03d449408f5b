<!-- 备份原来的检测统计 -->
<template>
  <div class="dashboard-container">
    <!-- 头部区域：标题、筛选器和统计卡片 -->
    <div class="dashboard-header">
      <div class="header-title-filter">
        <h1>职业健康检查统计分析</h1>
        <div class="date-filter">
          <el-radio-group v-model="timeFilterType" size="small" style="margin-right: 10px;">
            <el-radio-button label="timePoint">时间点</el-radio-button>
            <el-radio-button label="timeRange">时间段</el-radio-button>
          </el-radio-group>
          
          <el-date-picker
            v-if="timeFilterType === 'timePoint'"
            v-model="timePoint"
            type="date"
            :clearable="false"
            @change="fetchAllData"
            placeholder="选择日期"
          />
          <el-date-picker
            v-else
            v-model="timeRange"
            type="daterange"
            :clearable="false"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="fetchAllData"
          />
          
          <el-cascader
            style="width: 200px; margin-left: 10px;"
            :props="districtListProps"
            @change="handleRegionChange"
            v-model="regionData"
            clearable
            ref="regionCascader"
            size="small"
            placeholder="请选择地区"
          >
            <template slot-scope="{ data }">
              <span>{{ data.label }}</span>
            </template>
          </el-cascader>
          
          <el-button type="primary" icon="el-icon-refresh" circle @click="fetchAllData"></el-button>
        </div>
      </div>
      
      <!-- 统计卡片移至头部 -->
      <enterprise-stats-card
        :stats-data="enterpriseStatsData"
        :exceed-data="exceedStandardData"
        class="header-stats-card"
      />
    </div>

    <!-- 主体三栏布局 -->
    <div class="dashboard-main">
      <!-- 左侧栏 (20%) -->
      <div class="dashboard-left">
        <detection-rate-pie-chart
          :stats-data="enterpriseStatsData"
          :loading="loading.enterpriseStats"
          class="chart-item"
        />
        
        <enterprise-ranking-chart
          :ranking-data="enterpriseRankingData"
          :loading="loading.enterpriseRanking"
          class="chart-item"
        />
      </div>
      
      <!-- 中间部分 (60%) - 地图 -->
      <div class="dashboard-center">
        <div class="map-container">
          <tianditu-map
            ref="tiandituMap"
            @map-click="handleMapClick"
            @map-initialized="handleMapInitialized"
            @marker-click="handleMarkerClick"
          />
        
        </div>
      </div>
      
      <!-- 右侧栏 (20%) -->
      <div class="dashboard-right">
        <factor-enterprise-chart
          :factor-data="factorEnterpriseData"
          :loading="loading.factorStats"
          class="chart-item"
        />
        
        <factor-exceed-ranking-chart
          :factor-exceed-data="factorExceedData"
          :loading="loading.factorExceed"
          class="chart-item"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {
  getEnterpriseStatsByTime,
  getFactorEnterpriseStats,
  getEnterpriseRanking,
  getExceedStandardStats,
  getFactorExceedRanking,
  getDistrictList
} from '@/api/index';
import {
  EnterpriseStatsCard,
  DetectionRatePieChart,
  FactorEnterpriseChart,
  EnterpriseRankingChart,
  FactorExceedRankingChart
} from '@/components/charts';
import TiandituMap from '@/components/maps/TiandituMap';
import moment from 'moment';

export default {
  name: 'JobHealthStatistics',
  components: {
    EnterpriseStatsCard,
    DetectionRatePieChart,
    FactorEnterpriseChart,
    EnterpriseRankingChart,
    FactorExceedRankingChart,
    TiandituMap
  },
  data() {
    return {
      timeFilterType: 'timePoint',
      timePoint: '',
      timeRange: [],
      loading: {
        enterpriseStats: false,
        factorStats: false,
        enterpriseRanking: false,
        exceedStandard: false,
        factorExceed: false
      },
      enterpriseStatsData: {
        totalEnterpriseCount: 0,
        fullyCheckedCount: 0,
        partiallyCheckedCount: 0,
        notCheckedCount: 0,
        checkRate: '0%'
      },
      factorEnterpriseData: [],
      enterpriseRankingData: [],
      exceedStandardData: {
        exceedCount: 0,
        exceedRate: '0%',
        exceedEnterprises: []
      },
      factorExceedData: [],
      districtListProps: {
        checkStrictly: true,
        lazy: true,
        lazyLoad(node, resolve) {
          const { level } = node;
          const params = {
            level,
          };
          if (level) {
            params.parent_code = node.value.area_code;
          }
          getDistrictList(params).then(({ data }) => {
            try {
              const nodes = Array.from(data.docs).map((item) => ({
                value: item,
                label: item.name,
                leaf: item.level >= 3,
                disabled: item.name === "市辖区" ? true : false,
              }));
              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          });
        },
      },
      regionData: [],
      mapInstance: null
    };
  },
  mounted() {
    // 组件挂载后获取数据
    this.$nextTick(() => {
      this.fetchAllData();
    });
  },
  methods: {
    handleTimeFilterChange() {
      this.fetchAllData();
    },
    // 获取用于API请求的参数
    getTimeParams() {
      const params = {};
      if (this.timeFilterType === 'timePoint') {
        params.timePoint = this.timePoint ? moment(this.timePoint).format('YYYY-MM-DD') : null;
      } else {
        params.startTime = this.timeRange[0] ? moment(this.timeRange[0]).format('YYYY-MM-DD') : null;
        params.endTime = this.timeRange[1] ? moment(this.timeRange[1]).format('YYYY-MM-DD') : null;
      }

      // 添加地区参数
      if (this.regionData && this.regionData.length > 0) {
        params.regionCode = this.regionData[this.regionData.length - 1].area_code;
      }else{
        // ----250807----hwj-----数据隔离
        //没有选择的地区参数，使用用户信息的地区参数
        params.regionCode = this.$store.state.userInfo.area_code;
      }

      return params;
    },
    // 获取所有数据
    fetchAllData() {
      this.fetchEnterpriseStats();
      this.fetchFactorEnterpriseStats();
      this.fetchEnterpriseRanking();
      this.fetchExceedStandardStats();
      this.fetchFactorExceedRanking();
    },
    // 获取用人单位检测统计数据
    async fetchEnterpriseStats() {
      this.loading.enterpriseStats = true;
      try {
        const params = this.getTimeParams();
        const res = await getEnterpriseStatsByTime(params);
        if (res && res.data) {
          this.enterpriseStatsData = res.data;
        }
      } catch (error) {
        console.error('获取用人单位检测统计数据失败:', error);
        this.$message.error('获取用人单位检测统计数据失败');
      } finally {
        this.loading.enterpriseStats = false;
      }
    },
    // 获取各职业病危害因素检测的用人单位数
    async fetchFactorEnterpriseStats() {
      this.loading.factorStats = true;
      try {
        const params = this.getTimeParams();
        const res = await getFactorEnterpriseStats(params);
        if (res && res.data) {
          this.factorEnterpriseData = res.data;
        }
      } catch (error) {
        console.error('获取职业病危害因素统计数据失败:', error);
        this.$message.error('获取职业病危害因素统计数据失败');
      } finally {
        this.loading.factorStats = false;
      }
    },
    // 获取职业健康检测完成情况排名
    async fetchEnterpriseRanking() {
      this.loading.enterpriseRanking = true;
      try {
        const params = this.getTimeParams();
        params.limit = 10; // 限制返回前10条数据
        const res = await getEnterpriseRanking(params);
        if (res && res.data) {
          this.enterpriseRankingData = res.data;
        }
      } catch (error) {
        console.error('获取企业检测排名数据失败:', error);
        this.$message.error('获取企业检测排名数据失败');
      } finally {
        this.loading.enterpriseRanking = false;
      }
    },
    // 获取检测超标统计数据
    async fetchExceedStandardStats() {
      this.loading.exceedStandard = true;
      try {
        const params = this.getTimeParams();
        const res = await getExceedStandardStats(params);
        if (res && res.data) {
          this.exceedStandardData = res.data;
        }
      } catch (error) {
        console.error('获取检测超标统计数据失败:', error);
        this.$message.error('获取检测超标统计数据失败');
      } finally {
        this.loading.exceedStandard = false;
      }
    },
    // 获取危害因素检测超标排名
    async fetchFactorExceedRanking() {
      this.loading.factorExceed = true;
      try {
        const params = this.getTimeParams();
        params.limit = 10; // 限制返回前10条数据
        const res = await getFactorExceedRanking(params);
        if (res && res.data) {
          this.factorExceedData = res.data;
        }
      } catch (error) {
        console.error('获取危害因素超标排名数据失败:', error);
        this.$message.error('获取危害因素超标排名数据失败');
      } finally {
        this.loading.factorExceed = false;
      }
    },
    // 处理地区变化
    handleRegionChange(value) {
      this.regionData = value;
      if (this.$refs.regionCascader) {
        this.$refs.regionCascader.dropDownVisible = false;
      }
      this.fetchAllData();
    },
    // 控制地图
    controlMap(action) {
      try {
        if (this.$refs.tiandituMap && this.$refs.tiandituMap[action]) {
          this.$refs.tiandituMap[action]();
        }
      } catch (error) {
        console.error('地图控制失败:', error);
      }
    },
    // 处理地图点击
    handleMapClick(location) {
      console.log('地图点击位置:', location.lng, location.lat);
      // 可以在这里添加对地图点击的处理逻辑
    },
    // 处理地图初始化完成
    handleMapInitialized(mapInstance) {
      console.log('地图初始化完成');
      this.mapInstance = mapInstance;
      // 手动触发一次地图数据刷新
      if (this.$refs.tiandituMap) {
        this.$refs.tiandituMap.updateEnterpriseData();
      }
    },
    // 处理标记点点击
    handleMarkerClick(org) {
      console.log('服务机构被点击:', org);
      // 在这里可以添加服务机构点击后的处理逻辑
      // 例如显示详情面板或跳转到详情页
    }
  }
};
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 40px); // 考虑到页面padding的高度
  
  .dashboard-header {
    margin-bottom: 20px;
    
    .header-title-filter {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h1 {
        margin: 0;
        font-size: 24px;
        color: #303133;
      }
      
      .date-filter {
        display: flex;
        align-items: center;
      }
    }
    
    .header-stats-card {
      margin-bottom: 20px;
    }
  }
  
  .dashboard-main {
    display: flex;
    flex: 1;
    min-height: 0; // 解决Flex布局中的溢出问题
    
    .dashboard-left {
      width: 30%;
      padding-right: 10px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
    }
    
    .dashboard-center {
      width: 40%;
      padding: 0 10px;
      
      .map-container {
        height: 100%;
        position: relative;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        
        ::v-deep .tianditu-map-container {
          width: 100%;
          height: 100%;
        }
        
        .map-controls {
          position: absolute;
          bottom: 20px;
          right: 20px;
          z-index: 10;
          background-color: rgba(255, 255, 255, 0.8);
          border-radius: 4px;
          padding: 4px;
        }
      }
    }
    
    .dashboard-right {
      width: 30%;
      padding-left: 10px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
    }
    
    .chart-item {
      margin-bottom: 20px;
      flex: 0 0 auto;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
      border: 1px solid #ebeef5;
    }
  }
}
</style> 