
import {
  list,
} from '@/api/manage';


const state = {
  countss: {},
  tableDatass: [],
  warningConfig: [], // 预警配置
  isHz: null, // 是否是杭州
  branch: '',
  showReserve: false, // 是否显示预约相关功能
};


const mutations = {
  countss: (state, count) => {
    state.countss = count;
  },
  tableDatass: (state, tableData) => {
    state.tableDatass = tableData;
  },
  clearData: state => {
    state.countss = {};
    state.tableDatass = [];
  },
  setWarningConfig: (state, warningConfig) => {
    state.warningConfig = warningConfig;
  },
  getBranch: (state, branch) => {
    state.branch = branch;
    state.isHz = branch === 'hz';
    state.showReserve = ![ 'kt', 'hm' ].includes(branch);
  },
};

const actions = {
  async list({
    commit,
  }, params) {
    const res = await list(params);
    const reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
    // 处理手机号隐藏
    for (let i = 0; i < res.data.length; i++) {
      const ele = res.data[i];
      if (ele.projectDetail) {
        ele.projectDetail.contactPhoneNum2 = ele.projectDetail.contactPhoneNum;
        ele.projectDetail.contactPhoneNum = ele.projectDetail.contactPhoneNum.toString().replace(reg, '$1***$2');
      } else if (ele.adminuser && ele.adminuser.phoneNum) {
        ele.adminuser.phoneNum2 = ele.adminuser.phoneNum;
        ele.adminuser.phoneNum = ele.adminuser.phoneNum.toString().replace(reg, '$1***$2');
      } else if (ele.company && ele.company.contract) {
        ele.company.contract2 = ele.company.contract;
        ele.company.contract = ele.company.contract.toString().replace(reg, '$1***$2');
      }
    }
    commit('setWarningConfig', res.warningConfig || []);
    commit('countss', res.count);
    commit('tableDatass', res.data);
    commit('getBranch', res.branch);
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
