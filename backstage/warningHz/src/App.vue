<template>
  <div id="serviceOrg-app" class="warning">
   <!-- <div :class="classObj"> -->
      <!-- <div class="main-container"> -->
        <router-view />
      <!-- </div> -->
    <!-- </div> -->
  </div>
</template>
<script>
import { initEvent } from "@root/publicMethods/events";
export default {
  data() {
    return {
      sidebarOpened: true,
      device: "desktop",
    }
  },
 computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    },
  },
  mounted() {
    initEvent(this);
  },
 
  components: {},
};
</script>
<style lang="scss">
.warning{
  .el-table .cell, .el-table th div{
    padding-left: 5px;
    padding-right: 5px;
  }
  .el-tabs.el-tabs--top.el-tabs--border-card{
    width: 99%;
  }
  .el-dialog{
    width: 60%!important;
    padding: 10px 10px 0;
    box-sizing: border-box;
    max-width: 1100px;
    min-width: 600px;
  }
  .el-dialog .el-dialog__body{
    padding: 20px;
  }
  .wrap .el-form-item__label{
    color: #666;
  }
  .wrap .el-form-item {
    margin-bottom: 18px;
  }
  // .el-card__body .el-card__body{
  //   height: auto!important;
  // }
  .box-card .el-icon-circle-close{
    color: #f5f5ff!important;
  }
  .el-card__body:first-of-type {
    // height: 90vh;
    max-width: 76vw;
    min-width: 700px;
    overflow-y: scroll;
    // overflow-x: hidden;
    padding: 25px 25px 0;
    box-sizing: border-box;
  }
   /* 设置滚动条的样式 */
  .box-card ::-webkit-scrollbar {
    width:5px;
    background-color: #ddd;
  }
   /* 滚动槽 */
  .box-card ::-webkit-scrollbar-track {
    border-radius:4px;
    background-color: #fff;
  }

  /* 滚动条滑块 */
  .box-card ::-webkit-scrollbar-thumb {
    border-radius:4px;
    /* background:black; */
  }
  .wrap .el-select{ // 预警状态,自制下拉框
    width: 100%;
    .el-input--medium { 
      .el-input__inner{
        height: 60px;
        border-radius: 4px;
        border: 1px solid #DCDFE6;
        width: calc(100% - 16px);
        justify-content: space-around;
        align-items: center;
        padding-left: 20px;
        font-size: 15px;
        color: #333;
        font-weight: bold;
      }
      .el-input__suffix{
        right: 24px!important;
      }
      strong{
        color: rgb(42, 145, 252);
      }
    }
  }
}
.search{ // 顶部搜索
  .el-input--small .el-input__inner{
    background-color: #f5f7fa;
    border: none;
  }
  .el-input__icon.el-icon-search{
    color: rgba(42, 145, 252, 1);
  }
}
</style>
