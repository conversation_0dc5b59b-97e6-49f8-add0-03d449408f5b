import request from '@root/publicMethods/request';
export function deleteWarning(data) { // 删除预警
  return request({
    url: '/manage/warning/deleteWarning',
    data,
    method: 'post',
  });
}
export function updateWarning(data) { // 编辑修改删除预警
  return request({
    url: '/manage/warning/update',
    data,
    method: 'post',
  });
}
export function reject(data) { // 发送预警督促信息给企业联系人
  return request({
    url: '/manage/warning/reject',
    data,
    method: 'post',
  });
}
export function sendWarningMsg(data) { // 发送预警督促信息给企业联系人
  return request({
    url: '/manage/warning/sendWarningMsg',
    data,
    method: 'post',
  });
}
export function sendMeetingMsg(data) { // 发送会议信息给企业联系人
  return request({
    url: '/manage/warning/sendMeetingMsg',
    data,
    method: 'post',
  });
}
export function cancelMeetingMsg(data) {
  return request({
    url: '/manage/warning/cancelMeetingMsg',
    data,
    method: 'post',
  });
}
export function liftWarning(data) {
  return request({
    url: '/manage/warning/liftWarning',
    data,
    method: 'post',
  });
}
export function disposalWarningPlan(data) {
  return request({
    url: '/manage/warning/disposalWarningPlan',
    data,
    method: 'post',
  });
}
export function list(data) {
  return request({
    url: '/manage/warning/list',
    data,
    method: 'post',
  });
}
export function add(data) {
  return request({
    url: '/manage/warning/add',
    data,
    method: 'post',
  });
}
// 添加一条监管记录
export function addSupervision(data) {
  return request({
    url: '/manage/supervision/add',
    data,
    method: 'post',
  });
}
// 获取监管记录
export function supervisionList(data) {
  return request({
    url: '/manage/supervision/list',
    data,
    method: 'post',
  });
}
// 获取监管记录
export function supervisionCount(params) {
  return request({
    url: '/manage/supervision/count',
    params,
    method: 'get',
  });
}


export function getDistrictList(params) {
  return request({
    url: '/api/adminorgGov/address/list',
    params,
    method: 'get',
  });
}

// 获取当前监管单位下属的所有监管单位
export function getSubsidiary(params) {
  return request({
    url: '/manage/warning/getSubsidiary',
    params,
    method: 'get',
  });
}

// 同意修改项目数据的申请
export function applyModifyWarning(data) {
  return request({
    url: '/manage/warning/applyModifyWarning',
    data,
    method: 'post',
  });
}

// 清空回收站
export function clearRecycleBin() {
  return request({
    url: '/manage/warning/clearRecycleBin',
    method: 'get',
    params: {},
  });
}

// 上传整改文件
export function uploadFiles(data) {
  return request({
    url: '/manage/warning/uploadFiles',
    data,
    method: 'post',
  });
}

export function getUserSession() {
  return request({
    url: '/manage/getUserSession',
    method: 'get',
  });
}
// 获取生成预警的原项目报告
export function getOriginalReport(params) {
  return request({
    url: '/manage/warning/getOriginalReport',
    params,
    method: 'get',
  });
}
// 杭州 - 发送预警短信提醒
export function SMSnotification(data) {
  return request({
    url: '/manage/warning/SMSnotification',
    data,
    method: 'post',
  });
}
// 退回已解除的预警
export function retreatWarning(data) {
  return request({
    url: '/manage/warning/retreat',
    data,
    method: 'post',
  });
}
// 获取预警统计数据
export function getStatistics(params) {
  return request({
    url: '/manage/warning/getStatistics',
    params,
    method: 'get',
  });
}
// 退回
export function giveBack(data) {
  return request({
    url: '/manage/projectGiveBack/giveBack',
    method: 'post',
    data,
  });
}

// 退回窗口关闭后删除图片
export function giveBackDelImgs(data) {
  return request({
    url: '/manage/projectGiveBack/giveBackDelImgs',
    method: 'post',
    data,
  });
}
// 查看手机号记录到日志中
export function checkPhoneLog(params) {
  return request({
    url: '/manage/superUser/checkPhoneLog',
    params,
    method: 'get',
  });
}

// 获取预警视频token
export function getWarningVideoToken(params) {
  return request({
    url: '/manage/warning/getWarningVideoToken',
    params,
    method: 'get',
  });
}

// 创建预约
export function createReserve(data) {
  return request({
    url: '/manage/warning/createReserve',
    data,
    method: 'post',
  });
}

// 获取会议列表
export function getReserveList(params) {
  return request({
    url: '/manage/warning/getReserveList',
    params,
    method: 'get',
  });
}

export function getAllWarning(params) {
  return request({
    url: '/manage/warning/getAllWarning',
    params,
    method: 'get',
  });
}

export function cancelMeeting(data) {
  return request({
    url: '/manage/warning/cancelMeeting',
    data,
    method: 'post',
  });
}
