<template>
  <div :style="style">
    <!-- <div v-if="text" :style="{ marginTop: '10px', marginBottom: '10px' }">{{ text }}</div> -->
    <div ref="videoRef" :style="{ width, height }" />
  </div>
</template>


<script>
const DEFAULT_WIDTH = "64.53vw"
const DEFAULT_HEIGHT = "71.7vh"

export default {
  props: {
    videoTrack: {
      type: Object,
      default: null
    },
    audioTrack: {
      type: Object,
      default: null
    },
    config: {
      type: Object,
      default: () => ({})
    },
    isLocal: {
      type: Boolean,
      default: false
    },
    text: {
      type: [String, Number],
      default: ''
    },
    width: {
      type: String,
      default: DEFAULT_WIDTH
    },
    height: {
      type: String,
      default: DEFAULT_HEIGHT
    },
    style: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      videoRef: null
    };
  },
  mounted() {
    this.videoRef = this.$refs.videoRef;
    if (this.videoTrack) {
      this.videoTrack.play(this.videoRef, this.config);
    }
    if (!this.isLocal && this.audioTrack) {
      this.audioTrack.play();
    }

    this.$watch('videoTrack', (track) => {
      if (track && this.videoRef) {
        track.play(this.videoRef);
      }
    });

    this.$watch('audioTrack', (track) => {
      if (!this.isLocal && track) {
        track.play();
      }
    });
  },
  beforeDestroy() {
    if (this.videoTrack) {
      this.videoTrack.close();
    }
    if (this.audioTrack) {
      this.audioTrack.close();
    }
  },
  methods: {
    handleClick() {
      this.$emit('click');
    }
  }
};
</script>