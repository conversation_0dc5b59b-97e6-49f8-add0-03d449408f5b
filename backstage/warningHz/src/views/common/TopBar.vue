<template>
  <div class="dr-toolbar search">
    <el-row>
      <el-date-picker
          v-model="year"
          type="year"
          size="small"
          style="width: 110px"
          format="yyyy 年"
          placeholder="选择年份"
          @change="searchFun"
          >
        </el-date-picker> &nbsp;
      <el-cascader
          style="width: 200px"
          :props="districtListProps"
          @change="regaddChangeOptionFunc"
          v-model="searchParams.regAddr"
          clearable
          ref="regAddCas"
          size="small"
          placeholder="请选择地区"

        >
          <template slot-scope="{ data }">
            <span>{{ data.label }}</span>
          </template>
        </el-cascader> &nbsp;
      <el-input
          style="width: 200px"
          size="small"
          placeholder="关键字搜索"
          v-model="keyWords" clearable
          suffix-icon="el-icon-search"
          @change="searchFun"
        ></el-input>
      
      <!-- <el-col :span="3">
        <el-select v-model="searchParams.lever" placeholder="请选择风险等级" size="small" style="width: 100%" clearable>
          <el-option label="全部" value="all"></el-option>
          <el-option label="一级重要信息" value="1"></el-option>
          <el-option label="二级重要信息" value="2"></el-option>
          <el-option label="三级重要信息" value="3"></el-option>
          <el-option label="四级重要信息" value="4"></el-option>
        </el-select>
      </el-col>
      <el-col :span="3">
        <el-select v-model="searchParams.type" placeholder="请选择重要信息类型" size="small" style="width: 100%" clearable>
          <el-option label="检测重要信息" value="1"></el-option>
          <el-option label="体检重要信息" value="2"></el-option>
          <el-option label="其他" value="3"></el-option>
        </el-select>
      </el-col>
      <el-col :span="3">
        <el-select v-model="searchParams.status"  placeholder="请选择重要信息状态" size="small" style="width: 100%" clearable>
          <el-option label="待处理" :value="0"></el-option>
          <el-option label="已处理" :value="1"></el-option>
          <el-option label="已整改" :value="2"></el-option>
          <el-option label="已解除" :value="3"></el-option>
          <el-option label="已驳回" :value="4"></el-option>
        </el-select>
      </el-col> -->
      <el-button type="info" icon="el-icon-delete" size="mini" style="float:right" @click="toRecycleBin">回收站</el-button>
      <el-select v-model="downLoadType" placeholder="导出" size="mini" style="width: 125px; margin:0 15px 0 10px; float:right" clearable v-if="searchParams.tabName==1">
          <el-option label="导出重要信息统计" value="2"></el-option>
          <el-option label="导出重要信息详情" value="1"></el-option>
        </el-select>
      <el-button type="primary" plain icon="el-icon-download" size="mini" @click="$emit('downLoad')" style="margin-right:15px; float:right" v-else>导出</el-button>
      <el-button type="warning" plain icon="el-icon-mobile" size="mini" style="float:right;margin-right:5px;" @click="$emit('sendVerifyMassage')" v-if="isHz">短信提醒</el-button>
    </el-row>
  </div>
</template>
<script>
import { getDistrictList, getStatistics,list } from "@/api/manage";
import FileSaver from 'file-saver'
import XLSX from 'xlsx';
export default {
  props: {
    searchParams: Object,
    isHz: Boolean,
    tabSwitch:String,
  },
  data() {
    return {
      downLoadType: '',
      warningLever: '', // 风险等级
      year: '', // 选择年份
      visible:true,
      keyWords: '', //关键字
      district: [],
      districtListProps: {
        checkStrictly: true,
        lazy: true,
        lazyLoad(node, resolve) {
          const { level } = node;
          const params = {
            level,
          };
          if (level) {
            params.parent_code = node.value.area_code;
          }
          getDistrictList(params).then(({ data }) => {
            try {
              const nodes = Array.from(data.docs).map((item) => ({
                value: item,
                label: item.name,
                leaf: item.level >= 3,
                disabled: item.name === "市辖区" ? true : false,
              }));
              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          });

          
        },
      },
      statisticsData: [], // 导出的统计数据，不包括四级重要信息
    };
  },
  created() {
    this.keyWords = this.searchParams.keyWords || '';
    this.year = this.searchParams.year;
  },
  watch: {
    tabSwitch(val){
      this.year = this.searchParams.year||new Date() 
      this.keyWords = ''
    },
    downLoadType(newVal, oldVal){
      if(!newVal || newVal == oldVal) return;
      if(newVal == '1'){ // 导出重要信息详情
        this.$emit('downLoad');
      }else{ // 导出重要信息统计
        this.downLoadStatistics()
      }
    },
    keyWords(newVal, oldVal){
      if(newVal == oldVal) return;
      setTimeout( ()=> {
        this.searchParams.keyWords = this.keyWords;
        this.searchParams.curPage = 1;
      }, 2000)
    },
    'searchParams.keyWords': {
      deep:true,
      handler:function(newV,oldV){
        this.keyWords = this.searchParams.keyWords || '';
      }
    },
    year(year){
      this.searchParams.year = year;
    }
  },
  methods: {
   async searchFun(){
    console.log('这咋不对',this.searchParams);
    this.searchParams.keyWords = this.keyWords;
    this.searchParams.year = this.year;
    this.$store.dispatch('warning/list',this.searchParams)
    },
    // 完成/总数率计算
    overRatio(over=0, total=0){
      return (total ? parseInt((over/total).toFixed(2)*100) : 0)+ '%';
    },
    // 合并XLSX中的行和列
    mergeColumns(ec = 3, er = 0, sc = 0, sr = 0){
      return [// 合并第一行 0-13列
        {
          s: { //s为开始
            c: sc, //开始列
            r: sr, //可以看成开始行,实际是取值范围
          },
          e: { //e结束
            c: ec, //结束列
            r: er, //结束行
          },
        },
      ]
    },
    // 点击导出重要信息统计数据
    async downLoadStatistics(){
      const year = this.searchParams.year ? new Date(this.searchParams.year).getFullYear() + '年度' : '';
      let jsonData = this.statisticsData;
      if(jsonData.length === 0){
        const res = await getStatistics({
          year: this.searchParams.year, // 按年份
          regAddr: this.searchParams.regAddr, // 按搜索区域
        });
        jsonData = res.data || {}; // 要导出的统计数据
      }
      jsonData.areaStatistics = jsonData.areaStatistics.sort((a, b) => b.total - a.total);
      if(jsonData.curAreaStatistics && jsonData.curAreaStatistics.areaName) jsonData.areaStatistics.unshift(jsonData.curAreaStatistics);
      console.log(88888888, jsonData);
      const curSearchArea = jsonData.curSearchArea;
      const workbook = XLSX.utils.book_new();
      // 按照辖区统计
      const areaStatistics = [
        [year + curSearchArea + '重要信息辖区统计'],
        ['序号', '辖区', '重要信息总数', '重要信息解除', '解除率'],
      ]
      let total4 = 0, over4 = 0, index4 = 0;
      jsonData.areaStatistics.forEach(ele => {
        // total4 += ele ? ele.total : 0;
        // over4 += ele ?ele.over : 0;
        index4 ++;
        const curOverRatio = this.overRatio(ele.over, ele.total);
        areaStatistics.push([index4, ele.areaName, ele.total, ele.over, curOverRatio ])
      });
      // areaStatistics.push([++index4, '汇总', total4, over4, this.overRatio(over4, total4)]);
      const sheet4 = XLSX.utils.aoa_to_sheet(areaStatistics);
      sheet4["!merges"] = this.mergeColumns(4);
      XLSX.utils.book_append_sheet(workbook, sheet4, "重要信息辖区统计");

      // 重要信息分类统计
      const classificationStatistics = [ 
        [year + curSearchArea + '重要信息分类统计'],
        ['重要信息分类', '重要信息数量', '解除数量', '解除率' ],
        ['检测重要信息', jsonData.classificationStatistics.detect, jsonData.classificationStatistics.detectOver, this.overRatio(jsonData.classificationStatistics.detectOver, jsonData.classificationStatistics.detect) ],
        ['体检重要信息', jsonData.classificationStatistics.physical, jsonData.classificationStatistics.physicalOver, this.overRatio(jsonData.classificationStatistics.physicalOver, jsonData.classificationStatistics.physical) ],
        ['诊断重要信息', jsonData.classificationStatistics.diagnosis, jsonData.classificationStatistics.diagnosisOver, this.overRatio(jsonData.classificationStatistics.diagnosisOver, jsonData.classificationStatistics.diagnosis) ],
      ];
      const sheet1 = XLSX.utils.aoa_to_sheet(classificationStatistics); // aoa_to_sheet竖排
      const defaultMergeColumns = this.mergeColumns();
      sheet1["!merges"] = defaultMergeColumns;
      XLSX.utils.book_append_sheet(workbook, sheet1, "重要信息分类统计");

      // 风险等级统计
      // const leverStatistics = [ // 横排
      //   { '一级重要信息': jsonData.leverStatistics.lever1, '二级重要信息': jsonData.leverStatistics.lever2, '三级重要信息': jsonData.leverStatistics.lever3 },
      // ];
      const leverStatistics = [ // 竖排
        [year + curSearchArea + '风险等级统计'],
        ['风险等级', '重要信息数量', '解除数量', '解除率' ],
        ['一级重要信息', jsonData.leverStatistics.lever1, jsonData.leverStatistics.lever1Over, this.overRatio(jsonData.leverStatistics.lever1Over, jsonData.leverStatistics.lever1) ],
        ['二级重要信息', jsonData.leverStatistics.lever2, jsonData.leverStatistics.lever2Over, this.overRatio(jsonData.leverStatistics.lever2Over, jsonData.leverStatistics.lever2) ],
        ['三级重要信息', jsonData.leverStatistics.lever3, jsonData.leverStatistics.lever3Over, this.overRatio(jsonData.leverStatistics.lever3Over, jsonData.leverStatistics.lever3) ],
      ];
      const sheet2 = XLSX.utils.aoa_to_sheet(leverStatistics); // json_to_sheet横排
      sheet2["!merges"] = defaultMergeColumns;
      XLSX.utils.book_append_sheet(workbook, sheet2, "风险等级统计");

      // 重要信息处置状态统计
      const statusStatistics = [
        [year + curSearchArea + '重要信息处置统计'],
        ['处置状态', '重要信息数量' ],
        ['待处理', jsonData.statusStatistics.toDoNum ],
        ['已退回', jsonData.statusStatistics.processed ],
        ['已解除', jsonData.statusStatistics.over ],
        ['已撤销', jsonData.statusStatistics.revoke ],
      ];
      jsonData.areaStatistics.forEach(ele => {
        statusStatistics[1].push(ele.areaName);
        statusStatistics[2].push(ele.toDoNum);
        statusStatistics[3].push(ele.processed);
        statusStatistics[4].push(ele.over);
        statusStatistics[5].push(ele.revoke);
      });
      const sheet3 = XLSX.utils.aoa_to_sheet(statusStatistics);
      sheet3["!merges"] = this.mergeColumns(statusStatistics[2].length);
      XLSX.utils.book_append_sheet(workbook, sheet3, "重要信息处置统计");

      // 按月份统计
      const monthStatistics = [
        [year + curSearchArea + '重要信息按月统计'],
        ['月份', '重要信息总数', '重要信息解除', '解除率' ],
      ]
      let total5 = 0, over5 = 0;
      jsonData.monthStatistics.forEach((ele, i) => {
        total5 += ele ? ele.total : 0;
        over5 += ele ?ele.over : 0;
        const curOverRatio = this.overRatio(ele.over, ele.total);
        monthStatistics.push([i+1+'月', ele?ele.total:0, ele?ele.over:0, curOverRatio])
      });
      monthStatistics.push(['汇总', total5, over5, this.overRatio(over5, total5)]);
      const sheet5 = XLSX.utils.aoa_to_sheet(monthStatistics);
      sheet5["!merges"] = defaultMergeColumns;
      XLSX.utils.book_append_sheet(workbook, sheet5, "重要信息按月统计");
      

      /* get binary string as output */
      let wbOut = XLSX.write(workbook, {
        bookType: "xlsx",
        bookSST: false,
        type: "array"
      });

      try {
        FileSaver.saveAs(
          new Blob([wbOut], { type: "application/octet-stream" }),
          year + curSearchArea + "重要信息统计" + new Date().toLocaleString("chinese",{ year: "numeric", month: "2-digit",
          day: "2-digit" })+".xlsx"
        );
      } catch (e) {
        console.log(e, wbOut);
      }
    },
    toRecycleBin(){
      this.$router.push({name:'recycleBin'});
    },
    // 选择注册地
    regaddChangeOptionFunc(v) {
      this.searchParams.regAddr = v || [];
      this.$refs.regAddCas.dropDownVisible = false;
      this.$store.dispatch('warning/list',this.searchParams)
    },
  },
};
</script>
<style scoped>
.dr-toolbar .el-col{
  padding-right: 15px;
}
</style>
<style lang="scss">
  .dr-toolbar.search{
    margin-bottom: 20px;
    // input{
    //   background-color: rgb(235, 245, 255);
    //   color: #333;
    //   border: 0;
    // }
  }
</style>
