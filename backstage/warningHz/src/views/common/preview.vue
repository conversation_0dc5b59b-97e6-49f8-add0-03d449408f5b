<template>
  <!-- 预览文件的地方（用于渲染） -->
  <div class="preview" v-if="preview">
    <i
      class="el-icon-close"
      style="position: fixed; right: 20px; top: 5px; color: #fff; font-size: 36px"
      @click="closePreview"
    ></i>
    <div ref="file"></div>
  </div>
</template>
<script>
let docx = require("docx-preview");
import axios from "axios";

export default {
  name: "preview",
  data() {
    return {
      show: false,
    };
  },
  props: {
    filePath: String,
    preview: Boolean,
  },
  watch: {
    preview(val) {
      // console.log(this.filePath,this.preview, 'wwwwwwwwwwwwwwwwwww');
      // this.show = val;
      if (val && this.filePath) {
        axios({
          method: "get",
          responseType: "blob",
          url: this.filePath,
        }).then(({ data }) => {
          docx.renderAsync(data, this.$refs.file); // 渲染到页面
          // console.log(a,window.location.origin + this.$route.path, 'rrrrrrrrrrrrrrrr');
          // let routeData = this.$router.resolve({
          //   path: this.$route.path,
          // });
          // window.open(routeData.href, '_blank');
        });
      }
    },
  },
  methods: {
    closePreview() {
      // this.preview = false;
      this.$emit("fatherMethod", false);
    },
  },
};
</script>

<style scoped>
.preview {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  width: 100%;
  overflow: scroll;
}
.preview .el-icon-close {
  position: fixed;
  right: 20px;
  top: 5px;
  color: #fff;
  font-size: 36px;
  cursor: pointer;
}
</style>
