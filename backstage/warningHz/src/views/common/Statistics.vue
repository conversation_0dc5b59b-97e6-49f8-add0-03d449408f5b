<template>
    <el-row class="wrap">
        <el-col :span="4" >
            <div class="section selected">
                <img class="label" src="@/assets/1.png"/>
                <span class="txt">重要信息总数</span>
                <span class="word">{{this.count.totle || 0}}</span>
            </div>            
        </el-col>
        <el-col :span="4" >
            <div class="section point" @click="chooseType(1)" :class="type == 1 ? 'selected' : ''">
                <img class="label" src="@/assets/2.png" v-show="type == 1"/>
                <img class="label" src="@/assets/2_active.png" v-show="type != 1"/>
                <span class="txt">检测重要信息</span>
                <span class="word">{{this.count.detect || 0}}</span>
            </div>            
        </el-col>
        <el-col :span="4" >
            <div class="section point" @click="chooseType(2)" :class="type == 2 ? 'selected' : ''">
                <img class="label" src="@/assets/3.png" v-show="type == 2"/>
                <img class="label" src="@/assets/3_active.png" v-show="type != 2"/>
                <span class="txt">体检重要信息</span>
                <span class="word">{{this.count.physical || 0}}</span>
            </div>            
        </el-col>
        <el-col :span="4" >
            <div class="section point" @click="chooseType(3)" :class="type == 3 ? 'selected' : ''">
                <img class="label" src="@/assets/3.png" v-show="type ==3"/>
                <img class="label" src="@/assets/3_active.png" v-show="type != 3"/>
                <span class="txt">诊断重要信息</span>
                <span class="word">{{this.count.diagnosis || 0}}</span>
            </div>            
        </el-col>
        <el-col :span="4" class="my-select">
          <el-select v-model="status"  placeholder="重要重要信息状态" clearable>
            <el-option v-for="(item, index) in statusList" :key="index" :label="item.name" :value="item.status">
              <span :class="'circle'+index" class="circle"></span> &nbsp;
              <span >{{item.name}}</span> &nbsp;
              <strong>{{item.value}}</strong>
            </el-option>
          </el-select>
          <span class="curNum" v-if="status!==''">{{statusList.filter(ele => ele.status == status)[0].value}}</span>
        </el-col>

        <el-col :span="4" class="my-select" v-show="searchParams.tabName==='1'">
          <el-select v-model="searchParams.lever" placeholder="风险等级" clearable>
            <el-option label="全部" value="all"></el-option>
            <el-option label="一级" value="1"></el-option>
            <el-option label="二级" value="2"></el-option>
            <el-option label="三级" value="3"></el-option>
            <!-- <el-option label="超标提示" value="4"></el-option> -->
          </el-select>
        </el-col>

        <el-col :span="4" class="my-select" v-if="!isHz">
          <el-select v-model="searchParams.modifiedStatus" placeholder="修改数据申请" clearable>
            <el-option label="待审核" :value="2"></el-option>
            <el-option label="已同意" :value="3"></el-option>
            <el-option label="已驳回" :value="4"></el-option>
          </el-select>
        </el-col>
    </el-row>
</template>
<script>
export default {
  props: {
    count: Object,
    searchParams: Object,
    isHz: Boolean,
  },
  data() {
    return {
      type: '', // 重要重要信息类型
      status: '', // 重要重要信息状态
    };
  },
  created() {
    this.type = this.searchParams.type;
    this.status = this.searchParams.status;
  },
  watch: {
    status(n1, n2){
      if(n1 !== n2) this.searchParams.status = n1 === '' ? '' : +n1;
      if(n2 !== '') {
        this.$store.dispatch('warning/list',this.searchParams)
      }
    },
    type(n1, n2){
      if(n1 != n2) {
        this.searchParams.type = n1 ? +n1 : '';
        this.$store.dispatch('warning/list',this.searchParams)
      }
    },
    'searchParams.status': {
      deep:true,
      handler:function(newV,oldV){
        this.status = newV;
        this.$store.dispatch('warning/list',this.searchParams)
      }
    },
    'searchParams.lever': {
      deep:true,
      handler:function(newV,oldV){
        this.$store.dispatch('warning/list',this.searchParams)
      }
    },
    
  },
  computed: {
     statusList(){
        const list =  [
            {
                name: '待处理', 
                value: this.count.toDoNum ||  0, 
                status: 0,
            },
            {
                name: '已处理', 
                value: this.count.processed || 0, 
                status: 1,
            },
            {
                name: '待审核', 
                value: this.count.toBeRectified || 0,
                status: 2, 
            },
            {
                name: '已解除', 
                value: this.count.over || 0, 
                status: 3,
            },
            {
                name: '已驳回', 
                value: this.count.reject || 0, 
                status: 4,
            },
            {
                name: '已撤销', 
                value: this.count.revoke || 0, 
                status: 5,
            },
        ]
        if(this.isHz){
            list[1].name = '已退回';
        }
        return list;
     } 
  },
  methods: {
    chooseType(n){
      this.type = this.type == n ? '' : n;
    }
  },
 
};
</script>
<style scoped lang="scss">
.wrap{
  margin-bottom: 20px;
  .point:hover{
    cursor: pointer;
  }
}
.my-select{ // 自制下拉框
  position: relative;
  .curNum{
    display: inline-block;
    position: absolute;
    top: 0px;
    left: 50%;
    height: 60px;
    transform: translateX(-50%);
    color: rgb(42, 145, 252);
    font-size: 28px;
    letter-spacing: 1px;
    font-family: DINAlternate-Bold;
    line-height: 60px;
    text-align: center;
  }
}

.section {
  height: 60px;
  border-radius: 4px;
  background-color: rgb(235, 245, 255);
  border: 1px solid rgb(191, 219, 248);
  width: calc(100% - 16px);
  justify-content: space-around;
  align-items: center;
  padding: 10px;
  display: flex;
  .txt {
    overflow-wrap: break-word;
    color: #000;
    font-size: 15px;
    letter-spacing: 1px;
    font-family: PingFangSC-Medium;
    font-weight: bold;
    white-space: nowrap;
    line-height: 16px;
    text-align: center;
  }
  .word {
    overflow-wrap: break-word;
    color: rgb(42, 145, 252);
    font-size: 28px;
    letter-spacing: 1px;
    font-family: DINAlternate-Bold;
    white-space: nowrap;
    line-height: 32px;
    text-align: right;
  }
}
.section.selected{
  background-color:#0063e0;
  border-color:#0063e0;
  .txt{
    color: #fff;
  }
  .word{
    color: rgb(235, 245, 255);
  }
}
.label {
  width: 36px;
  height: 36px;
}
.el-col:nth-of-type(3){
  .label {
    width: 39px;
    height: 39px;
  }
}
.circle{
  width: 13px;
  height: 13px;
  display: inline-block;
  border-radius: 50%;
}
.circle0{
  background: rgb(80, 164, 252);
}
.circle1{
  background: rgb(103, 194, 58);
}
.circle2{
  background: rgb(231, 162, 59);
}
.circle3{
  background: green;
}
.circle4{
  background: rgb(244, 108, 108);
}
.circle5{
  background: rgb(145, 147, 152);
}


</style>
