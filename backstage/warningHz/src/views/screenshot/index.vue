<template>
  <div :class="classObj">
    <div class="main-container">
      <div class="videoLeft">
        <div class="reviewVideo">
          <JoinForm ref="formRef" v-show="false"></JoinForm>
          <div class="topStyle">xxxx</div>
          <div
            class="noJoin"
            v-show="Object.keys(remoteUsers).length === 0"
          ></div>
          <div class="videoStyle">
            <div v-if="joined" class="localVideoStyle">
              <AgoraVideoPlayer
                :isLocal="true"
                :videoTrack="videoTrack"
                :audioTrack="audioTrack"
                :width="'8.896rem'"
                :height="'9.88625rem'"
                :style="{
                  position: 'absolute',
                  'z-index': '1',
                  right: '-20px',
                  top: '3.05vh',
                }"
              ></AgoraVideoPlayer>
            </div>
            <div
              v-if="Object.keys(remoteUsers).length"
              class="remoteVideoStyle"
            >
              <AgoraVideoPlayer
                v-for="item in remoteUsers"
                :key="item.uid"
                :videoTrack="item.videoTrack"
                :audioTrack="item.audioTrack"
              >
              </AgoraVideoPlayer>
            </div>
          </div>
          <div class="bottomBtn">
            <el-button
              :style="{ marginLeft: '10px' }"
              :disabled="joined"
              @click="getCameraAndJoin"
              size="mini"
              type="text"
              >加入会议</el-button
            >
            <el-button
              :disabled="!joined"
              @click="leave"
              size="mini"
              type="text"
              >离开会议</el-button
            >
            <el-button
              :disabled="!joined"
              type="text"
              @click="takeScreenshot"
              size="mini"
              icon="el-icon-camera"
              round
              >截屏</el-button
            >
            <el-button
              :disabled="!joined"
              type="text"
              @click="controlCamera"
              size="mini"
              icon="el-icon-camera-solid"
              round
              >{{ cameraText }}</el-button
            >
            <el-button
              :disabled="!joined"
              @click="start"
              size="mini"
              icon="el-icon-video-camera"
              type="text"
              >开始录屏</el-button
            ><el-button
              :disabled="!joined"
              @click="stop"
              size="mini"
              icon="el-icon-video-camera"
              type="text"
              >结束录屏</el-button
            >
            <el-button
              v-show="recoderSign"
              :disabled="true"
              size="mini"
              icon="el-icon-loading"
              type="text"
              >录屏中</el-button
            >
          </div>
          <div class="bottom-warning" v-if="warnList.length > 0">
            <div
              style="
                width: 100%;
                margin-top: 5px;
                padding: 5px 5px;
                height: 20vh;
                overflow-y: auto;
              "
            >
              <div>
                <!-- 重要信息内容：<span style="color: #838383">{{
                  currentWarning.content[0] ? currentWarning.content[0] : ""
                }}</span> -->
                信息内容：<span
                  v-for="item2 in currentWarning.content"
                  style="color: #838383"
                  >{{ item2 }}</span
                >
              </div>
              <div style="padding-top: 10px">
                风险等级：<el-link type="warning">{{
                  currentWarning.lever
                }}</el-link>
              </div>
              <div style="padding-top: 10px">
                信息类型：<span
                  style="color: #838383"
                  v-if="currentWarning.type === 1"
                  >检测</span
                >
                <span
                  v-else-if="currentWarning.type === 2"
                  style="color: #838383"
                  >体检</span
                >
                <span
                  v-else-if="currentWarning.type === 3"
                  style="color: #838383"
                  >诊断</span
                >
              </div>
              <div style="margin-top: 10px">
                整改报告：
                <el-upload
                  class="upload-demo"
                  action=""
                  multiple
                  auto
                  accept=".jpg,.jpeg,.png,.xls,.xlsx,.pdf,.doc,.docx"
                  :auto-upload="false"
                  :on-preview="handlePreview"
                  :file-list="currentWarning.fixFiles"
                >
                  <div
                    style="
                      margin-bottom: 20px;
                      margin-top: 6px;
                      margin-left: 4px;
                    "
                  ></div>
                </el-upload>
              </div>
            </div>
            <div style="position: absolute; top: 115%; left: 550px">
              <el-button type="primary" plain @click="fixWarning"
                >解除风险</el-button
              >
              <el-button type="warning" plain @click="back">不符合</el-button>
              <el-button type="primary" plain @click="next">下一个</el-button>
              <el-button type="primary" plain @click="goPre">上一个</el-button>
            </div>
          </div>
        </div>
      </div>

      <div class="warning-right" v-if="warnList.length > 0">
        <div style="height: 75vh; overflow-y: auto; padding: 0 20px">
          <div><span>重要信息列表：</span></div>
          <div
            @click="changeWarn(item, index)"
            style="width: 100%; margin-top: 10px; padding: 10px 5px"
            v-for="(item, index) in warnList"
            :class="{ warnItem: true, bg: index === currentIndex }"
          >
            <div
              style="
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              "
            >
              信息内容：<span
                v-for="item2 in item.content"
                style="color: #838383"
                >{{ item2 }}</span
              >
            </div>
            <div style="padding-top: 10px">
              风险等级：<el-link type="warning">{{ item.lever }}</el-link>
            </div>
            <div style="padding-top: 10px">
              信息类型：<span v-if="item.type === 1" style="color: #838383"
                >检测</span
              >
              <span v-else-if="item.type === 2" style="color: #838383"
                >体检</span
              >
              <span v-else-if="item.type === 3" style="color: #838383"
                >诊断</span
              >
            </div>
            <div v-if="item.isLift">
              <span style="color: #2edc71; padding-top: 10px">已解除</span>
            </div>
            <div v-if="item.isBack">
              <span style="color: #fbc119; padding-top: 10px">已驳回</span>
            </div>
          </div>
        </div>
        <div style="margin-top: 20px; padding: 0 20px">
          <el-input
            type="textarea"
            :rows="5"
            placeholder="请输入不符合原因以及整改建议"
            v-model="remark"
          >
          </el-input>
        </div>
      </div>
      <!-- 预览 -->
      <el-dialog :visible.sync="viewerVisible" width="30%">
        <img :src="viewerImgList" alt="" width="100%" />
      </el-dialog>
      <preview
        :filePath="dialogPreviewUrl"
        :preview="showPreview"
        @fatherMethod="showPreview = false"
        style="z-index: 9999999"
      ></preview>
    </div>
  </div>
</template>

<script>
import AgoraRTC from "agora-rtc-sdk-ng";
import { showJoinedMessage, downloadImageData } from "@/utils/utils";
import JoinForm from "@/components/JoinForm";
import AgoraVideoPlayer from "@/components/AgoraVideoPlayer";
import { initEvent } from "@root/publicMethods/events";
import { getWarningVideoToken, liftWarning } from "@/api/manage";
import html2canvas from "html2canvas";
import { getAllWarning, reject } from "@/api/manage";
import io from "socket.io-client";
import preview from "../common/preview.vue";

export default {
  data() {
    return {
      remark: "",
      dialogPreviewUrl: "",
      showPreview: false,
      viewerVisible: false,
      viewerImgList: "",
      socket: null,
      lock: true,
      warnList: [],
      reason: "",
      currentWarning: {},
      client: AgoraRTC.createClient({
        mode: "rtc",
        codec: "vp8",
      }),
      joined: false,
      remoteUsers: {},
      videoTrack: null,
      audioTrack: null,
      sidebarOpened: true,
      device: "desktop",
      recoder: null,
      buffer: [],
      videoName: "录屏",
      chunks: [],
      remoteUsersVideoTrack: null,
      recoderSign: false,
      cameraStatus: true,
      cameraText: "关闭摄像头",
      error: false,
      currentIndex: 0,
      errorMes: "",
      filelist: [],
    };
  },
  components: {
    JoinForm,
    AgoraVideoPlayer,
    preview,
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    },
  },
  async created() {
    // const socket = io("http://127.0.0.1:7003", {
    //   transports: ["websocket"],
    // });
    // this.socket = socket;
    const res2 = await getAllWarning({ id: this.$route.params.EnterpriseId });
    this.warnList = res2.data.warningList;
    this.filelist = res2.data.filelist;
    this.warnList.forEach((item) => {
      item.fixFiles = [];
      item.isLift = false;
      item.isBack = false;
    });
    this.warnList.forEach((item) => {
      this.filelist.forEach((item2) => {
        if (item._id === item2.id) {
          item.fixFiles.push(item2);
        }
      });
    });
    console.log("ppppppppppppppppp", this.warnList);
    if (this.warnList.length > 0) {
      this.currentWarning = this.warnList[0];
    }
  },
  async mounted() {
    initEvent(this);
    const params = this.$route.params;
    const query = {
      channel: params.channel,
    };
    const res = await getWarningVideoToken(query);
    if (res.status === 200) {
      this.$refs.formRef.setValue(res.data);
      this.error = false;
      window.addEventListener('beforeunload', this.handleBeforeUnload);
      // this.join();
    } else {
      this.error = true;
      this.errorMes = res.message;
      this.$message.warning(res.message);
    }
  },
  beforeDestroy() {
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
    if (this.joined) {
      this.leave();
    }
  },
  methods: {
    handleBeforeUnload(event) {
      if (this.joined) {
        const message = '你确定要离开吗？请确保已经离开频道。';
        event.returnValue = message;
        return message;
      }
    },
    handlePreview(file, name) {
      //预览
      let url = file.url;
      let fileSplite = url.split(".");
      if (fileSplite[fileSplite.length - 1] === "pdf") {
        let a = document.createElement("a");
        a.href = url;
        a.target = "_blank";
        a.click();
        a.remove();
      } else if (
        fileSplite[fileSplite.length - 1] === "doc" ||
        fileSplite[fileSplite.length - 1] === "docx"
      ) {
        this.dialogPreviewUrl = url;
        this.showPreview = true;
      } else if (
        fileSplite[fileSplite.length - 1] === "xls" ||
        fileSplite[fileSplite.length - 1] === "xlsx"
      ) {
        this.excelPath = url;
      } else {
        this.viewerImgList = url;
        this.viewerVisible = true;
      }
    },
    async back() {
      const res = await reject({
        _id: this.currentWarning._id,
        remark: this.remark,
      });
      if (res.status === 200) {
        this.$message({
          type: "success",
          message: "已驳回",
        });
        this.warnList.forEach((item) => {
          if (this.currentWarning._id === item._id) {
            item.isBack = true;
          }
        });
      }
    },
    async fixWarning() {
      const res = await liftWarning({ _id: this.currentWarning._id });
      if (res.status === 200) {
        this.$message({
          type: "success",
          message: "本条重要信息已经解除",
        });
        this.warnList.forEach((item) => {
          if (this.currentWarning._id === item._id) {
            item.isLift = true;
          }
        });
      }
    },
    async openSocket(params) {
      // this.socket = io(document.location.host, {
      //   transports: ["websocket"],
      // query: {
      //   warnData: item,
      //   meetingLink:
      //     document.location.host +
      //     "/videoReview" +
      //     "/" +
      //     params.companyId +
      //     "/" +
      //     this.$route.params.contactPhoneNum.substr(-4),
      // },
      // });
      // this.socket.on("connect", (res) => {
      //   this.socket.emit("sendWarningData");
      //   this.socket.on("sendWarningDataEnd", (data) => {
      //     this.$message({
      //       type: "success",
      //       message: "重要信息数据发送完成",
      //     });
      //     this.lock = true;
      //     this.socket.close();
      //   });
      // });

      this.socket.emit("fromVue");
    },
    next() {
      this.remark = null;
      if (this.currentIndex > this.warnList.length - 1) {
        this.$message({
          type: "warning",
          message: "已经是最后一项",
        });
        return;
      }
      this.currentIndex++;
      this.currentWarning = this.warnList[this.currentIndex];
    },
    goPre() {
      this.remark = null;
      if (this.currentIndex === 0) {
        this.$message({
          type: "warning",
          message: "已经是第一项",
        });
        return;
      }
      this.currentIndex--;
      this.currentWarning = this.warnList[this.currentIndex];
    },
    changeWarn(item, index) {
      this.remark = null;
      this.currentIndex = index;
      this.currentWarning = item;
      // this.openSocket(item);
    },
    // 获取当前电脑是否有摄像头
    async getCameraAndJoin() {
      const devices = await AgoraRTC.getDevices();
      const cameras = devices.filter((device) => device.kind === "videoinput");
      if (cameras.length === 0) {
        this.$message.warning("当前设备没有摄像头");
      } else {
        this.join();
      }
    },
    controlCamera() {
      this.cameraStatus = !this.cameraStatus;
      if (this.cameraStatus) {
        this.openCamera();
        this.cameraText = "关闭摄像头";
      } else {
        this.closeCamera();
        this.cameraText = "打开摄像头";
      }
    },
    openCamera() {
      this.videoTrack.setEnabled(true);
      this.cameraStatus = true;
    },
    closeCamera() {
      this.videoTrack.setEnabled(false);
      this.cameraStatus = false;
    },
    async initTracks() {
      if (this.audioTrack && this.videoTrack) {
        return;
      }
      const tracks = await Promise.all([
        AgoraRTC.createMicrophoneAudioTrack(),
        AgoraRTC.createCameraVideoTrack({
          encoderConfig: "1080p_2",
        }),
      ]);
      this.audioTrack = tracks[0];
      this.videoTrack = tracks[1];
    },
    async handleUserPublished(user, mediaType) {
      await this.client.subscribe(user, mediaType);
      this.$set(this.remoteUsers, user.uid, user);
      this.remoteUsersVideoTrack = user.videoTrack;
    },
    handleUserUnpublished(user, mediaType) {
      if (mediaType === "video") {
        this.$delete(this.remoteUsers, user.uid);
      }
    },
    async join() {
      try {
        if (this.error) {
          this.$message.warning(this.errorMes);
          return null;
        }
        // Add event listeners to the client.
        this.client.on("user-published", this.handleUserPublished);
        this.client.on("user-unpublished", this.handleUserUnpublished);

        const options = this.$refs.formRef.getValue();
        if (!options.appId || !options.channel) {
          this.$message.error("视频通道异常，请稍后再试");
          return;
        }
        // Join a channel
        options.uid = await this.client.join(
          options.appId,
          options.channel,
          options.token || null,
          options.uid || null
        );
        await this.initTracks();
        await this.client.publish([this.videoTrack, this.audioTrack]);
        showJoinedMessage(options);
        this.joined = true;
      } catch (error) {
        console.error(error);
        this.$message.error(error.message);
      }
    },
    async leave() {
      if (this.videoTrack) {
        this.videoTrack.close();
        this.videoTrack = null;
      }
      if (this.audioTrack) {
        this.audioTrack.close();
        this.audioTrack = null;
      }
      this.remoteUsers = {};
      await this.client.leave();
      this.joined = false;
      this.cameraStatus = true;
      this.cameraText = "关闭摄像头";
      this.$message.success("已离开频道!");
    },
    takeScreenshot() {
      // html2canvas(document.body).then((canvas) => {
      //   const link = document.createElement('a');
      //   link.href = canvas.toDataURL('image/png');
      //   link.download = 'screenshot.png';
      //   link.click();
      // });
      //get imageData object picture from local video track.
      console.log(this.remoteUsersVideoTrack, 222);
      if (!this.remoteUsersVideoTrack) {
        this.$message.warning("没有其他用户");
        return null;
      }
      const imageData = this.remoteUsersVideoTrack.getCurrentFrameData();
      downloadImageData(imageData);
    },
    captureVideoFrame(videoElement) {
      let canvas = document.createElement("canvas");
      canvas.width = videoElement.videoWidth;
      canvas.height = videoElement.videoHeight;
      let ctx = canvas.getContext("2d");
      ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
      return canvas.toDataURL(); // 获取图像的DataURL
    },
    async start() {
      if (this.recoderSign) {
        this.$message.warning("已经在录制了，请先结束录制");
        return null;
      }
      if (!navigator.mediaDevices) {
        this.$message.warning("你的浏览器不支持这个特性");
        return null;
      }
      if (!this.remoteUsersVideoTrack) {
        this.$message.warning("没有其他用户");
        return null;
      } else {
        const stream = new MediaStream([
          this.remoteUsersVideoTrack.getMediaStreamTrack(),
        ]);
        this.recoder = new MediaRecorder(stream);
        this.recoder.ondataavailable = (e) => {
          this.chunks.push(e.data);
        };
        this.$message.success("开始录制");
        this.recoder.onstop = () => {
          const blob = new Blob(this.chunks, { type: "video/webm" });
          this.buffer.push(blob);
          this.download();
        };
        this.recoderSign = true;
        this.recoder.start();
      }
    },
    stop() {
      this.recoder.stop();
      this.recoderSign = false;
      this.$message.success("结束录制");
    },
    cancelDownload() {
      location.reload();
    },
    download() {
      if (this.chunks.length) {
        const blob = new Blob(this.chunks, { type: "video/webm" });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.style.display = "none";
        a.download = this.videoName + ".mp4";
        a.click();
        this.chunks = [];
      } else {
        this.$message.error("还没有录制任何内容");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.warning-right {
  position: absolute;
  padding-left: 15px;
  top: 0px;
  right: 0;
  width: calc(100% - 64.53vw);
  height: 93vh;
}
.bottom-warning {
  margin-top: 50px;
}
.warnItem:hover {
  background-color: #f3f9ff;
}
.bg {
  background-color: #f3f9ff;
}
.warnItem {
  cursor: pointer;
}
.videoLeft {
  width: 64.53vw;
  height: 92.4vh;
}
.reviewVideo {
  padding-left: 20px;
  position: relative;
  height: 77.55vh;
}
.topStyle {
  width: 64.53vw;
  height: 3.05vh;
  background-color: #000000;
  opacity: 0.6026;
}
.localVideoStyle {
  // position: absolute;
  // width: 64.53vw;
  // height: 64.5vh;
}
.noJoin {
  width: 64.53vw;
  height: 64.5vh;
  background-color: #000000;
  opacity: 0.5;
}
.remoteVideoStyle {
  // position: absolute;
  // width: 15.16vw;
  // height: 15.09vh;
}
.bottomBtn {
  position: absolute;
  // left: 20px;
  width: 64.53vw;
  // height: 3vh;
  opacity: 0.6026;
  background: #000000;
}
::v-deep .el-button {
  background-color: transparent;
  border: 0;
  color: #c0c4cc;
}
::v-deep .el-button.is-disabled,
.el-button.is-disabled:focus,
.el-button.is-disabled:hover {
  background-color: transparent;
  border: 0;
}
::v-deep .el-button:hover {
  background-color: transparent;
  border: 0;
}
</style>
