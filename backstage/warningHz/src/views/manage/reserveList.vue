<template>
  <el-row>
    <el-table
      :data="reserveList"
      stripe
      style="width: 100%"
      :header-cell-style="{
        color: '#333333',
      }"
    >
      <el-table-column prop="cname" label="单位" width="300"> </el-table-column>
      <el-table-column prop="companyContact" label="联系人" width="180">
      </el-table-column>
      <el-table-column prop="contactPhoneNum" label="联系电话" width="180">
      </el-table-column>
      <el-table-column label="预约时间" width="400" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.reserveDate[0] }}</span> <span>至</span>
          <span>{{ scope.row.reserveDate[1] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="通知状态">
        <template slot-scope="scope">
          <el-link type="success">已通知</el-link>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" width="400">
        <template slot-scope="scope">
          <el-button type="success" size="mini" @click="sendMessage(scope.row)"
            >短信通知</el-button
          >
          <el-button type="primary" size="mini" @click="gotoVideo(scope.row)"
            >进入会议</el-button
          >
          <el-button
            type="warning"
            size="mini"
            @click="handleCancelMeeting(scope.row)"
            >取消会议</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div style="display: flex; justify-content: center; margin-top: 10px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total,sizes, prev, pager, next"
        :total="count"
      >
      </el-pagination>
    </div>
  </el-row>
</template>

<script>
import {
  getReserveList,
  getAllWarning,
  sendMeetingMsg,
  cancelMeeting,
  cancelMeetingMsg,
} from "@/api/manage";
export default {
  data() {
    return {
      reserveList: [],
      count: 0,
      current: 1,
      pageSize: 10,
    };
  },
  methods: {
    async handleSizeChange(val) {
      this.pageSize = val;
      const res = await getReserveList({
        current: this.current,
        pageSize: this.pageSize,
      });
      this.reserveList = res.data[0].results;
      this.count = res.data[0].total[0].total;
    },
    async handleCurrentChange(val) {
      this.current = val;
      const res = await getReserveList({
        current: this.current,
        pageSize: this.pageSize,
      });
      this.reserveList = res.data[0].results;
      this.count = res.data[0].total[0].total;
    },
    convertDateFromString(dateString) {
      let parts = dateString.split(" ");
      let datePart = parts[0].split("-");
      let timePart = parts[1].split(":");
      let year = parseInt(datePart[0]);
      let month = parseInt(datePart[1]) - 1;
      let day = parseInt(datePart[2]);
      let hour = parseInt(timePart[0]);
      let minute = parseInt(timePart[1]);
      let second = parseInt(timePart[2]);
      let date = new Date(year, month, day, hour, minute, second);
      return date;
    },
    isMeetingExpired(meetingEndTime) {
      const currentTime = new Date();
      return (
        currentTime.getTime() >
        this.convertDateFromString(meetingEndTime).getTime()
      );
    },
    async sendMessage(row) {
      const res2 = await sendMeetingMsg({
        companyName: row.cname, // companyId
        meetingId: row.meetingId,
        time: row.reserveDate[0],
        contactPhoneNum: row.contactPhoneNum,
        type: 1,
      });
      if (res2.status === 200) {
        this.$message({
          type: "success",
          message: "已发送",
        });
      }
    },
    gotoVideo(row) {
      let isExpired = this.isMeetingExpired(row.reserveDate[1]);
      if (isExpired) {
        this.$message({
          type: "warning",
          message: "会议已结束",
        });
        return;
      }
      this.$router.push({ name: "videoReview", params: row });
    },
    handleCancelMeeting(row) {
      this.$confirm(`确定取消此次会议吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let isExpired = this.isMeetingExpired(row.reserveDate[1]);
        if (!isExpired) {
          await cancelMeetingMsg({
            companyName: row.cname,
            time: row.reserveDate[0],
            contactPhoneNum: row.contactPhoneNum,
          });
        }
        const res = await cancelMeeting({
          _id: row.EnterpriseId,
          meetingId: row._id,
        });
        if (res.status === 200) {
          this.$message({
            type: "success",
            message: "已取消会议",
          });
          const res2 = await getReserveList({
            current: this.current,
            pageSize: this.pageSize,
          });
          this.reserveList = res2.data[0].results;
          this.count = res2.data[0].total[0].total;
          // let isExpired = this.isMeetingExpired(row.reserveDate[1]);
          // if (!isExpired) {
          //   await sendMeetingMsg({
          //     companyName: row.cname,
          //     meetingId: row.meetingId,
          //     time: row.reserveDate[0],
          //     contactPhoneNum: row.contactPhoneNum,
          //     type: 2,
          //   });
          // }
        }
      });
    },
  },
  async created() {
    const res = await getReserveList({
      current: this.current,
      pageSize: this.pageSize,
    });
    this.reserveList = res.data[0].results;
    this.count = res.data[0].total[0].total;
    const res2 = await getAllWarning();
  },
};
</script>

<style scoped lang="scss">
::v-deep .el-table__fixed,
::v-deep .el-table__fixed-right {
  height: 100% !important;
}
</style>
