<template>
  <div :class="classObj" class="adminUser">
    <div class="main-container" v-if="isHz !== null">
      <transition name="el-zoom-in-center">
        <Details v-show="showDetail" :detail="detail" @refresh="getLits" />
      </transition>
      <transition name="el-zoom-in-center">
        <FileList
          v-show="showFiles"
          :files="files"
          @refresh="getLits"
          :isHz="isHz"
        />
      </transition>
      <el-row class="dr-datatable">
        <el-col :span="24">
          <el-tabs
            type="border-card"
            v-model="searchParams.tabName"
            @tab-click="handleClick"
          >
            <el-tab-pane label="重要信息" name="1">
              <TopBar
                :searchParams="searchParams"
                @search="getLits"
                :tabSwitch="tabSwitch"
                @downLoad="downLoadAll"
                :isHz="isHz"
                @sendVerifyMassage="sendVerifyMassage"
              ></TopBar>
              <Statistics
                :count="count"
                :searchParams="searchParams"
                :isHz="isHz"
                ref="statistics1"
              />
              <DataTable
                v-if="searchParams.tabName == 1"
                :isWarning="true"
                @refresh="getLits"
                :searchParams="searchParams"
                :list="list"
                :provincial="provincial"
                :warningConfig="warningConfig"
                :subsidiary="subsidiary"
                :detail="detail"
                :files="files"
                :isHz="isHz"
                :regAdd="regAdd"
                @handleSelectionChange="handleSelectionChange"
                @SMSnotificationHandle="SMSnotificationHandle"
              ></DataTable>
              <Pagination
                :searchParams="searchParams"
                v-show="list.length > 0"
                :totleCount="count.actualTotle"
              ></Pagination>
            </el-tab-pane>
            <el-tab-pane label="异常提醒" name="2">
              <TopBar
                :searchParams="searchParams"
                :isWarning="false"
                :tabSwitch="tabSwitch"
                @search="getLits"
                @downLoad="downLoadAll"
                :isHz="isHz"
                @sendVerifyMassage="sendVerifyMassage"
              ></TopBar>
              <Statistics
                :count="count"
                :searchParams="searchParams"
                :isHz="isHz"
                ref="statistics2"
              />
              <DataTable
                v-if="searchParams.tabName == 2"
                @refresh="getLits"
                :searchParams="searchParams"
                :list="list"
                :provincial="provincial"
                :warningConfig="warningConfig"
                :subsidiary="subsidiary"
                :detail="detail"
                :files="files"
                :isHz="isHz"
                :regAdd="regAdd"
                @handleSelectionChange="handleSelectionChange"
                @SMSnotificationHandle="SMSnotificationHandle"
              ></DataTable>
              <Pagination
                :searchParams="searchParams"
                v-show="list.length > 0"
                :totleCount="count.actualTotle"
              ></Pagination>
            </el-tab-pane>
            <el-tab-pane label="预约列表" name="3" v-if="showReserve">
              <ReserveList v-if="searchParams.tabName == 3"></ReserveList>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import Details from "./details";
import FileList from "./files";
import DataTable from "./dataTable.vue";
import ReserveList from "./reserveList.vue";
import TopBar from "../common/TopBar.vue";
import Statistics from "../common/Statistics.vue";
import Pagination from "../common/Pagination.vue";
import { initEvent } from "@root/publicMethods/events";
import { list, getSubsidiary, SMSnotification } from "@/api/manage";
import { mapGetters } from "vuex";
import io from "socket.io-client";
export default {
  data() {
    return {
      socket: null,
      list: [],
      searchParams: {
        curPage: 1,
        limit: 30,
        regAddr: null,
        keyWords: "",
        lever: "", // 风险等级
        status: 0, //是否处理, 默认是待处理
        type: "",
        year: new Date(),
        modifiedStatus: "", // 申请修改数据
        tabName: "1",
      },
      count: {
        // 总数统计tableData
        totle: 0, // 总条数
        over: 0, // 已解除总条数
        processed: 0, //已处理总数
        toBeRectified: 0, //待整改总数
        reject: 0, //已驳回总数
        physical: 0, // 体检数
        diagnosis: 0, // 诊断数
        detect: 0, // 检测数
        actualTotle: 0,
        revoke: 0, // 已撤销
      },
      detail: {
        // 处置方案
        show: false,
        data: {},
      },
      files: {
        // 查看整改报告
        show: false,
        status: 0,
        companyName: "",
        id: "",
        content: "",
        process: [],
        lever: "",
      },
      sidebarOpened: false,
      device: "desktop",
      subsidiary: [], // 当前监管单位下属的所有监管单位
      provincial: [], // 当前监管单位上属的所有省级监管单位
      oldSearchParams: null, // 老的搜索条件
      oldSearchTime: Date.now(),
      regAdd: [], // 当前监管单位的注册区域
      selectedData: [], // 已经勾选的重要信息数据
      lock: true,
      tabSwitch: "1",
    };
  },
  components: {
    DataTable,
    TopBar,
    Details,
    FileList,
    Pagination,
    Statistics,
    ReserveList,
  },
  watch: {
    // searchParams: { // 在各自的搜索页面进行触发list方法
    //   deep:true,
    //   handler:function(newV,oldV){
    //     // console.log('触发了watch - searchParams', newV, oldV);
    //     // this.getLits();
    //   }
    // },
    tableDatass: {
      deep: true,
      handler: function (newval, oldval) {
        this.list = newval;
      },
    },
    countss: {
      deep: true,
      handler: function (newval, oldval) {
        this.count = newval;
      },
    },
    "searchParams.tabName": {
      deep: true,
      handler: function (newV, oldV) {
        this.searchParams.keyWords = "";
        this.searchParams.status = 0;
      },
    },
    files: {
      deep: true,
      handler: function (newV, oldV) {
        console.log(newV);
        // this.showFiles = newV.show;
      },
    },
  },
  created() {
    initEvent(this); // 初始化 hideSidebar & openSidebar
    if (document.referrer.includes("adminorgGov/editAdminorg")) {
      const warningParams = sessionStorage.getItem("warningParams");
      if (warningParams) this.searchParams = JSON.parse(warningParams);
    } else if (this.$route.query.company) {
      this.searchParams.keyWords = this.$route.query.company;
    } else if (this.$route.query.year) {
      this.searchParams.year = this.$route.query.year;
    }
    this.getLits(); // 获取数据列表
    this.getSubsidiary();
  },
  computed: {
    ...mapGetters(["countss", "tableDatass", "warningConfig", "isHz", "showReserve"]),
    showDetail() {
      return this.detail.show;
    },
    showFiles() {
      return this.files.show;
    },
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    },
    address() {
      // 项目地址
      return (projectDetail, company) => {
        let res;
        if (projectDetail && projectDetail.workAddress) {
          res = projectDetail.workAddress.map(
            (ele) => ele.name || ele.address || ""
          );
        } else if (company && company.workAddress) {
          res = company.workAddress.map((ele) => ele.address || "");
        }
        return res && res.length ? res : [];
      };
    },
  },
  methods: {
    // tabs标签页的切换  两个页面来回切换的时候，需要讲前一个页面的数据清除掉，触发一个store中的方法进行删除
    handleClick(tab, event) {
      this.searchParams.year = "";
      this.searchParams.keyWords = "";
      this.tabSwitch = tab.name;
      this.searchParamss = {
        curPage: 1,
        limit: 30,
        regAddr: null,
        keyWords: "",
        lever: "", // 风险等级
        status: 0, //是否处理, 默认是待处理
        type: "",
        year: new Date(),
        modifiedStatus: "", // 申请修改数据
        tabName: tab.name,
      };
      this.searchParams.year = new Date();
      this.$store.dispatch("warning/list", this.searchParamss);
      
      const selectedEle = this.$refs[`statistics${tab.name}`].$el.querySelector(
        ".section.point.selected"
      );
      if(selectedEle) selectedEle.classList.remove("selected");
    },
    // 操作table多选框
    async handleSelectionChange(selectedArr) {
      if (
        selectedArr.length === this.searchParams.limit &&
        selectedArr.length !== this.count.actualTotle
      ) {
        // 全选
        const res = await list({ ...this.searchParams, limit: 10000 });
        selectedArr = res.data;
      }
      this.selectedData = selectedArr;
    },
    // 重要信息短信提醒
    sendVerifyMassage() {
      if (this.selectedData.length === 0) {
        this.$confirm(
          "无选中的重要信息，请问是否对筛选的所有待处理重要信息进行短信提醒?",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(async () => {
            const res = await list({
              ...this.searchParams,
              status: 0,
              limit: 10000,
            });
            const selectedIds = res.data.map((ele) => ele._id);
            this.SMSnotificationHandle(selectedIds);
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消操作",
            });
          });
      } else {
        const selectedIds = this.selectedData.map((ele) => ele._id);
        this.SMSnotificationHandle(selectedIds);
      }
    },
    // 发送短信
    SMSnotificationHandle(warningIds = []) {
      if (warningIds.length === 0) return;
      SMSnotification({ warningIds }).then((res) => {
        if (res.status === 200) {
          this.$message({
            type: "success",
            message: warningIds.length === 1 ? "短信提醒发送完成" : res.message,
          });
          if (warningIds.length === 1) {
            this.lock = true;
            this.getLits();
            return;
          }
          this.openSocket();
        } else {
          this.$message({
            type: "warning",
            message: `短信提醒发送失败: ${res.message || ""}`,
          });
        }
      });
    },
    // 开启websocket
    async openSocket() {
      this.socket = io(document.location.host, { transports: ["websocket"] });
      this.socket.on("connect", (res) => {
        this.socket.emit("watchSMSnotification");
        this.socket.on("watchSMSnotificationEnd", (data) => {
          this.$message({
            type: "success",
            message: "重要信息短信提醒发送完成",
          });
          this.lock = true;
          this.getLits();
          this.socket.close();
        });
      });
    },
    // 点击下载 下载所有数据
    async downLoadAll() {
      let jsonData = this.selectedData;
      if (jsonData.length === 0) {
        const res = await list({ ...this.searchParams, limit: 10000 });
        jsonData = res.data;
      }
      const leverName = ["一级", "二级", "三级", "四级"];
      const status = [
        "待处理",
        "已处理",
        "已整改",
        "已解除",
        "已驳回",
        "已撤销",
      ];
      const addrLen = this.regAdd.length;
      // 列标题，逗号隔开，每一个逗号就是隔开一个单元格
      let str = `序号,企业名称,重要信息类型,区域,地址,联系人,联系方式,重要信息内容,重要信息时间,整改时间,解除/撤销时间,风险等级,重要信息状态\n`;
      // 增加\t为了不让表格显示科学计数法或者其他格式
      for (let i = 0; i < jsonData.length; i++) {
        const item = jsonData[i];
        const content = item.content
          .join("；")
          .replace(/,/g, "，")
          .replace(/[\r\n\t]/g, "");
        const address = this.address(item.projectDetail, item.company); // 地址
        str += `${i + 1 + "\t"},`;
        str += `${(item.company.cname || item.companyName) + "\t"},`;
        str += `${item.type === 1 ? "检测" : "体检" + "\t"},`;
        str += `${
          item.workAddress
            .map((ele) => ele.slice(addrLen).join("/"))
            .join("；") + "\t"
        },`;
        str += `${address.join("；") + "\t"},`;
        str += `${
          (item.projectDetail
            ? item.projectDetail.companyContact
            : item.adminuser.name || item.company.contract || "") + "\t"
        },`;
        str += `${
          (item.projectDetail
            ? item.projectDetail.contactPhoneNum
            : item.adminuser.phoneNum || item.company.phoneNum || ""
          ).toString() + "\t"
        },`;
        str += `${content + "\t"},`;
        str += `${item.ctime + "\t"},`;
        str += `${item.rectifyTime || "" + "\t"},`;
        str += `${
          item.status === 3 || item.status === 5
            ? item.process[item.process.length - 1].time
            : "" + "\t"
        },`;
        str += `${leverName[+item.lever - 1] + "\t"},`;
        str += `${status[+item.status] + "\t"},`;
        str += "\n";
      }
      // encodeURIComponent解决中文乱码
      const uri =
        "data:text/csv;charset=utf-8,\ufeff" + encodeURIComponent(str);
      // 通过创建a标签实现
      const link = document.createElement("a");
      link.href = uri;
      // 对下载的文件命名
      link.download = "重要信息数据表.csv";
      link.click();
    },
    // 获取当前监管单位下属的所有监管单位,以及上属单位
    getSubsidiary() {
      getSubsidiary().then((res) => {
        console.log("获取当前监管单位下属的所有监管单位:", res.data);
        if (res && res.data) this.subsidiary = res.data.subsidiary;
        this.provincial =
          res.data.current.length == 2 ? res.data.provincial : [];
      });
    },
    // 获取列表
    getLits() {
      if (this.lock) {
        this.lock = false;
      } else {
        return;
      }
      if (
        Date.now() - this.oldSearchTime < 5000 &&
        JSON.stringify(this.searchParams) ==
          JSON.stringify(this.oldSearchParams)
      ) {
        return; // 加强防抖！！！
      }
      this.$store.dispatch("warning/list", this.searchParams);
      // list(this.searchParams).then(res => {
      //   console.log('更新啦~~~~~')
      //   this.list = res.data || [];
      //   this.count = res.count;
      this.oldSearchTime = Date.now();
      this.oldSearchParams = JSON.parse(JSON.stringify(this.searchParams));
      this.lock = true;
      // })
    },
  },
  beforeDestroy() {
    if (this.socket) {
      this.socket.close();
    }
  },
};
</script>
