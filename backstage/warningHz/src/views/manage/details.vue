<template>
  <div class="wrap">
    <el-card class="box-card" shadow="always">
      <div class="header">
        <span style="font-size:1.2em;font-weight: bold;color:#409EFF" class="el-icon-paperclip"> 处置方案</span>
        <span class="el-icon-error close" @click="closeDetail"></span>
      </div>
      <div class="text item">
        
        <el-form :model="dynamicValidateForm" ref="dynamicValidateForm" label-position="top" label-width="120px"  class="applyForm" >
          <el-card class="box-card jump" shadow="hover" id="org" ref="org">
            <div slot="header" class="clearfix">
              <el-row :gutter="20">
                <el-col :xs="20" :sm="21" :md="22" :lg="22" :xl="22">
                  <strong class="title">{{detailData.companyName}}</strong>
                </el-col>
              </el-row>
            </div>
            <div class="enterpriseFormBox">
              <el-row :gutter="20">
                <el-form-item
                  v-for="(domain, index) in dynamicValidateForm.domains"
                  :label="'处理记录' + (index+1)"
                  :key="domain.key"
                  :prop="'domains.' + index + '.value'"
                  :rules="{
                    required: true, message: '记录不能为空', trigger: 'blur'
                  }"
                >
                  <el-input v-model="domain.value"></el-input>
                  <el-button @click.prevent="removeDomain(domain)">删除</el-button>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submitForm('dynamicValidateForm')">提交</el-button>
                  <el-button @click="addDomain">新增记录</el-button>
                  <el-button @click="resetForm('dynamicValidateForm')">重置</el-button>
                </el-form-item>
               
              </el-row>
            </div>
          </el-card>
        </el-form>

      </div>
  </el-card>
</div>
  
</template>
<script>
import { disposalWarningPlan } from "@/api/manage";
export default {
  props: {
    detail: {
      type: Object,
      default: {}
    }
  },
  data(){
    return {
     dynamicValidateForm: {
        domains: [{
          key: Date.now(),
          value: ''
        }],
        // email: ''
      }
    }
  },
  computed: {
    detailData(){
      return this.detail.data;
    }
  },
  methods: {
    closeDetail(){
      this.detail.show = false;
      this.resetForm('dynamicValidateForm');
      this.dynamicValidateForm = {
        domains: [{
          key: Date.now(),
          value: ''
        }],
      }
    },
    // 提交表单
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          console.log(this.dynamicValidateForm.domains);
          disposalWarningPlan({
            disposalPlan: this.dynamicValidateForm.domains,
            _id: this.detail.data.id
          }).then(res => {
            // console.log(res);
            if(res.status == 200){
              this.$message.success(this.detailData.companyName + '风险处置方案添加成功');
              this.detail.show = false;
              this.$emit('refresh'); //刷新列表
            }
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    removeDomain(item) {
      var index = this.dynamicValidateForm.domains.indexOf(item)
      if (index !== -1) {
        this.dynamicValidateForm.domains.splice(index, 1)
      }
    },
    addDomain() {
      this.dynamicValidateForm.domains.push({
        value: '',
        key: Date.now()
      });
    }
  },
};
</script>

<style scoped lang="scss">
.text.item{
  height: calc(100vh - 200px);
  overflow: scroll;
}
.header{
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .close{
      // float: right!important; 
      // padding: 3px 0;
      font-size: 1.3em;
      cursor: pointer;
      opacity: .7;
      :hover{
        opacity: .9;
      }
    }
  }
  .demo-form-inline .el-form-item{
    width: 50%;
    display: inline-block;
  }
  .wrap{
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba($color: (#000000), $alpha: 0.6);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    .box-card{
      padding: 10px 3% 30px;
      box-sizing: border-box;
      // width: 70%;
      min-width: 800px;
      // max-height: 88vh;
      // overflow-y: hidden;
      position: relative;
      .el-row:hover{
        background-color: #f5f5f5;
      }
      .el-input{
        width: 90%;
      }
      .close{
        float: right; 
        padding: 3px 0;
        font-size: 1.3em;
        cursor: pointer;
        opacity: .7;
        :hover{
          opacity: .9;
        }
      }
      .el-row{
        padding: 10px 0;
      }
    }
  }
   .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both
  }

  
  /* 滚动槽 */
  ::-webkit-scrollbar-track {
  border-radius:10px;
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
  border-radius:10px;
  /* background:black; */
  }
</style>
