<template>
  <el-row>
    <el-table
      stripe
      align="center"
      :expand-row-keys="expands"
      v-loading="loading"
      ref="multipleTable"
      :data="list"
      tooltip-effect="dark"
      style="width: 100%"
      row-key="_id"
      class="warning-table"
      :row-class-name="tableRowClassName"
      :max-height="tableHeight"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column type="selection" width="35" fixed></el-table-column>
      <el-table-column type="expand" fixed width="35">
        <template slot-scope="props">
          <span>{{ props.row.name }}</span>
          <div class="process">
            <strong class="left">事件经过：</strong>
            <el-steps
              :space="70"
              :active="props.row.process.length"
              finish-status="success"
              class="right"
              direction="vertical"
            >
              <el-step
                :title="proce.thing.replace(/预警/g, '重要信息')"
                :description="
                  proce.time +
                  ' ' +
                  (proce.disposalPlan.map((ele) => ele.value || '') || '') +
                  (proce.remark || '')
                "
                v-for="(proce, i) in props.row.process"
                :key="i"
              ></el-step>
            </el-steps>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="企业名称"
        width="150"
        fixed
        v-if="showCol('企业名称')"
      >
        <template slot-scope="scope">
          <!-- <el-tooltip v-if="!scope.row.company._id" class="item" effect="dark" content="该企业已在系统中注销" placement="top">
            <span>{{ scope.row.companyName || ''}}</span>
          </el-tooltip> -->
          <span v-if="isHz">{{
            scope.row.company.cname || scope.row.companyName
          }}</span>
          <span
            v-else
            @click.stop="toAdminorg(scope.row.companyId)"
            class="alink"
            >{{ scope.row.company.cname || scope.row.companyName }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        label="类型"
        width="50"
        align="center"
        v-if="showCol('类型')"
      >
        <template slot-scope="scope">
          <span>{{ ["检测", "体检", "诊断"][scope.row.type - 1] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="区域"
        :width="(4 - regAdd.length) * 70 || 40"
        v-if="showCol('区域')"
      >
        <template slot-scope="scope">
          <span
            v-if="!scope.row.workAddress || scope.row.workAddress.length === 0"
          ></span>
          <template v-else>
            <p v-for="(addrArr, i) in scope.row.workAddress" :key="i">
              <span v-for="name in addrArr" :key="name"
                >{{ regAdd.includes(name) ? "" : name }}
              </span>
            </p>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="地址" width="150" v-if="showCol('地址')">
        <template slot-scope="scope">
          <div
            v-for="(name, i) in address(
              scope.row.projectDetail,
              scope.row.company
            )"
            :key="i"
          >
            {{ name || "" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="联系人" width="60" v-if="showCol('联系人')">
        <template slot-scope="scope">
          <span>{{
            scope.row.projectDetail
              ? scope.row.projectDetail.companyContact
              : scope.row.adminuser.name || scope.row.company.contract || ""
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系方式" width="110" v-if="showCol('联系方式')">
        <template slot-scope="scope">
          <span @click="showPhone(scope.row)" style="cursor: pointer">{{
            scope.row.projectDetail
              ? scope.row.projectDetail.contactPhoneNum
              : scope.row.adminuser.phoneNum || scope.row.company.phoneNum || ""
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="重要信息内容"
        min-width="350"
        v-if="showCol('重要信息内容')"
      >
        <template slot-scope="scope">
          <span @click="rowClickHandle(scope.row)" style="cursor: pointer"
            >{{ scope.row.content[0] }}
            {{ scope.row.content.length > 1 ? "......" : "" }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="整改时间" width="100" align="center">
        <template slot-scope="props" v-if="props.row.rectifyTime">
          {{ props.row.rectifyTime }}
        </template>
      </el-table-column>
      <el-table-column label="解除/撤销时间" width="110" align="center">
        <template
          slot-scope="props"
          v-if="props.row.status === 3 || props.row.status === 5"
        >
          {{ props.row.process[props.row.process.length - 1].time }}
        </template>
      </el-table-column>
      <!-- 风险等级 -->
      <el-table-column
        width="65px"
        align="center"
        class="warning-lever"
        fixed="right"
      >
        <template slot="header" slot-scope="scope">
          <el-popover placement="bottom" width="520" trigger="hover">
            <div style="padding: 0px 10px">
              <h4>风险等级的计算：</h4>
              <p>将根据企业的检测结果和体检结果，生成风险风险等级。</p>
              <p v-if="searchParams.tabName === '2'">
                异常提醒（四级重要信息）：职业健康检查发现复查；危害因素检测结果非严重危害因素超标1个点位以上；噪声85dB（A）及以上。
              </p>
              <template v-else>
                <p>一级重要信息：新发职业病病例1例及以上。</p>
                <p>
                  二级重要信息：有疑似职业病案例1例及以上；严重危害因素超标一个点位或一个以上点位。
                </p>
                <p>
                  三级重要信息：职业健康检查发现禁忌证；其他危害因素超标1倍以上。
                </p>
              </template>
            </div>
            <span slot="reference"
              >等级 <span class="el-icon-question"></span
            ></span>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <div slot="reference" class="name-wrapper">
            <span v-if="scope.row.lever > 2" class="warning2">{{
              leverName[+scope.row.lever - 1]
            }}</span>
            <span v-else class="danger">{{
              leverName[+scope.row.lever - 1]
            }}</span>
            <span
              v-if="
                scope.row.lever == 3 &&
                scope.row.process[scope.row.process.length - 1].thing.includes(
                  '升级重要信息'
                )
              "
              class="el-icon-top"
              style="color: #f56c6c"
            ></span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="状态"
        width="50px"
        prop="projectsCount"
        fixed="right"
        align="center"
      >
        <template slot-scope="scope">
          <div slot="reference" class="name-wrapper" style="font-size: 13px">
            <span style="color: rgb(80, 164, 252)" v-if="scope.row.status == 0"
              >待处理</span
            >
            <span
              style="color: rgb(103, 194, 58)"
              v-else-if="scope.row.status == 1"
              >{{ isHz ? "已退回" : "已处理" }}</span
            >
            <span
              style="color: rgb(231, 162, 59)"
              v-else-if="scope.row.status == 2"
              >已整改</span
            >
            <span
              style="color: rgb(244, 108, 108)"
              v-else-if="scope.row.status == 4"
              >已驳回</span
            >
            <span style="color: green" v-else-if="scope.row.status == 3"
              >已解除</span
            >
            <span style="color: rgb(145, 147, 152)" v-else>已撤销</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="处理"
        :width="isHz ? 180 : 140"
        fixed="right"
        align="center"
      >
        <template slot-scope="scope">
          <template v-if="[0, 1, 2, 4].includes(scope.row.status)">
            <template v-if="!isHz">
              <el-button
                type="primary"
                plain
                v-if="searchParams.tabName === '2'"
                size="mini"
                @click.stop="
                  changeStatus(
                    scope.row._id,
                    1,
                    scope.row.company.cname,
                    scope.row.content.join(';'),
                    scope.row.company.adminUserId
                  )
                "
                >督促企业</el-button
              >
              <el-button
                type="primary"
                plain
                v-else
                size="mini"
                @click.stop="
                  changeStatus(scope.row._id, 2, scope.row.company.cname)
                "
                >处置方案</el-button
              >
            </template>

            <el-button
              v-if="isWarning && showReserve"
              type="primary"
              plain
              size="mini"
              :disabled="
                'isReserve' in scope.row.company && scope.row.company.isReserve
              "
              @click.stop="reserveMeeting(scope.row.company.cname, scope.row)"
              >{{
                "isReserve" in scope.row.company && scope.row.company.isReserve
                  ? "已预约"
                  : "视频预约"
              }}</el-button
            >
            <el-button
              type="primary"
              plain
              size="mini"
              @click.stop="viewFiles(scope.row.company.cname, scope.row)"
              >解除</el-button
            >
          </template>
          <template v-if="retreatFlag(scope.row)">
            <el-button
              type="warning"
              plain
              size="mini"
              @click.stop="retreat(scope.row)"
              >重要信息退回</el-button
            >
          </template>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        :width="isHz ? '140' : '120'"
        fixed="right"
        align="center"
      >
        <template slot-scope="scope">
          <!-- <el-tooltip content="查看事件经过" placement="top">
            <el-button size="mini" circle @click="expandChange(scope.row._id)"><i class="el-icon-folder-opened"></i></el-button>
          </el-tooltip> -->
          <!-- <el-button @click="gotoVideo(scope.row)">进入视频</el-button> -->
          <el-button
            type="success"
            plain
            size="mini"
            v-if="!isHz && subsidiary.length && showReserve"
            @click.stop="changeWarning(scope.row, 0)"
            >下放</el-button
          >
          <el-button
            type="success"
            plain
            v-if="!isHz && provincial.length && showReserve"
            size="mini"
            @click.stop="changeWarning(scope.row, 1)"
            >上报</el-button
          >
          <!-- 重要信息短信 -->
          <template v-if="isHz">
            <template
              v-if="
                scope.row.SMSrecord &&
                scope.row.SMSrecord.length &&
                scope.row.SMSrecord[0].sendResult.includes('成功')
              "
            >
              <el-button
                slot="reference"
                type="success"
                plain
                size="mini"
                @click.stop="
                  getSMSrecord(
                    scope.row.SMSrecord,
                    scope.row.company.cname || scope.row.companyName
                  )
                "
                >短信</el-button
              >
            </template>
            <template v-else>
              <el-popover
                placement="top"
                width="300"
                trigger="hover"
                open-delay="400"
              >
                <h4>
                  <span class="el-icon-warning" style="color: #e6a23c"> </span>
                  重要信息短信发送失败！请检查
                </h4>
                <p>1. 企业联系方式是否正确</p>
                <p>2. 企业所属区域是否填写正确</p>
                <p>3. 确认服务器连接是否畅通</p>
                <div style="text-align: right; margin: 0 0 10px">
                  <el-button
                    type="primary"
                    size="mini"
                    @click="sendSMSagain(scope.row._id)"
                    >重新发送</el-button
                  >
                </div>
                <el-button
                  slot="reference"
                  :type="
                    scope.row.SMSrecord &&
                    scope.row.SMSrecord.length >= 3 &&
                    !scope.row.SMSrecord.some((ele2) =>
                      ele2.sendResult.includes('成功')
                    )
                      ? 'danger'
                      : 'warning'
                  "
                  plain
                  size="mini"
                  @click.stop="
                    getSMSrecord(
                      scope.row.SMSrecord,
                      scope.row.company.cname || scope.row.companyName
                    )
                  "
                  style="margin-right: 10px"
                  >短信</el-button
                >
              </el-popover>
            </template>
          </template>
          <el-button
            type="warning"
            plain
            size="mini"
            @click.stop="retreatProject(scope.row)"
            >退回</el-button
          >
          <el-button
            type="danger"
            plain
            size="mini"
            @click.stop="deleteWarning(scope.row._id)"
            ><span class="el-icon-delete-solid"></span
          ></el-button>
        </template>
      </el-table-column>
      <!-- 设置列的显示 -->
      <el-table-column fixed="right" width="37" align="center">
        <template slot="header">
          <div class="autoLine">
            <i
              class="el-icon-setting"
              style="font-size: 17px; cursor: pointer; vertical-align: -6px"
              @click="isShowColumn = true"
            ></i>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="reportWarning ? '上报重要信息' : '下放重要信息'"
      :visible.sync="changeWarningFlag"
    >
      <el-form label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="风险等级 ">
              <el-input
                :value="leverName[+curItem.lever - 1]"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重要信息状态 ">
              <el-input :value="status[+curItem.status]" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="企业名称 ">
          <el-input :value="curItem.companyName" disabled></el-input>
        </el-form-item>
        <!-- <el-form-item label="注册地址 ">
        <el-input :value="districtRegAdd" disabled></el-input>
      </el-form-item> -->
        <el-form-item label="重要信息内容 ">
          <li v-for="(item, i) in curItem.content" :key="i">{{ item }}</li>
        </el-form-item>
        <el-form-item :label="reportWarning ? '上报单位' : '下放单位'">
          <el-select
            placeholder="请选择"
            style="width: 100%"
            v-model="checkedSubsidiary"
          >
            <el-option
              :label="sub.cname"
              :value="sub._id"
              v-for="sub in reportWarning ? provincial : subsidiary"
              :key="sub._id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit"
            >确认{{ reportWarning ? "上报" : "下放" }}</el-button
          >
          <el-button @click="changeWarningFlag = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 短信记录 -->
    <el-dialog :title="SMSrecordTitle" :visible.sync="showSMSrecord">
      <el-table
        :data="SMSrecord"
        max-height="500"
        :row-style="SMSrowStyle"
        class="SMSrecordTable"
      >
        <el-table-column prop="time" label="时间" width="130">
          <template slot-scope="scope">{{
            doMomentTime(scope.row.time)
          }}</template>
        </el-table-column>
        <el-table-column prop="sendToPhone" label="发送至" width="115">
        </el-table-column>
        <el-table-column prop="sendResult" label="短信结果" width="170">
        </el-table-column>
        <el-table-column prop="content" label="短信内容"> </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 项目退回 -->
    <el-dialog
      title="项目退回"
      :visible.sync="showRetreatProject"
      @close="closeRetreatProject"
      class="retreatProject"
    >
      <el-form label-width="80px" v-if="curItem.projectDetail">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用人单位: ">
              {{ curItem.company.cname || curItem.companyName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="['检测', '体检', '诊断'][curItem.type - 1] + '机构: '"
              >{{ curItem.projectDetail.orgName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人: ">
              {{
                curItem.projectDetail
                  ? curItem.projectDetail.companyContact
                  : curItem.adminuser.name || curItem.company.contract || ""
              }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话: "
              >{{
                curItem.projectDetail
                  ? curItem.projectDetail.contactPhoneNum
                  : curItem.adminuser.phoneNum || curItem.company.phoneNum || ""
              }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="curItem.type == 3">
          <el-col :span="12">
            <el-form-item label="姓名: "
              >{{ curItem.projectDetail.odisease.name }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职业病: "
              >{{
                curItem.projectDetail.odisease.diseaseName
                  ? curItem.projectDetail.odisease.diseaseName.join(" / ")
                  : ""
              }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-else>
          <el-col :span="12" v-if="curItem.type == 1">
            <el-form-item label="项目名称: "
              >{{ curItem.projectDetail.projectName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目编号: "
              >{{ curItem.projectDetail.projectNumber }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="退回原因: ">
          <div style="border: 1px solid #ccc">
            <Editor
              style="height: 250px; overflow-y: hidden"
              :defaultConfig="editorConfig"
              v-model="content"
              mode="default"
              @onCreated="onCreated"
            />
          </div>
        </el-form-item>
        <div style="text-align: right">退回内容将即时通知机构</div>
        <el-form-item>
          <el-button type="warning" @click="retreatProjectHandle"
            >退回</el-button
          >
          <el-button type="info" @click="showRetreatProject = false"
            >取消</el-button
          >
        </el-form-item>
      </el-form>
      <p v-else>项目已被删除</p>
    </el-dialog>
    <!-- 选择隐藏列弹框 -->
    <el-drawer
      title="选择隐藏列"
      :visible.sync="isShowColumn"
      direction="rtl"
      :before-close="handleClose"
      size="22%"
    >
      <div>
        <div style="padding: 0 30px 30px; width: 160px; line-height: 30px">
          <el-checkbox
            :indeterminate="isIndeterminate"
            v-model="checkAll"
            @change="handleCheckAllChange"
            ><strong>全选</strong></el-checkbox
          >
          <el-checkbox
            v-model="item.show"
            v-for="item in allColumnList"
            :key="item.name"
            >{{ item.name }}</el-checkbox
          >
        </div>
        <el-button type="success" @click="saveColumn" style="margin-left: 30px"
          >保存</el-button
        >
      </div>
    </el-drawer>

    <!-- 预约对话框 -->
    <el-dialog
      title="预约视频会议"
      :visible.sync="reserveVisible"
      width="20%"
      class="reserveDia"
    >
      <el-form :model="reserveForm" :rules="rules" ref="ruleForm">
        <el-form-item label="选择会议时间" prop="reserveDate">
          <el-date-picker
            style="width: 380px"
            value-format="yyyy-MM-dd HH:mm:ss"
            v-model="reserveForm.reserveDate"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="
            reserveVisible = false;
            reserveForm.reserveDate = null;
          "
          >取 消</el-button
        >
        <el-button type="primary" @click="confirmReserve('ruleForm')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </el-row>
</template>

<script>
import {
  liftWarning,
  sendWarningMsg,
  deleteWarning,
  updateWarning,
  retreatWarning,
  giveBack,
  giveBackDelImgs,
  checkPhoneLog,
  createReserve,
  sendMeetingMsg,
} from "@/api/manage";
import moment from "moment";
import { nanoid, customAlphabet } from "nanoid";
import { Editor } from "@wangeditor/editor-for-vue";
import "@wangeditor/editor/dist/css/style.css";
import { mapGetters } from "vuex";
export default {
  props: {
    isWarning: Boolean,
    list: Array,
    detail: Object,
    searchParams: Object,
    // count: Object,
    files: Object,
    subsidiary: Array,
    provincial: Array,
    regAdd: Array, // 当前单位的管辖区域
    warningConfig: Array,
  },
  components: { Editor },
  data() {
    const self = this;
    return {
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 8.64e7;
        },
        selectableRange:
          new Date().getHours() +
          ":" +
          (new Date().getMinutes() + 1) +
          ":00" +
          " - 23:59:59",
      },
      rules: {
        reserveDate: [
          { required: true, message: "请选择会议日期", trigger: "blur" },
        ],
      },
      reserveForm: {
        reserveDate: "",
      },
      currentMeetingId: "",
      params: {},
      reserveVisible: false,
      currentWarning: {},
      green: { color: "#13CE66" },
      red: { color: "#FF4949" },
      loading: false,
      expands: [], //table展开的行
      changeWarningFlag: false,
      curItem: {},
      leverName: ["一", "二", "三", "四"],
      status: ["待处理", "已处理", "待审核", "已解除", "已驳回", "已撤销"],
      checkedSubsidiary: "", // 选择的下放单位
      districtRegAdd: "", // curItem的注册地址
      reportWarning: false, // 上报重要信息
      showSMSrecord: false, // 展示重要信息短信提醒记录
      SMSrecord: [],
      SMSrecordTitle: "重要信息短信提醒记录",
      showRetreatProject: false, // 展示项目退回

      // 退回编辑的内容
      content: "",
      editor: null,
      editorConfig: {
        placeholder: "",
        MENU_CONF: {
          uploadImage: {
            server: "/manage/projectGiveBack/uploadImage",
            fieldName: "your-fileName",
          },
          insertImage: {
            onInsertedImage: self.onInsertedImage,
          },
        },
      },
      insertImageList: [],
      // 选择隐藏列
      isShowColumn: false,
      columnList: [], // 展示列
      allColumnList: [], // 所有列
      checkAll: false,
      isIndeterminate: true, // 全选和全不选之外的状态显示 -
    };
  },
  computed: {
    ...mapGetters(["isHz", "showReserve"]),// showReserve 是否显示预约按钮
    showCol(label) {
      return (label) => {
        const items = this.columnList.filter((ele) => ele.name === label);
        return items.length === 1 ? items[0].show : false;
      };
    },
    tableHeight() {
      const innerHeight =
        window.innerHeight ||
        document.documentElement.clientHeight ||
        document.body.clientHeight;
      return innerHeight - 355;
    },
    doMomentTime(nowTime) {
      return function (nowTime) {
        return moment(nowTime).format("YYYY-MM-DD HH:mm");
      };
    },
    retreatFlag(row) {
      // 是否可以退回重要信息
      return (row) => {
        if (row.status !== 3) return false;
        const curWarningConfig = this.warningConfig.find(
          (ele) => ele.level === row.lever
        );
        return curWarningConfig ? curWarningConfig.releaseWarningBySelf : false;
      };
    },
    address() {
      // 项目地址
      return (projectDetail, company) => {
        let res;
        if (projectDetail && projectDetail.workAddress) {
          res = projectDetail.workAddress.map(
            (ele) => ele.name || ele.address || ""
          );
        } else if (company && company.workAddress) {
          res = company.workAddress.map((ele) => ele.address || "");
        }
        return res && res.length ? res : [];
      };
    },
  },
  watch: {
    allColumnList: {
      handler(newVal) {
        if (newVal) {
          const checkAll = this.allColumnList.every((ele) => ele.show);
          if (checkAll) {
            this.checkAll = true;
            this.isIndeterminate = false; // 非全选和非不选择状态
          } else {
            const checkNone = this.allColumnList.every((ele) => !ele.show);
            if (checkNone) {
              this.checkAll = false;
              this.isIndeterminate = false;
            } else {
              this.isIndeterminate = true;
            }
          }
        }
      },
      deep: true, // 深度监听
    },
  },
  created() {
    console.log("ccccccccccccccccc", this.isWarning);
    this.getColumnList();
  },
  methods: {
    disabledDate(time) {
      return time.getTime() < Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天
    },
    confirmReserve(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.params.reserveDate = this.reserveForm.reserveDate;

          const res = await createReserve(this.params);
          if (res.status === 200) {
            this.$message({
              type: "success",
              message: "预约成功",
            });
            this.reserveVisible = false;
            // 发送短信
            await sendMeetingMsg({
              companyName: this.currentWarning.companyName, // companyId
              meetingId: this.currentMeetingId,
              time: this.reserveForm.reserveDate[0],
              contactPhoneNum: this.params.contactPhoneNum,
            });
            this.reserveForm.reserveDate = null;
          }
        } else {
          return false;
        }
      });
    },
    reserveMeeting(name, row) {
      const nanoid = customAlphabet(
        "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",
        8
      );
      this.currentMeetingId = nanoid();
      this.currentWarning = row;
      this.params.meetingId = this.currentMeetingId;
      this.params.cname = name;
      this.params.adminUserId = row.company.adminUserId;
      this.params.EnterpriseId = row.companyId;
      this.params.companyContact =
        "projectDetail" in row
          ? row.projectDetail.companyContact
          : row.adminuser.name;
      this.params.contactPhoneNum =
        "projectDetail" in row
          ? row.projectDetail.contactPhoneNum2
          : row.adminuser.phoneNum;
      this.params.channel = row.companyId;
      this.reserveVisible = true;
    },
    gotoVideo(row) {
      this.$router.push({ name: "videoReview", params: row });
    },
    // 查看手机号
    async showPhone(row) {
      console.log(row);
      let phoneNum = "";
      if (row.projectDetail) {
        row.projectDetail.contactPhoneNum = row.projectDetail.contactPhoneNum2;
        phoneNum = row.projectDetail.contactPhoneNum;
        await checkPhoneLog({ model: "Warning", phoneNum });
      } else if (row.adminuser && row.adminuser.phoneNum) {
        row.adminuser.phoneNum = row.adminuser.phoneNum2;
        phoneNum = row.adminuser.phoneNum;
        console.log(row.adminuser.phoneNum, "222222");
        await checkPhoneLog({ model: "AdminUser", phoneNum });
      } else if (row.company && row.company.phoneNum) {
        row.company.phoneNum = row.company.phoneNum2;
        phoneNum = row.company.phoneNum;
        console.log(row.company.phoneNum, "333333");
        await checkPhoneLog({ model: "AdminOrg", phoneNum });
      }
    },
    handleCheckAllChange(allChecked) {
      if (allChecked) {
        this.allColumnList.forEach((ele) => (ele.show = true));
      } else {
        this.allColumnList.forEach((ele) => (ele.show = false));
      }
      this.isIndeterminate = false;
    },
    // 获取展示的列
    getColumnList() {
      if (localStorage.getItem("columnSet")) {
        this.columnList = JSON.parse(localStorage.getItem("columnSet"));
      } else {
        this.columnList = [
          { name: "企业名称", show: true },
          { name: "类型", show: true },
          { name: "区域", show: true },
          { name: "地址", show: true },
          { name: "联系人", show: true },
          { name: "联系方式", show: true },
          { name: "重要信息内容", show: true },
          { name: "重要信息时间", show: true },
          { name: "解除/撤销时间", show: true },
          { name: "等级", show: true },
          { name: "状态", show: true },
          { name: "处理", show: true },
          { name: "操作", show: true },
        ];
      }
      this.allColumnList = JSON.parse(JSON.stringify(this.columnList));
      this.checkAll = this.allColumnList.every((ele) => ele.show);
      if (this.checkAll) this.isIndeterminate = false;
    },
    // 保存显示的列
    saveColumn() {
      localStorage.setItem("columnSet", JSON.stringify(this.allColumnList));
      this.columnList = JSON.parse(JSON.stringify(this.allColumnList));
      this.isShowColumn = false;
      this.$message({
        message: "保存成功",
        type: "success",
      });
    },
    // 点击退回 退回项目
    retreatProject(row) {
      this.showRetreatProject = true;
      this.curItem = row;
      this.content = "";
    },
    onInsertedImage(imageNode) {
      this.insertImageList.push(imageNode.src);
    },
    onCreated(editor) {
      this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
    },
    retreatProjectHandle() {
      this.$confirm("是否退回该项目?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const content = this.editor.getHtml();
          if (!content || content === "<p><br></p>") {
            this.$message("请输入退回原因");
            return;
          }

          const images = this.editor.getElemsByType("image").map((item) => {
            return item.src;
          });
          // 对比最后提交的图片和插入的图片
          const delImageList = this.insertImageList.filter(
            (item) => !images.includes(item)
          );
          const res = await giveBack({
            type: [
              "serviceProject",
              "physicalExaminationProjects",
              "diagnostics",
            ][this.curItem.type - 1],
            projectID: this.curItem.projectDetail._id,
            remark: content,
            delImageList,
            warningId: this.curItem._id,
          });

          if (res.status === 200) {
            this.$message({
              message: "操作成功",
              type: "success",
            });
            this.closeRetreatProject();
            this.$emit("refresh"); //刷新列表
          }
        })
        .catch(() => {});
    },
    // 关闭退回项目弹框
    async closeRetreatProject() {
      if (!this.editor) return;
      const images = this.editor.getElemsByType("image").map((item) => {
        return item.src;
      });
      const delImageList = this.insertImageList.filter(
        (item) => !images.includes(item)
      );
      const allImgList = Array.from(new Set([...images, ...delImageList]));
      if (allImgList && allImgList.length > 0) {
        const res = await giveBackDelImgs({
          delImageList: allImgList,
        });
        if (res.status == 200) this.insertImageList = [];
      }
      this.showRetreatProject = false;
    },

    // 点击退回重要信息 退回已解除的重要信息
    retreat(row) {
      const companyName = row.company ? row.company.cname : row.companyName;
      this.$prompt("请输入退回的原因", companyName || "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(({ value }) => {
          retreatWarning({
            _id: row._id,
            remark: value || "",
            companyName,
            phoneNum: row.projectDetail
              ? row.projectDetail.contactPhoneNum
              : row.adminuser.phoneNum || row.company.phoneNum || "",
          }).then((res) => {
            console.log(res);
            if (res.status == 200) {
              this.$message({
                type: "success",
                message: res.message,
              });
              this.$emit("refresh"); //刷新列表
            } else {
              this.$message({
                type: "warning",
                message: res.message || "操作失败",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },
    SMSrowStyle({ row, rowIndex }) {
      if (row.sendResult.includes("成功")) {
        return {};
      }
      return { background: "#fdf5e6" };
    },
    // 查看当前重要信息短信提醒记录
    getSMSrecord(SMSrecord = [], companyName = "") {
      this.showSMSrecord = true;
      this.SMSrecordTitle = "重要信息短信提醒记录 - " + companyName || "";
      this.SMSrecord = SMSrecord;
    },
    // 多选
    handleSelectionChange(val) {
      this.$emit("handleSelectionChange", val);
    },
    // 点击重新发送重要信息短信
    sendSMSagain(warningId) {
      warningId && this.$emit("SMSnotificationHandle", [warningId]);
    },
    rowClickHandle(row) {
      this.viewFiles(row.company.cname, row);
    },
    toAdminorg(companyId) {
      sessionStorage.setItem(
        "warningParams",
        JSON.stringify(this.searchParams)
      );
      location.href = "/admin/adminorgGov/editAdminorg/" + companyId;
    },
    // 确认下放、上报重要信息
    onSubmit() {
      console.log(this.checkedSubsidiary);
      if (!this.checkedSubsidiary) {
        this.$message({
          showClose: true,
          message: `请选择${this.reportWarning ? "上报" : "下放"}单位`,
          type: "warning",
        });
        return;
      }
      updateWarning({
        _id: this.curItem._id,
        supervisionId: this.checkedSubsidiary, // supervisionId其实是一个数组
        reportWarning: Boolean(this.reportWarning),
      }).then((res) => {
        if (res.status == 200) {
          this.checkedSubsidiary = "";
          this.districtRegAdd = "";
          this.changeWarningFlag = this.reportWarning = false;
          this.$message({
            message: res.msg,
            type: "success",
          });
          // this.searchParams.curPage = 1;
          this.$emit("refresh");
        } else {
          this.$message({
            showClose: true,
            message: res.msg || "操作失败",
            type: "warning",
          });
        }
      });
    },
    // 点击下放/上报重要信息
    changeWarning(item, n = 0) {
      this.changeWarningFlag = true;
      this.curItem = item;
      this.districtRegAdd = item.company.districtRegAdd.join(" ");
      this.reportWarning = n;
      if (n && this.provincial.length === 1) {
        this.checkedSubsidiary = this.provincial[0]._id;
      }
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.status == 3) {
        return "grey-row";
      }
      return "";
    },
    // 删除重要信息
    deleteWarning(_id) {
      this.$confirm("是否将此重要信息移入回收站?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteWarning({ _id }).then((res) => {
            if (res.status == 200) {
              this.$message({
                type: "success",
                message: res.msg,
              });
              this.$emit("refresh"); //刷新列表
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 点击查看整改报告
    viewFiles(companyName, detail) {
      this.files.show = true;
      this.files.companyName = companyName;
      this.files.status = detail.status || 0;
      this.files.id = detail._id;
      this.files.content = detail.content;
      this.files.process = detail.process || [];
      this.files.lever = detail.lever;
      this.files.modifiedStatus = detail.modifiedStatus;
      this.files.companyId = detail.companyId;
      this.files.type = detail.type;
    },
    // 打开/关闭事件经过
    expandChange(id) {
      if (this.expands.includes(id)) {
        this.expands.splice(this.expands.indexOf(id), 1);
      } else {
        this.expands.push(id);
      }
    },
    // 解除重要信息
    delWarning(_id) {
      this.$prompt("请输入解除预定的原因", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(({ value }) => {
          liftWarning({
            _id,
            remark: value,
          }).then((res) => {
            console.log(res);
            if (res.status == 200) {
              this.$message({
                type: "success",
                message: res.msg,
              });
              this.$emit("refresh"); //刷新列表
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消操作",
          });
        });
    },
    //处理重要信息
    changeStatus(id, num, companyName, content, adminUserId) {
      switch (+num) {
        case 1: // 短信督促提醒
          this.$prompt("请输入短信提醒内容", "短信提醒", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            inputValue: content,
            inputType: "textarea",
            customClass: "warningPrompt",
          })
            .then(async ({ value }) => {
              try {
                await sendWarningMsg({
                  // 发送短信息
                  id,
                  companyName,
                  content: value,
                  adminUserId,
                }).then((res) => {
                  this.$message({
                    type: "success",
                    message: "督促短信已发送成功",
                  });
                  this.$emit("refresh"); //刷新列表
                });
              } catch (error) {
                console.error(error);
                this.$message({
                  type: "warning",
                  message: "督促短信发送失败",
                });
              }
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消提醒操作",
              });
            });
          break;
        case 2: // 生成重要信息解决方案, 打开添加重要信息弹框
          this.detail.show = true;
          this.detail.data = {
            companyName,
            id,
          };
      }
    },
  },
  mounted() {
    if (this.isHz) this.status[1] = "已退回";
  },
};
</script>

<style>
.warning-table .grey-row {
  color: #909399;
}
.warning-table th {
  background: #f5f7fa !important;
  color: #424242;
}
.warningPrompt {
  width: 70vw !important;
  padding: 10px;
  max-width: 1100px;
  min-width: 400px;
}
.warningPrompt .el-message-box__title {
  text-align: center;
}
.retreatProject .el-form-item {
  margin-bottom: 10px;
}
</style>
<style scoped lang="scss">
.alink {
  cursor: pointer;
}
.el-icon-question {
  color: #0063e0;
}
.warning-table {
  .warning2 {
    background-color: rgba(251, 237, 217, 1);
    padding: 5px 7px;
    text-align: center;
    color: rgba(231, 162, 59, 1);
    font-size: 13px;
  }
  .danger {
    background-color: rgba(253, 226, 226, 1);
    padding: 5px 7px;
    text-align: center;
    color: rgba(244, 108, 108, 1);
    font-size: 13px;
  }
  .process {
    display: flex;
    width: 100%;
    .left {
      flex: 0 0 80px;
      color: #606266;
    }
    .right {
      flex: 1 1;
    }
  }
}
// ::v-deep .reserveDia.el-dialog__body {
//   width: 200px !important;
// }
</style>
