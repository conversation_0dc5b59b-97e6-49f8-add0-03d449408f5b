<template>
  <div class="wrap">
    <el-card class="box-card">
      <div class="header">
        <span
          style="font-size: 1.2em; font-weight: bold; color: #409eff"
          class="el-icon-paperclip"
        >
          重要信息详情</span
        >
        <span class="el-icon-error close" @click="closeDetail"></span>
      </div>
      <div class="text item">
        <div class="box-card jump">
          <div slot="header" class="clearfix">
            <h3 class="title">
              {{ companyName }} &nbsp;
              <el-button
                :type="
                  warning_lever == '一' || warning_lever == '二'
                    ? 'danger'
                    : 'warning'
                "
                plain
                size="mini"
                >{{ warning_lever }}级</el-button
              >
              <el-button
                type="warning"
                plain
                size="mini"
                v-if="!isHz && warning_status < 2"
                >未整改</el-button
              >
            </h3>
            <div class="originalReportFiles">
              <strong
                >{{ ["检测", "体检", "诊断"][files.type - 1] }}报告：</strong
              >
              <!-- <el-upload style="display: inline-block;width:calc(100% - 7em);"
                :on-preview="handlePreview"
                :file-list="originalReportFiles"
                ref="fileid" :disabled="true" v-if="originalReportFiles.length">
              </el-upload> -->
              <div v-if="originalReportFiles.length" class="list">
                <div v-for="file in originalReportFiles" :key="file.name">
                  <span class="fileName" @click="handlePreview(file)">{{
                    file.name
                  }}</span>
                  <span @click="handlePreview(file)" class="preview">
                    <i class="el-icon-zoom-in"></i> 预览
                  </span>
                  <span @click="handleDownload(file)" class="downLoad">
                    <i class="el-icon-download"></i> 下载
                  </span>
                </div>
              </div>
              <span v-else class="noFile">未上传</span>
            </div>
            <ul class="warningContent">
              <li v-for="(item, i) in warningContent" :key="i">{{ item }}</li>
            </ul>
          </div>
          <div class="enterpriseFormBox">
            <el-tabs type="border-card" v-model="activeTab">
              <el-tab-pane
                v-for="(proce, n) in process"
                :key="proce._id"
                :name="n + 1"
              >
                <span slot="label"
                  ><i class="el-icon-date"></i> {{ proce.time }}
                </span>
                <h3>{{ proce.thing.replace(/预警/g, "重要信息") }}</h3>
                <!-- 已上传文件 -->
                <el-row :gutter="20">
                  <el-form label-width="80px">
                    <el-form-item
                      prop="approval"
                      label="整改文件:"
                      class="hasUploadFile"
                      v-if="proce.files.length > 0"
                    >
                      <el-upload
                        :on-preview="handlePreview"
                        :file-list="proce.files"
                        ref="fileid"
                        :disabled="true"
                      >
                      </el-upload>
                    </el-form-item>
                    <el-form-item
                      prop="documents"
                      label="执法文书:"
                      class="hasUploadFile"
                      v-if="proce.documents && proce.documents.length"
                    >
                      <el-upload
                        :on-preview="handlePreview"
                        :file-list="proce.documents"
                        ref="fileid"
                        :disabled="true"
                      >
                      </el-upload>
                    </el-form-item>
                    <el-form-item
                      prop="remark"
                      label=""
                      v-if="proce.remark"
                      label-width="20px"
                    >
                      <!-- 备注内容 -->
                      {{ proce.remark }}
                    </el-form-item>
                  </el-form>
                </el-row>
                <!-- 处置方案 -->
                <el-row v-if="proce.disposalPlan && proce.disposalPlan.length">
                  <li v-for="item in proce.disposalPlan" :key="item.key">
                    {{ item.value }}
                  </li>
                </el-row>
              </el-tab-pane>
            </el-tabs>
            <!-- 处理修改申请 -->
            <el-form v-if="files.modifiedStatus == 2">
              <el-form-item label="理由 / 备注：">
                <el-input type="textarea" v-model="remark"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" size="small" @click="applyModify(3)"
                  >同意修改</el-button
                >
                <el-button type="warning" size="small" @click="applyModify(4)"
                  >驳回申请</el-button
                >
              </el-form-item>
            </el-form>
            <!-- 解除重要信息 -->
            <el-form
              v-else-if="warning_status <= 2"
              :rules="rules"
              ref="baseInfoForm"
              :model="baseInfoForm"
            >
              <el-form-item
                prop="approval"
                label="企业整改文件或省平台已解除截屏:"
                v-if="isHz"
              >
                <el-upload
                  ref="fileid"
                  multiple
                  action
                  :auto-upload="false"
                  :on-preview="handlePreview"
                  :file-list="baseInfoForm.approval"
                  :on-change="onChangeApproval"
                  :on-remove="onRemoveApproval"
                >
                  <el-button size="small" type="primary" plain
                    >点击上传</el-button
                  >
                  <div slot="tip" class="el-upload__tip">
                    只能上传图片、pdf或word类型文件
                  </div>
                </el-upload>
                <div class="el-form-item__error">{{ approvalFileErr }}</div>
              </el-form-item>

              <el-form-item prop="documents" label="执法文书:">
                <el-upload
                  ref="fileid"
                  multiple
                  action
                  :auto-upload="false"
                  :on-preview="handlePreview"
                  :file-list="baseInfoForm.documents"
                  :on-change="onChangeDocument"
                  :on-remove="onRemoveDocument"
                >
                  <el-button size="small" type="primary" plain
                    >点击上传</el-button
                  >
                  <div slot="tip" class="el-upload__tip">
                    只能上传图片、pdf或word类型文件
                  </div>
                </el-upload>
                <div class="el-form-item__error">{{ documentFileErr }}</div>
              </el-form-item>

              <el-form-item label="理由 / 备注：">
                <el-input type="textarea" v-model="remark"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  size="small"
                  @click="submitForm('baseInfoForm')"
                  >解除风险</el-button
                >
                <el-button
                  type="warning"
                  size="small"
                  @click="rejectFiles"
                  v-if="warning_status === 2"
                  >驳回整改</el-button
                >
              </el-form-item>
            </el-form>
          </div>
        </div>

        <el-dialog :visible.sync="dialogVisible">
          <img width="100%" :src="dialogImageUrl" alt />
        </el-dialog>
      </div>
    </el-card>
    <!-- 预览word文件的组件（用于渲染） -->
    <preview
      :filePath="dialogPreviewUrl"
      :preview="showPreview"
      @fatherMethod="showPreview = false"
    ></preview>
  </div>
</template>
<script>
import {
  liftWarning,
  reject,
  applyModifyWarning,
  uploadFiles,
  getOriginalReport,
} from "@/api/manage";
import preview from "../../../../publicMethods/components/preview";
export default {
  props: {
    files: {
      type: Object,
      default: {},
    },
    isHz: Boolean,
  },
  components: {
    preview,
  },
  data() {
    return {
      dialogImageUrl: "",
      dialogVisible: false,
      remark: "", // 理由/备注
      activeTab: "",
      fileType: [
        "png",
        "jpeg",
        "docx",
        "doc",
        "pdf",
        "jpg",
        "bmp",
        "jfif",
        "PCX",
        "gif",
      ],
      approvalFileErr: "",
      documentFileErr: "",
      baseInfoForm: {
        approval: [], // // 上传的整改文件
        documents: [], // 上传的执法文书
      },
      rules: {
        approval: [
          {
            required: false,
            validator: (rule, value, callback) => {
              let err = "";
              if (value.length) {
                for (let i = 0; i < value.length; i++) {
                  if (value[i].size === 0) err = "请勿上传空文件！";
                }
              }
              callback(err);
            },
          },
        ],
        documents: [
          {
            required: false,
            validator: (rule, value, callback) => {
              let err = "";
              if (value.length) {
                for (let i = 0; i < value.length; i++) {
                  if (value[i].size === 0) err = "请勿上传空文件！";
                }
              }
              callback(err);
            },
          },
        ],
      },
      originalReportFiles: [], // 生成重要信息的原报告文件
      showPreview: false,
      dialogPreviewUrl: "",
    };
  },
  computed: {
    warning_id() {
      return this.files.id || "";
    },
    warningContent() {
      return this.files.content || "";
    },
    companyName() {
      return this.files.companyName || "";
    },
    warning_status() {
      return this.files.status;
    },
    warning_lever() {
      switch (this.files.lever) {
        case 1:
          return "一";
        case 2:
          return "二";
        case 3:
          return "三";
        case 4:
          return "四";
      }
    },
    process() {
      // 事件经过
      this.activeTab = this.files.process ? this.files.process.length : 1;
      return this.files.process;
      // return this.detail.process.filter(ele => ele.thing == '完成整改');
    },
  },
  created() {
    this.changeRules();
  },
  watch: {
    warning_status(status) {
      this.changeRules();
    },
    warning_lever() {
      this.changeRules();
    },
    warning_id(id) {
      id &&
        getOriginalReport({ warning_id: id }).then((res) => {
          this.originalReportFiles = res.data;
        });
    },
  },
  methods: {
    changeRules() {
      if (this.isHz && this.warning_status === 0) {
        // 因为杭州监管没有企业端  所以解除重要信息时 整改文件为必传
        const requiredFlag = ['一', '二'].includes(this.warning_lever) ? true : false;
        this.rules.approval = [
          {
            required: requiredFlag,
            validator: (rule, value, callback) => {
              if (requiredFlag && value.length === 0) {
                callback("请上传企业整改文件");
              } else {
                let err = "";
                for (let i = 0; i < value.length; i++) {
                  if (value[i].size === 0) err = "请勿上传空文件！";
                }
                callback(err);
              }
            },
          },
        ];
      }
    },
    // 处理修改数据申请
    applyModify(modifiedStatus) {
      applyModifyWarning({
        jobHealthId: this.files.id, // jobHealthId或者warningId都行
        reason: this.remark,
        modifiedStatus,
      }).then((res) => {
        if (res.status == 200) {
          this.$message({
            type: "success",
            message: res.message,
          });
          this.$emit("refresh"); //刷新列表
          this.closeDetail();
        }
      });
    },
    // 驳回整改
    rejectFiles() {
      reject({
        _id: this.files.id,
        remark: this.remark,
      }).then((res) => {
        if (res.status == 200) {
          this.$message({
            type: "success",
            message: res.msg,
          });
          this.$emit("refresh"); //刷新列表
          this.closeDetail();
        }
      });
    },
    // 提交表单 - 整改文件
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let formDate = new FormData();
          formDate.set("warning_id", this.files.id);
          formDate.set("companyId", this.files.companyId);
          this.baseInfoForm.approval.forEach((ele, i) => {
            if (ele.raw && ele.size) {
              formDate.append("file" + i, ele.raw);
            }
          });
          this.baseInfoForm.documents.forEach((ele, i) => {
            if (ele.raw && ele.size) {
              formDate.append("file" + i, ele.raw);
            }
          });
          uploadFiles(formDate).then((res) => {
            if (res.status == 200) {
              this.delWarning();
            } else {
              this.$message.error(res.msg || "文件上传失败");
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },

    // 解除重要信息
    delWarning() {
      const _id = this.files.id;
      liftWarning({
        _id,
        remark: this.remark,
        files: this.baseInfoForm.approval.map((ele) => ele.name),
        documents: this.baseInfoForm.documents.map((ele) => ele.name),
      }).then((res) => {
        console.log(res);
        if (res.status == 200) {
          this.$message({
            type: "success",
            message: res.msg,
          });
          this.$emit("refresh"); //刷新列表
          this.closeDetail();
        }
      });
    },
    closeDetail() {
      this.files.show = false;
      this.files.companyName = "";
      if (this.files.data) this.files.data = []; // 清空文件
      if (this.files.process) this.files.process = [];
      this.files.id =
        this.files.content =
        this.remark =
        this.approvalFileErr =
        this.documentFileErr =
          "";
      this.baseInfoForm.approval = [];
      this.baseInfoForm.documents = [];
    },
    // 预览文件
    handlePreview(file) {
      console.log(file);
      let fileSplite = file.name.split(".");
      if (fileSplite[fileSplite.length - 1] === "pdf") {
        let a = document.createElement("a");
        a.href = file.raw ? URL.createObjectURL(file.raw) : file.url;
        a.target = "_blank";
        a.click();
      } else if (
        fileSplite[fileSplite.length - 1] === "doc" ||
        fileSplite[fileSplite.length - 1] === "docx"
      ) {
        this.dialogVisible = false;
        // const url = file.raw ? URL.createObjectURL(file.raw) : file.url;
        // window.open('https://view.officeapps.live.com/op/view.aspx?src='+ window.location.origin + url);
        if (file.url) {
          this.dialogPreviewUrl = file.url;
          this.showPreview = true;
        } else {
          let a = document.createElement("a");
          a.href = file.raw ? URL.createObjectURL(file.raw) : file.url;
          a.target = "_blank";
          a.click();
        }
      } else {
        this.dialogImageUrl = file.raw
          ? URL.createObjectURL(file.raw)
          : file.url;
        this.dialogVisible = true;
      }
    },
    // 下载文件
    handleDownload(file) {
      console.log("下载", file);
      const a = document.createElement("a");
      let event = new MouseEvent("click");
      a.href = file.url;
      a.download = file.name || "";
      a.dispatchEvent(event); // a.click()火狐浏览器不触发
    },
    // 选择文件
    onChangeApproval(file, filelist) {
      this.baseInfoForm.approval = filelist;
      this.approvalFileErr = this.matchFileType(filelist, this.fileType, false);
    },
    onChangeDocument(file, filelist) {
      this.baseInfoForm.documents = filelist;
      this.documentFileErr = this.matchFileType(filelist, this.fileType, false);
    },
    // 删除文件
    onRemoveApproval(file, filelist) {
      this.baseInfoForm.approval = filelist;
      this.approvalFileErr = this.matchFileType(filelist, this.fileType, false);
      this.oldApproval &&
        this.oldApproval.forEach((item, i) => {
          if (file.url.indexOf(item.staticName) !== -1) {
            this.oldApproval.splice(i, 1);
            this.editAndDeleteFiles.push(item);
          }
        });
    },
    onRemoveDocument(file, filelist) {
      this.baseInfoForm.documents = filelist;
      this.documentFileErr = this.matchFileType(filelist, this.fileType, false);
      this.oldApproval &&
        this.oldApproval.forEach((item, i) => {
          if (file.url.indexOf(item.staticName) !== -1) {
            this.oldApproval.splice(i, 1);
            this.editAndDeleteFiles.push(item);
          }
        });
    },
    // 匹配文件
    matchFile(formName, data) {
      if (data.presentation.length > 0) {
        let presentation = data.presentation;
        formName.presentation = [];
        presentation.forEach((item) => {
          formName.presentation.push({
            name: item.originName,
            url: `${this.filePath}/${item.staticName}`,
          });
        });
      }
      if (data.duraData.length > 0) {
        let duraData = data.duraData;
        formName.duraData = [];
        duraData.forEach((item) => {
          formName.duraData.push({
            name: item.originName,
            url: `${this.filePath}/${item.staticName}`,
          });
        });
      }
    },
    //判断文件类型
    matchFileType(filelist, typeArr, isOtherFile) {
      let fileErr = "";
      let hasFileErrCount = 0;
      filelist.forEach((item) => {
        let nameArr = item.name.split(".");
        let type = nameArr[nameArr.length - 1];
        if (!typeArr.includes(type)) {
          hasFileErrCount++;
        }
      });
      if (hasFileErrCount > 0 && !isOtherFile) {
        fileErr = "请上传图片、pdf或word文件!";
      } else if (hasFileErrCount > 0 && isOtherFile) {
        fileErr = "请上传pdf、word文件";
      } else {
        fileErr = "";
      }
      return fileErr;
    },
  },
};
</script>

<style>
.hasUploadFile .el-form-item__content,
.hasUploadFile .el-form-item__label {
  line-height: 0px !important;
}
.originalReportFiles .el-upload-list .el-upload-list__item-name,
.originalReportFiles
  .el-upload-list
  .el-upload-list__item-name
  .el-icon-document {
  color: rgb(64, 158, 255);
}
.originalReportFiles .el-upload-list__item .el-icon-upload-success {
  display: none;
}
</style>
<style scoped lang="scss">
::v-deep .el-upload-list__item {
  // 去掉动画
  transition: none !important;
}
.originalReportFiles {
  strong {
    padding-left: 1em;
    vertical-align: top;
    display: inline-block;
    padding-top: 29px;
  }
  .noFile {
    vertical-align: 8px;
    color: #e6a23c;
    display: inline-block;
    padding-top: 29px;
  }
  .list {
    // 检测报告列表
    margin-top: 25px;
    line-height: 25px;
    display: inline-block;
    .fileName {
      margin-right: 17px;
      // color: rgb(64, 158, 255);
    }
    .preview,
    .downLoad {
      color: rgb(64, 158, 255);
    }
    .preview {
      margin-right: 10px;
    }
    .fileName:hover,
    .preview:hover,
    .downLoad:hover {
      color: rgb(64, 158, 255);
      text-decoration: underline;
      cursor: pointer;
    }
  }
}
.title .el-button:hover {
  cursor: auto;
}
.el-tabs {
  margin-bottom: 15px;
}
.text.item {
  height: calc(100vh - 200px);
  overflow-y: scroll;
  overflow-x: hidden;
  box-sizing: border-box;
  padding-left: 10px;
}
.warningContent {
  box-sizing: border-box;
  padding-right: 20px;
  margin-bottom: 20px;
  margin-top: 10px;
  // max-height: 300px;
  // overflow-y: scroll;
  li {
    list-style-type: decimal;
  }
}
.header {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  .close {
    float: right !important;
    // padding: 3px 0;
    font-size: 1.3em;
    cursor: pointer;
    opacity: 0.7;
    :hover {
      opacity: 0.9;
    }
  }
}
.demo-form-inline .el-form-item {
  width: 50%;
  display: inline-block;
}
.wrap {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(#000000, 0.6);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  .box-card {
    // padding: 10px 3% 30px;
    box-sizing: border-box;
    // width: 70%;
    min-width: 800px;
    // max-height: 88vh;
    // overflow-y: hidden;
    position: relative;
    .el-row:hover {
      background-color: #f5f5f5;
    }
    .el-input {
      width: 90%;
    }
    .close {
      float: right !important;
      padding: 3px 0;
      font-size: 1.3em;
      cursor: pointer;
      opacity: 0.7;
      :hover {
        opacity: 0.9;
      }
    }
    .el-row {
      padding: 10px 0;
    }
    .remark {
      line-height: 26px;
    }
    li {
      padding-top: 10px;
      line-height: 26px;
    }
    h3 {
      color: #606266;
    }
    h3.title {
      color: #303133;
      margin-bottom: 0;
    }
  }
}
.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
  border-radius: 10px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  /* background:black; */
}
</style>
