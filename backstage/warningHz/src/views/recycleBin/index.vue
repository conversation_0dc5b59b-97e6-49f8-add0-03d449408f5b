<!-- 重要报送信息回收站 -->
<template>
  <div :class="classObj" class="adminUser">
    <div class="main-container">  
      <!-- <transition name="el-zoom-in-center">
        <FileList v-show="showFiles" :files="files" @refresh="getLits" /> 
      </transition> -->
      <el-row class="dr-datatable">
        <el-col :span="24">
          <el-page-header @back="$router.push({name:'warning'})" content="回收站"></el-page-header><br/>
          <el-row class="search">
            <el-input style="width: 200px" size="small" placeholder="关键字搜索" v-model="searchParams.keyWords" suffix-icon="el-icon-search" @keyup.enter.native="$emit('search')"></el-input>
            <el-button type="danger" size="mini" style="float:right" icon="el-icon-delete" @click="clearRecycleBinHandle">清空回收站</el-button>
          </el-row><br/>
          <DataTable @refresh="getLits" :searchParams="searchParams" :list="list" :files="files"></DataTable>
          <Pagination :searchParams="searchParams" v-show="list.length > 0" :totleCount="count.actualTotle" ></Pagination>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
// import FileList from "./files";
import DataTable from "./dataTable.vue";
import Pagination from "../common/Pagination.vue";
import { initEvent } from "@root/publicMethods/events";
import { list, clearRecycleBin } from "@/api/manage";

export default {
  data() {
    return {
      list: [], 
      searchParams: {
        curPage: 1,
        limit: 10,
        keyWords: '',
        delete: true,
      },
      count: { // 总数统计
        actualTotle: 0
      },
      files: { // 查看整改报告
        show: false,
        status: 0,
        companyName: '',
        id: '',
        content: '',
        process: [],
        lever: '',
      },
      sidebarOpened: false,
      device: "desktop",
    };
  },
  components: {
    DataTable,
    // FileList,
    Pagination,
  },
  watch: {
    searchParams: {
      deep:true,
      handler:function(newV,oldV){
        console.log('触发了watch - searchParams');
        this.getLits();
      }
    },
    files: {
      deep:true,
      handler:function(newV,oldV){
        console.log(newV);
        // this.showFiles = newV.show;
      }
    },
  },
  created() {
    initEvent(this); // 初始化 hideSidebar & openSidebar
    this.getLits(); // 获取数据列表
  },
  computed: {
    showFiles(){
      return this.files.show;
    },
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    },
  },
  methods: {
    clearRecycleBinHandle(){
      const self = this;
      this.$confirm('确认清空回收站中的所有信息吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          clearRecycleBin().then(res => {
            if(res.status == 200){
              this.$message({
                type: 'success',
                message: res.data.deletedCount?`操作成功，已清除${res.data.deletedCount}条重要信息报送`:'回收站已清空'
              });
              self.getLits(); // 获取数据列表
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消还原操作'
          });          
        });
    },
    // 获取列表
    getLits(){
       console.log(1111111111)
      list(this.searchParams).then(res => {
        console.log('更新啦~~~~~',res)
        this.list = res.data || [];
        this.count = res.count;
      })
    }
  },
};
</script>

<style lang="">
</style>