<template>
  <div class="wrap">
    <el-card class="box-card">
      <div class="header">
        <span style="font-size:1.2em;font-weight: bold;color:#409EFF" class="el-icon-paperclip"> 信息详情</span>
        <span class="el-icon-error close" @click="closeDetail"></span>
      </div>
      <div class="text item">
        <div class="box-card jump" >
          <div slot="header" class="clearfix">
            <h3 class="title">{{companyName}}  &nbsp;
              <el-button :type="(warning_lever=='一' || warning_lever=='二') ? 'danger':'warning'" plain size="mini">{{warning_lever}}级</el-button>
            </h3>
            <ul class="warningContent">
              <li v-for="(item, i) in warningContent" :key="i">{{item}}</li>
            </ul>
          </div>
          <div class="enterpriseFormBox">
            <el-tabs type="border-card" v-model="activeTab">
              <el-tab-pane v-for="(proce,n) in process" :key="proce._id" :name="n+1">
                <span slot="label"><i class="el-icon-date"></i> {{ proce.time }} </span>
                <h3>{{proce.thing.replace(/预警/g, "重要信息")}}</h3>
                <!-- 已上传文件 -->
                <el-row :gutter="20" v-if="proce.files.length > 0">
                  <el-form label-width="80px">
                    <el-form-item prop="approval" label="整改文件:" class="hasUploadFile">
                      <el-upload
                        :on-preview="handlePictureCardPreview"
                        :file-list="proce.files"
                        ref="fileid">
                      </el-upload>
                    </el-form-item>
                  </el-form>
                </el-row>
                <!-- 备注内容 -->
                <el-row v-if="proce.remark" class="remark"> {{ proce.remark }} </el-row>
                <!-- 处置方案 -->
                <el-row v-if="proce.disposalPlan && proce.disposalPlan.length"> 
                  <li v-for="item in proce.disposalPlan" :key="item.key">{{ item.value }}</li>
                </el-row>
              </el-tab-pane>
            </el-tabs>
            <!-- 处理操作 -->
            <el-form v-if="warning_status==2">
              <el-form-item label="理由 / 备注：">
                <el-input type="textarea" v-model="remark"></el-input>
              </el-form-item>
              <el-form-item >
                <el-button type="primary" size="small" @click="delWarning">解除风险</el-button>
                <el-button type="warning" size="small" @click="rejectFiles">驳回整改</el-button>
              </el-form-item>
            </el-form>
            <el-form v-else-if="files.modifiedStatus==2">
              <el-form-item label="理由 / 备注：">
                <el-input type="textarea" v-model="remark"></el-input>
              </el-form-item>
              <el-form-item >
                <el-button type="primary" size="small" @click="applyModify(3)">同意修改</el-button>
                <el-button type="warning" size="small" @click="applyModify(4)">驳回申请</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
        
        <el-dialog :visible.sync="dialogVisible">
          <img width="100%" :src="dialogImageUrl" alt />
        </el-dialog>
      </div>
  </el-card>
</div>
  
</template>
<script>
import { liftWarning, reject, applyModifyWarning } from "@/api/manage";
export default {
  props: {
    files: {
      type: Object,
      default: {}
    }
  },
  data(){
    return {
      dialogImageUrl: "",
      dialogVisible: false,
      remark: '', // 解除重要报送信息的理由
      activeTab: '',
    }
  },
  computed: {
    warningContent(){
      return this.files.content || '';
    },
    companyName(){
      return this.files.companyName || '';
    },
    warning_status(){ 
      return this.files.status;
    },
    warning_lever(){
      switch(this.files.lever){
        case 1: return '一';
        case 2: return '二';
        case 3: return '三';
        case 4: return '四';
      }
    },
    process(){ // 事件经过
      console.log(222, this.files.process);
      this.activeTab = this.files.process ? this.files.process.length : 1;
      return this.files.process;
      // return this.detail.process.filter(ele => ele.thing == '完成整改');
    },
  },
  methods: {
    // 处理修改数据申请
    applyModify(modifiedStatus){
      applyModifyWarning({
        jobHealthId: this.files.id, // jobHealthId或者warningId都行
        reason: this.remark,
        modifiedStatus
      }).then(res => {
        if(res.status == 200){
          this.$message({
            type: 'success',
            message: res.message
          });
        }
      })
    },
    // 驳回整改
    rejectFiles(){
      reject({
        _id: this.files.id,
        remark: this.remark
      }).then(res => {
        if(res.status == 200){
          this.$message({
            type: 'success',
            message: res.msg
          });
          this.$emit('refresh'); //刷新列表
          this.closeDetail();
        }
      })
    },
    // 解除重要报送信息
    delWarning(){
      const _id = this.files.id;
      liftWarning({
        _id,
        remark: this.remark
      }).then(res => {
        console.log(res);
        if(res.status == 200){
          this.$message({
            type: 'success',
            message: res.msg
          });
          this.$emit('refresh'); //刷新列表
          this.closeDetail();
        }
      })
    },
    closeDetail(){
      this.files.show = false;
      this.files.companyName = '';
      if(this.files.data) this.files.data = []; // 清空文件
      this.files.id = '';
      this.files.content = '';
    },
    handlePictureCardPreview(file) {
      console.log(file);
      let fileSplite = file.name.split(".");
      if (fileSplite[fileSplite.length - 1] === "pdf") {
        let a = document.createElement("a");
        a.href = file.raw ? URL.createObjectURL(file.raw) : file.url;
        a.target = "_blank";
        a.click();
      } else if (
        fileSplite[fileSplite.length - 1] === "doc" ||
        fileSplite[fileSplite.length - 1] === "docx"
      ) {
        this.dialogVisible = false;
        window.open('https://view.officeapps.live.com/op/view.aspx?src='+ window.location.origin + file.url);
      } else {
        this.dialogImageUrl = file.raw
          ? URL.createObjectURL(file.raw)
          : file.url;
        this.dialogVisible = true;
      }
    },
    
  },
};
</script>

<style>
  .hasUploadFile .el-form-item__content, 
  .hasUploadFile .el-form-item__label {
      line-height: 0px!important;
  }
</style>
<style scoped lang="scss">
  .text.item{
    height: calc(100vh - 200px);
    overflow-y: scroll;
    overflow-x: hidden;
    box-sizing: border-box;
    padding-left: 10px;
  }
  .warningContent{
    box-sizing: border-box;
    padding-right: 20px;
    margin-bottom: 20px;
    li{
      list-style-type: decimal;
    }
  }
  .header{
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .close{
      float: right!important; 
      // padding: 3px 0;
      font-size: 1.3em;
      cursor: pointer;
      opacity: .7;
      :hover{
        opacity: .9;
      }
    }
  }
  .demo-form-inline .el-form-item{
    width: 50%;
    display: inline-block;
  }
  .wrap{
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba($color: (#000000), $alpha: 0.6);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    .box-card{
      // padding: 10px 3% 30px;
      box-sizing: border-box;
      // width: 70%;
      min-width: 800px;
      // max-height: 88vh;
      // overflow-y: hidden;
      position: relative;
      .el-row:hover{
        background-color: #f5f5f5;
      }
      .el-input{
        width: 90%;
      }
      .close{
        float: right!important; 
        padding: 3px 0;
        font-size: 1.3em;
        cursor: pointer;
        opacity: .7;
        :hover{
          opacity: .9;
        }
      }
      .el-row{
        padding: 10px 0;
      }
      .remark{
        line-height: 26px;
      }
      li{
        padding-top: 10px;
        line-height: 26px;
      }
      h3{
        color: #606266;
      }
      h3.title{
        color: #303133;
      }
    }
  }
   .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both
  }

  
  /* 滚动槽 */
  ::-webkit-scrollbar-track {
  border-radius:10px;
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
  border-radius:10px;
  /* background:black; */
  }
</style>
