<template>
<!-- 重要报送信息回收站列表 -->
  <el-row>
    <el-table stripe align="center" :expand-row-keys="expands" v-loading="loading" ref="multipleTable" :data="list"
    tooltip-effect="dark" style="width: 100%" row-key="_id" class="warning-table" :row-class-name="tableRowClassName">
      <!-- <el-table-column type="selection" width="42"></el-table-column> -->
      <el-table-column type="expand">
        <template slot-scope="props">
          <span>{{ props.row.name }}</span>
          <div class="process">
            <strong class="left">事件经过：</strong>
            <el-steps :space="70" :active="props.row.process.length" finish-status="success" class="right" direction="vertical">
              <el-step :title="proce.thing.replace(/预警/g, '重要信息')" :description="proce.time + ' ' + (proce.disposalPlan.map(ele=>ele.value||'')||'') + (proce.remark||'')" v-for="(proce, i) in props.row.process" :key="i"></el-step>
            </el-steps>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="企业名称" width="200px">
        <template slot-scope="scope">
          <el-tooltip v-if="!scope.row.company[0]" class="item" effect="dark" content="该企业已在系统中注销" placement="top">
            <span>{{ scope.row.companyName || ''}}</span>
          </el-tooltip>
          <el-link v-else :underline="false" @click.stop="toAdminorg(scope.row.companyId)">{{ scope.row.company.cname || scope.row.companyName}}</el-link>
        </template> 
      </el-table-column>
      <el-table-column label="报送信息类型" width="70" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.type===1?'检测':'体检'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="信息内容">
        <template slot-scope="scope">
          <template v-if="scope.row.content.length > 2">
            <li v-for="(item, i) in scope.row.content.slice(0, 2)" :key="i"> {{item}}</li>......
          </template>
          <li v-for="(item, i) in scope.row.content" :key="i" v-else> {{item}}</li>
        </template>
      </el-table-column>
      <el-table-column label="报送时间" width="120">
        <template slot-scope="scope">
          <i class="el-icon-time"></i>
          <span style="margin-left: 10px">{{ scope.row.ctime}}</span>
        </template>
      </el-table-column>
      <el-table-column label="整改时间" width="120">
        <template slot-scope="props" v-if="props.row.rectifyTime">
          <i class="el-icon-time"></i>
          {{ props.row.rectifyTime}}
        </template>
      </el-table-column>
      <el-table-column label="风险等级" width="70px" prop="projectsCount" fixed="right" class="warning-lever" align="center">
        <template slot-scope="scope">
          <div slot="reference" class="name-wrapper">
            <span v-if="scope.row.lever > 2" class="warning2">{{leverName[+scope.row.lever-1]}}</span>
            <span v-else class="danger">{{leverName[+scope.row.lever-1]}}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="90px" prop="projectsCount" fixed="right" align="center">
        <template slot-scope="scope">
          <div slot="reference" class="name-wrapper" style="font-size:13px">
            <span style="color:rgb(80, 164, 252);" v-if="scope.row.status==0">待处理</span>
            <span style="color:rgb(103, 194, 58);" v-else-if="scope.row.status==1">已处理</span>
            <span style="color:rgb(231, 162, 59);" v-else-if="scope.row.status==2">已整改</span>
            <span style="color:rgb(244, 108, 108);" v-else-if="scope.row.status==4">已驳回</span>
            <span style="color:green;" v-else-if="scope.row.status==3">已解除</span>
            <span style="color:rgb(145, 147, 152);" v-else>已撤销</span> 
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right" align="center">
        <template slot="header"> </template>
        <template slot-scope="scope">
          <el-tooltip content="还原重要信息报送" placement="top">
              <el-button size="mini" circle @click="reliveWarning(scope.row._id)"><i class="el-icon-refresh-left"></i></el-button>
          </el-tooltip>
          <el-tooltip content="彻底删除" placement="top">
              <el-button size="mini" circle @click="removeCompletely(scope.row._id)"><i class="el-icon-delete-solid"></i></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

  </el-row>
</template>

<script>
import {deleteWarning, updateWarning } from "@/api/manage";
export default {
  props: {
    list: Array,
    searchParams: Object,
    // count: Object,
    files: Object,
  },
  data() {
    return {
      green: { color: "#13CE66" },
      red: { color: "#FF4949" },
      loading: false,
      expands: [], //table展开的行
      changeWarningFlag: false,
      curItem: {},
      leverName: ['一级', '二级', '三级', '四级'],
      status:['待处理', '已处理', '已整改', '已解除', '已驳回', '已撤销'],
      districtRegAdd: '', // curItem的注册地址
      reportWarning: false, // 上报重要报送信息
      aa:''
   	}
    
  },
  methods: {
    toAdminorg(companyId){
      location.href = '/admin/adminorgGov/editAdminorg/' + companyId;
    },
    tableRowClassName({row, rowIndex}) {
      if (row.status == 3) {
        return 'grey-row';
      } 
      return '';
    },
    // 还原重要报送信息
    reliveWarning(_id){
      this.$confirm('确认还原该重要信息报送吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          updateWarning({ _id, delete: false }).then(res => {
            const flag = res.msg.indexOf('不可还原') === -1;
            this.$message({
              type: flag ? 'success' : 'warning',
              message: res.msg
            });
            if(flag){
              this.$emit('refresh'); //刷新列表
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消还原操作'
          });          
        });
    },
    // 删除重要报送信息
    removeCompletely(_id){
      this.$confirm('确认彻底删除该重要信息吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteWarning({ _id, thoroughly: true }).then(res => {
            if(res.status == 200){
              this.$message({
                type: 'success',
                message: res.msg
              });
              this.$emit('refresh'); //刷新列表
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
    },
    // 点击查看整改报告
    viewFiles(companyName, id, status, content, process, lever, modifiedStatus){
      this.files.show = true;
      this.files.companyName = companyName;
      this.files.status = status || 0;
      this.files.id = id;
      this.files.content = content;
      this.files.process = process || [];
      this.files.lever = lever;
      this.files.modifiedStatus = modifiedStatus;
    },
    // 打开/关闭事件经过
    expandChange(id){
      if(this.expands.includes(id)){
        this.expands.splice(this.expands.indexOf(id), 1);
      }else{
        this.expands.push(id);
      }
    },
  },
  
};
</script>

<style>
  .warning-table .grey-row {
    color: #909399;
  }
  .warning-table th{
    background: #f5f7fa!important;
    color: #424242;
  }
  .warningPrompt{
    width: 70vw!important;
    padding: 10px;
    max-width: 1100px;
    min-width: 400px;
  }
  .warningPrompt .el-message-box__title{
    text-align: center;
  }
</style>
<style scoped lang="scss">
.warning-table{
  // border: 1px solid #ddd;
  // box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  .el-button--warning.is-plain,
  .el-button--danger.is-plain{
    cursor: default!important;
  }
  .warning2{
    background-color: rgba(251, 237, 217, 1);
    padding: 5px 7px;
    text-align: center;
    color: rgba(231, 162, 59, 1);
    font-size: 13px;
  }
  .danger{
    background-color: rgba(253, 226, 226, 1);
    padding: 5px 7px;
    text-align: center;
    color: rgba(244, 108, 108, 1);
    font-size: 13px;
  }
  .process{
    display: flex;
    width: 100%;
    .left{
      flex: 0 0 80px;
      color:#606266;
    }
    .right{
      flex: 1 1;
      
    }

  }
}


</style>>
 
