<template>
  <div id="ledger-app" class="questionnaire">
    <div :class="classObj">
      <div class="main-container">
        <router-view/>
      </div>
    </div>
  </div>
</template>

<script>
import { initEvent } from "@root/publicMethods/events";
export default {
  name: "App",
  data() {
    return {
      sidebarOpened: true,
      device: "desktop",
    }
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile"
      };
    }
  },
  mounted() {
    initEvent(this);
  },
  methods: {
    handleSelect(value) {
      this.activeIndex = value
    }
  }
}
</script>
<style>
  .questionnaire .el-dialog{
    width: 80%;
    margin: 0 auto!important; /*水平居中*/
    position: relative;
    top: 50%; /*偏移*/
    transform: translateY(-50%);
  }
  .questionnaire .el-dialog ul{
    max-height: calc(99vh - 220px);
    overflow-y: scroll;
  }
  .questionnaire .el-dialog__body{
    padding-top: 0;
  }
  .questionnaire input::-webkit-outer-spin-button,
  .questionnaire input::-webkit-inner-spin-button {
      -webkit-appearance: none;
  }
  .questionnaire input[type="number"]{
      -moz-appearance: textfield;
  }

</style>