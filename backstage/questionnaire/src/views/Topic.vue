<template>
  <div class="home">
    <el-card shadow="hover">
      <div class="header">
        <div class="title">
          <el-button @click="$router.go(-1)" type="mini" icon="el-icon-arrow-left" style="margin-right: 10px">返回</el-button>
          编辑调查表
        </div>
        <div>
          <el-button @click="jsonToExcel" size="small" class="addBtn el-icon-download" type="primary"> 下载</el-button>
        </div>
      </div>
      <!-- 内容区 -->
      <div class="filterDv">
        <!-- <div class="tip">
          <p><span>1</span> 基本信息</p>
        </div> -->
        <el-form :model="form" :rules="rules" ref="form" label-width="150px" :inline="true" style="margin-bottom:20px">
          <el-form-item label="调查表名称： " prop="name">
            <el-input v-model="form.name" style="width:40vw"></el-input>
          </el-form-item>
          <el-form-item label="出题方式： " >
            <el-input :value="method" disabled style="width:18vw"></el-input>
          </el-form-item>
          
          <el-form-item label="调查开始时间： " prop="startTime" style="margin-top: 20px">
            <el-date-picker v-model="form.startTime" type="datetime" style="width:40vw" format="yyyy-MM-dd HH:mm"></el-date-picker>
          </el-form-item>
          <el-form-item label="调查截止时间： " prop="endTime" style="margin-top: 20px">
            <el-date-picker v-model="form.endTime" type="datetime" style="width:18vw" format="yyyy-MM-dd HH:mm"></el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="调查表描述： ">
            <el-input v-model="form.description" type="textarea" :rows="4"></el-input>
          </el-form-item> -->
          <el-form-item label="状态： "  prop="status" style="margin-top: 20px; width: 30%;">
            <el-switch v-model="form.status" active-text="启用" inactive-text="停用"></el-switch>
          </el-form-item>
          <el-form-item label="调查对象： "  prop="description" style="margin-top: 20px;">
             <el-input v-model="form.description" style="width: 45vw"></el-input>
          </el-form-item>
        </el-form>

        <!-- <div class="tip">
          <p><span>2</span> 调查表内容</p>
        </div> -->
        <div class="content">
          <!------- 左边 ------->
          <div class="left">
            <p style="font-weight: bold;">题目内容：</p>
            <ul v-show="totleAcount">
              <li v-for="type in typeSummary" :key="type.topicType" v-show="type.num!==0">
                <span>{{ type.topicType | topicType }} <span class="el-icon-close"></span> {{ type.num }}</span>
                <p>每题 <el-input size="mini" v-model="type.score" type="number"></el-input> 分</p>
              </li>
            </ul>
            <h4 v-show="totleAcount"> 总分：{{ totalScore }} 分</h4>
            <p class="sort" @click="showQuestionSort=true" v-if="selectedQuestions.length > 1"><i class="el-icon-sort"></i> 题目排序</p>
          </div>
          <!------- 右边 ------->
          <div class="right">
            <div class="title">
              <h4>题目列表</h4>
              <span>已添加<span> {{ totleAcount }} / 100 </span> 题</span>
            </div>
            <ul>
              <!-- 题目列表展示 -->
              <li v-for="(item, index) in selectedQuestions" :key="item.id">
                <p class="des">
                  {{index+1}}、{{item.topicType | topicType}} 
                  <!-- <span class="grey2">分值：</span><el-input size="mini"></el-input> -->
                  <span class="grey2">
                    <span v-show="item.questionBank">题库：{{item.questionBank || ''}} </span>
                    <i class="el-icon-edit" @click="edit(item, index)" v-if="methodNum==1"></i>
                    <el-popover placement="bottom-end" :ref="`popover-${item._id}`">
                      <p>确定删除该题目吗？</p>
                      <div style="text-align: right; ">
                        <el-button size="mini" type="text" @click="$refs[`popover-${item._id}`][0].doClose()">取消</el-button>
                        <el-button type="primary" size="mini" @click="delQuestion(index, item._id)">确定</el-button>
                      </div>
                      <i class="el-icon-delete" slot="reference"></i>
                    </el-popover>                    
                  </span>
                </p>
                <p>{{item.steam || ''}}</p>
                <div class="steamPic" v-if="item.steamPic && item.steamPic.length">
                  <el-image :src="img.staticSrc" :key="img._id"
                  :preview-src-list="item.steamPic.map(ele => ele.staticSrc)" v-for="img in item.steamPic"></el-image>
                </div>
                <div>
                  <!-- 多选题 -->
                  <el-checkbox-group :value="item.answer" v-if="item.topicType==2">
                    <el-checkbox :label="n" v-for="(option, n) in item.options" :key="option._id">{{ A_J[n] }} {{ option.optionText }}</el-checkbox>
                  </el-checkbox-group>
                  <!-- 判断题 -->
                  <el-radio-group :value="item.answer[0]" v-else-if="item.topicType==3">
                    <el-radio :label="0">正确</el-radio>
                    <el-radio :label="1">错误</el-radio>
                  </el-radio-group>
                  <!-- 单选题 -->
                  <el-radio-group :value="item.answer[0]" v-else-if="item.topicType==1">
                    <el-radio :label="n" v-for="(option, n) in item.options" :key="option._id">{{ A_J[n] }} {{ option.optionText }}</el-radio>
                  </el-radio-group>
                  <!-- 填空题/问答题 -->
                  <p v-else-if="item.topicType > 3 " class="grey">参考答案：{{ item.answer.join('、') || '无' }}</p>
                </div>
                <div class="tip">
                  <p class="analysis">答案解析： {{item.answerAnalysis || '无'}}</p>
                  <div class="steamPic" v-if="item.answerAnalysisPic && item.answerAnalysisPic.length">
                    <el-image :src="img.staticSrc" v-for="img in item.answerAnalysisPic" :key="img._id"
                    :preview-src-list="item.answerAnalysisPic.map(ele => ele.staticSrc)" ></el-image>
                  </div>
                </div>
              </li>
              <!-- 添加题目 -->
              <div v-if="methodNum==1" style="display:flex">
                <el-button type="primary" plain class="addQuestion el-icon-plus" @click="addTopic"> 添加题目</el-button>
                <!-- <el-button type="primary" plain class="addQuestion el-icon-upload2" @click="showImportTopic = true"> 批量导入</el-button> -->
              </div>
              <el-button type="primary" plain class="addQuestion el-icon-plus" @click="showQuestionBank=true" v-else-if="methodNum==2"> 从题库选择</el-button>
              <el-button type="primary" plain class="addQuestion el-icon-edit-outline" @click="showRandomQuestion=true" v-else> 组成规则</el-button>
            </ul>
          </div>
        </div>
        <!-- 提交/取消调查表 -->
        <div class="drawer__footer" v-if="totleAcount<100">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" :loading="loading" @click="subTestPaper">{{ loading ? "保存中 ..." : "保 存" }}</el-button>
        </div>
      </div>
      <!-- 新增试题 -->
      <TopicForm :editIndex='editIndex' :data="selectTopic" v-if="drawer" :drawer.sync="drawer" @updateSuccess="updateSuccess" :selectedQuestions="selectedQuestions"/>
      <!-- 选择题库 -->
      <el-dialog title="选择题库" :visible.sync="showQuestionBank" >
        <QuestionBank :curTopic="curTopic"/>
      </el-dialog>
      <!-- 选择题目 -->
      <el-dialog :title="questionListTitle" :visible.sync="showQuestionList">
        <QuestionList :curTopic="curTopic" :selectedQuestions="selectedQuestions" @close="showQuestionList=false"/>
      </el-dialog>
      <!-- 随机组卷规则 -->
      <el-dialog title="组题规则" :visible.sync="showRandomQuestion">
        <RandomQuestion :selectedQuestions="selectedQuestions" :combinationRule="combinationRule" />
      </el-dialog>
      <!-- 批量导入 -->
      <el-dialog title="批量导入题目" :visible.sync="showImportTopic">
        <ImportTopic :selectedQuestions="selectedQuestions"/>
      </el-dialog>
      <!-- 试题排序 -->
      <el-dialog title="题目排序" :visible.sync="showQuestionSort">
        <QuestionSort :selectedQuestions="selectedQuestions"/>
      </el-dialog>
    </el-card>   
  </div>
</template>

<script>
import serverApi from '../api'
// import moment from 'moment';
import TopicForm from '@/components/topicForm';
import QuestionBank from '@/components/questionBank';
import QuestionList from '@/components/questionList';
import RandomQuestion from '@/components/randomQuestion';
import ImportTopic from '@/views/importTopic';
import QuestionSort from '@/components/questionSort';
import XLSX from 'xlsx'
export default {
  components: {
    TopicForm,
    QuestionBank,
    QuestionList,
    ImportTopic,
    RandomQuestion,
    QuestionSort
  },
  computed: {
    questionListTitle(){
      return `从${this.curTopic.name}中选择试题`;
    },
    method(){ // 出题方式
      switch(+this.$route.query.method){
        case 1: return '手动录入/导入'; break;
        case 2: return '从题库选择'; break;
        case 3: return '随机组成'; break;
      }
    },
    // 格式化标签
    formatLabel(label) {
      return function(label) {
        const _this = this;
        if (label && label.length > 0) {
          const label_Names = []
          label.forEach(item => {
            const res = _this.labelOptions.filter(item2 => {
              return item2.value == item
            })
            if(res.length > 0) label_Names.push(res[0].label)
          })
          return label_Names.join('|')
        } else {
          return '-'
        }
      }
    }
  },
  data() {
    return {
      showQuestionSort: false,
      combinationRule: [], // 组卷规则
      showRandomQuestion: false, // 弹出随机组件规则
      methodNum: this.$route.query.method, // 出题方式  1 2 3
      showImportTopic: false,
      loading: false,
      showQuestionBank: false,
      showQuestionList: false,
      curTopic: { // 当前选择的题库
        id: '',
        name: ''
      },
      selectedQuestions: [], // 已选择的题目
      typeSummary: [ // 调查表的题型分数汇总 topicType 1：单选 2：多选 3：判断 4：填空 5：问答
        { topicType: 1, num: 0, score: 0 }, // score指分数
        { topicType: 2, num: 0, score: 0 },
        { topicType: 3, num: 0, score: 0 },
        { topicType: 4, num: 0, score: 0 },
        { topicType: 5, num: 0, score: 0 },
      ], 
      form: { 
        name: '', // 调查表名称
        startTime: '',
        endTime: '',
        status: true, // 是否启用
        description: '' // 调查对象
      },
      rules: {
        name: [
            { required: true, message: '请输入调查表名称', trigger: 'blur' },
            { min: 3, max: 30, message: '长度在 3 到 30 个字符', trigger: 'blur' }
          ],
          startTime: [
            { required: true, message: '请选择调查开始时间', trigger: 'blur' }
          ],
          endTime: [
            { required: true, message: '请选择调查截止时间', trigger: 'blur' }
          ],
      },
      drawer: false, // 控制抽屉开关
      tableField: [
        {label: '题干', prop: 'steam'},
        {label: '题型', prop: 'topicType'},
        {label: '标签', prop: 'labels'},
        {label: '更新时间', prop: 'updatedAt'},
      ], // 表格字段
      tableData: [], // 试题表格数据
      pageInfo: {
        pageCurrent: 1,
        size: 10,
        total: 0
      }, // 分页条件
      query: {
        keyWords: null,
        topicType: null,
        label: null
      }, // 筛选条件
      selectTopic: {}, // 编辑时选择的题目
      topicTypeOptions: [{
        value: null,
        label: '全部'
      },{
        value: 1,
        label: '单选题'
      }, {
        value: 2,
        label: '多选题'
      }, {
        value: 3,
        label: '判断题'
      }], // 题型的类型
      labelOptions: [], // 标签
      A_J: [ 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J' ],
      totleAcount: 0, // 当前总题数
      totalScore: 0, // 当前总分数
      editFlag: false, // 编辑调查表？
      editIndex: null, // 当前编辑的试题在调查表中的索引
    }
  },
  created() { 
    // this.initData();
    this.$route.query.id && this.getDetail(this.$route.query.id);
  },
  watch: {
    // 左边更新了
    'typeSummary': {
      deep:true, 
      handler:function(type){
        this.totalScore = 0;
        type.forEach(ele => {
          this.totalScore += (ele.num * (+ele.score))
        })
      }
    },
    // 考题列表更新
    selectedQuestions(list){
      console.log('添加题目了', list);
      this.showQuestionList = false;
      this.showImportTopic = false;
      this.showRandomQuestion = false;
      this.showQuestionSort = false;
      this.curTopic = {
        name: '',
        id: ''
      }
      // 更新题型汇总统计还有总分数
      this.typeSummary[0].num = this.typeSummary[1].num = this.typeSummary[2].num = this.typeSummary[3].num = this.typeSummary[4].num = 0;
      list.forEach(ele => {
         this.typeSummary[+ele.topicType-1].num++;
      })
      this.totleAcount = list.length;
    },
    'curTopic': { // 切换题库,这个在QuestionBank组件中触发
      deep:true, 
      handler:function(topic){
        if(topic.id){
          this.showQuestionBank = false;
          this.showQuestionList = true;
          this.curTopic.id = topic.id;
          this.curTopic.name = topic.name;
        }
      }
    },
  },
  methods: {
    // 获取数据（之前wzq写的）
    async initData() {
      const questionBankID = this.$route.query.id;
      let res = await serverApi.getTopic({ ...this.pageInfo , query: this.query, questionBankID });
      this.tableData = res.data.doc;
      this.pageInfo = res.data.pageInfo;
      this.labelOptions = res.data.topicLable.doc;
      
    },
    // 获取调查表详情
    getDetail(_id){
      serverApi.getDetail({ _id }).then(res => {
        if(res.status == 200){
          const data = res.data;
          console.log('调查表详情: ', data);
          this.typeSummary = data.typeSummary;
          this.totleAcount = data.totleAcount;
          this.form.name = data.name;
          this.form.startTime = data.startTime;
          this.form.endTime = data.endTime;
          this.form.status = data.status;
          this.form.description = data.description;
          this.selectedQuestions = data.questions;
          this.editFlag = true;
          this.combinationRule.push(...data.combinationRule);
        }
      })
    },
     // 保存提交前的校验
    validate(){
      // if(this.typeSummary[0].num && !this.typeSummary[0].score){
      //   this.$message({
      //     message: '请填写单选题分值',
      //     type: 'warning'
      //   }); 
      //   return false;
      // }
      // if(this.typeSummary[1].num && !this.typeSummary[1].score){
      //   this.$message({
      //     message: '请填写多选题分值',
      //     type: 'warning'
      //   }); 
      //   return false;
      // }
      // if(this.typeSummary[2].num && !this.typeSummary[2].score){
      //   this.$message({
      //     message: '请填写判断题分值',
      //     type: 'warning'
      //   }); 
      //   return false;
      // }
      if(this.selectedQuestions.length == 0){
        this.$message({
          message: '没有考题不能保存，只能到调查表列表页删除！',
          type: 'warning'
        }); 
        return false;
      }
      if(!this.form.name){
        this.$message({
          message: '请输入调查表名称',
          type: 'warning'
        }); 
        return false;
      }
      if(!this.form.startTime || !this.form.endTime){
        this.$message({
          message: '请选择开始时间和截止时间',
          type: 'warning'
        }); 
        return false;
      }
      if(this.form.startTime && this.form.endTime){
        const startTime = new Date(this.form.startTime).getTime();
        const endTime = new Date(this.form.endTime).getTime();
        if(startTime > endTime){
          this.$message({
            message: '开始时间不能大于截止时间',
            type: 'warning'
          }); 
          return false;
        }
      }
      return true;
    },
    // 提交调查表
    subTestPaper(){
      if(!this.validate()) return; // 校验
      if(this.editFlag){ // 编辑
        serverApi.editTestPaper({
          _id: this.$route.query.id,
          name: this.form.name,
          startTime: this.form.startTime,
          endTime: this.form.endTime,
          status: this.form.status,
          description: this.form.description,
          typeSummary: this.typeSummary,
          totleAcount: this.totleAcount,
          questions: this.selectedQuestions.map(ele => ele._id),
          combinationRule: this.combinationRule
        }).then(res => {
          console.log('调查表编辑成功:', res);
          if(res.status == 200){
            this.$message({
              message: '调查表编辑成功',
              type: 'success'
            });
            this.$router.go(-1);
          }else{
            this.$message({
              message: res.message,
              type: 'warning'
            });
          }
        })
      }else{ // 添加
        serverApi.addTestPaper({
          method: this.$route.query.method || 2,
          name: this.form.name,
          startTime: this.form.startTime,
          endTime: this.form.endTime,
          typeSummary: this.typeSummary,
          totleAcount: this.totleAcount,
          questions: this.selectedQuestions.map(ele => ele._id),
          combinationRule: this.combinationRule
        }).then(res => {
          console.log('调查表添加成功:', res);
          if(res.status == 200){
            this.$message({
              message: '调查表添加成功',
              type: 'success'
            });
            this.$router.go(-1);
          }else{
            this.$message({
              message: res.message,
              type: 'warning'
            });
          }
        })
      }
    },
    // 点击取消
    cancel(){
      this.selectedQuestions = [];
      this.form.name = '';
      this.$router.go(-1);
    },
    // 删除某个考题
    delQuestion(index, id){
      // const res = await serverApi.delTopic({ids: [ row._id ]})
      if(this.selectedQuestions[index]._id == id){
        this.selectedQuestions.splice(index, 1);
      }else{
        this.selectedQuestions = this.selectedQuestions.filter(ele => ele._id != id);
      }
      if(this.methodNum == '1') serverApi.delTopic({ids: [ id ]}); // 如果是自己建的可以删除数据库
      this.$message({
        message: '删除成功',
        type: 'success'
      });
    },
    
    // 点击 创建试题 后，初始化参数传入组件
    addTopic() {
      this.selectTopic = {}
      this.drawer = true
    },
    // 添加试题成功后，请求最新的数据
    async updateSuccess() {
      await this.initData()
    },
    // 处理编辑
    edit(row, index) {
      this.selectTopic = row;
      this.drawer = true;
      this.editIndex = index;
    },
    
    // 导出 试题
    async jsonToExcel() {
      const tableField = {
        topicType: '题型（必填）',
        steam: '题干（必填）',
        answer: '答案（必填）',
        answerAnalysis: '答案解析（选填）',
        labels: '标签（仅支持题库中已存在）',
        options: '选项',
      }
      const _this = this;
      const newJson = []
      const data = this.selectedQuestions.map(item => {
        const newData = {}
        Object.keys(tableField).forEach(key => {
          if(key == 'answer') {
            const answerArr = item[key].map(item2 => {
              return _this.A_J[item2]
            })
            newData[tableField[key]] = answerArr.join('|')
          } else if (key == 'topicType') {
            let topicTypeText = '';
            switch(item[key]) {
              case 1:
                topicTypeText = '单选题'
                break;
              case 2:
                topicTypeText = '多选题'
                break;
              case 2:
                topicTypeText = '判断题'
                break;
            }
            newData[tableField[key]] = topicTypeText
          } else if (key == 'options') {
            console.log(item[key])
            item[key].forEach((item2, i) => {
              newData[tableField[key] + _this.A_J[i]] = item[key][i].optionText
            })
          } else {
            newData[tableField[key]] = item[key]
          }
        })
        newJson.push(newData)
      })
      const ws = XLSX.utils.json_to_sheet(newJson, { origin: 1 })
      ws['!merges'] = [{s: {r: 0, c: 0}, e: {r: 0, c: 15}}]
      ws.A1 = { t: 's', v: "填写注意事项：从其他Excel或Word复制试题时请使用选择性粘贴，Word：右键——选择性粘贴——文本，Excel：右键——选择性粘贴——只勾选“值”。1.第1行和第2行不可修改或删除！2.单选题/多选题：最多可添加10个选项（选项A,选项B……选项I,选项J）；多选题有多个答案时需用“|”分隔（例如“A|B|C”）。3.判断题：只能设置两个选项，正确和错误。4.填空题：题干填空处使用[]，中间不能有空格，答案列按填空顺序填写正确答案，不同填空用“、”分隔，填空处用[]，同一空的多个答案之间用“|”分隔开,。5.问答题：答案为空。6.标签：非必填项，仅在题库导入时有效，可根据需要填写，多个标签则用“|”分隔(例如“美工|PS技巧”)。7.分数：仅在导入调查表/考试时有效，此时为必填项，支持小数点后一位。8.图片：试题中包括图片需导入后再进行编辑上传。9.若填写后单元格显示红色底色表示填写有误，请参照样例检查填写内容。" }
      var wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "People");
      XLSX.writeFile(wb, '试题.xlsx')
    }
  },
  filters: {
    topicType(n){
      switch(+n){
        case 1: return '单选题'; break;
        case 2: return '多选题'; break;
        case 3: return '判断题'; break;
        case 4: return '填空题'; break;
        case 5: return '问答题'; break;
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.steamPic{
  margin-bottom: 10px;
  .el-image{
    width: 100px; 
    height: 100px;
    margin-right: 10px;
  }
  .el-image:hover{
    border:1px solid #ddd;
  }
}
.tip {
    padding: 0px 16px 5px;
    background: rgba(217, 236, 255, .2);
    border-radius: 4px;
    margin: 20px 0;
    p{
      font-size: 1.1em;
      margin: 7px 0;
      span{
        font-size: 2.6em;
        margin-right: 20px;
        color: rgba(140, 197, 255, .7);
        vertical-align: middle;
      }
    }
}
.content{
  display: flex;
  .el-input{
    display: inline-block;
    width: 50px;
    margin: 0 5px;
  }
  .grey{
    color: #606266;
    font-size: 13px;
  }
  .grey2{
    color: #909399;
    font-size: 13px;
  }
  p{
    font-size: .9em;
    color: #666;
  }
  li{
    list-style: none;
    border-bottom: 1px solid #eee;
  }
  li:last-of-type{
    border-bottom: 0;
  }
  .float{
    float: right;
  }
  .left{
    flex: 0 0 140px;
    margin-right: 10px;
    >p{
      text-align: right;
      font-size: .9em;
    }
    ul{
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      li{
        padding-top: 10px;
        font-size: 13px;
        color: #606266;
      }
    }
    .sort{ // 试题排序
      text-align: center;
      color: #409EFF;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 8px 0;
      font-size: 13px;
      cursor: pointer;
    }
    .sort:hover{
      border-color: #409EFF;
    }
  }
  .right{
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 4px;
    .title{
       border-bottom: 1px solid #ddd;
       padding: 0 15px;
      h4{
        display: inline-block;
        font-weight: normal;
        margin: 0;
        line-height: 50px;
      }
      >span{
        float: right;
        line-height: 50px;
        font-size: 13px;
        color: #909399;
        span{
          color: #666;
        }
      }
    }
    ul{
      padding: 0 15px;
      li{
        .des{
          span:first-of-type{
            margin-left: 15px;
          }
          .el-input{
            display: inline-block;
          }
          span:last-of-type{
            float: right;
            .el-icon-delete, .el-icon-edit{
              color: #409EFF;
              cursor: pointer;
              margin-left: 10px;
            }
          }
        }
        .analysis{ // 答案解析
          padding: 10px 0 0px;
          color:#839AB3;
          line-height: 22px;
          font-size: 13px;
        }
      }
    }
    .RandomTest{ // 随机组卷form
      padding: 15px;
      box-sizing: border-box;
      .el-icon-delete, .el-icon-edit{
        color: #409EFF;
        cursor: pointer;
        margin-left: 10px;
      }
    }
    .addQuestion{
      width: 100%;
    }
  }
}
// 提交取消
.drawer__footer{
  margin: 20px 0;
  padding-left: 150px;
}

  .btnDv {
    width: 100%;
    text-align: center;
    margin-top: 20px;
  }
  .home {
    width: 98%;
    margin: 20px auto;
  }
  .addBtn {
    margin: 10px 0;
  }
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      display: flex;
      align-items: center;
      font-weight: bold;
    }
  }
  .filterDv {
    width: 100%;
    margin-top: 10px;
  }
 
  .el-form-item {
    margin-right: 0;
    margin-bottom: 0px;
  }

</style>

