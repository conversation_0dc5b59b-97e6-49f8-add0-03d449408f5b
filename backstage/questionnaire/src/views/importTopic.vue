<template>
  <div class="page" id="employee0">
    <el-card shadow="hover">
      <div slot="header" class="header">
        <div class="title" @click="info">操作指引 <i class="el-icon-question" ></i></div>
      </div>
      <upload-excel
        :before-upload="beforeUpload"
        :on-success="handleSuccess"
        :submit="submit"
        @downloadTemplate="downloadTemplate"
      >
        <transition name="fade">
          <el-button
            type="danger"
            size="mini"
            v-show="isShowMultiDel"
            @click="multiDel"
            >批量删除</el-button
          >
        </transition>
      </upload-excel>
      <el-form :model="form" :show-message="false" ref="form" class="excelForm">
        <pl-table
          :data="form.tableData"
          highlight-current-row
          style="width: 100%; margin-top: 20px; height:55vh; overflow: scroll;"
          @cell-click="cellClick"
          use-virtual
          @selection-change="handleSelectionChange"
          ref="multipleTable"
        >
          <pl-table-column type="selection" align="center" width="55">
          </pl-table-column>
          <pl-table-column label="序号" align="center" width="55">
            <template slot-scope="scope">
              {{ scope.row.id + 1 }}
            </template>
          </pl-table-column>
          <pl-table-column
            v-for="(item, index) of tableHeader"
            :key="index"
            :label="item.label"
            :width="tableHeaderWidth[index]"
            align="center"
          >
            <template slot-scope="scope">
              <div v-if="!isEdit[scope.row.id + item.label]">{{scope.row[item.prop]}}</div>
              <el-form-item
                  v-else
                  :prop="`tableData[${scope.$index}][${item.prop}]`"
                  :rules="rules[item.prop]"
                >
              <el-input v-model="form.tableData[scope.$index][item.prop]" @blur.stop="blur(scope, item)" />
              </el-form-item>
            </template>
          </pl-table-column>

          <pl-table-column label="操作" align="center" width="80">
            <template slot-scope="scope">
              <el-button type="danger" size="mini" @click="detele(scope)" >删除</el-button>
            </template>
          </pl-table-column>
        </pl-table>
      </el-form>
    </el-card>
    <!-- 导入错误提示组件 -->
    <import-tips
      :dialogVisible.sync="dialogVisible"
      :tipsMessage="tipsMessage"
      href="/static/dataTemplate/试题模板.xlsx"
    >
      <template #tipImage>
        <img src="@/assets/excelts.png" style="width: 100%" />
      </template>
    </import-tips>
  </div>
</template>

<script>
import Driver from "driver.js";
import "driver.js/dist/driver.min.css";
import UploadExcel from "@/components/UploadExcel/index";
import { PlTable, PlTableColumn } from "pl-table";
import serviceAPI from "@/api/index.js";
import moment from "moment";
import axios from "@/utils/axios.js";
import ImportTips from "@/components/importTips/index";

const driver = new Driver({
  allowClose: false,
  doneBtnText: "结束引导",
  closeBtnText: "关闭引导",
  nextBtnText: "下一步",
  prevBtnText: "上一步",
});
export default {
  props: {
    selectedQuestions: Array
  },
  data() {
    return {
      driver: null,
      form: {
        tableData: [],
      },
      tableHeader: [
        { label: "题型（必填）", prop: "topicType" },
        { label: "题干（必填）", prop: "steam" },
        { label: "答案", prop: "answer" },
        { label: "答案解析（选填）", prop: "answerAnalysis" },
        { label: "标签（仅支持题库中已存在）", prop: "labels" },
        { label: "分数", prop: "score" },
        { label: "选项A", prop: "options_A" },
        { label: "选项B", prop: "options_B" },
        { label: "选项C", prop: "options_C" },
        { label: "选项D", prop: "options_D" },
        { label: "选项E", prop: "options_E" },
        { label: "选项F", prop: "options_F" },
        { label: "选项G", prop: "options_G" },
        { label: "选项H", prop: "options_H" },
        { label: "选项I", prop: "options_I" },
        { label: "选项J", prop: "options_J" },
      ],
      tableHeaderWidth: ["100", "150", "", "150", "200", "100", "120", "", ""],
      isEdit: {},
      rules: {
        topicType: [{ required: true, message: "题型必填" }],
        steam: [{ required: true, message: "题干必填" }],
        answer: [{ required: false, message: "答案必填" }],
      },
      isShowMultiDel: false,
      multipleSelection: [],
      dialogVisible: false,
      tipsMessage: [],
      beforeSubmitErrorArr: [],
      A_J: [ 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J' ],
    };
  },
  watch: {
    multipleSelection: function (val) {
      val.length > 0
        ? (this.isShowMultiDel = true)
        : (this.isShowMultiDel = false);
    },
  },
  computed: {

  },
  created() {
    this.freezeObjs([
      "tableHeader",
      "tableHeaderWidth",
      "rules",
    ]); // 将静态数据冻结
  },
  methods: {
    freezeObjs(objs) {
      objs.forEach((item) => {
        this[item] = Object.freeze(this[item]);
      });
    },
    // 上传excel前验证
    beforeUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 10;
      if (isLt1M) {
        return true;
      }
      this.$message({
        message: "Please do not upload files larger than 10m in size.",
        type: "warning",
      });
      return false;
    },

    // 成功处理 excel 表格数据
    handleSuccess({ excelHeader, results }) {
      console.log(1111, excelHeader, results);
      this.tipsMessage = [];
      let isTrue = true;
      if (results.length === 0) {
        isTrue = false;
        this.tipsMessage = "表格内容为空，请检查后重新导入";
      }

      if (!isTrue) {
        this.dialogVisible = true;
      } else {
        this.dialogVisible = false;
        let newData = [];
        let header = [ ...this.tableHeader ];
        results.forEach((item, index) => {
          let oneData = {};
          oneData.id = index;
          header.forEach((val) => {
            oneData[val.prop] = item[val.label];
          });
          newData.push(oneData);
        });
        this.form.tableData = newData;
        // 判断有哪些是空的
        this.form.tableData.forEach((item, index) => {
          this.tableHeader.forEach((item2) => {
            if (!item[item2.prop] && item[item2.prop] !== 0) {
              this.$set(
                this.isEdit,
                this.form.tableData[index].id + item2.label,
                true
              );
            }
          });
        });
      }
      console.log(this.form.tableData)
      driver.isActivated && driver.moveNext();
    },
    // 输入框失去焦点
    blur(scope, item) {
      // 判断是否有校验规则
      let isRules = Object.keys(this.rules).includes(item.prop);
      // 如果有校验规则，走这里
      isRules &&
        this.$refs.form.validateField(
          `tableData[${scope.$index}][${item.prop}]`,
          (err) => {
            if (err) {
              this.$notify.error({
                title: "错误",
                message: err,
                duration: 5000,
              });
            } else {
              this.$delete(this.isEdit, scope.row.id + item.label);
            }
          }
        );
      // 如果没校验规则走这里
      !isRules && this.$delete(this.isEdit, scope.row.id + item.label);
    },
    // 点击单元格
    cellClick(row, column) {
      this.$set(this.isEdit, row.id + column.label, true);
      console.log(666, this.isEdit)
    },
    clickExtend(scope, item) {
      this.$set(this.isEdit, scope.row.id + item.label, true);
    },
    // 删除
    detele(scope) {
      this.$confirm("是否删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.form.tableData.splice(scope.$index, 1);
        })
        .catch(() => {});
    },
    // 提交事件 - 校验
    async submit() {
      const _this = this;
      console.log(this.beforeSubmitErrorArr);
      // 关闭 提交前的校验错误信息
      this.beforeSubmitErrorArr.forEach((item) => {
        item.close();
      });
      this.beforeSubmitErrorArr = [];
      driver.isActivated && driver.reset();

      let isErr = false;
      this.form.tableData.forEach((item, i) => {
        // 全局非空校验
        Object.keys(this.rules).forEach((key) => {
          if (!item[key] && item[key] !== 0) {
            isErr = true;
            if (this.beforeSubmitErrorArr.length < 5) {
              const index = this.form.tableData.indexOf(item);
              let beforeSubmitError = this.$notify.error({
                title: `${this.rules[key][0].message}`,
                dangerouslyUseHTMLString: true,
                message: `请检查后填写完整！<br />错误位置：序号 ${index + 1}`,
                duration: 0,
                offset: 20,
              });
              this.beforeSubmitErrorArr.push(beforeSubmitError);
            }
          }
        });
        _this.A_J.forEach((zm, i) => {
          if(!item['options_'+zm] && i < 2) {
            isErr = true;
            if (this.beforeSubmitErrorArr.length < 5) {
              const index = this.form.tableData.indexOf(item);
              let beforeSubmitError = this.$notify.error({
                title: `每个题目至少存在两个选项`,
                dangerouslyUseHTMLString: true,
                message: `请检查后填写完整！<br />错误位置：序号 ${index + 1}`,
                duration: 0,
                offset: 20,
              });
              this.beforeSubmitErrorArr.push(beforeSubmitError);
            }
          }
        })
      });
      
      if (this.form.tableData.length > 0 && !isErr) {
        this.handleAddSomeEmployeeRes();
      }
    },
    async handleAddSomeEmployeeRes() {
      const _this = this;
      const formatAnswer = function(answer) {
        const arr = answer.split('|');
        const newArr = arr.map(item => {
          return _this.A_J.indexOf(item)
        })
        return newArr
      }
      // 格式化 数据结构
      this.form.tableData.forEach(item => {
        item.questionBankID = this.$route.query.id;
        if(item.topicType == '单选题') {
          item.topicType = 1
          item.answer = formatAnswer(item.answer)
        } else if (item.topicType == '多选题') {
          item.topicType = 2
          item.answer = formatAnswer(item.answer)
        } else if (item.topicType == '判断题') {
          item.topicType = 3
          if(item.answer == '正确') {
            item.answer = [ 0 ]
          } else {
            item.answer = [ 1 ]
          }
        } else if (item.topicType == '填空题') {
          item.topicType = 4
        } else if (item.topicType == '简答题') {
          item.topicType = 5
        }
        item.options = []
        this.A_J.forEach((zm, i) => {
          if(item['options_' + zm]) {
            item.options.push({
              optionText: item['options_' + zm],
              optionPic: '',
            })
          }
        })
      })

      let res = await serviceAPI.addSomeTopic({
        data: this.form.tableData,
      });

      if (!res) {
        return;
      }
      if (res.data.length > 0) {
        this.$message({
          message: `${
            this.form.tableData.length - res.data.length
          }条保存成功，剩下${res.data.length}条保存失败，详情请看右侧通知`,
          type: "warning",
        });
      } else {
        this.$message({
          message: `成功导入${
            res.successData.length
          }条数据`,
          type: "success",
          duration: 4000,
        });
        this.form.tableData = [];
      }
      if(res.status == 200){
        this.selectedQuestions.push(...res.successData);
      }
    },
    downloadTemplate() {
      const a = document.createElement("a");
      a.href = "/static/dataTemplate/试题模板.xlsx?" + new Date().getTime();
      a.click();
      driver.isActivated && driver.moveNext();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 批量删除
    async multiDel() {
      this.$confirm("是否删除所选数据", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.multipleSelection.forEach((item) => {
            this.form.tableData.splice(this.form.tableData.indexOf(item), 1);
          });
        })
        .catch(() => {});
    },
    async info() {
      driver.defineSteps([
        {
          element: ".downloadBtn",
          popover: {
            className: "first-step-popover-class",
            title: "下载模板（1/4）",
            description:
              "提供Excel模板下载，您可以参照Excel模板来完善您的Excel",
            position: "bottom",
          },
        },
        {
          element: ".importExcel",
          popover: {
            className: "first-step-popover-class",
            title: "导入Excel（2/4）",
            description: "点击导入，选择对应的Excel文件，即可导入成功",
            position: "bottom",
          },
        },
        {
          element: ".excelForm",
          popover: {
            className: "first-step-popover-class",
            title: "检查信息（3/4）",
            description:
              "查看导入的Excel人员信息，可点击对应的信息进行编辑修改",
            position: "top",
          },
        },
        {
          element: ".submitBtn",
          popover: {
            className: "first-step-popover-class",
            title: "保存信息（4/4）",
            description: "信息检查完毕后，保存即可提交人员信息",
            position: "left",
          },
        },
      ]);
      driver.start();
    },
  },
  beforeDestroy() {
    this.beforeSubmitErrorArr.forEach((item) => {
      item.close();
    });
  },
  components: {
    UploadExcel,
    PlTable,
    PlTableColumn,
    ImportTips,
  },
};
</script>

<style lang="scss" scoped>
.excel-upload-input {
  display: none;
  z-index: -9999;
}
.demo-table-expand {
  font-size: 0;
}
.el-form-item {
  margin-right: 0;
  margin-bottom: 0px;
}
.extendsInput {
  width: 200px;
}
.avatar {
  display: inline-block;
  color: white;
  border-radius: 50%;
  background: #3296fa;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 14px;
}
.submitBtn {
  margin: 30px auto;
  display: block;
}
.inputSpan {
  min-width: 200px;
  display: inline-block;
  height: 28px;
}
.page {
  width: 98%;
  margin: 20px auto;
}
.title {
  padding-left: 20px;
  font-size: 16px;
  color: #303133;
  border-left: 1px solid #dcdfe6;
  margin: 0 10px 0 20px;
  cursor: pointer;
}
.header {
  align-items: center;
  display: flex;
}
#employee0 ::v-deep .el-form-item__label {
  width: 90px;
  color: #99a9bf;
  align-content: right;
}
</style>

<style>
.datePick .el-input__inner {
  /* border: 0px; */
  width: 150px;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.4s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
