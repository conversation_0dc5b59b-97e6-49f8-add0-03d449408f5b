<template>
  <div class="home">
    <el-card shadow="hover">
      <header>
        <div class="filterDv">
          <div>
            调查表名称：<el-input v-model="keyWord" size="mini"/>
          </div>
          <div>
            <!-- <el-checkbox v-model="checked" style="margin-right:20px">只看我创建的</el-checkbox> -->
            <el-button type="primary" size="small" @click="search">搜索</el-button><el-button size="small" @click="resetSearch">重置</el-button>
          </div>
        </div>
        <el-button @click="showDialog" size="small" class="addBtn el-icon-plus" type="primary"> 创建调查表</el-button>
      </header>
      <!-- table -->
      <el-table :data="tableData" >
        <el-table-column prop="name" label="调查表名称" min-width="160"></el-table-column>
        <el-table-column prop="totleAcount" label="总题数" align="center"></el-table-column>
        <el-table-column label="单选题" align="center">
          <template scope="scope">{{scope.row.typeSummary[0].num}}</template>
        </el-table-column>
        <el-table-column label="多选题" align="center">
          <template scope="scope">{{scope.row.typeSummary[1].num}}</template>
        </el-table-column>
        <el-table-column label="判断题" align="center">
          <template scope="scope">{{scope.row.typeSummary[2].num}}</template>
        </el-table-column>
        <el-table-column label="填空题" align="center">
          <template scope="scope">{{scope.row.typeSummary[3].num}}</template>
        </el-table-column>
        <el-table-column label="问答题" align="center">
          <template scope="scope">{{scope.row.typeSummary[4].num}}</template>
        </el-table-column>
        <!-- <el-table-column label="总分" width="80">
          <template scope="scope">{{scope.row.typeSummary | totalScore}}</template>
        </el-table-column> -->
        <el-table-column label="参与人次" align="center">
          <template scope="scope">
            <el-link type="primary" :href="'/admin/answer?testPaperId='+scope.row._id">{{ scope.row.answerCount }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="调查开始时间" width="140">
          <template scope="scope"> {{ formatTime(scope.row.startTime) }} </template>
        </el-table-column>
        <el-table-column label="调查截止时间" width="140">
          <template scope="scope"> {{ formatTime(scope.row.endTime) }} </template>
        </el-table-column>
        <el-table-column label="状态" width="65" align="center">
          <template scope="scope"> 
            <el-tag v-if="scope.row.status" type="success" size="small">启用</el-tag>
            <el-tag v-else type="warning" size="small">停用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="gotoTopic(scope.row.method, scope.row._id)">编辑</el-button>
            <el-button size="mini" type="danger" @click="del(scope.row._id, scope.row.name)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageInfo.pageCurrent"
          :page-sizes="[5, 10, 20, 30, 50]"
          :page-size="pageInfo.size"
          layout="total, prev, pager, next, sizes"
          :total="pageInfo.total">
        </el-pagination>
      </div>
      <!-- 选择出题方式 -->
      <el-dialog :visible.sync="dialogFormVisible" width="600px" title="选择出题方式" :show-close="true" >
          <el-row :gutter="20" type="flex" justify="space-around" class="method">
            <el-col :span="8" @click.native="gotoTopic(1)">
              <span class="el-icon el-icon-edit-outline"></span>
              <h4>手动录入</h4>
              <p>手动录入题目</p>
            </el-col>
            <el-col :span="8" @click.native="addHandle(2)">
              <span class="el-icon el-icon-finished"></span>
              <h4>从题库选择</h4>
              <p>从题库中选择题目组成</p>
            </el-col>
            <el-col :span="8" @click.native="addHandle(3)">
              <span class="el-icon el-icon-set-up"></span>
              <h4>随机组题</h4>
              <p>设置组题规则，从题库抽题组成</p>
            </el-col>
          </el-row>
      </el-dialog>

      <el-drawer
        title="标签管理"
        :visible.sync="drawerVisible"
        direction="rtl"
        :before-close="handleClose">
        <LabelManage v-if="drawerVisible" />
      </el-drawer>
    </el-card>   
  </div>
</template>

<script>
import serverApi from '../api'
import moment from 'moment';
import LabelManage from '../components/labelManage'
export default {
  components: {
    LabelManage
  },
  computed: {
    formatTime(time) {
      return function(time) {
        const newTime = new Date(time)
        return moment(newTime).format('YYYY-MM-DD HH:mm')
      }
    }
  },
  data() {
    return {
      checked: false, // 只看我创建的调查表
      method: 1, // 出题方式
      pickerOptions: {
        disabledDate(time) {
          return time.getFullYear() > (new Date()).getFullYear() + 1
        }
      },
      tableData: [{}],
      dialogFormVisible: false,
      QBForm: {
        questionBankName: '',
        _id: '',
      },
      pageInfo: {
        pageCurrent: 1,
        size: 10,
        total: 0
      },
      keyWord: '',
      drawerVisible: false
    }
  },
  created() { 
    this.getList();
  },
  methods: {
    addHandle(n){
      console.log(n);
      this.method = n; 
      this.dialogFormVisible = true;
      this.gotoTopic(n);
    },
    async getList() {
      let res = await serverApi.getList({ ...this.pageInfo , keyWord: this.keyWord, lookMy: this.checked });
      this.tableData = res.data.res;
      console.log(2222, this.tableData);
      this.pageInfo = res.data.pageInfo;
    },
    showDialog() {
      this.QBForm.questionBankName = ''
      this.QBForm._id = ''
      this.dialogFormVisible = true
    },
    async addQB() {
      this.$refs.QBform.validate(async valid => {
        if (valid) {
          const res = await serverApi.createQB({ ...this.QBForm, ...this.pageInfo });
          this.dialogFormVisible = false
          this.tableData = res.data
          this.pageInfo = res.pageInfo
          this.$message({
            type: 'success',
            message: '成功创建题库'
          })
        }
      })
    },
    // 删除
    del(_id, name) {
      this.$confirm(`确认删除调查表 - ${name}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let res = await serverApi.delTestPaper({ _id });
        console.log('删除:', res)
        if(res.status == 200) {
          this.$message({
            type: "success",
            message: "删除成功"
          });
          this.getList();
        } else {
          this.$notify({
            title: '删除失败',
            type: 'warning',
            message: res.message
          })
        }
      }).catch(() => {
        
      })  
    },
    async search() {
      this.getList()
    },
    async resetSearch() {
      this.keyWord = '';
      this.lookMy = false;
      this.getList()
    },
    handleSizeChange(e) {
      this.pageInfo.size = e
      this.getList()
    },
    handleCurrentChange(e) {
      this.pageInfo.pageCurrent = e
      this.getList()
    },
    gotoTopic(method, id="") {
      this.$router.push({ path: '/admin/questionnaire/topic',  query: { method, id } })
    }
  },
  filters: {
    totalScore(typeSummary){
      let acount = 0;
      typeSummary.forEach(ele => {
        acount += ele.num*ele.score;
      })
      return acount;
    }
  }
}
</script>

<style lang="scss" scoped>
 
  .method{
    padding: 0 30px;
    .el-col{
      cursor: pointer;
      text-align: center;
      .el-icon{
        font-size: 40px;
        color: #409EFF;
      }
      p{
        padding: 0 18%;
        color: #909399;
        font-size: 13px
      };
    }
    .el-col:hover{
      background: rgba(198, 226, 255, .4);
      border-radius: 4px;
    }
  }
  .btnDv {
    width: 100%;
    text-align: center;
    margin-top: 20px;
  }
  .home {
    width: 98%;
    margin: 20px auto;
  }
  .addBtn {
    margin: 10px 0;
  }
  .header {
    margin: 0px 0 20px;
  }
  header{
    display: flex;
    justify-content: space-between;
  }
  .filterDv {
    width: 600px;
    display: flex;
    div {
      display: flex;
      width: 300px;
      flex: 1;
      align-items: center;
      margin-right: 10px;
      font-size: 14px;
    }
  }
  .pagination {
    text-align: center;
    margin-top: 30px;
  }
  .el-form-item {
    margin-right: 0;
    margin-bottom: 0px;
  }
</style>

