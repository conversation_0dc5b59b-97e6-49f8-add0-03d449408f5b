import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import '@/styles/index.scss'; // global css
import singleSpaVue from 'single-spa-vue';
import './set-public-path';
// import * as Sentry from '@sentry/browser';
// import { Vue as VueIntegration } from '@sentry/integrations';

// Sentry.init({
//   dsn: 'https://<EMAIL>/5337371',
//   integrations: [ new VueIntegration({ Vue, attachProps: true }) ],
// });
Vue.use(ElementUI);

Vue.config.productionTip = false;

const vueLifecycles = singleSpaVue({
  Vue,
  appOptions: {
    render: h => h(App),
    router,
    store,
  },
});

export const bootstrap = [
  vueLifecycles.bootstrap,
];

export function mount(props) {
  console.log('preventionFunds pros', props);
  return vueLifecycles.mount(props);
}

export const unmount = [
  vueLifecycles.unmount,
];

