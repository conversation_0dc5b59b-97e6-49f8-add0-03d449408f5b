<template>
  <div>
    <el-dialog
      title="导入失败"
      :visible="dialogVisible"
      width="60%"
      :before-close="handleClose">
      <div v-if="typeof tipsMessage === 'object'">表头缺少<span style="color: #F56C6C">{{tipsMessage.join('，')}}</span>请检查后重新导入</div>
      <div v-else><span style="color: #F56C6C;">{{tipsMessage}}</span></div>
      <div style="margin-top: 10px"><span style="color: #F56C6C">*</span>最新模板<el-link type="primary" :href="excelTemplateUrl">点击下载</el-link></div>
      <slot name="tipImage"></slot>
      <div style="margin-top: 10px">提示：如若导入失败请检查您的 Excel <span style="color: #F56C6C">表头是否符合要求</span>，确保表头文字正确，无空格</div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleClose">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: ['dialogVisible', 'tipsMessage', 'href'],
  watch: {
    dialogVisible: function(val) {
      console.log(val)
    }
  },
  computed: {
    excelTemplateUrl() {
      return this.href + '?' + new Date().getTime()
    }
  },
  data() {
    return {

    }
  },
  methods: {
    handleClose() {
      this.$emit('update:dialogVisible', false)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>