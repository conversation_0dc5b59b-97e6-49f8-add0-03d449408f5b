<template>
  <div class="home">
    <el-card shadow="hover">
      <!-- <div class="header">
        <el-button @click="showDialog" size="small" class="addBtn" type="primary">创建题库</el-button>
        <el-button size="small" class="addBtn" @click="drawerVisible = true">试题标签管理</el-button>
      </div> -->

      <div class="filterDv">
        <div>
          题库名称：<el-input v-model="keyWord" size="mini"/>
        </div>
        <div>
          <el-button type="primary" size="small" @click="search">搜索</el-button><el-button size="small" @click="resetSearch">重置</el-button>
        </div>
      </div>

      <el-table :data="tableData">
        <el-table-column align="center" v-for="item in tableField" :prop="item.prop" :label="item.label" :key="item.prop">
          <template scope="scope">
            <div v-if="item.prop == 'updatedAt'">{{ formatTime(scope.row[item.prop]) }}</div>
            <div v-else>{{ scope.row[item.prop] }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="180">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="gotoTopic(scope.row)">试题</el-button>
            <!-- <el-button @click="edit(scope)" class="editBtn" size="mini">编辑</el-button>
            <el-button size="mini" type="danger" @click="del(scope)">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageInfo.pageCurrent"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageInfo.size"
          layout="total, prev, pager, next, sizes"
          :total="pageInfo.total">
        </el-pagination>
      </div>
      <el-dialog :visible.sync="dialogFormVisible" width="600px" title="题库设置" :show-close="true" :before-close="beforeClose">
        <el-form v-if="dialogFormVisible" :model="QBForm" ref="QBform" label-width="120px" :rules="rules">
          <el-form-item label="题库名称" prop="questionBankName">
            <el-input v-model="QBForm.questionBankName" />
          </el-form-item>
        </el-form>
          <div class="btnDv">
            <el-button @click="addQB">确定</el-button>
          </div>
      </el-dialog>

      <el-drawer
        title="标签管理"
        :visible.sync="drawerVisible"
        direction="rtl"
        :before-close="handleClose">
        <LabelManage v-if="drawerVisible" />
      </el-drawer>
    </el-card>   
  </div>
</template>

<script>
import serverApi from '@/api'
import moment from 'moment';
import LabelManage from '@/components/labelManage'
export default {
  props: {
    curTopic: Object,
  },
  components: {
    LabelManage
  },
  computed: {
    formatTime(time) {
      return function(time) {
        const newTime = new Date(time)
        return moment(newTime).format('YYYY-MM-DD HH:mm')
      }
    }
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getFullYear() > (new Date()).getFullYear() + 1
        }
      },
      tableField: [
        {label: '题库名称', prop: 'name'},
        {label: '题数', prop: 'count'},
        {label: '单选', prop: 'singleTopic'},
        {label: '多选', prop: 'multipleTopic'},
        {label: '判断', prop: 'judgeTopic'},
        {label: '更新时间', prop: 'updatedAt'},
        // {label: '创建人', prop: ''},
        // {label: '判断', prop: ''},
        // {label: '填空', prop: ''},
        // {label: '问答', prop: ''},
      ],
      tableData: [],
      dialogFormVisible: false,
      QBForm: {
        questionBankName: '',
        _id: '',
      },
      rules: {
        questionBankName: [{ required: true, message: '请输入题库名称' }]
      },
      pageInfo: {
        pageCurrent: 1,
        size: 10,
        total: 10
      },
      keyWord: '',
      drawerVisible: false
    }
  },
  created() { 
    this.initData()
  },
  methods: {
    async initData() {
      let res = await serverApi.getQB({ ...this.pageInfo , keyWord: this.keyWord });
      this.tableData = res.data.res;
      this.pageInfo = res.data.pageInfo;
      console.log(res)
    },
    showDialog() {
      this.QBForm.questionBankName = ''
      this.QBForm._id = ''
      this.dialogFormVisible = true
    },
    async addQB() {
      this.$refs.QBform.validate(async valid => {
        if (valid) {
          const res = await serverApi.createQB({ ...this.QBForm, ...this.pageInfo });
          this.dialogFormVisible = false
          this.tableData = res.data
          this.pageInfo = res.pageInfo
          this.$message({
            type: 'success',
            message: '成功创建题库'
          })
        }
      })
    },
    edit(scope) {
      this.QBForm.questionBankName = scope.row.name;
      this.QBForm._id = scope.row._id;
      this.dialogFormVisible = true;
    },
    del(scope) {
      this.$confirm('是否删除该题库', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        console.log(scope.row)
        let res = await serverApi.delQB({ _id: scope.row._id })
        console.log(333, res)
        if(res.message == 'success') {
          this.$message({
            type: "success",
            message: "删除成功"
          });
          this.initData()
        } else {
          this.$notify({
            title: '删除失败',
            type: 'warning',
            message: res.message
          })
        }
      }).catch(() => {
        
      })  
    },
    beforeClose(done) {
      done()
    },
    async search() {
      this.initData()
    },
    async resetSearch() {
      this.keyWord = '';
      this.initData()
    },
    handleSizeChange(e) {
      this.pageInfo.size = e
      this.initData()
    },
    handleCurrentChange(e) {
      this.pageInfo.pageCurrent = e
      this.initData()
    },
    gotoTopic(row) {
        this.curTopic.name = row.name;
        this.curTopic.id = row._id;
    }
  },

}
</script>

<style lang="scss" scoped>
  .btnDv {
    width: 100%;
    text-align: center;
    margin-top: 20px;
  }
  .home {
    width: 98%;
    margin: 20px auto;
  }
  .addBtn {
    margin: 10px 0;
  }
  .header {
    margin: 0px 0 20px;
  }
  .filterDv {
    width: 600px;
    display: flex;
    
    div {
      display: flex;
      width: 300px;
      flex: 1;
      align-items: center;
      margin-right: 10px;
      font-size: 14px;
    }
  }
  .pagination {
    text-align: center;
    margin-top: 30px;
  }
  .el-form-item {
    margin-right: 0;
    margin-bottom: 0px;
  }
</style>

