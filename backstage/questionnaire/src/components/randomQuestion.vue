<template>
  <div class="home">
    <el-card shadow="hover">
        <el-form :model="randomTest" ref="randomTest" class="RandomTest">
            <el-form-item v-for="domain in randomTest.domains" :key="domain.key" >
                题库：
                <el-select v-model="domain.questionBankId" placeholder="请选择" size="mini">
                    <el-option :label="item.name" v-for="item in tableData" :value="item._id" :key="item._id"></el-option>
                </el-select> &nbsp; &nbsp;
                题型：
                <el-select v-model="domain.topicType" placeholder="请选择" size="mini">
                    <el-option label="单选题" value="singleTopic"></el-option>
                    <el-option label="多选题" value="multipleTopic"></el-option>
                    <el-option label="判断题" value="judgeTopic"></el-option>
                </el-select> &nbsp; &nbsp;
                数量：
                <el-input type="number" v-model="domain.num" autocomplete="off" size="mini">
                </el-input>&nbsp; / {{maxNum(domain)}} &nbsp; &nbsp;
            <i class="el-icon-delete" @click.prevent="removeDomain(domain)"></i>
            </el-form-item>
            <p v-if="warningMsg" class="warningMsg">提示：{{warningMsg}}</p>
            <el-form-item>
                <el-button type="primary" plain class="addQuestion el-icon-plus" @click="addDomain"> 添加一条规则</el-button>
                <el-button type="primary" plain class="addQuestion el-icon-refresh" @click="submitForm" v-if="randomTest.domains.length"> {{combinationRule.length ? '重新组题' : '保存'}}</el-button>
            </el-form-item>
        </el-form>      
    </el-card>   
  </div>
</template>

<script>
import serverApi from '@/api'
export default {
  props: {
    selectedQuestions: Array,
    combinationRule: Array,
  },
  computed: {
    maxNum(domains){
        return function(domains) {
            if(domains.questionBankId){
              const item = this.tableData.filter(ele => ele._id == domains.questionBankId);
              if(domains.topicType){
                  return +(item[0][domains.topicType]);
              }else{
                  return +(item[0].count);
              }
          }
        }
    }
  },
  data() {
    return {
      randomTest: { // 随机组题
        domains: [],
      },
      tableData: [],
      warningMsg: '', // 校验提示
    }
  },
  created() { 
    this.initData();
    if(this.combinationRule.length){
        const domains = JSON.parse(JSON.stringify(this.combinationRule)).map(ele => {
            ele.key = ele._id;
            delete ele._id;
            return ele;
        });
        this.randomTest = { domains };
    } 
  },
  methods: {
    async getTopics(questionBankID, topicType){
        return serverApi.getTopicsRandom({
            questionBankID,
            topicType,
        });
      },
    async validate(domainArr){ // 保存前的校验
        const warningMsg = [];
        domainArr.forEach((ele,i) => {
            let n = 0;
            let str = '';
            if(!ele.questionBankId){
                str += (`第${i+1}条规则请选择题库`);
                n++;
            }
            if(!ele.topicType){
                str += n > 0 ? (`和题型`) : (`第${i+1}条规则请选择题型`);
                n++;
            }
            if(!ele.num){
                str += n > 0 ? (`并填写数量`) : (`第${i+1}条规则请填写数量`);
            }
            if(str) warningMsg.push(str);
        });
        this.warningMsg = warningMsg.join('; ') + '。';
        return !Boolean(warningMsg.length);
    },
    async submitForm() {
        const domainArr = this.randomTest.domains;
        if(domainArr.length==0) return;
        const flag = await this.validate(domainArr);
        if(!flag) return; // 校验未通过
        const tempArr = [];
        for(let domain of domainArr){
            const topicType = domain.topicType == "singleTopic" ? 1 : (domain.topicType == "multipleTopic" ? 2 : 3);
            const topics = await this.getTopics(domain.questionBankId, topicType);
            if(topics.data && topics.data.length){
                if(topics.data.length <= domain.num){
                    tempArr.push(...topics.data);
                }else{
                    const newArr = topics.data.sort(() => Math.random() - 0.5); // 乱序
                    const resultArr = newArr.slice(0, domain.num);
                    tempArr.push(...resultArr);
                }
            }
        };
        this.selectedQuestions.length = 0; // 清空原有的考题数组
        this.combinationRule.length = 0;
        this.selectedQuestions.push(...tempArr);
        this.combinationRule.push(...domainArr);
        this.warningMsg = '';
    },
    removeDomain(item) {
        var index = this.randomTest.domains.indexOf(item)
        if (index !== -1) {
            this.randomTest.domains.splice(index, 1)
        }
    },
    addDomain() {
        this.randomTest.domains.push({
            key: Date.now()
        });
    },
    // 获取所有题库
    async initData() {
      let res = await serverApi.getQB({ 
        pageCurrent: 1,
        size: 100,
        total: 10, 
        keyWord: '' 
      });
      this.tableData = res.data.res;
      console.log('题库：', this.tableData);
    },
   
  },
  
}
</script>

<style lang="scss" scoped>
    .RandomTest{
        .warningMsg{
            color: #F56C6C;
            font-size: 13px;
        }
       .el-input{
           display: inline-block;
           width: auto;
       } 
       .el-icon-delete{
            color: #409EFF;
            cursor: pointer;
            margin: 0 15px;
       }
       .addQuestion{
            width: 48%;
            margin: 20px 1%;
        }
    }
  .home {
    width: 98%;
    margin: 20px auto;
  }
  .addBtn {
    margin: 10px 0;
  }
  .header {
    margin: 0px 0 20px;
  }
  .el-form-item {
    margin-right: 0;
    margin-bottom: 0px;
  }
</style>

