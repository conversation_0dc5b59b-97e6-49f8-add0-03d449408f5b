<template>
  <div>
    <el-dialog title="预览" :visible="dialogTableVisible" :before-close="beforeClose" :destroy-on-close="true" top="5vh">
      <embed v-if="supportPreview" id="embedDv" width="100%" height="600px" :src="fileUrl" />
      <div v-else><span class="text">{{this.previewFile.name}}</span> 暂不支持预览</div>
      <div class="downloadBtn">
        <el-button @click="download()">下载</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: ['dialogTableVisible', 'previewFile'],
  data() {
    return {
      fileUrl: '',
      supportPreview: true
    }
  },
  watch: {
    dialogTableVisible: function() {
      const Regex = /(mp4|png|jpg|pdf|gif|bmp|jpeg|avi)/gi
      if(Regex.test(this.previewFile.fileType)) {
        this.supportPreview = true
        const fileUrl = this.previewFile.staticSrc
        const embed = document.querySelector('#embedDv')
        if(embed) {
          this.fileUrl = ''
          embed.src = fileUrl
        } else {
          this.fileUrl = fileUrl
        }
      } else {
        this.fileUrl = ''
        this.supportPreview = false
      }
    }
  },
  methods: {
    beforeClose() {
      this.$emit('update:dialogTableVisible', false)
    },
    download() {
      const { fileType, staticSrc, name} = this.previewFile
      const url = this.previewFile.staticSrc
      this.fileLinkToStreamDownload(url, name, fileType)
    },
    fileLinkToStreamDownload (url, fileName, type) {
      let xhr = new XMLHttpRequest()
      xhr.open('get', url, true)
      xhr.setRequestHeader('Content-Type', type)
      xhr.responseType = 'blob'
      xhr.onload = (res) => {
        if (res.currentTarget.status === 200) {
          // 接受二进制文件流
          var blob = res.currentTarget.response
          this.downloadExportFile(blob, fileName, type)
        }
      }
      xhr.send()
    },
    downloadExportFile (blob, tagFileName) {
      let downloadElement = document.createElement('a')
      let href = blob
      if (typeof blob === 'string') {
        downloadElement.target = '_blank'
      } else {
        href = window.URL.createObjectURL(blob) // 创建下载的链接
      }
      downloadElement.href = href
      downloadElement.download = tagFileName // 下载后文件名
      document.body.appendChild(downloadElement)
      downloadElement.click() // 点击下载
      document.body.removeChild(downloadElement) // 下载完成移除元素
      if (typeof blob !== 'string') {
        window.URL.revokeObjectURL(href) // 释放掉blob对象
      }
    },
  },
}
</script>

<style lang="scss" scoped>
  .downloadBtn {
    text-align: center;
    margin: 20px 0 0;
  }
  .text {
    margin: 0 10px 0 0;
    color: #99a9bf;
    text-decoration: underline;
  }
</style>