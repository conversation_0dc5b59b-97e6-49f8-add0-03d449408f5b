<template>
  <el-drawer
    :direction="rtl"
    :visible.sync="drawer"
    :title="isCreated ? '新增题目' : '编辑题目'"
    :with-header="false"
    @close="closeDrawer"
    size="42%"
    :wrapperClosable="false"
  >
    <div class="drawer__content">
      <el-form :model="topicForm" ref="topicForm" label-width="90px" :rules="rules">
        <el-radio-group :disabled="!isCreated" v-model="topicForm.topicType" @change="topicTypeChange" style="margin-bottom: 20px">
          <el-radio :label="1">单选题</el-radio>
          <el-radio :label="2">多选题</el-radio>
          <el-radio :label="3">判断题</el-radio>
          <el-radio :label="4">填空题</el-radio>
          <el-radio :label="5">问答题</el-radio>
        </el-radio-group>
        <el-form-item label="题干" prop="steam">
          <el-input class="stretchNone" v-model="topicForm.steam" type="textarea" :maxlength="500" :show-word-limit="true" :rows="4"/>
        </el-form-item>
        <el-form-item label="图片">
          <el-upload
            action="#"
            list-type="picture-card"
            :accept="imageTypeSettings.join(',')"
            :file-list="topicForm.steamPic"
            :on-change="(file,fileList) => { return fileChange(file, fileList, 'steamPic')}"
            :auto-upload="false">
              <i slot="default" class="el-icon-plus"></i>
              <div slot="file" slot-scope="{file}">
                <img class="el-upload-list__item-thumbnail" :src="file.staticSrc ? file.staticSrc : file.url" alt="" >
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)" >
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file, 'steamPic')" >
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="选项" v-if="topicForm.topicType === 1 || topicForm.topicType === 2" prop="options">
          <div class="option">

            <div class="optionItem" v-for="(item, index) in topicForm.options" :key="item.optionItem">
                {{ A_J[index] }}：
                <el-form ref="optionsForm" :model="topicForm.options[index]" :rules="rules" :show-message="false" @submit.native.prevent>
                  <el-form-item :prop="'optionText'" >
                    <el-input size="mini" v-model="item.optionText" style="width: 100%" />
                  </el-form-item>
                </el-form>
                <i v-if="topicForm.options.length > 2" class="el-icon-close" @click="delOption(index)" style="margin-left: 10px"></i>
                <!-- <el-link type="primary" :underline="false" style="margin-left: 10px">添加图片</el-link> -->
            </div>

            <div class="flexDv">
              <el-link type="primary" icon="el-icon-circle-plus-outline" :underline="false" @click="addOption">添加选项</el-link>
              <span class="tipsText">最少2个选项，最多10个选项</span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="正确答案" prop="answer">
          <el-radio-group v-model="topicForm.answer" @change="radioChange" v-if="topicForm.topicType == 1">
            <el-radio :label="index" v-for="(item, index) in topicForm.options" :key="index">{{ A_J[index] }}</el-radio>
          </el-radio-group>
          <el-checkbox-group v-model="topicForm.answer" v-if="topicForm.topicType == 2">
            <el-checkbox :label="index" v-for="(item, index) in topicForm.options" :key="index">{{ A_J[index] }}</el-checkbox>
          </el-checkbox-group>
          <el-radio-group v-model="topicForm.answer" v-if="topicForm.topicType == 3">
            <el-radio :label="0">正确</el-radio>
            <el-radio :label="1">错误</el-radio>
          </el-radio-group>
          <el-input v-if="topicForm.topicType == 4" v-model="topicForm.answer"></el-input>
          <el-input v-if="topicForm.topicType == 5" v-model="topicForm.answer" type="textarea"></el-input>
        </el-form-item>
        <el-form-item label="答案解析">
          <el-input v-model="topicForm.answerAnalysis" type="textarea" class="stretchNone" :maxlength="500" :show-word-limit="true" :rows="4"/>
          <el-upload
            style="margin-top: 20px" action="#" list-type="picture-card" :auto-upload="false"
            :accept="imageTypeSettings.join(',')"
            :file-list="topicForm.answerAnalysisPic"
            :on-change="(file,fileList) => fileChange(file, fileList, 'answerAnalysisPic')">
              <i slot="default" class="el-icon-plus"></i>
              <div slot="file" slot-scope="{file}">
                <img class="el-upload-list__item-thumbnail" :src="file.staticSrc ? file.staticSrc : file.url" alt="" >
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)" >
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file, 'answerAnalysisPic')" >
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="类别/标签">
          <el-select multiple v-model="topicForm.labels" filterable placeholder="请选择" @change="labelChange">
            <el-option
              v-for="item in labelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-link style="margin-left: 10px" type="primary" :underline="false" icon="el-icon-plus" @click="addLabel">新增</el-link>
        </el-form-item>
      </el-form>

      <div class="drawer__footer">
        <el-button @click="closeDrawer">取 消</el-button>
        <el-button
          type="primary"
          @click="submit"
          :loading="loading"
          >{{ loading ? "提交中 ..." : "确 定" }}</el-button
        >
      </div>
    </div>
  </el-drawer>
</template>

<script>
import serverApi from '../../api'
import settings from "@root/publicMethods/settings"
export default {
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: {}
    },
    selectedQuestions: Array,
    editIndex: Number,
  },
  async created() {
    const res = await serverApi.findLabel()
    if(res.status == 200) {
      this.labelOptions = res.data.doc;
    }
  },
  watch: {
    data: {
      handler: function(newVal, val) {
        console.log('试题详情: ', newVal, val)
        if(newVal) {
          const arr = Object.keys(newVal);
          if(arr.length == 0) {
            this.isCreated = true
          }
          arr.forEach(key => {
            if (key == 'answer') {
              if(newVal.topicType == 1 || newVal.topicType == 3) {
                this.topicForm[key] = newVal[key][0]
              } else {
                this.topicForm[key] = newVal[key]
              }
            } else {
              this.topicForm[key] = JSON.parse(JSON.stringify(newVal[key]))
            }
          })
        }
      },
      immediate: true
    }
  },
  data() {
    const optionsValid = (rule, value, callback) => {
      console.log(1111, rule, value)
      const isEmpty = value.filter(item => {
        return item.optionText == ''
      })
      if (isEmpty.length === 0) {
        callback()
      } else {
        callback('选项内容不能为空')
      }
    };
    return {
      isCreated: false, // 是否是 新建题目
      topicForm: {
        topicType: 1,
        steam: '',
        steamPic: [],
        answer: [],
        answerAnalysis: '',
        answerAnalysisPic: [],
        options: [
          {
            optionText: '',
            optionPic: ''
          },
          {
            optionText: '',
            optionPic: ''
          },
          {
            optionText: '',
            optionPic: ''
          },
        ],
        labels: [],
      }, // 初始化 题目数据
      delFileList: [], // 编辑题目时，删除已上传的图片时保存进这里
      rules: {
        steam: [{ required: true, message: "请输入题干" }],
        answer: [{ required: false, message: "请输入答案" }],
        optionText:  [{required: false, message: '选项不能为空', trigger: 'blur'}],
      },
      labelWidth: '70px',
      A_J: [ 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J' ],
      disabled: false,
      labelOptions: [], // 标签
      imageTypeSettings: settings.imageType, //允许上传的文件类型
    };
  },
  methods: {
    // 关闭抽屉
    closeDrawer() {
      this.$emit('update:drawer', false)
    },
    // 试题类型 切换 时 初始化下表单数据
    topicTypeChange(e) {
      console.log(e)
      this.topicForm.answer = []
      this.topicForm.options = [
        {
          optionText: '',
          optionPic: ''
        },
        {
          optionText: '',
          optionPic: ''
        },
        {
          optionText: '',
          optionPic: ''
        },
      ];
      this.topicForm.answerAnalysis = ''
      this.topicForm.topicType = parseInt(e)
    },
    // 添加 选项
    addOption() {
      const index = this.topicForm.options.length;
      index < 10 && this.topicForm.options.push({
        // optionItem: this.A_J[index],
        optionText: '',
        optionPic: '',
      })
    },
    // 删除 选项
    delOption(index) {
      this.topicForm.options.splice(index, 1)
    },
    // 处理 上传图片
    fileChange(file, fileList, type) {
      this.topicForm[type] = fileList
      console.log(2222, this.topicForm[type])
    },
    // 处理 删除图片
    handleRemove(file, type) {
      const index = this.topicForm[type].findIndex(item => {
        return item.uid == file.uid
      })
      if(index >= 0) {
        this.topicForm[type].splice(index, 1)
      }
      if(file.staticSrc) {
        this.delFileList.push(file)
      }
    },
    handlePictureCardPreview(file) {
    },
    handleDownload(file) {
      
    },
    // 处理 答案 单选
    radioChange(e) {
      console.log(this.topicForm.answer)
    },
    // 提交 试题
    async submit() {
      const _this = this;
      let isTrue = true
      this.$refs.topicForm.validate(valid => {
        if(!valid) {
          isTrue = false
        }
      })
      if(this.topicForm.topicType == 1 || this.topicForm.topicType == 2) {
        const dom =  this.$refs.optionsForm;
        dom.forEach(item => {
          item.validate(valid => {
            if(!valid) {
              isTrue = false
            }
          })
        })
      }

      if(isTrue) {
        const newData = new FormData()
        // const questionBankID = this.$route.query.id || '';
        if(this.delFileList.length > 0) {
          newData.append('delFileList', JSON.stringify(this.delFileList))
        }
        // if(!this.topicForm.questionBankID) {
        //   newData.append('questionBankID', questionBankID);
        // } 
        Object.keys(this.topicForm).forEach(key => {
          if(key == 'steamPic' || key == 'answerAnalysisPic') {
            this.topicForm[key].forEach((item, i) => {
              newData.append(key + '_' + i, (item.raw != undefined) ? item.raw : JSON.stringify(item))
            })
          } else if(key == 'answer' || key == 'labels' || key == 'options') {
            newData.append(key, JSON.stringify(this.topicForm[key]))
          } else {
            newData.append(key, this.topicForm[key])
          }
        })
        let res;
        if(this.isCreated) {
          res = await serverApi.createTopic(newData);
        } else {
          res = await serverApi.updateTopic(newData);
        }
        if(res.status == 200) {
          this.$message({
            type: 'success',
            message: this.isCreated ? '添加成功' : '编辑成功'
          });
          if(this.isCreated){
            this.selectedQuestions.push(res.data);
          }else{
            this.selectedQuestions.splice(this.editIndex, 1, res.data);
          }
          this.$emit('updateSuccess')
          this.$emit('update:drawer', false)
        } 
      }
    },
    // 新增标签
    async addLabel() {
      this.$prompt('请输入类别/标签', '新增类别/标签', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }).then(async ({ value }) => {
          const res = await serverApi.addLabel({ name: value });
          if (res.status == 200) {
            console.log(res.data)
            this.labelOptions = res.data.doc;
            this.$message({
              type: 'success',
              message: '添加成功'
            })
          }
        }).catch(() => {
        });
    },
    labelChange(e) {
      const newData = []
      const _this = this;
      e.forEach(item => {
        const data =  _this.labelOptions.filter(item2 => {
          return item2.value == item
        })
        newData.push(data[0].value)
      })
      this.topicForm.labels = newData
    }
  },
};
</script>

<style lang="scss" scoped>
  ::v-deep .el-drawer {
    overflow: scroll;
  }
  .drawer__content {
    padding: 0 20px 20px;
    .drawer__footer {
      text-align: center;
    }
  }
  ::v-deep .el-upload--picture-card {
    font-size: 28px;
    color: #8c939d;
    width: 90px;
    height: 90px;
    line-height: 90px;
    text-align: center;
    border: 1px dashed #d9d9d9;
  }
  ::v-deep .el-upload-list--picture-card .el-upload-list__item {
    width: 90px;
    height: 90px;
    line-height: 90px;
  }
  .tipsText {
    font-size: 8px;
    margin-left: 10px;  
    color: #909399;
  }
  .flexDv {
    display: flex;
    align-items: center;
  }
  .optionItem {
    display: flex;
    align-items: center;
  }
  ::v-deep.stretchNone{
    .el-textarea__inner{
      resize: none;
    }
    .el-input__count {
      bottom: 0px;
      background: transparent;
    }
  }
   ::v-deep .el-textarea__inner::-webkit-scrollbar {
    width: 5px; // 横向滚动条
    height: 6px; // 纵向滚动条 必写
  }

  ::v-deep .el-textarea__inner::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 15px;
  }
</style>