<template>
  <div class="labelManageHome">
    <div class="searchDv">
      <div>
        名称：<el-input size="mini" v-model="keyWord" style="width: 120px" /> 
        <el-button size="mini" type="primary" @click="search">搜索</el-button>
        <el-button size="mini" type="warning" @click="resetSearch">重置</el-button>
      </div>
      <div>
        <el-button size="mini" @click="add">添加标签</el-button>
      </div>
    </div>
    <el-table :data="tableData">
      <el-table-column label="标签名称" prop="label"></el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template scope="scope">
          <el-link :underline="false" type="primary" @click="edit(scope.row)">编辑</el-link>
          <!-- <el-link :underline="false" type="primary" @click="del(scope.row)">删除</el-link> -->
          <el-popover
              style="margin-left: 10px"
              placement="top"
              width="160"
              :ref="`popover-${scope.$index}`">
              <p>这是一段内容这是一段内容确定删除吗？</p>
              <div style="text-align: right; margin: 0">
                <el-button size="mini" type="text" @click="scope._self.$refs[`popover-${scope.$index}`].doClose()">取消</el-button>
                <el-button type="primary" size="mini" @click="del(scope.row, scope.$index)">确定</el-button>
              </div>
              <el-link slot="reference" :underline="false" type="primary">删除</el-link>
            </el-popover>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
        <el-pagination
          size="mini"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageInfo.pageCurrent"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageInfo.size"
          layout="total, prev, pager, next, sizes"
          :total="pageInfo.total">
        </el-pagination>
      </div>
  </div>
</template>

<script>
import serverApi from '../../api'
export default {
  async created() {
    await this.initData()
  },
  data() {
    return {
      pageInfo: {
        pageCurrent: 1,
        size: 10,
        total: 0
      },
      tableData: [],
      keyWord: '',
      selectLabel: {
        value: '',
        label: '',
      }
    }
  },
  methods: {
    async initData() {
      const res = await serverApi.findLabel({ ...this.pageInfo, keyWord: this.keyWord });
      if (res.status == 200) {
        this.tableData = res.data.doc;
        this.pageInfo = res.data.pageInfo;
      }
    },
    search() {
      this.initData()
    },
    resetSearch() {
      this.keyWord = '';
      this.pageInfo = {
        pageCurrent: 1,
        size: 10,
        total: 0
      }
      this.initData()
    },
    async del(row, index) {
      const dom = this.$refs['popover-' + index]
      const res = await serverApi.delLabel(row)
      console.log(111, res)
      if(res.status == 200) {
        this.$message({
          type: 'success',
          message: '删除成功', 
        })
        dom.doClose()
        this.initData()
      }
    },
    async edit(row) {
      this.selectLabel = row;
      this.$prompt('请修改标签', '编辑标签', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: this.selectLabel.label,
      }).then(async ({ value }) => {
        if (value) {
          this.selectLabel.label = value
          const res = await serverApi.updateLabel(this.selectLabel)
          if (res.status == 200) {
            this.$message({
              type: 'success',
              message: '修改成功'
            })
          }
        }
      }).catch(() => {
        this.initData()
      });
    },
    async add(row) {
      this.selectLabel = {
        value: '',
        label: '',
      };
      this.$prompt('请输入标签', '新增标签', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: this.selectLabel.label,
      }).then(async ({ value }) => {
        if (value) {
          const res = await serverApi.addLabel({ name: value })
          if (res.status == 200) {
            this.$message({
              type: 'success',
              message: '添加成功'
            })
            this.initData()
          }
        }
      }).catch(() => {
    
      });
    }
  }
}
</script>

<style lang="scss" scoped>
  .labelManageHome {
    padding: 20px;
  }
  .searchDv {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
  }
  .pagination {
    text-align: center;
    margin-top: 30px;
  }
</style>