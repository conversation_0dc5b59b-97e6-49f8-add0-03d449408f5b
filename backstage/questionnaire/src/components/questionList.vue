<template>
  <div class="home">
    <el-card shadow="hover">
      <!-- <div class="header">
        <div class="title">题库名称：{{curTopic.name}}</div>
        <div>
          <el-button @click="jsonToExcel" size="small" class="addBtn" type="primary">全部导出</el-button>
          <el-button @click="importTopic" size="small" class="addBtn">批量导入试题</el-button>
          <el-button size="small" class="addBtn" @click="addTopic">创建试题</el-button>
        </div>
      </div> -->

      <div class="filterDv">
        <div style="width: 600px">
          题干内容：<el-input v-model="query.keyWords" size="mini"/>
          题型：<el-select v-model="query.topicType" placeholder="请选择" size="mini">
                <el-option
                  v-for="item in topicTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
          标签：<el-select v-model="query.label" size="mini">
            <el-option
              v-for="item in labelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div>
          <el-button type="primary" size="small" @click="search">搜索</el-button>
          <el-button size="small" @click="resetSearch">重置</el-button>
        </div>
        <!-- <transition name="fade">
          <el-button
            type="danger"
            size="mini"
            v-show="isShowMultiDel"
            @click="multiDel"
            >批量删除</el-button
          >
        </transition> -->
      </div>

      <el-table :data="tableData" @selection-change="handleSelectionChange">
        <el-table-column
          type="selection"
          width="55">
        </el-table-column>
        <el-table-column v-for="item in tableField" :prop="item.prop" :label="item.label" :key="item.prop" align="center">
          <template scope="scope">
            <div v-if="item.prop == 'updatedAt'">{{ formatTime(scope.row[item.prop]) }}</div>
            <div v-else-if="item.prop == 'topicType'">{{ formatType(scope.row[item.prop]) }}</div>
            <div v-else-if="item.prop == 'labels'">{{ formatLabel(scope.row[item.prop]) }}</div>
            <div v-else>{{ scope.row[item.prop] }}</div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="操作" align="center" width="180">
          <template slot-scope="scope">
            <el-button @click="edit(scope.row)" class="editBtn" size="mini">编辑</el-button>
            <el-popover
              style="margin-left: 10px"
              placement="top"
              width="160"
              :ref="`popover-${scope.$index}`">
              <p>这是一段内容这是一段内容确定删除吗？</p>
              <div style="text-align: right; margin: 0">
                <el-button size="mini" type="text" @click="scope._self.$refs[`popover-${scope.$index}`].doClose()">取消</el-button>
                <el-button type="primary" size="mini" @click="del(scope.row, scope.$index,scope)">确定</el-button>
              </div>
              <el-button slot="reference"  size="mini" type="danger">删除</el-button>
            </el-popover>
          </template>
        </el-table-column> -->
      </el-table>

      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageInfo.pageCurrent"
          :page-sizes="[5, 10, 20, 30, 50]"
          :page-size="pageInfo.size"
          layout="total, prev, pager, next, sizes"
          :total="pageInfo.total">
        </el-pagination>
      </div>
      <div class="footer">
        <span>已选择{{multipleSelection.length}}道题</span>
        <el-button size="mini" @click="$emit('close')">取 消</el-button>
        <el-button size="mini" type="primary" @click="subSelected">确 定</el-button>
      </div>

      <!-- <TopicForm :data="selectTopic" v-if="drawer" :drawer.sync="drawer" @updateSuccess="updateSuccess" /> -->
    </el-card>   
  </div>
</template>

<script>
import serverApi from '../api'
import moment from 'moment';
// import TopicForm from '../components/topicForm';
import XLSX from 'xlsx'
export default {
  props: {
    curTopic: Object,
    selectedQuestions: Array
  },
  // components: {
  //   TopicForm
  // },
  computed: {
    // 格式化时间
    formatTime(time) {
      return function(time) {
        const newTime = new Date(time)
        return moment(newTime).format('YYYY-MM-DD HH:mm')
      }
    },
    // 格式化题型
    formatType(type) {
      return function(type) {
        let typeName = '-'
        switch(type) {
          case 1:
            typeName = '单选题';
            break;
          case 2:
            typeName = '多选题';
            break;
          case 3:
            typeName = '判断题';
            break;
          case 4:
            typeName = '填空题';
            break;
          case 5:
            typeName = '问答题';
            break;
        }
        return typeName;
      }
    },
    // 格式化标签
    formatLabel(label) {
      return function(label) {
        const _this = this;
        if (label && label.length > 0) {
          const label_Names = []
          label.forEach(item => {
            const res = _this.labelOptions.filter(item2 => {
              return item2.value == item
            })
            if(res.length > 0) label_Names.push(res[0].label)
          })
          return label_Names.join('|')
        } else {
          return '-'
        }
      }
    }
  },
  data() {
    return {
      drawer: false, // 控制抽屉开关
      tableField: [
        {label: '题干', prop: 'steam'},
        {label: '题型', prop: 'topicType'},
        {label: '标签', prop: 'labels'},
        {label: '更新时间', prop: 'updatedAt'},
      ], // 表格字段
      tableData: [], // 试题表格数据
      pageInfo: {
        pageCurrent: 1,
        size: 10,
        total: 0
      }, // 分页条件
      query: {
        keyWords: null,
        topicType: null,
        label: null
      }, // 筛选条件
      selectTopic: {}, // 编辑时选择的题目
      topicTypeOptions: [{
        value: null,
        label: '全部'
      },{
        value: 1,
        label: '单选题'
      }, {
        value: 2,
        label: '多选题'
      }, {
        value: 3,
        label: '判断题'
      }], // 题型的类型
      labelOptions: [], // 标签
      multipleSelection: [], // 表格多选的结果
      isShowMultiDel: false, // 是否显示 多选删除 的按钮
      A_J: [ 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J' ],
    }
  },
  created() { 
    this.initData();
  },
  watch: {
    'curTopic.id': { // 切换题库,这个在QuestionBank组件中触发
      deep:true, 
      handler:function(topicId){
        if(topicId){
          this.initData();
        }
      }
    },
    // 监听 表格 的多选框
    multipleSelection: function (val) {
      val.length > 0
        ? (this.isShowMultiDel = true)
        : (this.isShowMultiDel = false);
    },
  },
  methods: {
    // 点击提交
    subSelected(){
      const newArr = this.multipleSelection.map(ele => {
        ele.questionBank = this.curTopic.name;
        return ele;
      })
      this.selectedQuestions.push(...newArr);
    },
    // 获取数据
    async initData() {
      const questionBankID = this.curTopic.id;
      let res = await serverApi.getTopic({ ...this.pageInfo , query: this.query, questionBankID });
      console.log(res)
      this.tableData = res.data.doc;
      this.pageInfo = res.data.pageInfo;
      this.labelOptions = res.data.topicLable.doc;
    },
    // 点击 创建试题 后，初始化参数传入组件
    addTopic() {
      this.selectTopic = {}
      this.drawer = true
    },
    // 添加试题成功后，请求最新的数据
    async updateSuccess() {
      await this.initData()
    },
    // 处理编辑
    edit(row) {
      this.selectTopic = row
      this.drawer = true
    },
    // 处理删除
    async del(row, index, scope) {
      console.log(scope)
      const dom = this.$refs['popover-' + index]
      const res = await serverApi.delTopic({ids: [ row._id ]})
      if (res.status == 200) {
        this.initData()
      }
      dom.doClose()
    },
    // 搜索
    async search() {
      this.initData()
    },
    // 重置搜索
    async resetSearch() {
      this.query = {
        keyWords: null,
        topicType: null,
        label: null
      };
      this.initData()
    },
    // 处理 分页 条数变化
    handleSizeChange(e) {
      this.pageInfo.size = e
      this.initData()
    },
    // 处理 分页 页码变化
    handleCurrentChange(e) {
      this.pageInfo.pageCurrent = e
      this.initData()
    },
    // 跳转 导入试题
    importTopic() {
      this.$router.push({ path: '/admin/questionBank/importTopic',  query: { id: this.curTopic.id } })
    },
    // 处理多选
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 批量删除
    async multiDel() {
      this.$confirm('是否删除所选题目？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const ids = this.multipleSelection.map(item => {
          return item._id
        })
        const res = await serverApi.delTopic({ ids })
        if (res.status == 200) {
          this.initData()
        }
      }).catch(() => {
      });
    },
    // 导出 试题
    async jsonToExcel() {
      const tableField = {
        topicType: '题型（必填）',
        steam: '题干（必填）',
        answer: '答案（必填）',
        answerAnalysis: '答案解析（选填）',
        labels: '标签（仅支持题库中已存在）',
        options: '选项',
      }
      const _this = this;
      const newJson = []
      const data = this.tableData.map(item => {
        const newData = {}
        Object.keys(tableField).forEach(key => {
          if(key == 'answer') {
            const answerArr = item[key].map(item2 => {
              return _this.A_J[item2]
            })
            newData[tableField[key]] = answerArr.join('|')
          } else if (key == 'topicType') {
            let topicTypeText = '';
            switch(item[key]) {
              case 1:
                topicTypeText = '单选题'
                break;
              case 2:
                topicTypeText = '多选题'
                break;
              case 2:
                topicTypeText = '判断题'
                break;
            }
            newData[tableField[key]] = topicTypeText
          } else if (key == 'options') {
            console.log(item[key])
            item[key].forEach((item2, i) => {
              newData[tableField[key] + _this.A_J[i]] = item[key][i].optionText
            })
          } else {
            newData[tableField[key]] = item[key]
          }
        })
        newJson.push(newData)
      })
      const ws = XLSX.utils.json_to_sheet(newJson, { origin: 1 })
      ws['!merges'] = [{s: {r: 0, c: 0}, e: {r: 0, c: 15}}]
      ws.A1 = { t: 's', v: "填写注意事项：从其他Excel或Word复制试题时请使用选择性粘贴，Word：右键——选择性粘贴——文本，Excel：右键——选择性粘贴——只勾选“值”。1.第1行和第2行不可修改或删除！2.单选题/多选题：最多可添加10个选项（选项A,选项B……选项I,选项J）；多选题有多个答案时需用“|”分隔（例如“A|B|C”）。3.判断题：只能设置两个选项，正确和错误。4.填空题：题干填空处使用[]，中间不能有空格，答案列按填空顺序填写正确答案，不同填空用“、”分隔，填空处用[]，同一空的多个答案之间用“|”分隔开,。5.问答题：答案为空。6.标签：非必填项，仅在题库导入时有效，可根据需要填写，多个标签则用“|”分隔(例如“美工|PS技巧”)。7.分数：仅在导入试卷/考试时有效，此时为必填项，支持小数点后一位。8.图片：试题中包括图片需导入后再进行编辑上传。9.若填写后单元格显示红色底色表示填写有误，请参照样例检查填写内容。" }
      var wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "People");
      XLSX.writeFile(wb, '试题.xlsx')
    }
  },

}
</script>

<style lang="scss" scoped>
  .footer{
    text-align: right;
    span{
      float: left;
    }
  }
  .btnDv {
    width: 100%;
    text-align: center;
    margin-top: 20px;
  }
  .home {
    width: 98%;
    margin: 20px auto;
    height: 80vh;
    overflow: scroll;
  }
  .addBtn {
    margin: 10px 0;
  }
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      display: flex;
      align-items: center;
      font-weight: bold;
    }
  }
  .filterDv {
    width: 100%;
    display: flex;
    margin-top: 10px;
    
    div {
      display: flex;
      width: 300px;
      flex: 1;
      align-items: center;
      margin-right: 10px;
      font-size: 14px;
    }
  }
  .pagination {
    text-align: center;
    margin-top: 30px;
  }
  .el-form-item {
    margin-right: 0;
    margin-bottom: 0px;
  }

</style>

