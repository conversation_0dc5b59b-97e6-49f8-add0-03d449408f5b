<template>
  <div class="questionSort">
    <el-card shadow="hover">
        <ul>
            <li v-for="(item, index) in list" :key="index">
                <span>{{index+1}}</span>
                <span class="topicType">【{{item.topicType | topicType}}】</span>
                <span class="steam">{{item.steam}}</span>
                <span class="sort"> 
                    <el-button type="primary" size="mini" class="el-icon-upload2" @click="up(index)" plain></el-button>
                    <el-button type="primary" size="mini" class="el-icon-download" @click="down(index)" plain></el-button>
                </span>
            </li>
        </ul>
        <footer>
            <el-button @click="selectedQuestions.push(...[])">取消</el-button>
            <el-button type="primary" @click="save">保存</el-button>
        </footer>
    </el-card>   
  </div>
</template>

<script>
export default {
  props: {
    selectedQuestions: Array,
  },
 
  data() {
    return {
        list: [],
    }
  },
  created() { 
      this.list = JSON.parse(JSON.stringify(this.selectedQuestions));
  },
  methods: {
        up(index){
            if(index == 0) return;
            const temp = this.list.splice(index, 1);
            temp.length && this.list.splice(index-1, 0, temp[0]);
        },
        down(index){
            if(index == (this.list.length - 1)) return;
            const temp = this.list.splice(index, 1);
            temp.length && this.list.splice(index+1, 0, temp[0]);
        },
        save(){
            this.selectedQuestions.length = 0;
            this.selectedQuestions.push(...this.list);
        },
        setSort(){
            const el = this.$refs.dragTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
            this.sortable = Sortable.create(el,{
                ghostClass:'sortable-ghost',
                setData:function(dataTransfer){
                dataTransfer.setData('Text', '')
                },
                onEnd: evt => {
                const targetRow = this.list.splice(evt.oldIndex, 1)[0];
                this.list.splice(evt.newIndex, 0, targetRow);
                }
            })
        },
  },
  filters: {
      topicType(n){
        switch(+n){
            case 1: return '单选题'; break;
            case 2: return '多选题'; break;
            case 3: return '判断题'; break;
            case 4: return '填空题'; break;
            case 5: return '问答题'; break;
        }
      }
  }
}
</script>

<style lang="scss" scoped>
    .questionSort{
        li{
            list-style: none;
            border-bottom: 1px solid #eee;
            padding: 12px 0;
        }
        li:hover{
            background-color:rgba(244, 244, 245, 0.7);
        }
        .topicType{
            margin-left: 10px;
            margin-right: 8px;
        }
        .steam{
            display: inline-block;
            width: 80%;
            overflow: hidden;
            text-overflow:ellipsis;
            white-space: nowrap;
            vertical-align: middle;
            margin-right: 20px;
        }
        footer{
            text-align: right;
            margin-top: 20px;
        }
    }
</style>

