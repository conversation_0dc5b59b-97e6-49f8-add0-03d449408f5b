import request from '@root/publicMethods/request';

export default {
  getTopicsRandom: data => {
    return request({
      url: '/manage/testPaper/getTopicsRandom',
      method: 'post',
      data,
    });
  },
  getDetail: params => {
    return request({
      url: '/manage/testPaper/getDetail',
      method: 'get',
      params,
    });
  },
  addTestPaper: data => {
    data.category = 2; // 1是培训 2是问卷调查
    return request({
      url: '/manage/testPaper/add',
      method: 'post',
      data,
    });
  },
  delTestPaper: data => {
    return request({
      url: '/manage/testPaper/del',
      method: 'post',
      data,
    });
  },
  editTestPaper: data => {
    return request({
      url: '/manage/testPaper/edit',
      method: 'post',
      data,
    });
  },
  // 获取试卷列表
  getList: data => {
    data.category = 2; // 1是培训 2是问卷调查
    return request({
      url: '/manage/testPaper/getList',
      method: 'post',
      data,
    });
  },
  // 获取题库数据
  getQB: data => {
    return request({
      url: '/manage/questionBank/getQB',
      method: 'post',
      data,
    });
  },
  createQB: data => {
    return request({
      url: '/manage/questionBank/createQB',
      method: 'post',
      data,
    });
  },
  delQB: data => {
    return request({
      url: '/manage/questionBank/delQB',
      method: 'post',
      data,
    });
  },
  createTopic: data => {
    return request({
      url: '/manage/questionBank/createTopic',
      method: 'post',
      data,
    });
  },
  addSomeTopic: data => {
    return request({
      url: '/manage/questionBank/addSomeTopic',
      method: 'post',
      data,
    });
  },
  updateTopic: data => {
    return request({
      url: '/manage/questionBank/updateTopic',
      method: 'post',
      data,
    });
  },
  getTopic: data => {
    return request({
      url: '/manage/questionBank/getTopic',
      method: 'post',
      data,
    });
  },
  delTopic: data => {
    return request({
      url: '/manage/questionBank/delTopic',
      method: 'post',
      data,
    });
  },
  findLabel: data => {
    return request({
      url: '/manage/questionBank/findLabel',
      method: 'post',
      data,
    });
  },
  addLabel: data => {
    return request({
      url: '/manage/questionBank/addLabel',
      method: 'post',
      data,
    });
  },
  delLabel: data => {
    return request({
      url: '/manage/questionBank/delLabel',
      method: 'post',
      data,
    });
  },
  updateLabel: data => {
    return request({
      url: '/manage/questionBank/updateLabel',
      method: 'post',
      data,
    });
  },
};

