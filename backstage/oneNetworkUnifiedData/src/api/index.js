import request from '@root/publicMethods/request';

// 获取用人单位和劳动者数
export function getEnterpriseAndWorkerCount(params) {
  return request({
    url: '/manage/display/enterpriseAndWorkerCount',
    method: 'get',
    params,
  });
}

/**
 * 获取用人单位检测统计数据
 * @param {Object} params
 * @return {Promise}
 */
export function getEnterpriseStatsByTime(params) {
  return request({
    url: '/manage/jobHealthStatistics/getEnterpriseStatsByTime',
    method: 'get',
    params,
  });
}

/**
 * 获取检测超标统计数据
 * @param {Object} params
 * @return {Promise}
 */
export function getExceedStandardStats(params) {
  return request({
    url: '/manage/jobHealthStatistics/getExceedStandardStats',
    method: 'get',
    params,
  });
}

// 获取诊断统计数据
export function statisticDiagnosisByYear(params) {
  return request({
    url: '/manage/diagnosis/statisticDiagnosisByYear',
    method: 'get',
    params,
  });
}

// 获取鉴定统计数据
export function statisticByYear(params) {
  return request({
    url: '/manage/identify/statisticByYear',
    params,
    method: 'get',
  });
}

// 职业病病人全程服务数据展示 - 就诊预约
export function appointmentByArea(params) {
  return request({
    url: '/manage/display/appointmentByArea',
    method: 'get',
    params,
  });
}

// 职业病病人全程服务数据展示 - 诊疗服务
export function treatmentInformationCountByArea(params) {
  return request({
    url: '/manage/display/treatmentInformationCountByArea',
    method: 'get',
    params,
  });
}

// 职业病病人全程服务数据展示 - 用药服务
export function medicationGuidanceCountByArea(params) {
  return request({
    url: '/manage/display/medicationGuidanceCountByArea',
    method: 'get',
    params,
  });
}


// 职业病病人全程服务数据展示 - 随访记录
export function followUpByArea(params) {
  return request({
    url: '/manage/display/followUpByArea',
    method: 'get',
    params,
  });
}

// 职业病病人全程服务数据展示 - 康复指导
export function recoveryInfoByArea(params) {
  return request({
    url: '/manage/display/recoveryInfoByArea',
    method: 'get',
    params,
  });
}

// 危害因素在线监测数据
export function getonlineMonitoringList() {
  // 获取所有单位
  return request({
    url: '/manage/onlineMonitoring/statistics',
    method: 'get',
  });
}


// 职业防护
export function getoproAbnormalStatistics() {
  return request({
    url: '/api/defendproducts/proAbnormalStatistics',
    method: 'get',
  });
}
