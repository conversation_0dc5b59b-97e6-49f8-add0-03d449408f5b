<template>
  <div class="content">
    <div class="metric-container">
      <!-- 左侧：核心指标区域 -->
      <div class="metric-left">
        <div class="main-card primary-card">
          <div class="card-icon">
            <i class="el-icon-office-building"></i>
          </div>
          <div class="card-content">
            <div class="card-title">用人单位数量</div>
            <div class="card-value">{{ enterPriseNum || 0 }}</div>
          </div>
        </div>
        
        <div class="main-card success-card">
          <div class="card-icon">
            <i class="el-icon-user"></i>
          </div>
          <div class="card-content">
            <div class="card-title">劳动者数量</div>
            <div class="card-value">{{ workerNum || 0 }}</div>
          </div>
        </div>
      </div>
      <!-- 右侧：健康检查数据区域 -->
      <div class="metric-right">
        <h3>职业健康检查数据</h3>
        <el-divider></el-divider>
        <div class="stats-grid">
          <el-card class="stats-card primary">
            <div class="stats-inner">
              <div class="stats-icon">
                <i class="el-icon-office-building"></i>
              </div>
              <div class="stats-details">
                <div class="stats-title">全部检测用人单位数</div>
                <div class="stats-value">{{ statsData.fullyCheckedCount || 0 }}</div>
              </div>
            </div>
          </el-card>
          
          <el-card class="stats-card warning">
            <div class="stats-inner">
              <div class="stats-icon">
                <i class="el-icon-warning-outline"></i>
              </div>
              <div class="stats-details">
                <div class="stats-title">部分检测用人单位数</div>
                <div class="stats-value">{{ statsData.partiallyCheckedCount || 0 }}</div>
              </div>
            </div>
          </el-card>
          
          <el-card class="stats-card danger">
            <div class="stats-inner">
              <div class="stats-icon">
                <i class="el-icon-close"></i>
              </div>
              <div class="stats-details">
                <div class="stats-title">未检测用人单位数</div>
                <div class="stats-value">{{ statsData.notCheckedCount || 0 }}</div>
              </div>
            </div>
          </el-card>
          <el-card class="stats-card success">
            <div class="stats-inner">
              <div class="stats-icon">
                <i class="el-icon-check"></i>
              </div>
              <div class="stats-details">
                <div class="stats-title">检测完成率</div>
                <div class="stats-value">{{ statsData.checkRate || '0%' }}</div>
              </div>
            </div>
          </el-card>
          
          <el-card class="stats-card purple">
            <div class="stats-inner">
              <div class="stats-icon">
                <i class="el-icon-data-analysis"></i>
              </div>
              <div class="stats-details">
                <div class="stats-title">超标率</div>
                <div class="stats-value">{{ exceedData.exceedRate || '0%' }}</div>
              </div>
            </div>
          </el-card>
          
          <el-card class="stats-card orange">
            <div class="stats-inner">
              <div class="stats-icon">
                <i class="el-icon-error"></i>
              </div>
              <div class="stats-details">
                <div class="stats-title">超标用人单位数</div>
                <div class="stats-value">{{ exceedData.exceedCount || 0 }}</div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-col">
        <div ref="chart1" class="chart-container"></div>
      </div>
      <div class="chart-col">
        <div ref="chart2" class="chart-container"></div>
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-col">
        <h3>职业病病人全生命周期数据</h3>
        <el-divider></el-divider>
        <div class="metric-cards">
          <div class="card">
            <div class="metric-value">就诊预约总人次</div>
            <div class="metric-number">{{ totalAppointments }}</div>
          </div>
          <div class="card">
            <div class="metric-value">诊疗服务总人次</div>
            <div class="metric-number">{{ totalTreatments }}</div>
          </div>
          <div class="card">
            <div class="metric-value">用药服务总人次</div>
            <div class="metric-number">{{ totalMedications }}</div>
          </div>
          <div class="card">
            <div class="metric-value">随访记录总人次</div>
            <div class="metric-number">{{ totalFollowUps }}</div>
          </div>
          <div class="card">
            <div class="metric-value">康复指导总人次</div>
            <div class="metric-number">{{ totalRecoveries }}</div>
          </div>
        </div>
      </div>
      <div class="chart-col">
        <h3>危害因素在线监测数据</h3>
        <el-divider></el-divider>
        <div class="metric-cards">
          <div class="card" v-for="item in detectData" :key="item.name">
            <div class="metric-value">{{item.name}}数量</div>
            <div class="metric-number">{{item.count}}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-col">
        <h3>职业防护数据</h3>
        <el-divider></el-divider>
        <div class="metric-cards">
          <div class="card">
            <div class="metric-value">未佩戴防护用品用人单位数</div>
            <div class="metric-number">{{abnormalCount}}</div>
          </div>
          <div class="card">
            <div class="metric-value">未佩戴防护用品人数</div>
            <div class="metric-number">{{abnormalEmployeeCount}}</div>
          </div>
          <div class="card">
            <div class="metric-value">监督处罚用人单位数</div>
            <div class="metric-number">{{punishmentCount}}</div>
          </div>
          <div class="card">
            <div class="metric-value">防护用品总数</div>
            <div class="metric-number">{{countList}}</div>
          </div>
        </div>
        <div ref="chart3" class="chart-container"></div>
      </div>
    </div>  
  </div> 
 </template>
 
 <script>
 import * as echarts from 'echarts';
 import { getEnterpriseAndWorkerCount,getEnterpriseStatsByTime,getExceedStandardStats,statisticDiagnosisByYear,statisticByYear,appointmentByArea,treatmentInformationCountByArea,
  medicationGuidanceCountByArea,followUpByArea,recoveryInfoByArea,getonlineMonitoringList,getoproAbnormalStatistics} from "@/api/index";
 export default {
   data() {
     return {
      enterPriseNum:0,
      workerNum:0,
      search:{
         page: 1,
         limit: 999999,
         startDate:'',
         endDate:'',
         administerArea: '',
       },
       zdSummarized: [],
       jdSummarized: [],
       formData:{
        yearBegin: '',
        yearEnd: '',
        areaCode: '',
        disease: '',
        administerAreaCode:''
      },
      statsData:{
        totalEnterpriseCount: 0,
        fullyCheckedCount: 0,
        partiallyCheckedCount: 0,
        notCheckedCount: 0,
        checkRate: '0%'
      },
      exceedData:{
        exceedCount: 0,
        exceedRate: '0%'
      },
      totalAppointments: 0,
      totalTreatments: 0,
      totalMedications: 0,
      totalFollowUps: 0,
      totalRecoveries: 0,
      detectData: [],
      // 1.防护用品使用情况
      defendProductList: [
      ],

      // 2.未佩戴预警情况
      alertStats: [
      ],

      // 3.监督处罚情况
      penaltyStats: [
      ],
      countList: 0,// 各危害因素种类防护用品数量
      abnormalCount:0,// 未佩戴单位数
      abnormalEmployeeCount:0,// 未佩戴人数
      punishmentCount:0,// 处罚单位数
     };
   },
   mounted() {
     this.getTotalNum();
   },
   methods: {
    // 获取统计数据
    getTotalNum(){
      getEnterpriseAndWorkerCount().then(res => {
        this.enterPriseNum = res.data.enterPriseNum;
        this.workerNum = res.data.workerNum;
      });
      getEnterpriseStatsByTime().then(res => {
        this.statsData = res.data;
      });
      getExceedStandardStats().then(res => {
        this.exceedData = res.data;
      });
      statisticDiagnosisByYear(this.formData).then(res => {
        this.zdSummarized = res.data.data || [];
        this.initEchart1();
      });
      statisticByYear(this.formData).then(res => {
        this.jdSummarized = res.data.data || [];
        this.initEchart2();
      });
      getonlineMonitoringList().then(res => {
        this.detectData = res.data.detectData || []
      })
      appointmentByArea(this.search).then(res => {
        this.totalAppointments = this.calculateTotal(res.data.data) || 0;
      });
      treatmentInformationCountByArea(this.search).then(res => {
        this.totalTreatments = this.calculateTotal(res.data.data) || 0;
      });
      medicationGuidanceCountByArea(this.search).then(res => {
        this.totalMedications = this.calculateTotal(res.data.data) || 0;
      });
      followUpByArea(this.search).then(res => {
        this.totalFollowUps = this.calculateTotal(res.data.data) || 0;
      });
      recoveryInfoByArea(this.search).then(res => {
        this.totalRecoveries = this.calculateTotal(res.data.data) || 0;
      });
      getoproAbnormalStatistics().then(res => {
        this.defendProductList = res.data.defendProductList
        this.alertStats = res.data.abnormalList
        this.penaltyStats = res.data.abnormalList
        this.countList = this.defendProductList.reduce((acc, cur) => acc + cur.count, 0)
        this.abnormalCount = this.alertStats.reduce((acc, cur) => acc + cur.abnormalCount, 0)
        this.abnormalEmployeeCount = this.alertStats.reduce((acc, cur) => acc + cur.abnormalEmployeeCount, 0)
        this.punishmentCount = this.penaltyStats.reduce((acc, cur) => acc + cur.punishmentCount, 0)
        this.initEchart3();
      })
    },
    initEchart1(){
      // 初始化ECharts实例
      const chart = echarts.init(this.$refs.chart1);
      // 配置ECharts选项
      const option = {
        title: {
          text: '职业病诊断人数统计'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          bottom: 0,
          data: ['职业病诊断人数']
        },
        xAxis: {
          type: 'category',
          data: this.zdSummarized && this.zdSummarized.map((item) => item.year)
        },
        yAxis: {
          type: 'value',
        },
        series: {
          name: '职业病诊断人数',
          type: 'line',
          data: this.zdSummarized && this.zdSummarized.map((item) => item.count)
        }
      }
      // 使用配置项和数据显示图表
      chart.setOption(option)
    },
    initEchart2(){
      // 初始化ECharts实例
      const chart = echarts.init(this.$refs.chart2);
      // 配置ECharts选项
      const option = {
        title: {
          text: '职业病鉴定人数统计'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          bottom: 0,
          data: ['职业病鉴定人数']
        },
        xAxis: {
          type: 'category',
          data: this.jdSummarized && this.jdSummarized.map((item) => item.year)
        },
        yAxis: {
          type: 'value'
        },
        series: {
          name: '职业病鉴定人数',
          type: 'line',
          data: this.jdSummarized && this.jdSummarized.map((item) => item.count)
        }
      }
      // 使用配置项和数据显示图表
      chart.setOption(option)
    },
    calculateTotal(data) {
      if (!Array.isArray(data)) return 0
      return data && data.reduce((sum, item) => sum + Number(item.value), 0)
    },
    initEchart3() {
      try {
        const chart = echarts.init(this.$refs.chart3)
        const option = {
          title: { 
            text: '各危害因素种类防护用品数量占比',
          },
          tooltip: {
            trigger: 'item'
          },
          series: [{ 
            type: 'pie', 
            radius: '50%',
            data: this.defendProductList.map(item => ({ 
              name: item.product, 
              value: item.count 
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }]
        }
        chart.setOption(option)
      } catch (error) {
        console.error('初始化饼图失败:', error)
      }
    },
   },
 
 };
 </script>
 
 <style lang="scss" scoped>
 .content {
   padding: 10px 20px;
 }
 
 .chart-row {
   display: flex;
   gap: 20px;
   margin-bottom: 20px;
 }
 
 .chart-col {
   flex: 1;
   background: white;
   padding: 15px;
   border-radius: 5px;
   box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 增加了垂直偏移量和模糊半径，调整了颜色 */
 }
 
 .chart-container {
   height: 300px;
 }


 /* 容器样式 */
.metric-container {
  display: flex;
  gap: 24px;
  width: 100%;
  box-sizing: border-box;
}

/* 左侧主要指标样式 */
.metric-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.main-card {
  display: flex;
  border-radius: 12px;
  padding: 29px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.main-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
}

.main-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.primary-card {
  background-color: #f0f7ff;
}

.primary-card::before {
  background-color: #1890ff;
}

.success-card {
  background-color: #f6ffed;
}

.success-card::before {
  background-color: #52c41a;
}

.card-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 16px;
  margin-right: 20px;
}

.primary-card .card-icon {
  background-color: rgba(24, 144, 255, 0.15);
  color: #1890ff;
}

.success-card .card-icon {
  background-color: rgba(82, 196, 26, 0.15);
  color: #52c41a;
}

.card-title {
  font-size: 16px;
  color: #595959;
  margin-bottom: 8px;
}

.card-value {
  font-size: 36px;
  font-weight: 700;
  color: #1f2329;
  line-height: 1.2;
}

.card-extra {
  margin-top: 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 右侧健康检查数据样式 */
.metric-right {
  flex: 2;
}

.stats-grid {
  display: flex;
  flex-wrap: wrap;
  grid-template-columns: repeat(3, 1fr);
}

.stats-card {
  width: 30%;
  height: 100%;
  border-radius: 12px;
  border: none;
  transition: all 0.3s ease;
  overflow: hidden;
  margin-bottom: 16px;
  margin-right: 24px;
}

.stats-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.stats-inner {
  display: flex;
  align-items: center;
  height: 100%;
  gap: 16px;
}

.stats-icon {
  width: 44px;
  height: 44px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
}

.stats-details {
  flex: 1;
}

.stats-title {
  font-size: 14px;
  color: #595959;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stats-value {
  font-size: 22px;
  font-weight: 600;
  color: #1f2329;
}

/* 右侧卡片颜色主题 */
.stats-card.primary .stats-icon {
  background-color: rgba(24, 144, 255, 0.15);
  color: #1890ff;
}

.stats-card.warning .stats-icon {
  background-color: rgba(250, 173, 20, 0.15);
  color: #faad14;
}

.stats-card.danger .stats-icon {
  background-color: rgba(255, 77, 79, 0.15);
  color: #ff4d4f;
}

.stats-card.success .stats-icon {
  background-color: rgba(82, 196, 26, 0.15);
  color: #52c41a;
}

.stats-card.purple .stats-icon {
  background-color: rgba(122, 95, 255, 0.15);
  color: #7a5fff;
}

.stats-card.orange .stats-icon {
  background-color: rgba(255, 128, 0, 0.15);
  color: #ff8000;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .metric-container {
    flex-direction: column;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .card-value {
    font-size: 28px;
  }
}

@media (max-width: 576px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .main-card {
    padding: 18px;
  }
  
  .card-value {
    font-size: 24px;
  }
}
.metric-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.card {
  background: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 增加了垂直偏移量和模糊半径，调整了颜色 */
}

.metric-value {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.metric-number {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
}
 </style>