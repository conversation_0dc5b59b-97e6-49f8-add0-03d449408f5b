import request from "@root/publicMethods/request";

export function getDeclarations(params) {
  return request({
    url: "/manage/evaluationSystem/getDeclarationList",
    method: "get",
    params,
  });
}

export const reviewRemind = (data) => {
  return request({
    url: "/manage/sendMessage/send",
    method: "post",
    data,
  });
};

export const updateReReviewStatus = (data) => {
  return request({
    url: "/manage/evaluationSystem/updateReReviewStatus",
    method: "put",
    data,
  });
};
