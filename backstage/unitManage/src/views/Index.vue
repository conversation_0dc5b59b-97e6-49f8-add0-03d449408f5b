<script>
import { getDeclarations, reviewRemind, updateReReviewStatus } from "@/api/index"
import settings from "@root/publicMethods/settings";

export default {
  name: "FirstTrialComp",
  data() {
    return {
      currentPage: 1,
      limit: 10,
      tableData: [],
      total: 0,
      search: {
        unit_name: undefined,
        year: undefined,
      },
      dialogVisible: false,
      formData: {
        title: undefined,
        message: undefined
      },
      formRules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' },
        ],
        message: [
          { required: true, message: '请输入通知内容', trigger: 'blur' },
        ]
      },
      enterprise_id: undefined,
      id: undefined,
    }
  },
  created() {
    this.getDeclarationList()
  },
  methods: {
    async getDeclarationList() {
      const params = {
        page: this.currentPage,
        pageSize: this.limit,
        ...this.search,
        type: "3",
        is_join: '1'
      }

      try {
        const res = await getDeclarations(params);
        this.tableData = res.data.records;
        this.total = res.data.total;
      } catch (error) {
        console.log(error)
        this.$message.error("列表数据获取失败")
      }
    },
    handleSearch() {
      this.getDeclarationList()
    },
    handleReset() {
      this.search = {
        unit_name: undefined,
        year: undefined,
        declaration_status: null,
      }
      this.getDeclarationList()
    },
    lookDeclarationRecord(id) {
      this.$router.push({
        path: `${settings.admin_base_path}/unitVote/lookDeclaration`,
        query: {
          id
        }
      })
    },
    lookEvaluationRecord(id) {
      this.$router.push({
        path: `${settings.admin_base_path}/unitReview/lookReviewDeclaration`,
        query: {
          id
        }
      })
    },
    lookExpertReviewRecord(id) {
      this.$router.push({
        path: `${settings.admin_base_path}/unitReview/exportReview`,
        query: {
          id,
          type: 'look'
        }
      })
    },
    handleReviewRemind({ enterprise_id, id }) {
      this.enterprise_id = enterprise_id
      this.id = id
      this.dialogVisible = true
    },
    handleClose() {
      this.formData = {
        title: undefined,
        message: undefined
      }
      this.enterprise_id = undefined
      this.$refs.ruleForm.clearValidate()
    },
    handleDialogConfirm() {
      // 校验表单
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          try {
            await reviewRemind({
              ...this.formData,
              reader: [{
                readerID: this.enterprise_id
              }],
              sendWay: ['站内信息']
            })
            // 修改复评审状态
            await updateReReviewStatus({
              id: this.id
            })
            this.$message.success("提醒成功")
            this.getDeclarationList()
          } catch (error) {
            this.$message.error("提醒失败")
          }
          this.dialogVisible = false
        } else {
          return false;
        }
      });

    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDeclarationList()
    }
  }
}
</script>

<template>
  <div class="first-trial">
    <div class="header">
      <el-input v-model="search.unit_name" style="width: 240px;" placeholder="请输入用人单位名称"></el-input>
      <el-date-picker style="width: 240px;" v-model="search.year" type="year" placeholder="选择年度" value-format="yyyy">
      </el-date-picker>
      <div>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="handleSearch">查询</el-button>
      </div>
    </div>

    <el-table :data="tableData" border style="width: 100%; margin-top: 20px" :header-cell-style="{
      color: 'black',
      'background-color': '#f5f7fa',
      'font-wight': 300,
    }">
      <el-table-column fixed type="index" label="序号" width="80px" align="center">
      </el-table-column>
      <el-table-column fixed prop="unit_name" label="单位名称" align="center">
      </el-table-column>
      <el-table-column fixed prop="year" label="年度" align="center">
      </el-table-column>
      <!-- <el-table-column prop="declaration_time_format" label="申报时间" align="center">
      </el-table-column>
      <el-table-column prop="city_review_time_format" label="初审时间" align="center">
      </el-table-column>
      <el-table-column prop="score" label="自评分数" align="center">
      </el-table-column>
      <el-table-column prop="declaration_status" label="状态" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.declaration_status === '0'">待初审</el-tag>
          <el-tag v-else-if="scope.row.declaration_status === '1'" type="warning">审核需修改</el-tag>
          <el-tag v-else-if="scope.row.declaration_status === '2'" type="danger">初审不通过</el-tag>
          <el-tag v-else-if="scope.row.declaration_status === '3'">初审通过</el-tag>
          <el-tag v-else-if="scope.row.declaration_status === '4'" type="danger">审核不通过</el-tag>
          <el-tag v-else-if="scope.row.declaration_status === '5'" type="success">审核通过</el-tag>
        </template>
</el-table-column>
<el-table-column prop="is_submit" label="是否提交到兵团" align="center">
  <template slot-scope="scope">
          <el-tag v-if="scope.row.is_submit === '0'" type="warning">未提交</el-tag>
          <el-tag v-else-if="scope.row.is_submit === '1'" type="success">已提交</el-tag>
        </template>
</el-table-column> -->
      <el-table-column fixed prop="code" label="社会统一信用代码" align="center">
      </el-table-column>
      <el-table-column fixed prop="contract" label="联系人" align="center">
      </el-table-column>
      <el-table-column fixed prop="phoneNum" label="联系电话" align="center">
      </el-table-column>
      <el-table-column prop="companyScale" label="企业规模" align="center">
        <template slot-scope="scope">
          {{ scope.row.companyScale }}
        </template>
      </el-table-column>
      <el-table-column prop="regAdd" label="企业地址" align="center" width="240">
      </el-table-column>
      <!-- <el-table-column prop="industry" label="行业类别" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.industry_category?.join(',') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="regType" label="注册类型" align="center" /> -->
      <el-table-column prop="re_review_status" label="复评审提醒状态" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.re_review_status === '0'">未提醒</el-tag>
          <el-tag v-else-if="scope.row.re_review_status === '1'" type="success">已提醒</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="360" align="center">
        <template slot-scope="scope">
          <div style="display: flex; justify-content: space-around">
            <el-link type="primary" @click="lookDeclarationRecord(scope.row.id)">申报记录</el-link>
            <el-link type="primary" @click="lookEvaluationRecord(scope.row.id)">评估记录</el-link>
            <el-link type="primary" @click="lookExpertReviewRecord(scope.row.id)">专家评审记录</el-link>
            <el-link type="primary" @click="handleReviewRemind(scope.row)">复评审提醒</el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination style="display: flex; justify-content: right; margin-top: 20px" @current-change="handleCurrentChange"
      :current-page="currentPage" :page-size="limit" layout="total, prev, pager, next" :total="total">
    </el-pagination>

    <!-- 复评审提醒 -->
    <el-dialog title="复评审提醒" :visible.sync="dialogVisible" width="30%" @close="handleClose">
      <el-form :model="formData" :rules="formRules" ref="ruleForm" label-width="100px" label-position="top">
        <el-form-item label="标题" prop="title">
          <el-input v-model="formData.title"></el-input>
        </el-form-item>
        <el-form-item label="通知内容" prop="message">
          <el-input type="textarea" v-model="formData.message"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleDialogConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.first-trial {
  padding: 24px;

  .header {
    display: flex;
    gap: 24px;
  }
}
</style>