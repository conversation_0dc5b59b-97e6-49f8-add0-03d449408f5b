const state = {
  tableConfig: {
    title: '巡查记录',
    needAdd: true,
    border: true,
    needExport: true,
    noDelete: true,
  }, // 表单页面配置
  topBarInfo: [
    {
      title: '巡查内容',
      prop: 'keyword',
      type: 'input',
      remote: true,
      placeholder: '请输入',
    },
    {
      title: '巡查结果',
      prop: 'wornCorrectly',
      type: 'select',
      options: [
        {
          label: '合格',
          value: true,
        },
        {
          label: '异常',
          value: false,
        },
      ],
    },
  ], // 搜索栏内容
  tableHeader: [
    {
      label: '巡查单位',
      prop: 'createUnitName',
      align: 'center',
      width: '100',
      type: 'string',
    },
    {
      label: '巡查班次',
      prop: 'shift',
      align: 'center',
      width: '100',
      type: 'select',
    },
    { label: '巡查人员', prop: 'creator', align: 'center', width: '90' },
    {
      label: '巡查时间',
      prop: 'recordedAt',
      align: 'center',
      width: '140',
      type: 'date',
    },
    {
      label: '巡查内容',
      prop: 'content',
      minWidth: '180',
      align: 'center',
      type: 'array',
    },
    {
      label: '用人单位',
      prop: 'EnterpriseID',
      align: 'center',
      minWidth: '140',
      remote: true,
      type: 'select',
    },
    {
      label: '巡查结果',
      prop: 'wornCorrectly',
      width: '90',
      align: 'center',
      type: 'bool',
      options: [ '合格', '异常' ],
    },

    { label: '异常情况', prop: 'description', align: 'left', minWidth: '200' },
    { label: '整改文件', prop: 'rectify_file', width: '180', align: 'center' },
    {
      label: '整改状态',
      prop: 'status',
      width: '90',
      align: 'center',
      type: 'string',
    },
    {
      prop: 'operation',
      label: '操作',
      width: '200',
      align: 'center',
      fixed: 'right',
    },
  ], // 表头信息
  dialogFormInfo: [
    {
      label: '巡查班次',
      prop: 'shift',
      type: 'select',
      options: [
        {
          label: '巡查班次',
          value: 'shift',
        },
      ],
      placeholder: '请选择巡查班次',
    },
    {
      label: '巡查人员',
      prop: 'creator',
    },
    {
      label: '巡查时间',
      prop: 'recordedAt',
      type: 'date',
    },
    // {
    //   label: '劳动者',
    //   prop: 'employees',
    //   placeholder: '请输入劳动者姓名',
    // },
    {
      label: '巡查内容',
      prop: 'content',
    },
    // {
    //   label: '穿戴正确',
    //   prop: 'wornCorrectly',
    //   type: 'bool',
    //   options: [ '正确', '错误' ],
    // },
    // {
    //   label: '整改状态',
    //   prop: 'status',
    //   type: 'string',
    // },
    {
      label: '用人单位',
      prop: 'EnterpriseID',
      type: 'select',
      options: [
        {
          label: '用人单位',
          value: 'EnterpriseID',
        },
      ],
      placeholder: '请选择用人单位',
      remote: true,
    },
    {
      label: '存查结果',
      prop: 'wornCorrectly',
      type: 'bool',
      options: [ '合格', '异常' ],
    },
    { label: '异常情况', prop: 'description', type: 'textarea' },
  ], // 对话框内容
  dialogRules: {
    shift: [
      { required: true, message: '请选择巡查班次', trigger: 'blur' },
    ],
    creator: [
      { required: true, message: '请输入巡查人员', trigger: 'blur' },
    ],
    EnterpriseID: [
      { required: true, message: '请选择用人单位', trigger: 'blur' },
    ],
    employees: [{ required: false, message: '请输入劳动者', trigger: 'blur' }],
    content: [
      { required: true, message: '请输入巡查内容', trigger: 'blur' },
    ],
    wornCorrectly: [
      { required: true, message: '请选择巡查结果', trigger: 'blur' },
    ],
    // has_abnormal: [
    //   { required: true, message: '请确认是否异常', trigger: 'blur' },
    // ],
    description: [
      { required: false, message: '请输入异常情况', trigger: 'blur' },
    ],
    recordedAt: [
      { required: true, message: '请选择汇报时间', trigger: 'blur' },
    ],
  }, // 对话框规则
};

const mutations = {};

const actions = {};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
