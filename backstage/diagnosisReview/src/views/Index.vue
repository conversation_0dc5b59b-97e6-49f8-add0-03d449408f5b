<template>
  <div class="serveEnterprise">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="备案审核" name="first">
        <div class="header">
          <el-form
            :inline="true"
            :model="searchParams"
            class="demo-form-inline"
          >
            <el-form-item label="备案类型">
              <el-select
                v-model="searchParams.recordType"
                placeholder="请选择"
                clearable
              >
                <el-option label="首次备案" value="1"></el-option>
                <el-option label="变更备案" value="2"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="机构名称">
              <el-input
                v-model="searchParams.institutionName"
                placeholder="请输入机构名称"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                size="small"
                type="primary"
                @click="handleQuery"
                >查询</el-button
              >
              <el-button
                size="small"
                @click="handleReset"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </div>

        <div class="table">
          <el-table
            :data="tableData"
            style="width: 100%"
            stripe
            ref="multipleTable"
            header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
          >
            <el-table-column
              label="备案类别"
              align="center"
              prop="recordType"
              fixed="left"
            >
            </el-table-column>
            <el-table-column
              label="机构名称"
              align="center"
              prop="institutionName"
              show-overflow-tooltip
              width="150"
              fixed="left"
            >
            </el-table-column>
            <el-table-column
              label="单位地址"
              align="center"
              prop="address"
              show-overflow-tooltip
              width="150"
            >
            </el-table-column>

            <el-table-column
              prop="institutionNature"
              label="单位性质"
              align="center"
              show-overflow-tooltip
              :formatter="institutionNatureFormatter"
            >
            </el-table-column>

            <el-table-column
              prop="legalPerson"
              label="法定代表人"
              align="center"
            >
            </el-table-column>

            <el-table-column
              prop="legalPersonDuty"
              label="职务"
              align="center"
              show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column prop="contactPerson" label="联系人" align="center">
            </el-table-column>
            <el-table-column
              prop="contactPhone"
              label="联系方式"
              align="center"
              width="120"
            >
            </el-table-column>

            <el-table-column
              prop="email"
              label="邮箱"
              align="center"
              width="120"
            >
            </el-table-column>

            <el-table-column prop="staffNum" label="职工总数" align="center">
            </el-table-column>

            <el-table-column
              prop="practicingDoctorNum"
              label="从事职业病诊断执业医师人数"
              align="center"
            >
            </el-table-column>

            <el-table-column
              prop="qualificationDoctorNum"
              label="取得职业病诊断资格人数"
              align="center"
            >
            </el-table-column>

            <el-table-column
              prop="practiceLicenseCode"
              label="医疗机构执业许可证编号"
              align="center"
            >
            </el-table-column>

            <el-table-column
              prop="licenseIssueDate"
              label="发证日期"
              align="center"
              width="120"
            >
            </el-table-column>
            <el-table-column
              prop="licenseIssueInstitution"
              label="发证机关"
              align="center"
              show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
              prop="creditCode"
              label="统一社会信用代码"
              align="center"
              show-overflow-tooltip
            >
            </el-table-column>

            <!-- <el-table-column prop="issueOrg" label="职业病诊断类别" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.diagnosticCategoryList.join() }}</span>
          </template>
        </el-table-column> -->

            <el-table-column
              prop="status"
              label="备案状态"
              align="center"
              width="120"
            >
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.status === 10"
                  type="success"
                  size="small"
                  round
                  >审核通过</el-button
                >
                <el-button
                  v-if="scope.row.status === 2"
                  type="warning"
                  size="small"
                  round
                  >补正备案材料</el-button
                >
                <el-button
                  v-if="scope.row.status === -10"
                  type="danger"
                  size="small"
                  round
                  >不予备案</el-button
                >
              </template>
            </el-table-column>

            <el-table-column
              prop="auditMessage"
              label="审核意见"
              align="center"
              show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
              align="center"
              width="220"
              label="操作"
              fixed="right"
            >
              <template slot-scope="scope">
                <div style="display: flex; justify-content: center">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="查看详情"
                    placement="top"
                  >
                    <el-button
                      size="mini"
                      type="primary"
                      circle
                      @click="view(scope.row)"
                      icon="el-icon-view"
                    ></el-button>
                  </el-tooltip>

                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="审核通过"
                    placement="top"
                  >
                    <el-button
                      size="mini"
                      circle
                      type="success"
                      :disabled="scope.row.status !== 1"
                      @click="pass(scope.row)"
                      icon="el-icon-check"
                    ></el-button>
                  </el-tooltip>

                  <!-- <el-tooltip
                    class="item"
                    effect="dark"
                    content="补正备案材料"
                    placement="top"
                  >
                    <el-button
                      size="mini"
                      type="warning"
                      circle
                      :disabled="scope.row.status !== 1"
                      @click="fix(scope.row)"
                      icon="el-icon-paperclip"
                    ></el-button>
                  </el-tooltip> -->

                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="不予备案"
                    placement="top"
                  >
                    <el-button
                      size="mini"
                      type="danger"
                      circle
                      :disabled="scope.row.status !== 1"
                      @click="back(scope.row)"
                      icon="el-icon-close"
                    ></el-button>
                  </el-tooltip>

                  <!-- <el-tooltip
                class="item"
                effect="dark"
                content="注销备案"
                placement="top"
                v-if="scope.row.recordStatus === '审核通过'"
              >
                <el-button
                  size="mini"
                  type="danger"
                  circle
                  @click="logOff(scope.row)"
                  icon="el-icon-key"
                ></el-button>
              </el-tooltip> -->
                </div>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="searchParams.curPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="searchParams.limit"
              layout="total,sizes, prev, pager, next"
              :total="total"
            >
            </el-pagination>
          </div>
        </div>
      </el-tab-pane>
      <!-- <el-tab-pane label="备案注销" name="second">
        <div class="header">
          <el-form
            :inline="true"
            :model="searchParams"
            class="demo-form-inline"
          >
            <el-form-item label="备案类型">
              <el-select
                v-model="searchParams.recordType"
                placeholder="请选择"
                clearable
              >
                <el-option label="首次备案" value="1"></el-option>
                <el-option label="变更备案" value="2"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="机构名称">
              <el-input
                v-model="searchParams.institutionName"
                placeholder="请输入机构名称"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                @click="handleQuery"
                size="small"
                >查询</el-button
              >
              <el-button
                size="small"
                @click="handleReset"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </div>

        <div class="table">
          <el-table
            :data="tableData"
            style="width: 100%"
            stripe
            ref="multipleTable"
            header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
          >
            <el-table-column
              label="备案类别"
              align="center"
              prop="recordType"
              fixed="left"
            >
            </el-table-column>
            <el-table-column
              label="机构名称"
              align="center"
              prop="institutionName"
              show-overflow-tooltip
              width="150"
              fixed="left"
            >
            </el-table-column>
            <el-table-column
              label="单位地址"
              align="center"
              prop="address"
              show-overflow-tooltip
              width="150"
            >
            </el-table-column>

            <el-table-column
              prop="legalPerson"
              label="法定代表人"
              align="center"
            >
            </el-table-column>

            <el-table-column
              prop="legalPersonDuty"
              label="职务"
              align="center"
              show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column prop="contactPerson" label="联系人" align="center">
            </el-table-column>
            <el-table-column
              prop="contactPhone"
              label="联系方式"
              align="center"
              width="120"
            >
            </el-table-column>

            <el-table-column
              prop="email"
              label="邮箱"
              align="center"
              width="120"
            >
            </el-table-column>

            <el-table-column prop="staffNum" label="职工总数" align="center">
            </el-table-column>

            <el-table-column
              prop="practicingDoctorNum"
              label="从事职业病诊断执业医师人数"
              align="center"
            >
            </el-table-column>

            <el-table-column
              prop="qualificationDoctorNum"
              label="取得职业病诊断资格人数"
              align="center"
            >
            </el-table-column>

            <el-table-column
              prop="practiceLicenseCode"
              label="医疗机构执业许可证编号"
              align="center"
            >
            </el-table-column>

            <el-table-column
              prop="licenseIssueDate"
              label="发证日期"
              align="center"
              width="120"
            >
            </el-table-column>
            <el-table-column
              prop="licenseIssueInstitution"
              label="发证机关"
              align="center"
              show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
              prop="creditCode"
              label="统一社会信用代码"
              align="center"
              show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
              prop="status"
              label="备案是否已注销"
              align="center"
              width="120"
            >
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.status === 10"
                  type="success"
                  size="small"
                  round
                  >否</el-button
                >
              </template>
            </el-table-column>

            <el-table-column
              prop="auditMessage"
              label="注销原因"
              align="center"
              show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
              align="center"
              width="220"
              label="操作"
              fixed="right"
            >
              <template slot-scope="scope">
                <div style="display: flex; justify-content: center">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="注销备案"
                    placement="top"
                  >
                    <el-button
                      size="mini"
                      type="danger"
                      circle
                      @click="logOff(scope.row)"
                      icon="el-icon-key"
                    ></el-button>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="searchParams.curPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="searchParams.limit"
              layout="total,sizes, prev, pager, next"
              :total="total"
            >
            </el-pagination>
          </div>
        </div>
      </el-tab-pane> -->
    </el-tabs>

    <el-dialog title="不予备案" :visible.sync="dialogFormVisible">
      <el-form :model="form">
        <el-form-item label="不予备案理由">
          <el-input
            v-model="form.reason"
            autocomplete="off"
            type="textarea"
            :rows="2"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="
            dialogFormVisible = false;
            form.reason = null;
          "
          >取 消</el-button
        >
        <el-button type="primary" @click="confirmBack">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="补正备案材料" :visible.sync="dialogFormVisible2">
      <el-form :model="form2">
        <el-form-item label="补正内容">
          <el-input
            v-model="form2.content"
            autocomplete="off"
            type="textarea"
            :rows="2"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="
            dialogFormVisible2 = false;
            form2.content = null;
          "
          >取 消</el-button
        >
        <el-button type="primary" @click="confirmFix">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="注销备案" :visible.sync="logOffVisible">
      <el-form :model="logOffForm">
        <el-form-item label="注销备案理由">
          <el-select
            v-model="logOffForm.reason"
            placeholder="请选择注销备案理由"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="
            logOffVisible = false;
            logOffForm.reason = null;
          "
          >取 消</el-button
        >
        <el-button type="primary" @click="confirmOff">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRecordList, diagnosisReview, getUserSession,getDictByType } from "@/api/index.js";

export default {
  data() {
    return {
      activeName: "first",
      username: "",
      form: {
        reason: "",
      },
      form2: {
        content: "",
      },
      logOffForm: {
        reason: "",
      },
      options: [
        {
          label: "已不具备备案条件",
          value: "已不具备备案条件",
        },
        {
          label: "不再从事职业健康检查工作",
          value: "不再从事职业健康检查工作",
        },
      ],
      logOffVisible: false,
      dialogFormVisible: false,
      dialogFormVisible2: false,
      currentId: "",
      tableData: [],
      searchParams: {
        recordType: "",
        unitNature: "",
        institutionName: "",
        administerAreaCode: "",
        limit: 10,
        curPage: 1,
      },
      total: 0,
      institutionRecordNatureList:[], //机构性质
    };
  },

  methods: {
    // 获取数据字典
    async getDictByType(){
      const res = await getDictByType({dictType:'institution_record_nature'})
      this.institutionRecordNatureList = res.data.data || []
    },
    institutionNatureFormatter(row, column) {
      const item = this.institutionRecordNatureList.find(el => el.dictCode === row[column.property]);
      return item ? item.dictLabel : '';
    },
    async pass(row) {
      const res = await diagnosisReview({
        id: row.id,
        auditStatus: 10,
        remark: null,
        reviewer: this.username,
      });
      if (res.status === 200) {
        this.$message.success("已通过");
        const res2 = await getRecordList(this.searchParams);
        this.tableData = res.data.data.list; 
        this.searchParams.curPage = res.data.data.pageNum     
        this.searchParams.limit = res.data.data.pageSize           
        this.total = res.data.data.total;
      }
    },
    async back(row) {
      this.currentId = row.id;
      this.dialogFormVisible = true;
    },
    async confirmBack() {
      const res = await diagnosisReview({
        id: this.currentId,
        auditStatus: -10,
        remark: this.form.reason,
        reviewer: this.username,
      });
      if (res.status === 200) {
        this.$message.success("已驳回");
        this.dialogFormVisible = false;
        const res2 = await getRecordList(this.searchParams);
        this.tableData = res.data.data.list; 
        this.searchParams.curPage = res.data.data.pageNum     
        this.searchParams.limit = res.data.data.pageSize           
        this.total = res.data.data.total;
      }
    },

    fix(row) {
      this.currentId = row.id;
      this.dialogFormVisible2 = true;
    },
    async confirmFix() {
      const res = await diagnosisReview({
        id: this.currentId,
        auditStatus: 2,
        remark: this.form2.content,
        reviewer: this.username,
      });

      if (res.status === 200) {
        this.$message.success("已告知");
        this.dialogFormVisible2 = false;
        const res2 = await getRecordList(this.searchParams);
        this.tableData = res.data.data.list; 
        this.searchParams.curPage = res.data.data.pageNum     
        this.searchParams.limit = res.data.data.pageSize           
        this.total = res.data.data.total;
      }
    },

    // logOff(row) {
    //   this.currentId = row._id;
    //   this.logOffVisible = true;
    // },
    // async confirmOff() {
    //   const res = await checkupLogoff({
    //     id: this.currentId,
    //     reason: this.logOffForm.reason,
    //   });
    //   if (res.status === 200) {
    //     this.$message.success("已告知");
    //     this.logOffVisible = false;
    //     const res2 = await getRecordList({
    //       limit: 10,
    //       curPage: 1,
    //     });
    //     this.tableData = res2.data.res;
    //     this.total = res2.data.total;
    //   }
    // },
    async view(row) {
      this.$router.push({
        name: "detail",
        params: {
          id: row.id,
          institutionRecordNatureList:JSON.stringify(this.institutionRecordNatureList)
        },
      });
    },
    async handleQuery() {
      this.searchParams.curPage = 1;
      const res = await getRecordList(this.searchParams);
      this.tableData = res.data.data.list; 
      this.searchParams.curPage = res.data.data.pageNum     
      this.searchParams.limit = res.data.data.pageSize           
      this.total = res.data.data.total;
    },
    async handleReset(){
      this.searchParams = {
        recordType: "",
        unitNature: "",
        institutionName: "",
        limit: 10,
        curPage: 1,
      }
      const res = await getRecordList(this.searchParams);
      this.tableData = res.data.data.list; 
      this.searchParams.curPage = res.data.data.pageNum     
      this.searchParams.limit = res.data.data.pageSize           
      this.total = res.data.data.total;
    },
    async handleSizeChange(val) {
      this.searchParams.limit = val;
      const res = await getRecordList(this.searchParams);
      this.tableData = res.data.data.list; 
      this.searchParams.curPage = res.data.data.pageNum     
      this.searchParams.limit = res.data.data.pageSize           
      this.total = res.data.data.total;
    },
    async handleCurrentChange(val) {
      this.searchParams.curPage = val;
      const res = await getRecordList(this.searchParams);
      this.tableData = res.data.data.list; 
      this.searchParams.curPage = res.data.data.pageNum     
      this.searchParams.limit = res.data.data.pageSize           
      this.total = res.data.data.total;
    },
  },
  async created() {
    const user = await getUserSession();
    this.username = user.data.userInfo.userName;
    this.searchParams.administerAreaCode = user.data.userInfo.area_code 
    if(this.searchParams.administerAreaCode){
      const res = await getRecordList(this.searchParams);
      this.tableData = res.data.data.list; 
      this.searchParams.curPage = res.data.data.pageNum     
      this.searchParams.limit = res.data.data.pageSize           
      this.total = res.data.data.total;
    }
    this.getDictByType()
  },
};
</script>

<style lang="scss" scoped>
.serveEnterprise {
  padding: 0 15px;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}
.num {
  font-size: 18px;
  color: #1691e0;
  font-weight: bolder;
}
.table {
  margin-top: 10px;
}
.pagination {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
::v-deep .el-table__fixed,
::v-deep .el-table__fixed-right {
  height: 100% !important;
}

.excel-upload-input {
  display: none;
  z-index: -9999;
}
</style>
