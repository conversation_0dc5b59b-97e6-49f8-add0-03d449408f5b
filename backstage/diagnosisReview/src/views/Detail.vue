<template>
  <div class="detail">
    <el-button
      type="primary"
      size="small"
      icon="el-icon-back"
      @click="$router.go(-1)"
      >返回</el-button
    >

    <el-tabs type="border-card" style="margin-top: 10px">
      <el-tab-pane label="备案信息">
        <span slot="label"><i class="el-icon-info"></i> 备案信息</span>
        <table>
          <tr>
            <th>机构名称</th>
            <td>{{ info.institutionName }}</td>
            <th>单位性质</th>
            <td>{{ institutionNatureFn(info.institutionNature)}}</td>
            <th>法定代表人</th>
            <td>{{ info.legalPerson }}</td>
          </tr>
          <tr>
            <th>职务</th>
            <td>{{ info.legalPersonDuty }}</td>
            <th>联系人</th>
            <td>{{ info.contactPerson }}</td>
            <th>联系方式</th>
            <td>{{ info.contactPhone }}</td>
          </tr>
          <tr>
            <th>单位地址</th>
            <td>{{ info.address }}</td>
            <th>邮箱</th>
            <td>{{ info.email }}</td>
            <th>职工总数</th>
            <td>{{ info.staffNum }}</td>
          </tr>

          <tr>
            <th>从事职业病诊断执业医师人数</th>
            <td>{{ info.practicingDoctorNum }}</td>
            <th>取得职业病诊断资格人数</th>
            <td>{{ info.qualificationDoctorNum }}</td>
            <th>医疗机构执业许可证编号</th>
            <td>{{ info.practiceLicenseCode }}</td>
          </tr>

          <tr>
            <th>发证日期</th>
            <td>{{ info.licenseIssueDate }}</td>
            <th>医疗机构执业许可证发证机关</th>
            <td>{{ info.licenseIssueInstitution }}</td>
          </tr>
        </table>
      </el-tab-pane>
      <el-tab-pane label="人员信息">
        <span slot="label"><i class="el-icon-user-solid"></i> 人员信息</span>
        <el-table
          style="width: 100%; margin-top: 20px"
          :data="personsData"
          border
          :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333333' }"
        >
          <el-table-column prop="job" label="岗位"> </el-table-column>
          <el-table-column prop="name" label="姓名" min-width="110">
          </el-table-column>
          <el-table-column
            prop="professionalTitle"
            label="职称"
            min-width="110"
          >
          </el-table-column>
          <el-table-column
            prop="graduatedUniversity"
            label="毕业院校"
            width="110"
          >
          </el-table-column>
          <el-table-column
            prop="graduatedDate"
            label="毕业日期"
            min-width="120"
          >
          </el-table-column>
          <el-table-column prop="department" label="科室/专业" min-width="80">
          </el-table-column>
          <el-table-column prop="workYear" label="专业工作年限" min-width="80">
          </el-table-column>
          <el-table-column
            prop="practiceCertificateCode"
            label="医师/护士执业证书编号"
            min-width="80"
          >
          </el-table-column>
          <el-table-column
            prop="diagnosticQualificationCertificateCode"
            label="职业病诊断医师资格证书号"
            min-width="80"
          >
          </el-table-column>

          <el-table-column
            label="医师/护士执业证明文件"
            min-width="80"
            align="center"
          >
            <template slot-scope="scope">
              <div>
                <el-link
                  type="primary"
                  v-for="item in scope.row.practiceDocumentationList"
                  :key="item.id"
                  :href="item.fileUrl" 
                  target="_blank"
                >
                  {{ item.fileName }}
                </el-link>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            label="职业病诊断医师资格证明文件"
            min-width="80"
            align="center"
          >
            <template slot-scope="scope">
              <div>
                <el-link
                  type="primary"
                  v-for="item in scope.row.diagnosticQualificationDocumentationList"
                  :key="item.id"
                  :href="item.fileUrl" 
                  target="_blank"
                >
                 {{ item.fileName }}
                </el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <el-tab-pane label="设备列表">
        <span slot="label"><i class="el-icon-s-tools"></i> 设备列表</span>
        <el-table
          style="width: 100%; margin-top: 20px"
          :data="deviceData"
          border
          :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333333' }"
        >
          <el-table-column
            prop="name"
            label="名称"
            :show-overflow-tooltip="true"
            min-width="110"
          >
          </el-table-column>
          <el-table-column prop="model" label="型号" min-width="110">
          </el-table-column>
          <el-table-column prop="manufacturer" label="生产厂家" min-width="110">
          </el-table-column>
          <el-table-column
            prop="purpose"
            label="用途"
            :show-overflow-tooltip="true"
            min-width="80"
          >
          </el-table-column>
          <el-table-column
            prop="quantity"
            label="数量"
            :show-overflow-tooltip="true"
            min-width="80"
          >
          </el-table-column>
          <el-table-column
            prop="performanceStatus"
            label="性能状况"
            min-width="80"
          >
          </el-table-column>
          <el-table-column
            prop="purchasingDate"
            label="购机时间"
            min-width="80"
          >
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="附件列表">
        <span slot="label"><i class="el-icon-document"></i> 附件列表</span>
        <el-collapse v-model="activeNames">
          <el-collapse-item title="职业病诊断质量管理体系文件" name="1">
            <div v-if="files.qualityFile.length > 0">
              <el-link v-for="item in files.qualityFile" :key="item.id" :href="item.fileUrl" target="_blank">
                {{ item.fileName }}
              </el-link>
            </div>

            <div v-else>
              <span style="color: #909399">暂无数据</span>
            </div>
          </el-collapse-item>
          <el-collapse-item title="职业病诊断详细报告能力情况" name="2">
            <div v-if="files.diagnosticAbilityReport.length > 0">
              <el-link
                v-for="item in files.diagnosticAbilityReport"
                :key="item.id"
                :href="item.fileUrl" 
                target="_blank"
              >
                {{ item.fileName }}
              </el-link>
            </div>

            <div v-else>
              <span style="color: #909399">暂无数据</span>
            </div>
          </el-collapse-item>
          <el-collapse-item
            title="	职业病诊断场所、候检场所和检验室设置情况"
            name="3"
          >
            <div v-if="files.diagnosticPlace.length > 0">
              <el-link v-for="item in files.diagnosticPlace" :key="item.id" :href="item.fileUrl" target="_blank">
                {{ item.fileName }}
              </el-link>
            </div>

            <div v-else>
              <span style="color: #909399">暂无数据</span>
            </div>
          </el-collapse-item>
          <el-collapse-item
            title="备案登记表"
            name="4"
          >
            <div v-if="files.registrationForm.length > 0">
              <el-link v-for="item in files.registrationForm" :key="item.id" :href="item.fileUrl" target="_blank">
                {{ item.fileName }}
              </el-link>
            </div>

            <div v-else>
              <span style="color: #909399">暂无数据</span>
            </div>
          </el-collapse-item>
          <el-collapse-item
            title="营业执照"
            name="5"
          >
          <div v-if="files.businessLicense.length > 0">
            <el-link v-for="item in files.businessLicense" :key="item.id" :href="item.fileUrl" target="_blank">
              {{ item.fileName }}
            </el-link>
          </div>

          <div v-else>
            <span style="color: #909399">暂无数据</span>
          </div>
        </el-collapse-item>
        <el-collapse-item
          title="医疗机构执业许可证"
          name="6"
        >
          <div v-if="files.practiceLicense.length > 0">
            <el-link v-for="item in files.practiceLicense" :key="item.id" :href="item.fileUrl" target="_blank">
              {{ item.fileName }}
            </el-link>
          </div>

          <div v-else>
            <span style="color: #909399">暂无数据</span>
          </div>
        </el-collapse-item>
        </el-collapse>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getRecordDetail } from "@/api/index.js";

export default {
  data() {
    return {
      personsData: [],
      deviceData: [],
      info: {},
      files: {},
      institutionRecordList:[]
    };
  },

  methods: {
    institutionNatureFn(code) {
      const item = this.institutionRecordList.find(el => el.dictCode === code);
      return item ? item.dictLabel : '';
    },
    openFile(){
      window.open(url, "_blank");
    }
  },
  async created() {
    const res = await getRecordDetail({
      id: this.$route.params.id,
    });
    this.info = res.data.data;
    this.personsData = res.data.data.personList;
    this.deviceData = res.data.data.deviceList;
    this.files = res.data.data.file;
    this.institutionRecordList = JSON.parse(this.$route.params.institutionRecordNatureList)    
  },

};
</script>

<style lang="scss" scoped>
.detail {
  padding: 10px 15px;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}
.num {
  font-size: 18px;
  color: #1691e0;
  font-weight: bolder;
}
.table {
  margin-top: 10px;
}
.pagination {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
::v-deep .el-table__fixed,
::v-deep .el-table__fixed-right {
  height: 100% !important;
}

.excel-upload-input {
  display: none;
  z-index: -9999;
}

table {
  width: 100%;
  border-collapse: collapse;
  font-family: Arial, sans-serif;
}
th,
td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}
th {
  background-color: #f2f2f2;
  font-weight: bold;
}
td {
  background-color: #fff;
}
.highlight {
  background-color: #e6f7ff;
}
</style>
