import request from '@root/publicMethods/request';

export function getUserSession() {
  return request({
    url: '/manage/getUserSession',
    method: 'get',
  });
}

export function getRecordList(params) {
  return request({
    url: '/manage/recordManage/getDiagnosisRecordList',
    params,
    method: 'get',
  });
}

export function getRecordDetail(params) {
  return request({
    url: '/manage/recordManage/diagnosisDetail',
    params,
    method: 'get',
  });
}

// checkupPass 体检机构备案通过
export function diagnosisReview(data) {
  return request({
    url: '/manage/recordManage/diagnosisPass',
    data,
    method: 'post',
  });
}

export function getDictByType(params) {
  return request({
    url: '/manage/recordManage/getDictByType',
    method: 'get',
    params,
  });
}
