import request from '@root/publicMethods/request';
// 获取地区列表
export function getAddressList(params) {
  return request({
    url: '/api/adminorgGov/address/list',
    params,
    method: 'get',
  });
}

// 获取门户机构类别数据字典
export function listByKey(params) {
  return request({
    url: '/manage/kqySupervise/listByKey',
    method: 'get',
    params,
  });
}

// 获取地区和职业病诊断类别数据字典
export function getDictDataByType(params) {
  return request({
    url: '/manage/diagnosis/getDictData',
    params,
    method: 'get',
  });
}
// 获取用户登录信息
export function getUserSession(params) {
  return request({
    url: '/manage/getUserSession',
    method: 'get',
    params,
  });
}

// 获取机构类型列表
export function getInstitutionList(params) {
  return request({
    url: '/manage/techVendorManagement/getList',
    method: 'get',
    params,
  });
}

// 获取人才库列表
export function getTalentPoolList(params) {
  return request({
    url: '/manage/talentPool/getList',
    method: 'get',
    params,
  });
}

// 获取联络员列表
export function getContactOfficerList(params) {
  return request({
    url: '/manage/contactOfficer/getList',
    method: 'get',
    params,
  });
}


// 获取跨区域开设的工厂用人单位的职业健康信息共享
export function getEnterpriseList(params) {
  return request({
    url: '/manage/crossRegionHealthShare/getEnterpriseList',
    method: 'get',
    params,
  });
}

// 放射卫生监测数据共享
export function projectsList(data) {
  return request({
    url: '/manage/adminRadiateProjects/list',
    method: 'post',
    data,
  });
}

// 卫生监督执法数据共享
export function getEnforcement(params) {
  return request({
    url: '/manage/crossRegionLawEnforcementShare/getlist',
    method: 'get',
    params,
  });
}

// 职业病、重点职业病监测数据共享
export function getDiseasePatientList(params) {
  return request({
    url: '/manage/crossRegionDiseaseData/getList',
    method: 'get',
    params,
  });
}

// 职业危害因素监测数据共享
export function getProjectList(data) {
  return request({
    url: '/manage/adminServiceProject/getProjects',
    data,
    method: 'post',
  });
}


// 职业卫生专家库数据共享
export function getExpertList(params) {
  return request({
    url: '/manage/expert',
    params,
    method: 'get',
  });
}


// 第三方服务机构跨区域业务数据共享/职业健康监护数据
export function getDistrictList(data) {
  return request({
    url: '/manage/eHealthRecord/getEHealthRecordList',
    data,
    method: 'post',
  });
}

// 第三方服务机构跨区域业务数据共享/职业病诊断数据
export function getEmployerList(params) {
  return request({
    url: '/manage/diagnosis/getDiaList',
    method: 'get',
    params,
  });
}

// 第三方服务机构跨区域业务数据共享/职业病鉴定数据
export function getIdentifyList(params) {
  return request({
    url: '/manage/identify/getIdentificationList',
    method: 'get',
    params,
  });
}

// 监督执法机构联合情况情况
export function getJointEnforcementList(params) {
  return request({
    url: '/manage/jointEnforcement/getlist',
    method: 'get',
    params,
  });
}
// 职业病人跨区域情况
export function getFollowUpList(params) {
  return request({
    url: '/manage/kqyFollowUp/getList',
    method: 'get',
    params,
  });
}
// 第三方服务机构跨区域服务监管情况
export function getSuperviseList(params) {
  return request({
    url: '/manage/kqySupervise/getList',
    method: 'get',
    params,
  });
}
// 职业病诊断、鉴定跨区域用人单位调查情况
export function getInvestigationList(params) {
  return request({
    url: '/manage/kqyInvestigation/getList',
    method: 'get',
    params,
  });
}
// 职业健康检查质控中心业务协同情况
export function getCheckCenterList(params) {
  return request({
    url: '/manage/kqycheckCenter/getList',
    method: 'get',
    params,
  });
}

// 职业健康诊断质控中心业务协同情况
export function getDiagnosisCenterList(params) {
  return request({
    url: '/manage/kqydiagnosisCenter/getList',
    method: 'get',
    params,
  });
}
// 职业病鉴定质控中心业务协同情况
export function getIdentificationCenterList(params) {
  return request({
    url: '/manage/kqyIdentificationCenter/getList',
    method: 'get',
    params,
  });
}

