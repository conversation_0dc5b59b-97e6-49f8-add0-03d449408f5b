<template>
  <div class="content">
    <el-tabs v-model="activeTab">
      <!-- 1. 跨区域化职业健康机构情况 -->
      <el-tab-pane label="机构情况统计" name="institutions">
        <!-- <Institutions></Institutions> -->
      </el-tab-pane>
      <!-- 2.人员保障情况 -->
      <el-tab-pane label="人员保障情况" name="personnel">
        <!-- <Personnel></Personnel> -->
      </el-tab-pane>
      <!-- 3.数据互联互通 -->
      <el-tab-pane label="数据互联互通" name="dataSharing">
        <!-- <DataSharing></DataSharing> -->
      </el-tab-pane>
      <!-- 4.业务协同情况 -->
      <el-tab-pane label="业务协同情况" name="business">
        <!-- <Business></Business> -->
      </el-tab-pane>
    </el-tabs>
    <div v-if="activeTab == 'institutions'"><Institutions></Institutions></div>
    <div v-if="activeTab == 'personnel'"><Personnel></Personnel></div>
    <div v-if="activeTab == 'dataSharing'"><DataSharing></DataSharing></div>
    <div v-if="activeTab == 'business'"> <Business></Business></div>
  </div>
</template>

<script>
import Institutions from '../components/Institutions/index.vue'
import Personnel from '../components/Personnel/index.vue'
import DataSharing from '../components/DataSharing/index.vue'
import Business from '../components/Business/index.vue'
export default {
  components: {
    Institutions,Personnel,DataSharing,Business
  },
  data() {
    return {
      activeTab: "institutions"
    };
  },
  created() {
  },
  methods: {
    handleTabChange(tabName) {
      this.activeTab = tabName;
    }
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 10px 20px;
}
</style>