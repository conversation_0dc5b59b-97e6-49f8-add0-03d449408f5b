<template>
  <div>
    <div>
        <el-select v-model="timeway" placeholder="请选择时间筛选方式" @change="changeWay">
          <el-option label="时间点" :value="1"></el-option>
          <el-option label="时间段" :value="2"></el-option>
        </el-select>
        &nbsp;
        <el-date-picker v-show="timeway === 1" v-model="query.timePoint" value="yyyy-MM-dd"
          value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
        </el-date-picker>
        &nbsp;
        <el-date-picker v-show="timeway === 2" v-model="dateRange" type="daterange"value="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']"  range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
        &nbsp;
        <!-- <el-cascader class="custom-cascader" v-model="search.areaCode" :props="districtListProps" clearable collapse-tags
          ref="regAddCas" placeholder="请选择地区">
        </el-cascader> -->
        &nbsp;
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="onReset">重置</el-button>
    </div>
    <TitleTag titleName="跨区域开设的工厂用人单位的职业健康信息共享情况"></TitleTag>
    <el-table :data="dataSharingStats" border style="width: 100%;" 
    header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    :summary-method="getSummaries"
    show-summary>
      <el-table-column type="index" label="序号" width="80px" align="center"></el-table-column>
      <el-table-column prop="name" label="区域"></el-table-column>
      <el-table-column prop="count" label="用人单位数量"></el-table-column>
    </el-table>
    <TitleTag titleName="卫生监督执法数据共享情况"></TitleTag>
    <el-table :data="jdzfStats" border style="width: 100%;" 
    header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"    
    :summary-method="getSummaries"
    show-summary>
      <el-table-column type="index" label="序号" width="80px" align="center"></el-table-column>
      <el-table-column prop="name" label="区域"></el-table-column>
      <el-table-column prop="count" label="执法机构数量"></el-table-column>
    </el-table>
    <TitleTag titleName="职业病、重点职业病监测数据共享情况"></TitleTag>
    <el-table :data="zybStats" border style="width: 100%;" 
    header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    :summary-method="getSummaries"
    show-summary>
      <el-table-column type="index" label="序号" width="80px" align="center"></el-table-column>
      <el-table-column prop="dictLabel" label="职业病种类"></el-table-column>
      <el-table-column prop="count" label="患者数量"></el-table-column>
    </el-table>
    <TitleTag titleName="职业危害因素监测数据共享情况"></TitleTag>
    <el-table :data="whysStats" border style="width: 100%;" 
    header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    :summary-method="getSummaries"
    show-summary>
      <el-table-column type="index" label="序号" width="80px" align="center"></el-table-column>
      <el-table-column prop="name" label="区域"></el-table-column>
      <el-table-column prop="count" label="用人单位数"></el-table-column>
    </el-table>
    <TitleTag titleName="放射卫生监测数据共享情况"></TitleTag>
    <el-table :data="fswsStats" border style="width: 100%;" 
    header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    :summary-method="getSummaries"
    show-summary>
      <el-table-column type="index" label="序号" width="80px" align="center"></el-table-column>
      <el-table-column prop="name" label="区域"></el-table-column>
      <el-table-column prop="count" label="用人单位数"></el-table-column>
    </el-table>
    <TitleTag titleName="第三方服务机构跨区域业务数据共享情况/职业健康监护数据"></TitleTag>
    <el-table :data="jkjhStats" border style="width: 100%;" 
    header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    :summary-method="getSummaries"
    show-summary>
      <el-table-column type="index" label="序号" width="80px" align="center"></el-table-column>
      <el-table-column prop="name" label="区域"></el-table-column>
      <el-table-column prop="count" label="患者数量"></el-table-column>
    </el-table>
    <TitleTag titleName="第三方服务机构跨区域业务数据共享情况/职业病诊断数据"></TitleTag>
    <el-table :data="zdStats" border style="width: 100%;"
     header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
     :summary-method="getSummaries"
     show-summary>
      <el-table-column type="index" label="序号" width="80px" align="center"></el-table-column>
      <el-table-column prop="name" label="区域"></el-table-column>
      <el-table-column prop="count" label="患者数量"></el-table-column>
    </el-table>
    <TitleTag titleName="第三方服务机构跨区域业务数据共享情况/职业病鉴定数据"></TitleTag>
    <el-table :data="jdStats" border style="width: 100%;" 
    header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    :summary-method="getSummaries"
    show-summary>
      <el-table-column type="index" label="序号" width="80px" align="center"></el-table-column>
      <el-table-column prop="name" label="区域"></el-table-column>
      <el-table-column prop="count" label="患者数量"></el-table-column>
    </el-table>
    <TitleTag titleName="职业卫生专家库数据共享情况"></TitleTag>
    <el-table :data="zjkStats" border style="width: 100%;" 
    header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    :summary-method="getSummaries"
    show-summary>
      <el-table-column type="index" label="序号" width="80px" align="center"></el-table-column>
      <el-table-column prop="name" label="专家级别"></el-table-column>
      <el-table-column prop="count" label="专家数量"></el-table-column>
    </el-table>
  </div>
</template>
<script>
import TitleTag from "../TitleTag.vue";
import {getUserSession,getDictDataByType,getAddressList,getEnterpriseList,projectsList,getEnforcement,getDiseasePatientList,getProjectList,getExpertList,getDistrictList,getEmployerList,getIdentifyList} from '@/api/index'
export default {
  components: {
    TitleTag,
  },
  data() {
    return {
      // 3.数据互联互通（新疆数据）
      dataSharingStats: [],
      jdzfStats: [],
      zybStats: [],
      whysStats:[],
      fswsStats:[],
      jkjhStats:[],
      zdStats:[],
      jdStats:[],
      zjkStats:[],
      query:{
        pageNum: 1,
        pageSize: 9999999,
        timePoint: '',
        startDate:'',
        endDate:'',
      },
      dateRange: [],
      timeway: 1,
      addressList:[],
      diseaseList:[],
      categoryList:[
        {
          name:'兵团级',
          value:'1'
        },
        {
          name:'师市级',
          value:'2'
        },
        {
          name:'团镇级',
          value:'3'
        }
      ],
      userInfo:{}
    }
  },
  async created() {
    const res =  await getUserSession()
    this.userInfo = res.data && res.data.userInfo || {}
    getDictDataByType({dictType:'occupational_disease'}).then(res=>{
      this.diseaseList = res.data
      if(this.diseaseList && this.diseaseList.length){
      this.getzybStats()
      }
    })
    const {data}  = await getAddressList({level:0})
    this.addressList = data && data.docs || []
    if (this.userInfo.regAdd && this.userInfo.area_code) {
        this.addressList.push({
          name: this.userInfo.regAdd[this.userInfo.regAdd.length -1],
          id: this.userInfo.area_code
        });
      }
    if(this.addressList && this.addressList.length){
      this.getSharingStats();
      this.getjdzfStats();
      this.getwhysStats()
      this.getjkjhStats()
      this.getzjkStats()
      this.getfswsStats()
      this.getzdStats()
      this.getjdStats()
    }
  },
  methods: {
    async getSharingStats() {
      try {
        const { data } = await getEnterpriseList({page:1,size:999999})
        const institutionList = data.list || []; 
        this.dataSharingStats = this.addressList.map(item => {
          const matchedInstitutions = institutionList.filter(
            inst => inst.districtRegAdd && inst.districtRegAdd.includes(item.name)
          );
          // 统计数量
          const count = matchedInstitutions.length || 0;
          return {
            ...item, 
            count, 
          };
        });

      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.dataSharingStats = [];
      }
    },
    async getjdzfStats (){
      try {
        const { data } = await getEnforcement({page:1,pageSize:999999})
        const institutionList = data.list || []; 
        this.jdzfStats = this.addressList.map(item => {
          const matchedInstitutions = institutionList.filter(
            inst => inst.zonecode && inst.zonecode.includes(item.id) 
          );
          const count = matchedInstitutions.length || 0;
          return {
            ...item, 
            count,
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.jdzfStats = []; 
      }
    },
    async getzybStats (){
      try {
        const { data } = await getDiseasePatientList({pageNum:1,pageSize:999999})
        const institutionList = data && data.list || []; 
        this.zybStats = this.diseaseList.map(item => {
          const matchedInstitutions = institutionList.filter(inst => {
            const diseaseCodes = inst.diseaseList && inst.diseaseList.map(el => el.diseaseCategoryCode) || []
            return diseaseCodes.includes(item.dictCode)
          })
          const count = matchedInstitutions.length || 0;
          return {
            ...item,
            count,
          }
        })
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.zybStats = []; 
      }
    },
    async getwhysStats(){
      try {
        const { data } = await getProjectList({curPage:1,limit:999999})
        const institutionList = data || []; 
        this.whysStats = this.addressList.map(item => {
          const matchedInstitutions = institutionList.filter(
            inst => inst.districtRegAdd && inst.districtRegAdd.includes(item.name)
          );
          const count = matchedInstitutions.length || 0;
          return {
            ...item, 
            count,
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.whysStats = []; 
      }
    },
    async getjkjhStats(){
      try {
        const {data}  = await getDistrictList({pageNum:1,pageSize:999999})
        const institutionList = data && data.list || []; 
        this.jkjhStats = this.addressList.map(item => {
          const matchedInstitutions = institutionList.filter(
            inst => inst.enterpriseDistrict && inst.enterpriseDistrict.includes(item.name)
          );
          const count = matchedInstitutions.length || 0;
          return {
            ...item, 
            count,
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.jkjhStats = []; 
      }
    },
    async getzjkStats(){
      try {
        const { data } = await getExpertList({curPage:1,pageSize:999999})
        const institutionList = data && data.list || []; 
        this.zjkStats = this.categoryList.map(item => {
          const matchedInstitutions = institutionList.filter(
            inst => inst.level && inst.level.includes(item.name)
          );
          const count = matchedInstitutions.length || 0;
          return {
            ...item, 
            count,
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.zjkStats = []; 
      }
    },
    async getfswsStats(){
      try {
        const { data } = await projectsList({curPage: 1, limit: 999999})
        const institutionList = data || []
        this.fswsStats = this.addressList.map(item => {
          const matchedInstitutions = institutionList.filter(inst => {
            return inst.workPlaces && inst.workPlaces.some(workPlace => {
              if (workPlace.workAdd && workPlace.workAdd.includes(item.id)) {
                return true;
              }
              return false;
            });
          });
          const count = matchedInstitutions.length || 0;
          return {
            ...item,
            count,
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.fswsStats = []; 
      }
    },
    async getzdStats(){
      try {
        const { data } = await getEmployerList({pageNum: 1, pageSize: 999999})
        const institutionList = data.data.list || []; 
        this.zdStats = this.addressList.map(item => {
          const matchedInstitutions = institutionList.filter(
            inst => inst.institutionAreaCode && inst.institutionAreaCode.includes(item.id)
          );
          const count = matchedInstitutions.length || 0;
          return {
            ...item, 
            count,
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.zdStats = []; 
      }
    },
    async getjdStats(){
      try {
        const { data } = await getIdentifyList({pageNum: 1, pageSize: 999999})
        const institutionList = data && data.list || []; 
        this.jdStats = this.addressList.map(item => {
          const matchedInstitutions = institutionList.filter(
            inst => inst.institutionAreaCode && inst.institutionAreaCode.includes(item.id)
          );
          const count = matchedInstitutions.length || 0;
          return {
            ...item, 
            count,
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.jdStats = []; 
      }
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总次数';
          return;
        }
        if (index === 1) {
          sums[index] = '';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] += '';
        } else {
          sums[index] = '';
        }
      });

      return sums;
    },
    onSearch(){
      this.getSharingStats();
      this.getjdzfStats();
      this.getwhysStats()
      this.getjkjhStats()
      this.getzjkStats()
      this.getfswsStats()
      this.getzdStats()
      this.getjdStats()
    },
    onReset(){
    this.query = {
      pageNum: 1,
      pageSize: 9999999,
      timePoint: '',
      startDate:'',
      endDate:'',
    },
      this.dateRange= []
      this.$forceUpdate()
      this.getSharingStats();
      this.getjdzfStats();
      this.getwhysStats()
      this.getjkjhStats()
      this.getzjkStats()
      this.getfswsStats()
      this.getzdStats()
      this.getjdStats()
    },
    changeWay(){
      this.query.timePoint = ''
      this.query.startDate = ''
      this.search.endDate = ''
      this.dateRange=[]
    }
  },
};
</script>
<style lang="scss" scoped>

</style>