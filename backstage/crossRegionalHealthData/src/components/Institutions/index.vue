<template>
  <div>
    <div>
      <div>
        <el-select v-model="timeway" placeholder="请选择时间筛选方式" @change="changeWay">
          <el-option label="时间点" :value="1"></el-option>
          <el-option label="时间段" :value="2"></el-option>
        </el-select>
        &nbsp;
        <el-date-picker v-show="timeway === 1" v-model="query.timePoint" value="yyyy-MM-dd"
          value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
        </el-date-picker>
        &nbsp;
        <el-date-picker v-show="timeway === 2" v-model="dateRange" type="daterange"value="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']"  range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
        &nbsp;
        <!-- <el-cascader class="custom-cascader" v-model="search.areaCode" :props="districtListProps" clearable collapse-tags
          ref="regAddCas" placeholder="请选择地区">
        </el-cascader> -->
        &nbsp;
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="onReset">重置</el-button>
    </div>
      <TitleTag titleName="机构分布情况"></TitleTag>
    <el-table :data="institutionTable" border style="width: 100%;" 
      header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
      :summary-method="getSummaries"
      show-summary>
        <el-table-column type="index" label="序号" width="50px" align="center"></el-table-column>
        <el-table-column prop="name" label="机构类型" min-width="80px" show-overflow-tooltip></el-table-column>
        <el-table-column prop="areaNames" label="区域分布" min-width="150px" show-overflow-tooltip >
          <template slot-scope="scope">
            {{ scope.row.areaNames.join(',') }}
          </template>
        </el-table-column>
        <el-table-column prop="sum" label="机构数量" min-width="80px" align="center"></el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import TitleTag from "../TitleTag.vue";
import {listByKey,getInstitutionList} from '@/api/index'
export default {
  components: {
    TitleTag,
  },
  data() {
    return {
      // 1.机构情况统计（新疆数据）
      institutionTable: [ ],
      query:{
        orgTypes: "",
        name: "",
        creditCode: "",
        zonecode: "",
        pageNum: 1,
        pageSize: 999999,
        timePoint: '',
        startDate:'',
        endDate:'',
      },
      institutionStats:[],
      dateRange: [],
      timeway: 1,
    };
  },
  async created() {
    try {
      const res = await listByKey({key: 'org_type'})
      if (res.data.length) {
        this.institutionStats = res.data.filter(item => item.value !== '300')
        await this.getInstitution()
      } else {
        this.institutionStats = []
      }
    } catch (error) {
      this.institutionStats = []
    }
  },
  methods: {
    async getInstitution() {
      try {
        const { data } = await getInstitutionList(this.query); // 接口返回的机构列表数据
        const institutionList = data.list || []; // 机构列表数组

        // 合并数据：为每种机构类型添加数量和分布区域
        this.institutionTable = this.institutionStats.map(item => {
          // 筛选出包含当前机构类型的机构（orgTypes数组中包含item.value）
          const matchedInstitutions = institutionList.filter(
            inst => inst.orgTypes && inst.orgTypes.includes(item.value)
          );

          // 统计数量
          const sum = matchedInstitutions.length || '0';

          // 收集去重后的分布区域
          const areaNames = [...new Set(
            matchedInstitutions.map(inst => inst.areaName || '未知区域')
          )];

          return {
            ...item, // 保留原有的name和value
            sum, // 该类型机构的数量
            areaNames // 该类型机构的分布区域（去重后）
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.institutionTable = []; // 出错时置空，避免渲染异常
      }
    },
    getSummaries(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '总计';
            return;
          }
          if (index === 1 || index === 2) {
            sums[index] = '';
            return;
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
            sums[index] += '';
          } else {
            sums[index] = '';
          }
        });

        return sums;
      },
    onSearch(){
      this.getInstitution()
     },
    onReset(){
    this.query = {
      orgTypes: "",
      name: "",
      creditCode: "",
      zonecode: "",
      pageNum: 1,
      pageSize: 999999,
      timePoint: '',
      startDate:'',
      endDate:'',
    },
      this.dateRange= []
      this.$forceUpdate()
      this.getInstitution()
    },
    changeWay(){
      this.query.timePoint = ''
      this.query.startDate = ''
      this.query.endDate = ''
      this.dateRange=[]
    }
  },
};
</script>
<style lang="scss" scoped>

</style>