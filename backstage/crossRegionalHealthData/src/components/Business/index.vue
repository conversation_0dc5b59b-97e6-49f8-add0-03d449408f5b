<template>
  <div>
    <div>
      <el-select v-model="timeway" placeholder="请选择时间筛选方式" @change="changeWay">
        <el-option label="时间点" :value="1"></el-option>
        <el-option label="时间段" :value="2"></el-option>
      </el-select>
      &nbsp;
      <el-date-picker v-show="timeway === 1" v-model="query.timePoint" value="yyyy-MM-dd"
        value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
      </el-date-picker>
      &nbsp;
      <el-date-picker v-show="timeway === 2" v-model="dateRange" type="daterange"value="yyyy-MM-dd HH:mm:ss"
      value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']"  range-separator="至"
        start-placeholder="开始日期" end-placeholder="结束日期">
      </el-date-picker>
      &nbsp;
      <!-- <el-cascader class="custom-cascader" v-model="search.areaCode" :props="districtListProps" clearable collapse-tags
        ref="regAddCas" placeholder="请选择地区">
      </el-cascader> -->
      &nbsp;
      <el-button type="primary" @click="onSearch">查询</el-button>
      <el-button @click="onReset">重置</el-button>
    </div>
    <TitleTag titleName="监督执法机构联合情况情况"></TitleTag>
    <el-table :data="jdzfOrgStats" border style="width: 100%;" 
    header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    :summary-method="getSummaries"
    show-summary>
      <el-table-column type="index" label="序号" width="80px" align="center"></el-table-column>
      <el-table-column prop="name" label="协同区域"></el-table-column>
      <el-table-column prop="count" label="案件信息数量"></el-table-column>
    </el-table>
    <TitleTag titleName="职业病人跨区域情况"></TitleTag>
    <el-table :data="zybOrgStats" border style="width: 100%;" 
    header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    :summary-method="getSummaries"
    show-summary>
      <el-table-column type="index" label="序号" width="80px" align="center"></el-table-column>
      <el-table-column prop="name" label="协同区域"></el-table-column>
      <el-table-column prop="count" label="患者数量（人次）"></el-table-column>
    </el-table>
    <TitleTag titleName="第三方服务机构跨区域服务监管情况"></TitleTag>
    <el-table :data="dsfOrgStats" border style="width: 100%;" 
    header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    :summary-method="getSummaries"
    show-summary>
      <el-table-column type="index" label="序号" width="80px" align="center"></el-table-column>
      <el-table-column prop="name" label="协同区域"></el-table-column>
      <el-table-column prop="count" label="机构数量"></el-table-column>
    </el-table>
    <TitleTag titleName="职业病诊断、鉴定跨区域用人单位调查情况"></TitleTag>
    <el-table :data="zddwOrgStats" border style="width: 100%;" 
    header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    :summary-method="getSummaries"
    show-summary>
      <el-table-column type="index" label="序号" width="80px" align="center"></el-table-column>
      <el-table-column prop="name" label="协同区域"></el-table-column>
      <el-table-column prop="count" label="用人单位数量"></el-table-column>
    </el-table>
    <TitleTag titleName="职业健康检查质控中心业务协同情况"></TitleTag>
    <el-table :data="jkjcOrgStats" border style="width: 100%;" 
    header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    :summary-method="getSummaries"
    show-summary>
      <el-table-column type="index" label="序号" width="80px" align="center"></el-table-column>
      <el-table-column prop="name" label="协同区域"></el-table-column>
      <el-table-column prop="count" label="健康检查机构数量"></el-table-column>
    </el-table>
    <TitleTag titleName="职业健康诊断质控中心业务协同情况"></TitleTag>
    <el-table :data="zdOrgStats" border style="width: 100%;" 
    header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    :summary-method="getSummaries"
    show-summary>
      <el-table-column type="index" label="序号" width="80px" align="center"></el-table-column>
      <el-table-column prop="name" label="协同区域"></el-table-column>
      <el-table-column prop="count" label="诊断机构数量"></el-table-column>
    </el-table>
    <TitleTag titleName="职业病鉴定质控中心业务协同情况"></TitleTag>
    <el-table :data="jdOrgStats" border style="width: 100%;" 
    header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    :summary-method="getSummaries"
    show-summary>
      <el-table-column type="index" label="序号" width="80px" align="center"></el-table-column>
      <el-table-column prop="name" label="协同区域"></el-table-column>
      <el-table-column prop="count" label="鉴定机构数量"></el-table-column>
    </el-table>
  </div>
</template>
<script>
import TitleTag from "../TitleTag.vue";
import {getAddressList,getDictDataByType,getUserSession,getJointEnforcementList,getFollowUpList,getSuperviseList,getInvestigationList,getCheckCenterList,getDiagnosisCenterList,getIdentificationCenterList} from '@/api/index'
export default {
  components: {
    TitleTag,
  },
  data() {
    return {
      // 4.业务协同情况（新疆数据）
      businessStats: [],
      query:{
        submitDepartment: "",
        pageNum: 1,
        pageSize: 99999,
        timePoint: '',
        startDate:'',
        endDate:'',
      },
      dateRange: [],
      timeway: 1,
      jdzfOrgStats:[],
      zybOrgStats:[],
      dsfOrgStats:[],
      zddwOrgStats:[],
      jkjcOrgStats:[],
      zdOrgStats:[],
      jdOrgStats:[],
      addressList:[],
      userInfo:{}
    };
  },
  async created() {
    const res =  await getUserSession()
    this.userInfo = res.data && res.data.userInfo || {}
    this.getAreaList()
  },
  methods: {
    // 获取地区列表
    async getAreaList(){
      const {data}  = await getAddressList({level:0})
      this.addressList = data && data.docs || []
      if (this.userInfo.regAdd && this.userInfo.area_code) {
        this.addressList.push({
          name: this.userInfo.regAdd[this.userInfo.regAdd.length -1],
          id: this.userInfo.area_code
        });
      }
      // const res = await getDictDataByType({dictType:'area'})
      // this.addressList = res.data[0].children
      // this.addressList.push({name: "建设兵团",id: "660000000"})
      if(this.addressList && this.addressList.length){
        this.getJdzfOrgStats()
        this.getzybOrgStats()
        this.getdsfOrgStats()
        this.getzddwOrgStats()
        this.getjkjcOrgStats()
        this.getzdOrgStats()
        this.getjdOrgStats()
      }
    },
    async getJdzfOrgStats(){
      try {
        const {data} = await getJointEnforcementList(this.query)
        const institutionList = data && data.resList || []
        this.jdzfOrgStats = this.addressList.map(item => {
          const matchedInstitutions = institutionList.filter(
            inst => inst.area_code && inst.area_code.includes(item.id)
          );
          // 统计数量
          const count = matchedInstitutions.length || 0;
          return {
            ...item, 
            count, 
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.jdzfOrgStats = [];
      }
    },
    async getzybOrgStats(){
      try {
        const {data} = await getFollowUpList(this.query)
        const institutionList = data && data.resList || []
        this.zybOrgStats = this.addressList.map(item => {
          const matchedInstitutions = institutionList.filter(
            inst => inst.area_code && inst.area_code.includes(item.id)
          );
          // 统计数量
          const count = matchedInstitutions.length || 0;
          return {
            ...item, 
            count, 
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.zybOrgStats = [];
      }
    },
    async getdsfOrgStats(){
      try {
        const {data} = await getSuperviseList(this.query)
        const institutionList = data && data.resList || []
        this.dsfOrgStats = this.addressList.map(item => {
          const matchedInstitutions = institutionList.filter(
            inst => inst.area_code && inst.area_code.includes(item.id)
          );
          // 统计数量
          const count = matchedInstitutions.length || 0;
          return {
            ...item, 
            count, 
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.dsfOrgStats = [];
      }
    },
    async getzddwOrgStats(){
      try {
        const {data} = await getInvestigationList(this.query)
        const institutionList = data && data.resList || []
        this.zddwOrgStats = this.addressList.map(item => {
          const matchedInstitutions = institutionList.filter(
            inst => inst.area_code && inst.area_code.includes(item.id)
          );
          // 统计数量
          const count = matchedInstitutions.length || 0;
          return {
            ...item, 
            count, 
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.zddwOrgStats = [];
      }
    },
    async getjkjcOrgStats(){
      try {
        const {data} = await getCheckCenterList(this.query)
        const institutionList = data && data.resList || []
        this.jkjcOrgStats = this.addressList.map(item => {
          const matchedInstitutions = institutionList.filter(
            inst => inst.area_code && inst.area_code.includes(item.id)
          );
          // 统计数量
          const count = matchedInstitutions.length || 0;
          return {
            ...item, 
            count, 
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.jkjcOrgStats = [];
      }
    },
    async getzdOrgStats(){
      try {
        const {data} = await getDiagnosisCenterList(this.query)
        const institutionList = data && data.resList || []
        this.zdOrgStats = this.addressList.map(item => {
          const matchedInstitutions = institutionList.filter(
            inst => inst.area_code && inst.area_code.includes(item.id)
          );
          // 统计数量
          const count = matchedInstitutions.length || 0;
          return {
            ...item, 
            count, 
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.zdOrgStats = [];
      }
    },
    async getjdOrgStats(){
      try {
        const {data} = await getIdentificationCenterList(this.query)
        const institutionList = data && data.resList || []
        this.jdOrgStats = this.addressList.map(item => {
          const matchedInstitutions = institutionList.filter(
            inst => inst.area_code && inst.area_code.includes(item.id)
          );
          // 统计数量
          const count = matchedInstitutions.length || 0;
          return {
            ...item, 
            count, 
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.jdOrgStats = [];
      }
    },

    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总次数';
          return;
        }
        if (index === 1) {
          sums[index] = '';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] += '';
        } else {
          sums[index] = '';
        }
      });

      return sums;
    },
    onSearch(){
      this.getJdzfOrgStats()
      this.getzybOrgStats()
      this.getdsfOrgStats()
      this.getzddwOrgStats()
      this.getjkjcOrgStats()
      this.getzdOrgStats()
      this.getjdOrgStats()
    },
    onReset(){
      this.query = {
        submitDepartment: "",
        pageNum: 1,
        pageSize: 99999,
        timePoint: '',
        startDate:'',
        endDate:'',
      },
      this.dateRange= []
      this.getJdzfOrgStats()
      this.getzybOrgStats()
      this.getdsfOrgStats()
      this.getzddwOrgStats()
      this.getjkjcOrgStats()
      this.getzdOrgStats()
      this.getjdOrgStats()
    },
    changeWay(){
      this.query.timePoint = ''
      this.query.startDate = ''
      this.search.endDate = ''
      this.dateRange=[]
    }
  },
};
</script>
<style lang="scss" scoped>

</style>