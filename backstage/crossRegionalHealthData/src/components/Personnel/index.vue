<template>
  <div>
    <div>
        <el-select v-model="timeway" placeholder="请选择时间筛选方式" @change="changeWay">
          <el-option label="时间点" :value="1"></el-option>
          <el-option label="时间段" :value="2"></el-option>
        </el-select>
        &nbsp;
        <el-date-picker v-show="timeway === 1" v-model="query.timePoint" value="yyyy-MM-dd"
          value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
        </el-date-picker>
        &nbsp;
        <el-date-picker v-show="timeway === 2" v-model="dateRange" type="daterange"value="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']"  range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
        &nbsp;
        <!-- <el-cascader class="custom-cascader" v-model="search.areaCode" :props="districtListProps" clearable collapse-tags
          ref="regAddCas" placeholder="请选择地区">
        </el-cascader> -->
        &nbsp;
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="onReset">重置</el-button>
    </div>
    <TitleTag titleName="人才库数量统计"></TitleTag>
    <el-table :data="personnelStats" border style="width: 100%;"
     header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
     :summary-method="getSummaries"
     show-summary>
      <el-table-column type="index" label="序号" width="50px" align="center"></el-table-column>
      <el-table-column prop="name" label="机构类型" min-width="80px" show-overflow-tooltip></el-table-column>
      <el-table-column prop="regAdds" label="区域分布" min-width="150px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.regAdds.join(',') }}</span>
        </template>  
      </el-table-column>
      <el-table-column prop="count" label="人才数量" min-width="80px" align="center"></el-table-column>
    </el-table>
    <TitleTag titleName="联络员数量统计"></TitleTag>
    <el-table :data="connectorStats" border style="width: 100%;" 
    header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    :summary-method="getSummaries"
    show-summary>
      <el-table-column type="index" label="序号" width="50px" align="center"></el-table-column>
      <el-table-column prop="name" label="机构类型" min-width="80px" show-overflow-tooltip></el-table-column>
      <el-table-column prop="regAdds" label="区域分布" min-width="150px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.regAdds.join(',') }}</span>
        </template>  
      </el-table-column>
      <el-table-column prop="count" label="联络员数量" min-width="80px" align="center"></el-table-column>
    </el-table>
  </div>
</template>
<script>
import TitleTag from "../TitleTag.vue";
import {getTalentPoolList,getContactOfficerList} from '@/api/index'
export default {
  components: {
    TitleTag,
  },
  data() {
    return {
      personnelList:[
        {
          name:'卫生健康行政部门',
          value:'healthDepartment',
        },
        {
          name:'监督机构',
          value:'supervisionInstitution',
        }
      ],
      personnelStats: [],
      connectorStats: [],
      query:{
        page: 1,
        pageSize: 999999,
        name: '',
        organization: '',
        timePoint: '',
        startDate:'',
        endDate:'',
      },
      dateRange: [],
      timeway: 1,
    };
  },
  created() {
    this.getPersonnelStats();
    this.getConnectorStats();
  },
  methods: {
    async getPersonnelStats() {
      try {
        const { data } = await getTalentPoolList(this.query); // 接口返回的机构列表数据
        const institutionList = data.docs || []; // 机构列表数组

        // 合并数据：为每种机构类型添加数量和分布区域
        this.personnelStats = this.personnelList.map(item => {
          // 筛选出包含当前机构类型的机构（orgTypes数组中包含item.value）
          const matchedInstitutions = institutionList.filter(
            inst => inst.organization == item.value
          );

          // 统计数量
          const count = matchedInstitutions.length || '0';

          // 收集去重后的分布区域
          const regAdds = [...new Set(
            matchedInstitutions.map(inst => inst.regAdd[inst.regAdd.length -1] || '未知区域')
          )];

          return {
            ...item, // 保留原有的name和value
            count, // 该类型机构的数量
            regAdds // 该类型机构的分布区域（去重后）
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.personnelStats = []; // 出错时置空，避免渲染异常
      }
    },
    async getConnectorStats() {
      try {
        const { data } = await getContactOfficerList(this.query); // 接口返回的机构列表数据
        const institutionList = data.docs || []; // 机构列表数组

        // 合并数据：为每种机构类型添加数量和分布区域
        this.connectorStats = this.personnelList.map(item => {
          // 筛选出包含当前机构类型的机构（orgTypes数组中包含item.value）
          const matchedInstitutions = institutionList.filter(
            inst => inst.organization == item.value
          );

          // 统计数量
          const count = matchedInstitutions.length || '0';

          // 收集去重后的分布区域
          const regAdds = [...new Set(
            matchedInstitutions.map(inst => inst.regAdd[inst.regAdd.length -1] || '未知区域')
          )];

          return {
            ...item, // 保留原有的name和value
            count, // 该类型机构的数量
            regAdds // 该类型机构的分布区域（去重后）
          };
        });
      } catch (error) {
        console.error('获取机构列表失败：', error);
        this.connectorStats = []; // 出错时置空，避免渲染异常
      }
    },
    getSummaries(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '总计';
            return;
          }
          if (index === 1 || index === 2) {
            sums[index] = '';
            return;
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
            sums[index] += '';
          } else {
            sums[index] = '';
          }
        });

        return sums;
      },
    onSearch(){
      this.getPersonnelStats();
      this.getConnectorStats();
    },
    onReset(){
    this.query = {
      page: 1,
      pageSize: 999999,
      name: '',
      organization: '',
      timePoint: '',
      startDate:'',
      endDate:'',
    },
      this.dateRange= []
      this.$forceUpdate()
      this.getPersonnelStats();
      this.getConnectorStats();
    },
    changeWay(){
      this.query.timePoint = ''
      this.query.startDate = ''
      this.search.endDate = ''
      this.dateRange=[]
    }
  },
};
</script>
<style lang="scss" scoped>
</style>