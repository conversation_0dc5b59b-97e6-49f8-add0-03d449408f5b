<template>
  <div>
    <el-row :gutter="10" class="main-container">
      <el-button @click="createNewNow" size="mini" type="primary"> 新增培训计划 </el-button>
      <el-button @click="handleImport" size="mini" type="primary" v-if="branch === 'fz'">
        导入培训清单
      </el-button>
      <input
        type="file"
        ref="fileInput"
        style="display: none"
        accept=".xlsx"
        @change="handleFileChange"
      />
      <el-input
        placeholder="可根据[培训名称]搜索"
        class="search"
        suffix-icon="el-icon-search"
        v-model="searchKey"
        @change="getPlanList"
        @clear="getPlanList"
        clearable
        size="mini"
      >
      </el-input>

      <div class="rightSearch">
        <el-date-picker
          @change="getPlanList"
          v-model="selectYear"
          type="year"
          placeholder="选择年份"
          size="mini"
        >
        </el-date-picker>
        <el-select
          v-model="searchPlanStatus"
          clearable
          placeholder="培训状态"
          @change="getPlanList"
          size="mini"
        >
          <el-option
            v-for="item in planStatus"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
    </el-row>
    <div style="width: 100%; height: 16px"></div>
    <el-table stripe @cell-dblclick="cellDblclick" :data="planList" style="width: 100%">
      <el-table-column show-overflow-tooltip prop="name" label="培训名称" width="230">
        <template slot-scope="scope">
          <!-- <el-tooltip
            class="item"
            effect="dark"
            content="双击修改培训名称及介绍"
            placement="top"
          > -->
          <span>{{ scope.row.name }}</span>
          <!-- </el-tooltip> -->
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="name" label="培训介绍">
        <template slot-scope="scope">
          <span>{{ scope.row.Introduction }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="requiredCoursesHours" label="必修学时">
      </el-table-column>
      <el-table-column align="center" prop="electivesCoursesHours" label="选修学时">
      </el-table-column>
      <!-- <el-table-column prop="coursesID.length" label="必修课程数" >
      </el-table-column>
      <el-table-column prop="electives.length" label="选修课程数" >
      </el-table-column> -->
      <el-table-column prop="needExam" label="考试" align="center">
        <template slot-scope="scope">
          {{ scope.row.needExam ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="completeTime" label="培训开始时间" width="140">
        <template slot-scope="scope">
          {{ dateFormat('YYYY-mm-dd', new Date(scope.row.date)) }}
        </template>
      </el-table-column>
      <el-table-column prop="completeTime" label="培训截止时间" width="140">
        <template slot-scope="scope">
          {{ dateFormat('YYYY-mm-dd', new Date(scope.row.completeTime)) }}
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="updateCompleteTime(scope.row, scope.row.completeTime)"
            size="mini"
          ></el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="EnterpriseID.length" label="应学企业数">
      </el-table-column>
      <el-table-column align="center" prop="completedEnterprise.length" label="完成企业数">
      </el-table-column>
      <el-table-column label="操作" width="220">
        <template slot-scope="scope">
          <el-button
            size="mini"
            plain
            type="success"
            icon="el-icon-view"
            @click="editOne(scope.row._id)"
            >查看</el-button
          >
          <el-button
            size="mini"
            plain
            type="primary"
            icon="el-icon-edit"
            @click="editPlan(scope.row)"
            >编辑</el-button
          >
          <el-button
            size="mini"
            plain
            type="danger"
            icon="el-icon-delete"
            @click="deletePlan(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" class="row-bg" justify="center">
      <el-col :span="8">
        <div>
          <br />
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="count"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>

    <el-dialog width="25%" title="请修改培训截止时间" :visible.sync="dialogVisible">
      <el-date-picker v-model="newPlan.completeTime" type="date" placeholder="选择日期">
      </el-date-picker>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialog">取 消</el-button>
        <el-button type="primary" @click="updatePlan('newCompleteTime')">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog top="5vh" width="55%" title="培训信息" :visible.sync="updateNameDialog">
      <el-form
        :model="newPlan"
        :rules="planRules"
        ref="ruleForm"
        label-width="110px"
        class="demo-ruleForm"
        label-position="right"
      >
        <el-row :gutter="10">
          <!-- 培训名 -->
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-form-item label="培训名称" prop="name">
              <el-input v-model="newPlan.name"></el-input>
            </el-form-item>
          </el-col>
          <!-- 防护用品类别 -->
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-form-item label="防护用品类别" prop="protectiveClassification">
              <el-select filterable style="width: 100%" multiple placeholder="请选择防护用品类别" v-model.trim="newPlan.protectiveClassification">
                <el-option v-for="item in defendProducts" :key="item._id" :label="item.name" :value="item._id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 介绍 -->
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-form-item label="培训介绍" prop="Introduction">
              <el-input
                type="textarea"
                :autosize="{ minRows: 3, maxRows: 8 }"
                placeholder="请输入内容"
                v-model="newPlan.Introduction"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <!-- 截止时间 -->
          <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
            <el-form-item label="截止时间" prop="completeTime">
              <el-date-picker
                v-model="newPlan.completeTime"
                type="date"
                placeholder="选择日期"
                size="small"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 学时 -->
          <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
            <el-form-item label="学时要求" prop="requiredCoursesHours">
              必修
              <el-input
                disabled
                style="width: 30%"
                v-model="newPlan.requiredCoursesHours"
                size="small"
                type="number"
              ></el-input>
              &emsp;&emsp; 选修
              <el-input
                style="width: 30%"
                v-model="newPlan.electivesCoursesHours"
                size="small"
                type="number"
                min="0"
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 考试开关 -->
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-form-item label="是否考试">
              <el-switch disabled class="need-exam" v-model="newPlan.needExam"> </el-switch>
            </el-form-item>
          </el-col>
          <!-- 证书开关 -->
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-form-item label="证书是否审核">
              <el-switch
                class="need-exam"
                v-model="newPlan.certificateReview"
                :disabled="newPlan.trainingType === 2"
              >
              </el-switch>
            </el-form-item>
          </el-col>
          <!-- 开始时间点 -->
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" v-show="newPlan.needExam">
            <el-form-item label="考试时间点">
              <el-select
                disabled
                style="width: 100%"
                v-model="newPlan.examinationType"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in examinationTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 考试次数 -->
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" v-show="newPlan.needExam">
            <el-form-item label="考试次数">
              最多考
              <el-input
                disabled
                style="width: 40%"
                v-model="newPlan.examination.timesLimit"
                size="small"
              ></el-input>
              次
            </el-form-item>
          </el-col>

          <!-- 题目 -->
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" v-show="newPlan.needExam">
            <el-form-item label="单选题" prop="required">
              <el-row :gutter="10">
                <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                  <el-input
                    disabled
                    style="width: 30%"
                    @change="subAllScores"
                    class="input-number"
                    type="number"
                    v-model="newPlan.examination.singleChoice.num"
                  ></el-input>
                  <span>&ensp;题</span>
                </el-col>
                <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                  <span>每题&ensp;</span>
                  <el-input
                    disabled
                    style="width: 30%"
                    @change="subAllScores"
                    class="input-number"
                    type="number"
                    v-model="newPlan.examination.singleChoice.scores"
                  ></el-input>
                  <span>&ensp;分</span>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="判断题" prop="required">
              <el-row :gutter="10">
                <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                  <el-input
                    disabled
                    style="width: 30%"
                    @change="subAllScores"
                    class="input-number"
                    type="number"
                    v-model="newPlan.examination.Judgment.num"
                  ></el-input>
                  <span>&ensp;题</span>
                </el-col>
                <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                  <span>每题&ensp;</span>
                  <el-input
                    disabled
                    style="width: 30%"
                    @change="subAllScores"
                    class="input-number"
                    type="number"
                    v-model="newPlan.examination.Judgment.scores"
                  ></el-input>
                  <span>&ensp;分</span>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="多选题" prop="required">
              <el-row :gutter="10">
                <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                  <el-input
                    disabled
                    style="width: 30%"
                    @change="subAllScores"
                    class="input-number"
                    type="number"
                    v-model="newPlan.examination.multipleChoice.num"
                  ></el-input>
                  <span>&ensp;题</span>
                </el-col>
                <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                  <span>每题&ensp;</span>
                  <el-input
                    disabled
                    style="width: 30%"
                    @change="subAllScores"
                    class="input-number"
                    type="number"
                    v-model="newPlan.examination.multipleChoice.scores"
                  ></el-input>
                  <span>&ensp;分</span>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" v-show="newPlan.needExam">
            <el-form-item label="总分数" prop="required">
              <!-- <span>{{ subAllScores() }}</span> -->
              &thinsp;
              <el-input style="width: 30%" type="number" v-model="totalScore" disabled></el-input
              ><span>&ensp;分</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" v-show="newPlan.needExam">
            <el-form-item label="及格分占比">
              <el-input
                disabled
                style="width: 30%"
                type="number"
                v-model="newPlan.examination.passingGrate"
              ></el-input>
              <span>&ensp;%</span>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" v-show="newPlan.needExam">
            <el-form-item label="允许直接考试">
              <el-switch disabled class="need-exam" v-model="newPlan.allowTestOnly"> </el-switch>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" v-show="newPlan.needExam">
            <el-form-item label="考试时长">
              <el-input
                disabled
                style="width: 30%"
                type="number"
                v-model="newPlan.examination.limitTime"
              ></el-input>
              <span>&ensp;分钟</span>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-form-item label="弹窗提醒">
              <el-select style="width: 100%" v-model="newPlan.breakpoint" placeholder="请选择">
                <el-option
                  v-for="item in breakpoints"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <span v-show="newPlan.breakpoint === 2">
              每
              <el-input
                style="width: 30%"
                v-model="newPlan.breakpointInterval"
                size="small"
                type="number"
              ></el-input>
              分钟提醒1次
            </span>
            <span v-show="newPlan.breakpoint === 1">
              <el-input
                style="width: 25%"
                v-model="newPlan.breakpointRange.botton"
                size="small"
                type="number"
              ></el-input>
              ~
              <el-input
                style="width: 25%"
                v-model="newPlan.breakpointRange.top"
                size="small"
                type="number"
              ></el-input>
              分钟提醒一次
            </span>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="button">
        <el-button @click="updateNameDialog = false">取 消</el-button>
        <el-button type="primary" @click="updatePlan('newName')">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { updatePlan, getPlanList, deletePlan, getBranch } from '@/api/addTraining';
import { importExcel } from '@/utils/importExcel';
export default {
  props: {
    messageList: Object,
    defendProducts:{
      type: Array,
      default: () => []
    }
  },
  components: {},
  data() {
    return {
      branch: '',
      examinationTypes: [
        {
          value: 1,
          label: '课程结束考试',
        },
        {
          value: 2,
          label: '培训结束考试',
        },
      ],
      breakpoints: [
        {
          value: 0,
          label: '不设提醒',
        },
        {
          value: 1,
          label: '随机提醒',
        },
        {
          value: 2,
          label: '固定间隔',
        },
      ],
      dialogVisible: false,

      updateNameDialog: false,

      pageSize: 10,
      current: 1,
      count: 0,

      searchKey: '',
      selectYear: '',
      searchPlanStatus: '',
      planStatus: [
        {
          label: '进行中',
          value: '$gt',
        },
        {
          label: '已截止',
          value: '$lte',
        },
      ],

      planList: [],

      planRules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          {
            min: 2,
            max: 50,
            message: '长度在 2 到 50 个字符',
            trigger: 'blur',
          },
        ],

        completeTime: [{ required: true, message: '请选择计划完成时间', trigger: 'change' }],
      },
      newPlan: {
        certificateReview: true,
        name: '',
        Introduction: '',
        completeTime: null,
        needExam: true,
        breakpoint: 1,
        breakpointInterval: 5,
        breakpointRange: {
          botton: 5,
          top: 10,
        },
        examination: {
          singleChoice: {
            num: 10,
            scores: 5,
          },
          multipleChoice: {
            num: 0,
            scores: 0,
          },
          Judgment: {
            num: 10,
            scores: 5,
          },
          fillBlank: {
            num: 0,
            scores: 0,
          },
          essayQuestion: {
            num: 0,
            scores: 0,
          },
          passingGrate: 60,
          limitTime: 30,
          timesLimit: 3,
        },
        coursesID: [],
        electives: [],
        allowTestOnly: false,
        completeTime: null,
        examinationType: 2,
        requiredCoursesHours: 10,
        electivesCoursesHours: 6,
        protectiveClassification:[]
      },
      totalScore: 100,
    };
  },
  computed: {
    // ...mapGetters(["messageList"]),
  },
  inject: ['reload'],
  methods: {
    // 上传excel
    handleImport() {
      // 跳转到importTrain页面
      this.$router.push({
        name: 'importTrain',
      });
      // this.$refs.fileInput.click();
    },
    async handleFileChange(e) {
      const file = e.target.files[0];
      if (!file) return;
      try {
        const jsonData = await importExcel(file);
        console.log(jsonData);
        //jsonData 是解析出来的数据，格式是数组 jsonDate[0] 是表头
        // ['考试签到', '单位', '性别', '职务', '身份证号码']
        const headers = [
          {
            key: 'name',
            label: '考试签到',
          },
          {
            key: 'enterprise',
            label: '单位',
          },
          {
            key: 'sex',
            label: '性别',
          },
          {
            key: 'position',
            label: '职务',
          },
          {
            key: 'idCard',
            label: '身份证号码',
          },
          {
            key: 'idCard',
            label: '培训单位',
          },
          {
            key: 'unit',
            label: '发证单位',
          },
          {
            key: 'issuanceTime',
            label: '发证日期',
          },
          {
            key: 'effectiveTime',
            label: '有效日期',
          },
        ];
        // 判断表头是否和headers一致
        if (jsonData[0].every((item, index) => item === headers[index].label)) {
          jsonData.shift();
          console.log(jsonData);
          // 处理导入的 JSON 数据 将JSON数据转换成需要的格式
          const data = jsonData.map(item => {
            const obj = {};
            headers.forEach((header, index) => {
              obj[header.key] = item[index];
            });
            return obj;
          });
          console.log(data);
          // 将data转化成json文件并下载
          // const blob = new Blob([JSON.stringify(data)], {
          //   type: "application/json",
          // });
        } else {
          console.log('格式错误');
          this.$message({
            type: 'error',
            message: '导入文件格式失败，请联系客服',
          });
          return;
        }
        // 处理导入的 JSON 数据
      } catch (error) {
        console.error(error);
        this.$message({
          type: 'error',
          message: '导入文件失败，请联系客服',
        });
      }
    },
    subAllScores() {
      this.totalScore = Number(
        this.newPlan.examination.singleChoice.num * this.newPlan.examination.singleChoice.scores +
          this.newPlan.examination.multipleChoice.num *
            this.newPlan.examination.multipleChoice.scores +
          this.newPlan.examination.Judgment.num * this.newPlan.examination.Judgment.scores
      );
    },
    updatePlan(key) {
      if (key === 'newName') {
        this.$refs['ruleForm'].validate(valid => {
          if (valid) {
            updatePlan({
              Introduction: this.newPlan.Introduction,
              name: this.newPlan.name,
              _id: this.newPlan._id,
              completeTime: this.newPlan.completeTime,
              breakpoint: this.newPlan.breakpoint,
              breakpointInterval: this.newPlan.breakpointInterval,
              breakpointRange: this.newPlan.breakpointRange,
              certificateReview: this.newPlan.certificateReview,
              electivesCoursesHours: this.newPlan.electivesCoursesHours,
              protectiveClassification: this.newPlan.protectiveClassification,
            }).then(({ data }) => {
              // this.$router.go(0);
              this.reload();
            });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      } else {
        updatePlan({
          completeTime: this.newPlan.completeTime,
          _id: this.newPlan._id,
        }).then(({ data }) => {
          this.$router.go(0);
        });
      }
    },
    editPlan(row) {
      // this.newPlan._id = row._id;
      this.updateNameDialog = true;
      // this.newPlan.name = row.name;
      // this.newPlan.Introduction = row.Introduction;
      this.newPlan = row;
    },
    cellDblclick(row, column, cell, event) {
      console.log(column.label, column.id[column.id.length - 1]);
      if (column.label === '培训名称' && column.id[column.id.length - 1] === '1') {
        this.newPlan._id = row._id;
        this.updateNameDialog = true;
        this.newPlan.name = row.name;
        this.newPlan.Introduction = row.Introduction;
      }
    },
    updateCompleteTime(row, completeTime) {
      this.dialogVisible = true;
      this.newPlan._id = row._id;
      this.newPlan.completeTime = completeTime ? completeTime : '';
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getPlanList();
    },
    handleCurrentChange(val) {
      this.current = val;
      this.getPlanList();
    },
    createNewNow() {
      this.$emit('createNewNow');
    },
    deletePlan(row) {
      this.$confirm('此操作将永久删除该培训, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$prompt('为避免误操作，请输入想要删除课程的名称', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputValidator: value => {
              if (value === row.name) {
                return true;
              }
              return false;
            },
            inputPlaceholder: '请输入培训名称',
          })
            .then(({ value }) => {
              if (value === row.name) {
                deletePlan({
                  planID: row._id,
                }).then(({ data }) => {
                  if (data.message === 'OK') {
                    this.$message({
                      type: 'success',
                      message: '删除成功!',
                    });
                    this.reload();
                  }
                });
              }
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '已取消删除',
              });
            });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          });
        });
    },
    editOne(_id) {
      this.$router.push({
        name: 'planInfo',
        params: { id: _id },
      });
    },
    dateFormat(fmt, date) {
      let ret;
      const opt = {
        'Y+': date.getFullYear().toString(), // 年
        'm+': (date.getMonth() + 1).toString(), // 月
        'd+': date.getDate().toString(), // 日
        'H+': date.getHours().toString(), // 时
        'M+': date.getMinutes().toString(), // 分
        'S+': date.getSeconds().toString(), // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
      };
      for (let k in opt) {
        ret = new RegExp('(' + k + ')').exec(fmt);
        if (ret) {
          fmt = fmt.replace(
            ret[1],
            ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0')
          );
        }
      }
      return fmt;
    },
    getPlanList() {
      getPlanList({
        searchKey: this.searchKey,
        searchPlanStatus: this.searchPlanStatus,
        current: this.current,
        pageSize: this.pageSize,
        selectYear: this.selectYear,
      }).then(({ data }) => {
        console.log(data);
        this.planList = data.list;
        this.count = data.count;
      });
    },
  },
  created() {
    this.getPlanList();
    getBranch().then(res => {
      if (res.status == '200') {
        this.branch = res.data.branch;
      }
    });
  },
};
</script>

<style scoped>
.button {
  display: block;
  text-align: center;
  margin-top: 10px;
}
.el-input.search {
  /* font-size: 14px; */
  display: inline-block;
  width: auto;
  margin-left: 10px;
  border: 0;
  background-color: #f5f7fa;
}
.rightSearch {
  float: right;
}
.rightSearch .el-select {
  width: 120px !important;
  margin-left: 10px;
}
.rightSearch .el-date-editor.el-input,
.rightSearch .el-date-editor.el-input__inner {
  width: 120px !important;
}
</style>
<style>
.search .el-input__icon {
  color: #2a91fc;
  font-size: 14px;
}
</style>
