<template>
  <div>
    <div>
      <el-dialog
        top="5vh"
        title="请填写培训信息"
        :visible.sync="planInfoDialog"
        width="55%"
        :close-on-click-modal="false"
      >
        <el-form
          :model="plan"
          :rules="planRules"
          ref="ruleForm"
          label-width="110px"
          class="demo-ruleForm"
          label-position="right"
        >
          <el-row :gutter="10">
            <!-- 培训名 -->
            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
              <el-form-item label="培训名称" prop="name">
                <el-input v-model="plan.name"></el-input>
              </el-form-item>
            </el-col>
            <!-- 防护用品类别 -->
            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
              <el-form-item label="防护用品类别" prop="protectiveClassification">
                <el-select filterable style="width: 100%" multiple placeholder="请选择防护用品类别" v-model.trim="plan.protectiveClassification">
                  <el-option v-for="item in defendProducts" :key="item._id" :label="item.name" :value="item._id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 介绍 -->
            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
              <el-form-item label="培训介绍" prop="Introduction">
                <el-input
                  type="textarea"
                  :autosize="{ minRows: 3, maxRows: 8 }"
                  placeholder="请输入内容"
                  v-model="plan.Introduction"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 截止时间 -->
            <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
              <el-form-item label="截止时间" prop="completeTime">
                <el-date-picker
                  v-model="plan.completeTime"
                  type="date"
                  placeholder="选择日期"
                  size="small"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 学时 -->
            <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
              <el-form-item label="学时要求" prop="requiredCoursesHours">
                必修
                <el-input
                  style="width: 30%"
                  v-model="requiredCoursesHours"
                  size="small"
                  type="number"
                  disabled
                ></el-input>
                &emsp;&emsp; 选修
                <el-input
                  style="width: 30%"
                  v-model="electivesCoursesHours"
                  size="small"
                  type="number"
                  :disabled="plan.allowTestOnly"
                  @change="plan.electivesCoursesHours = electivesCoursesHours"
                ></el-input>
              </el-form-item>
            </el-col>
            <!-- 考试开关 -->
            <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
              <el-form-item label="是否考试">
                <el-switch class="need-exam" v-model="plan.needExam"> </el-switch>
              </el-form-item>
            </el-col>
            <!-- 证书开关 -->
            <!-- <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
              <el-form-item label="证书是否审核">
                <el-switch class="need-exam" v-model="plan.certificateReview">
                </el-switch>
              </el-form-item>
            </el-col> -->
            <!-- 开始时间点 -->
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" v-show="plan.needExam">
              <el-form-item label="考试时间点">
                <el-select style="width: 100%" v-model="plan.examinationType" placeholder="请选择">
                  <el-option
                    v-for="item in examinationTypes"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 考试次数 -->
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" v-show="plan.needExam">
              <el-form-item label="考试次数">
                最多考
                <el-input
                  style="width: 40%"
                  v-model="plan.examination.timesLimit"
                  size="small"
                ></el-input>
                次
              </el-form-item>
            </el-col>

            <!-- 题目 -->
            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" v-show="plan.needExam">
              <el-form-item label="单选题" prop="required">
                <el-row :gutter="10">
                  <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                    <el-input
                      style="width: 30%"
                      @change="subAllScores"
                      class="input-number"
                      type="number"
                      v-model="plan.examination.singleChoice.num"
                    ></el-input>
                    <span>&ensp;题</span>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                    <span>每题&ensp;</span>
                    <el-input
                      style="width: 30%"
                      @change="subAllScores"
                      class="input-number"
                      type="number"
                      v-model="plan.examination.singleChoice.scores"
                    ></el-input>
                    <span>&ensp;分</span>
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item label="判断题" prop="required">
                <el-row :gutter="10">
                  <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                    <el-input
                      style="width: 30%"
                      @change="subAllScores"
                      class="input-number"
                      type="number"
                      v-model="plan.examination.Judgment.num"
                    ></el-input>
                    <span>&ensp;题</span>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                    <span>每题&ensp;</span>
                    <el-input
                      style="width: 30%"
                      @change="subAllScores"
                      class="input-number"
                      type="number"
                      v-model="plan.examination.Judgment.scores"
                    ></el-input>
                    <span>&ensp;分</span>
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item label="多选题" prop="required">
                <el-row :gutter="10">
                  <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                    <el-input
                      style="width: 30%"
                      @change="subAllScores"
                      class="input-number"
                      type="number"
                      v-model="plan.examination.multipleChoice.num"
                    ></el-input>
                    <span>&ensp;题</span>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="11" :lg="10" :xl="10">
                    <span>每题&ensp;</span>
                    <el-input
                      style="width: 30%"
                      @change="subAllScores"
                      class="input-number"
                      type="number"
                      v-model="plan.examination.multipleChoice.scores"
                    ></el-input>
                    <span>&ensp;分</span>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" v-show="plan.needExam">
              <el-form-item label="总分数" prop="required">
                <!-- <span>{{ subAllScores() }}</span> -->
                &thinsp;
                <el-input style="width: 30%" type="number" v-model="totalScore" disabled></el-input
                ><span>&ensp;分</span>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" v-show="plan.needExam">
              <el-form-item label="及格分占比">
                <el-input
                  style="width: 30%"
                  type="number"
                  v-model="plan.examination.passingGrate"
                ></el-input>
                <span>&ensp;%</span>
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" v-show="plan.needExam">
              <el-form-item label="允许直接考试">
                <el-switch
                  class="need-exam"
                  @change="allowTestOnlyFun"
                  v-model="plan.allowTestOnly"
                >
                </el-switch>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" v-show="plan.needExam">
              <el-form-item label="考试时长">
                <el-input
                  style="width: 30%"
                  type="number"
                  v-model="plan.examination.limitTime"
                ></el-input>
                <span>&ensp;分钟</span>
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
              <el-form-item label="弹窗提醒">
                <el-select style="width: 100%" v-model="plan.breakpoint" placeholder="请选择">
                  <el-option
                    v-for="item in breakpoints"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <span v-show="plan.breakpoint === 2">
                每
                <el-input
                  style="width: 30%"
                  v-model="plan.breakpointInterval"
                  size="small"
                  type="number"
                ></el-input>
                分钟提醒1次
              </span>
              <span v-show="plan.breakpoint === 1">
                <el-input
                  style="width: 25%"
                  v-model="plan.breakpointRange.botton"
                  size="small"
                  type="number"
                ></el-input>
                ~
                <el-input
                  style="width: 25%"
                  v-model="plan.breakpointRange.top"
                  size="small"
                  type="number"
                ></el-input>
                分钟提醒一次
              </span>
            </el-col>
          </el-row>
        </el-form>
        <el-row>
          <el-col :span="24">
            <div class="button">
              <el-button @click="previousCompany" type="primary" size="mini">返回</el-button>
              <el-button type="primary" @click="createNewPlan('ruleForm')" size="mini"
                >立即创建</el-button
              >
            </div>
          </el-col>
        </el-row>
      </el-dialog>
    </div>

    <div>
      <el-dialog
        top="2vh"
        title="请选择企业"
        :visible.sync="selectCompany"
        width="80%"
        :close-on-click-modal="false"
      >
        <selectCompany
          @toPlanInfo="toPlanInfo"
          @previousCourses="previousCourses"
          :needExam="plan.needExam"
        />
      </el-dialog>
    </div>

    <div>
      <el-dialog
        title="课程列表"
        top="2vh"
        :visible.sync="selectCourses"
        :width="couresesWidth"
        :close-on-click-modal="false"
      >
        <selectCourses
          @modyfieWidth="modyfieWidth"
          @checkedCourses="checkedCourses"
          :needExam="plan.needExam"
        />
      </el-dialog>
    </div>

    <div>
      <el-dialog
        title="课程顺序调整"
        :visible.sync="sortCourses"
        width="40%"
        :close-on-click-modal="false"
      >
        <sort-courses :needExam="plan.needExam" :courseList="courses" />
      </el-dialog>
    </div>

    <div>
      <el-dialog
        title="请填写考试信息"
        :visible.sync="examinarionInfoDialog"
        width="40%"
        :close-on-click-modal="false"
      >
        <examinarion />
      </el-dialog>
    </div>
  </div>
</template>

<script>
import selectCompany from './selectCompany';
import selectCourses from './selectCourses';
import sortCourses from './sortCourses';
import examinarion from './examinarion/planForm.vue';
import { createPlan } from '@/api/addTraining';
export default {
  props: {
    defendProducts:{
      type: Array,
      default: () => []
    }
  },
  components: {
    selectCompany,
    selectCourses,
    examinarion,
    sortCourses,
  },
  data() {
    return {
      totalScore: 100,
      couresesWidth: '75%',
      planInfoDialog: false,
      selectCompany: false,
      selectCourses: false,
      sortCourses: false,
      examinarionInfoDialog: false,
      companys: [],
      courses: [],
      afterSortCourses: [],
      lesson: 0,

      plan: {
        certificateReview: true,
        name: '',
        Introduction: '',
        completeTime: null,
        needExam: true,
        breakpoint: 1,
        breakpointInterval: 5,
        breakpointRange: {
          botton: 5,
          top: 10,
        },
        examination: {
          singleChoice: {
            num: 10,
            scores: 5,
          },
          multipleChoice: {
            num: 0,
            scores: 0,
          },
          Judgment: {
            num: 10,
            scores: 5,
          },
          fillBlank: {
            num: 0,
            scores: 0,
          },
          essayQuestion: {
            num: 0,
            scores: 0,
          },
          passingGrate: 60,
          limitTime: 30,
          timesLimit: 3,
        },
        coursesID: [],
        electives: [],
        allowTestOnly: false,
        completeTime: null,
        examinationType: 2,
        requiredCoursesHours: 10,
        electivesCoursesHours: 0,
        protectiveClassification:[]
      },
      requiredCoursesHours: 10,
      electivesCoursesHours: 0,

      planRules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          {
            min: 2,
            max: 50,
            message: '长度在 2 到 50 个字符',
            trigger: 'blur',
          },
        ],

        completeTime: [{ required: true, message: '请选择计划完成时间', trigger: 'change' }],
        requiredCoursesHours: [{ required: true, message: '请填写学时', trigger: 'blur' }],
      },

      breakpoints: [
        {
          value: 0,
          label: '不设提醒',
        },
        {
          value: 1,
          label: '随机提醒',
        },
        {
          value: 2,
          label: '固定间隔',
        },
      ],

      examinationTypes: [
        {
          value: 1,
          label: '课程结束考试',
        },
        {
          value: 2,
          label: '培训结束考试',
        },
      ],
    };
  },
  methods: {
    subAllScores() {
      this.totalScore = Number(
        this.plan.examination.singleChoice.num * this.plan.examination.singleChoice.scores +
          this.plan.examination.multipleChoice.num * this.plan.examination.multipleChoice.scores +
          this.plan.examination.Judgment.num * this.plan.examination.Judgment.scores
      );
    },
    createNewNow() {
      this.selectCourses = true;
    },
    checkedCourses(data) {
      const { requiredCoursesHours, electivesCoursesHours, coursesID, electives } = data;
      this.plan.requiredCoursesHours = requiredCoursesHours;
      this.requiredCoursesHours = requiredCoursesHours;
      this.plan.coursesID = coursesID;
      this.plan.electives = electives;
      this.selectCourses = false;
      this.selectCompany = true;
    },
    toPlanInfo(companys) {
      this.companys = companys; // 保存选择的企业
      this.selectCompany = false;
      this.planInfoDialog = true;
    },
    toSelectExamination(courses) {
      this.selectCompany = false;
      this.planInfoDialog = true;
    },
    previousPlanInfo() {
      this.selectCompany = false;
      this.planInfoDialog = true;
    },
    previousCompany() {
      this.planInfoDialog = false;
      this.selectCompany = true;
    },
    previousCourses() {
      this.selectCourses = true;
      this.selectCompany = false;
    },
    createNewPlan(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.planInfoDialog = false;
          const newPlan = {
            ...this.plan,
            EnterpriseID: this.companys,
          };
          if (newPlan.needExam && newPlan.allowTestOnly) {
            newPlan.electives.push(...newPlan.coursesID);
            newPlan.electivesCoursesHours = 0;
            newPlan.requiredCoursesHours = 0;
            newPlan.coursesID = [];
          } else {
            newPlan.requiredCoursesHours = this.plan.requiredCoursesHours;
            newPlan.electivesCoursesHours = this.plan.electivesCoursesHours;
          }
          console.log(newPlan);
          createPlan({ newPlan }).then(({ data }) => {
            if (data.status === 200) {
              this.$message({
                message: '创建成功',
                type: 'success',
              });
              this.$router.go(0);
            } else {
              this.$message.error('网络开小差了，请重试');
            }
          });
        } else {
          console.log('error submit!!');
          // this.$message.error("请检查表格");
          return false;
        }
      });
    },
    allowTestOnlyFun(val) {
      console.log(val);
      if (val) {
        this.$confirm('允许直接考试将使所有课程转换为选修课，选修课学时变为0！ 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.electivesCoursesHours = 0;
            this.requiredCoursesHours = 0;
            this.$message({
              type: 'success',
              message: '设置成功!',
            });
          })
          .catch(() => {
            this.plan.allowTestOnly = false;
            this.$message({
              type: 'info',
              message: '已取消',
            });
          });
      } else {
        this.electivesCoursesHours = this.plan.electivesCoursesHours;
        this.requiredCoursesHours = this.plan.requiredCoursesHours;
      }
    },
  },
};
</script>

<style scoped>
.button {
  display: block;
  text-align: center;
  margin-top: 10px;
}
.need-exam {
  margin-top: 0px;
}
.el-dialog__title {
  width: 40px;
  height: 10px;
  font-size: 10px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #000000;
  line-height: 10px;
}
</style>
