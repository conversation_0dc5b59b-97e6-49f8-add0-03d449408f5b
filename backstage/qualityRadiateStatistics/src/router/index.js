import Vue from "vue";
import VueRouter from "vue-router";
import settings from "@root/publicMethods/settings";
import Index from "../views/Index.vue";

Vue.use(VueRouter);

const createRouter = () =>
  new VueRouter({
    mode: "history",
    base: process.env.BASE_URL,
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: [
      {
        path: settings.admin_base_path + "/qualityRadiateStatistics",
        name: "index",
        component: Index,
      },
    ],
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
