<template>
  <div id="serve-app" class="butlerServiceOrg">
    <div :class="classObj">
      <div class="main-container">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
import { initEvent } from "@root/publicMethods/events";
export default {
  name: "App",
  data() {
    return {
      sidebarOpened: true,
      device: "desktop",
    };
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    },
  },
  mounted() {
    initEvent(this);
  },
  methods: {},
};
</script>
<style></style>
