<script>
import { Popconfirm } from "element-ui"

export default {
  name: "IndicatorTableComp",
  components: {
    Popconfirm
  },
  props: {
    tableData: Array,
    default: () => []
  },
  data() {
    return {}
  },
  mounted() {

  },
  methods: {
    handleEdit(val) {
      this.$emit("edit", val)
    },
    handleDelete(val) {

      this.$confirm(`你确定删除该条数据吗？`)
        .then(_ => {
          this.$emit("delete", val)
        })
        .catch(_ => { });
    },
  }
}
</script>

<template>
  <div class="indicator-table">
    <table>
      <!-- 表头 -->
      <thead>
        <tr>
          <th>一级指标</th>
          <th>二级指标</th>
          <th>三级指标</th>
          <th>操作</th>
        </tr>
      </thead>
      <!-- 表格体 -->
      <tbody>
        <tr v-for="(item1, index1) in tableData">
          <td class="bnl bnr">{{ item1.name }}</td>
          <td colspan="3" class="pz">
            <table class="bn">
              <tbody>
                <tr v-for="(item2, index2) in item1.children">
                  <td class="bnl bnr bnt" :class="{ bnb: item1.children.length === index2 + 1 }">{{ item2.name }}</td>
                  <td colspan="2" class="pz bnr bnt" :class="{ bnb: item1.children.length == index2 + 1 }">
                    <table class="bn">
                      <tbody class="bn">
                        <tr class="bn" v-for="(item3, index3) in item2.children">
                          <td class="bnl bnr bnt" :class="{ bnb: item2.children.length === index3 + 1 }">{{ item3.name
                          }}
                          </td>
                          <td class="bnr" :class="{ bnb: item2.children.length === index3 + 1 }">
                            <div class="operate">
                              <el-button type="primary" @click="handleEdit(item3)">编辑</el-button>
                              <el-button slot="reference" type="danger" @click="handleDelete(item3)">删除</el-button>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<style lang="scss" scoped>
@import url("./index.moudle.scss")
</style>