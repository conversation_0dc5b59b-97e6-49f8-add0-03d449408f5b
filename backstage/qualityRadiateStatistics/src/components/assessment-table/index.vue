<script>
import { Popconfirm } from "element-ui"

export default {
  name: "AssessmentTableComp",
  components: {
    Popconfirm
  },
  props: {
    tableData: Array,
    hidden: <PERSON><PERSON><PERSON>,
    edit: <PERSON><PERSON><PERSON>
  },
  data() {
    return {}
  },
  mounted() {

  },
  watch: {
    tableData: {
      handler() {
        console.log("aaa")
        let assessment_order = 0
        this.tableData.forEach(a => {
          a.children.forEach(b => {
            b.children.forEach(c => {
              c.assessment_order = assessment_order;
              assessment_order += 1
            })
          })
        })
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleEdit(val) {
      this.$emit("edit", val)
    },
    handleDelete(val) {

      this.$confirm(`你确定删除该条数据吗？`)
        .then(_ => {
          this.$emit("delete", val)
        })
        .catch(_ => { });
    },
    moveUp(items, index) {
      if (index > 0) {
        // 删除 index 位置的元素
        const element = items.splice(index, 1)[0];
        // 插入到 index-1 的位置
        items.splice(index - 1, 0, element);
      }
    },
    moveDown(items, index) {
      if (index < items.length - 1) { // 修复原代码中的 this.items -> items
        // 删除 index 位置的元素
        const element = items.splice(index, 1)[0];
        // 插入到 index+1 的位置
        items.splice(index + 1, 0, element);
      }
    }
  }
}
</script>

<template>
  <div class="assessment-table">
    <table>
      <!-- 表头 -->
      <thead>
        <tr>
          <th>一级指标</th>
          <th>二级指标</th>
          <th>考核内容</th>
          <th>考核内容序号</th>
          <th>评分细则及说明</th>
          <th>关键项</th>
          <th>分值</th>
          <th>结果选项</th>
          <th v-if="!hidden">操作</th>
        </tr>
      </thead>
      <!-- 表格体 -->
      <tbody>
        <tr v-for="(item1, index1) in tableData" :key="item1.id">
          <td class="bnl bnr">
            {{ item1.name }}
            <span class="move">
              <el-link v-if="edit && index1 != 0" type="primary" @click="moveUp(tableData, index1)">上移</el-link>
              <el-link v-if="edit && index1 != tableData.length - 1" type="primary"
                @click="moveDown(tableData, index1)">下移</el-link>
            </span>
          </td>
          <td :colspan="hidden ? 7 : 8" class="pz">
            <table class="bn">
              <tbody>
                <tr v-for="(item2, index2) in item1.children" :key="item2.id">
                  <td class="bnl bnr bnt" :class="{ bnb: item1.children.length === index2 + 1 }">
                    {{ item2.name }}
                    <span class="move">
                      <el-link v-if="edit && index2 != 0" type="primary"
                        @click="moveUp(item1.children, index2)">上移</el-link>
                      <el-link v-if="edit && index2 != item1.children.length - 1" type="primary"
                        @click="moveDown(item1.children, index2)">下移</el-link>
                    </span>
                  </td>
                  <td :colspan="hidden ? 6 : 7" class="pz bnr bnt"
                    :class="{ bnb: item1.children.length == index2 + 1 }">
                    <table class="bn">
                      <tbody class="bn">
                        <tr class="bn" v-for="(item3, index3) in item2.children" :key="item3.id">
                          <td class="bnl bnr bnt" :class="{ bnb: item2.children.length === index3 + 1 }">
                            {{ item3.name }}
                            <span class="move">
                              <el-link v-if="edit && index3 != 0" type="primary"
                                @click="moveUp(item2.children, index3)">上移</el-link>
                              <el-link v-if="edit && index3 != item2.children.length - 1" type="primary"
                                @click="moveDown(item2.children, index3)">下移</el-link>
                            </span>
                          </td>
                          <td class="bnt" :class="{ bnb: item2.children.length === index3 + 1 }">
                            {{ item3.assessment_order }}
                          </td>
                          <td class="bnt" :class="{ bnb: item2.children.length === index3 + 1 }">
                            {{ item3.rule }}
                          </td>
                          <td class="bnt" :class="{ bnb: item2.children.length === index3 + 1 }">
                            <span v-if="item3.is_key === '1'">▲</span>
                          </td>
                          <td class="bnt" :class="{ bnb: item2.children.length === index3 + 1 }">
                            {{ item3.score }}
                          </td>
                          <td class="bnt" :class="{ bnb: item2.children.length === index3 + 1 }">
                            <span v-for="option in item3.result_option" :key="option">
                              {{ option }}
                            </span>
                          </td>
                          <td class="bnr" :class="{ bnb: item2.children.length === index3 + 1 }" v-if="!hidden">
                            <div class="operate">
                              <el-button type="primary" size="mini" @click="handleEdit(item3)">编辑</el-button>
                              <el-button slot="reference" size="mini" type="danger"
                                @click="handleDelete(item3)">删除</el-button>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<style lang="scss" scoped>
@import url("./index.moudle.scss")
</style>