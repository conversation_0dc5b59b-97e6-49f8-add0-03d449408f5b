.assessment-table {
  width: 100%;

  table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    border: 1px solid #ebeef5;

    thead {
      color: rgb(96, 98, 102);
      font-weight: 500;
      font-size: 14px;
      background-color: #f5f7fa;
    }

    tbody {
      th,
      td {
        font-size: 14px;
        font-weight: normal;
        color: #606266;
      }

      td {
        text-align: center;
        position: relative;
      }
    }

    th,
    td {
      border: 1px solid #ebeef5;
      padding: 28px;
    }

    .pz {
      padding: 0;
    }

    .bn {
      border: none;
    }

    .bnl {
      border-left: none;
    }

    .bnr {
      border-right: none;
    }

    .bnt {
      border-top: none;
    }

    .bnb {
      border-bottom: none;
    }

    .operate {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;
    }

    .move {
      position: absolute;
      right: 8px;
      bottom: 8px;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }
}
