export const fillMixin = {
  methods: {
    fillHandler(row, column, type, tableField, tableData) {
      const field = tableField.filter(item => {
        return item.label === column.label;
      })[0].name;
      const index = this[tableData][type].findIndex(item => {
        return item === row;
      });
      index < this[tableData][type].length - 1 && !this[tableData][type][index + 1][field] && row[field] && this.$confirm('是否向下填充', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        for (let i = index; i < this[tableData][type].length; i++) {
          if (this[tableData][type][i + 1][field]) {
            break;
          }
          this[tableData][type][i + 1][field] = this[tableData][type][i][field];
        }
      }).catch(() => {});
    },
  },
};
