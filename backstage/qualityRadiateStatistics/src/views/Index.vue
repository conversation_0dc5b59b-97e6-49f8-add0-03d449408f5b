<script>
import * as echarts from 'echarts';

import { getAreaDict, getCheckDict, getRadiateStatistics, getSupervisorOpinionStatistics, getQualityRanking } from "@/api/index"

export default {
  name: "QHS",
  data() {
    return {
      search: {
        area_code: null,
        evaluate_time: [],
        check_list_id: null
      },
      area_code: [],
      check_list: [],
      sta_data: {},
      chartInstanceFC: null,
      chartInstanceHX: null,
      chartInstanceWL: null,
      chartInstanceSW: null,
      chartInstanceDD: null,

      /* 排名 */
      rankingSearch: {
        check_list_id: null,
      },
      rankingList: [],
      rankingListPage: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      rankingCheckTitle: "",
    }
  },
  async created() {
    await this.getCheckList()
    this.getRankingList()
  },
  async mounted() {
    await this.getAreaDict()
    this.initEchartsContainer()
    this.getRadiateStatistics()
    this.getSupervisorOpinionStatistics()
  },
  beforeDestroy() {
    this.destroyEcharts()
  },
  methods: {
    // 获取管辖区域字典
    async getAreaDict() {
      try {
        const { status, data } = await getAreaDict()
        if (status != 200) return this.$message.error("地区数据获取失败")
        this.area_code = data
        // 取第一个作为地区默认值
        this.search.area_code = data[0].area_code
      } catch (error) {
        this.$message.error("地区数据获取失败")
      }
    },
    // 获取检查表
    async getCheckList() {
      try {
        const { status, data } = await getCheckDict({
          type: '2'
        })
        if (status != 200) return this.$message.error("检查表数据获取失败")
        this.check_list = data
        if (data.length > 0) {
          this.rankingSearch.check_list_id = data[0].id
        }
      } catch (error) {
        this.$message.error("检查表数据获取失败")
      }
    },
    handleSearch() {
      this.getRadiateStatistics()
      this.getSupervisorOpinionStatistics()
    },
    handleResetSearch() {
      this.search = {
        area_code: null,
        evaluate_time: [],
        check_list_id: null
      }
      this.search.area_code = this.area_code[0].area_code
      this.getRadiateStatistics()
      this.getSupervisorOpinionStatistics()
    },
    /* 获取饼图数据 */
    async getRadiateStatistics() {
      try {
        const { status, data } = await getRadiateStatistics(this.search)
        if (status != 200) return this.$message.error("数据获取失败")
        this.sta_data = data
        this.initEcharts(this.chartInstanceFC, data.quality)
        this.initEcharts(this.chartInstanceHX, data.evaluate)
        this.initEcharts(this.chartInstanceWL, data.result)
        this.initEcharts(this.chartInstanceSW, data.report)
      } catch (error) {
        this.$message.error("数据获取失败")
      }
    },
    // 初始化 echarts 容器
    initEchartsContainer() {
      this.chartInstanceFC = echarts.init(this.$refs.chartFC)
      this.chartInstanceHX = echarts.init(this.$refs.chartHX)
      this.chartInstanceWL = echarts.init(this.$refs.chartWL)
      this.chartInstanceSW = echarts.init(this.$refs.chartSW)
      this.chartInstanceDD = echarts.init(this.$refs.chartDD)
      window.addEventListener('resize', () => {
        this.chartInstanceFC.resize();
        this.chartInstanceHX.resize();
        this.chartInstanceWL.resize();
        this.chartInstanceSW.resize();
        this.chartInstanceDD.resize();
      });
    },
    // 初始化 echarts 方法
    initEcharts(chartInstance, chartData) {
      try {
        const option = {
          tooltip: {
            trigger: 'item'
          },
          label: {
            show: true,
            formatter: function (params) {
              // 在文字标签旁显示百分比
              // return `${params.name}\n${params.percent}%`;

              // 可选：更美观的格式
              return `${params.name}：${params.percent}%`;
            },
            position: 'outside',
            alignTo: 'labelLine',
            margin: 15,
            rich: {
              name: {
                fontSize: 12,
                fontWeight: 'normal',
                color: '#555',
                padding: [2, 0, 0, 0]
              },
              percent: {
                fontSize: 14,
                fontWeight: 'normal',
                color: '#e74c3c',
                padding: [8, 0, 0, 0]
              }
            }
          },
          series: [
            {
              name: '评分情况',
              type: 'pie',
              radius: '70%',
              data: chartData,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
        chartInstance.setOption(option)
      } catch (error) {
        console.error('初始化图表失败:', error)
      }
    },
    // 销毁 echarts
    destroyEcharts() {
      this.chartInstanceFC.dispose();
      this.chartInstanceFC = null
      this.chartInstanceHX.dispose();
      this.chartInstanceHX = null
      this.chartInstanceWL.dispose();
      this.chartInstanceWL = null
      this.chartInstanceSW.dispose();
      this.chartInstanceSW = null
      this.chartInstanceDD.dispose();
      this.chartInstanceDD = null
      window.removeEventListener("resize")
    },

    /* 柱状图 */
    async getSupervisorOpinionStatistics() {
      try {
        const { status, data } = await getSupervisorOpinionStatistics(this.search)
        if (status != 200) return this.$message.error("数据获取失败")
        const xData = data.map(item => item.org_name)
        const yData = data.map(item => item.problem_num)
        this.initBarChart(this.chartInstanceDD, xData, yData)
      } catch (error) {
        this.$message.error("数据获取失败")
      }
    },
    initBarChart(instance, xData, yData) {
      try {
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              data: xData,
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              // 关键配置：强制 Y 轴刻度为整数
              axisLabel: {
                formatter: function (value) {
                  // 数值取整后显示
                  return Math.floor(value);
                }
              },
              // 确保刻度间隔为整数
              interval: 1, // 设置刻度间隔为 1
              minInterval: 1 // 最小间隔为 1
            }
          ],
          series: [
            {
              name: '存在问题数量',
              type: 'bar',
              barWidth: '12%',
              data: yData
            }
          ]
        }
        instance.setOption(option)
      } catch (error) {
        console.error('初始化图表失败:', error)
      }
    },

    /* 排名 */
    async getRankingList() {
      try {

        const res = this.check_list.find((item) => item.id === this.rankingSearch.check_list_id)

        if (res) {
          this.rankingCheckTitle = res.check_name
        }

        const { status, data } = await getQualityRanking({
          ...this.rankingSearch,
          ...this.rankingListPage,
          type: "2"
        })
        if (status != 200) {
          return this.$message.error("列表数据获取失败")
        }
        this.rankingList = data.records
        this.rankingListPage.total = data.total
      } catch (error) {
        this.$message.error("列表数据获取失败")
      }
    },
    handleRankingResetSearch() {
      if (this.check_list.length > 0) {
        this.rankingSearch.check_list_id = this.check_list[0].id
      }
      this.rankingListPage = {
        page: 1,
        pageSize: 10,
        total: 0
      }
      this.handleRankingSearch()
    },
    handleRankingSearch() {
      this.getRankingList()
    },
    handleRankingListPageChange() {
      this.getRankingList()
    },
  }
}
</script>

<template>
  <div class="QHC_check-list">
    <div class="header">
      <div class="search">
        <el-select style="width: 180px;" v-model="search.area_code" placeholder="请选择地区">
          <el-option v-for="item in area_code" :key="item.area_code" :label="item.name" :value="item.area_code" />
        </el-select>
        <el-select style="width: 180px;" v-model="search.check_list_id" placeholder="请选择检查表">
          <el-option v-for="item in check_list" :key="item.id" :label="item.check_name" :value="item.id" />
        </el-select>
        <el-date-picker v-model="search.evaluate_time" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="evaluatePickerOptions" style="width: 440px;">
        </el-date-picker>
        <div>
          <el-button @click="handleResetSearch">重置</el-button>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </div>
      </div>
    </div>
    <div class="check-table">
      <div class="row">
        <div class="card">
          <div class="title">职业病检测机构质量情况</div>
          <div class="content">
            <div ref="chartFC" class="chart-container"></div>
          </div>
        </div>
        <div class="card">
          <div class="title">检测机构质量在线评价</div>
          <div class="content">
            <div ref="chartHX" class="chart-container"></div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="card">
          <div class="title">检测机构质量结果汇总</div>
          <div class="content">
            <div ref="chartWL" class="chart-container"></div>
          </div>
        </div>
        <div class="card">
          <div class="title">质控报告及异常结果上报</div>
          <div class="content">
            <div ref="chartSW" class="chart-container"></div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="card">
          <div class="title">检测机构质量督导意见</div>
          <div class="content">
            <div ref="chartDD" class="chart-container"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="ranking-table_container">
      <div class="header">
        <div class="title">{{ rankingCheckTitle }}</div>
        <div class="search">
          <el-select style="width: 180px;" v-model="rankingSearch.check_list_id" placeholder="请选择检查表">
            <el-option v-for="item in check_list" :key="item.id" :label="item.check_name" :value="item.id" />
          </el-select>
          <div>
            <el-button @click="handleRankingResetSearch">重置</el-button>
            <el-button type="primary" @click="handleRankingSearch">查询</el-button>
          </div>
        </div>
      </div>
      <div class="ranking-table">
        <el-table :data="rankingList" border style="width: 100%; margin-top: 20px" :header-cell-style="{
          color: 'black',
          'background-color': '#f5f7fa',
          'font-wight': 300,
        }">
          <el-table-column fixed type="rank" label="排名" width="160px" align="center">
            <template slot-scope="scope">
              <span
                :class="[scope.row.rank == '1' ? 'index_one' : scope.$index + 1 == '2' ? 'index_two' : scope.$index + 1 == '3' ? 'index_three' : '']">
                {{ scope.row.rank }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="org_name" label="考核对象" align="center"></el-table-column>
          <el-table-column prop="score" label="分数" align="center"></el-table-column>
        </el-table>
        <div class="page">
          <el-pagination layout="total, prev, pager, next" :current-page.sync="rankingListPage.page"
            :page-size.sync="rankingListPage.pageSize" :total="rankingListPage.total"
            @current-change="handleRankingListPageChange"></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.QHC_check-list {
  width: 100%;
  height: 100%;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .header {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 24px;

    .search {
      display: flex;
      gap: 24px;
      flex-wrap: wrap;
    }
  }

  .check-table {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .row {
      display: flex;
      gap: 24px;
      flex-wrap: wrap;

      .card {
        flex: 1;
        border-radius: 6px;
        border: 1px solid #000;
        height: 360px;
        min-width: 460px;
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 16px;

        .content {
          flex: 1;
          display: flex;
          justify-content: center;

          .chart-container {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }

  .ranking-table_container {
    width: 100%;
    height: 100%;
    padding-top: 32px;
    min-height: 500px;

    .header {
      display: flex;
      flex-direction: column;
      gap: 24px;

      .title {
        align-self: center;
      }

      .search {
        display: flex;
        gap: 24px;
        flex-wrap: wrap;
      }
    }

    .ranking-table {
      .page {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
      }

      .index_one {
        color: #fec412;
      }

      .index_two {
        color: #b4c0c7;
      }

      .index_three {
        color: #714e3a;
      }
    }
  }
}
</style>