<template>
  <div class="content">
    <div>
      <el-select v-model="timeway" placeholder="请选择时间筛选方式" @change="changeWay">
        <el-option label="时间点" :value="1"></el-option>
        <el-option label="时间段" :value="2"></el-option>
      </el-select>
      &nbsp;
      <el-date-picker v-show="timeway == 1" v-model="search.startTime" value="yyyy-MM-dd"
        value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
      </el-date-picker>
      &nbsp;
      <el-date-picker v-show="timeway == 2" v-model="dateRange" type="daterange" value="yyyy-MM-dd"
        value-format="yyyy-MM-dd" range-separator="至"
        start-placeholder="开始日期" end-placeholder="结束日期">
      </el-date-picker>
      &nbsp;
      <el-cascader class="custom-cascader" v-model="search.areaCode" :options="addressList" clearable collapse-tags
        ref="regAddCas" placeholder="请选择地区">
      </el-cascader>
      &nbsp;
      <el-button type="primary" @click="onSearch">查询</el-button>
      <el-button @click="onReset">重置</el-button>
    </div>
    <el-divider></el-divider>
    <div class="metric-cards">
      <div class="card">
        <div class="metric-value">未佩戴防护用品用人单位数</div>
        <div class="metric-number">{{abnormalCount}}</div>
        <!-- <div class="metric-change">+10%</div> -->
      </div>
      <div class="card">
        <div class="metric-value">未佩戴防护用品人数</div>
        <div class="metric-number">{{abnormalEmployeeCount}}</div>
        <!-- <div class="metric-change">+10%</div> -->
      </div>
      <div class="card">
        <div class="metric-value">监督处罚用人单位数</div>
        <div class="metric-number">{{punishmentCount}}</div>
        <!-- <div class="metric-change">+10%</div> -->
      </div>
      <div class="card">
        <div class="metric-value">防护用品总数</div>
        <div class="metric-number">{{countList}}</div>
        <!-- <div class="metric-change">+10%</div> -->
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-col">
        <div ref="chart1" class="chart-container"></div>
      </div>
      <div class="chart-col">
        <div ref="chart2" class="chart-container"></div>
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-col">
        <div ref="chart3" class="chart-container"></div>
      </div>
    </div>
    <!-- 1.防护用品使用情况 -->
    <div class="dr-title">
      <span class="dr-title-left">
        <span class="dr-title-text">防护用品使用情况</span>
      </span>
      <span class="dr-title-divider"><el-divider></el-divider></span>
      <span class="dr-title-btn"></span>
    </div>
    <el-table :data="defendProductList" border style="width: 100%;" header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px">
      <el-table-column prop="harmFactor" label="危害因素类型"></el-table-column>
      <el-table-column prop="product" label="防护用品名称"></el-table-column>
      <el-table-column prop="count" label="防护用品数量"></el-table-column>
      <el-table-column prop="contactCount" label="接触人数"></el-table-column>
      <el-table-column prop="wearRate" label="配备比例"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { getoproAbnormalStatistics,getUserSession,getDistrictList } from "@/api/index";
export default {
  data() {
    return {
      search:{
        startTime:'',
        endTime:'',
        areaCode: '',
      },
      dateRange: [],
      timeway: 2,
      // 1.防护用品使用情况
      defendProductList: [
      ],

      // 2.未佩戴预警情况
      alertStats: [
      ],

      // 3.监督处罚情况
      penaltyStats: [
      ],
      countList: 0,// 各危害因素种类防护用品数量
      abnormalCount:0,// 未佩戴单位数
      abnormalEmployeeCount:0,// 未佩戴人数
      punishmentCount:0,// 处罚单位数
      userInfo:{},
      addressList: [],
    };
  },
  created(){
    this.getAddressList()
  },
  methods: {
    async getAddressList(){
      const res =  await getUserSession()
      this.userInfo = res.data && res.data.userInfo || {}
      const {data}  = await getDistrictList({level:0})
      this.addressList = data && data.docs|| []
      if (this.userInfo.regAdd && this.userInfo.area_code) {
        this.addressList.unshift({
          area_code:this.userInfo.area_code,
          name:this.userInfo.regAdd[this.userInfo.regAdd.length -1],
          level:1
        })
        this.addressList = this.addressList.map((item) => ({
          value: item.area_code,
          label: item.name,
          area_code: item.area_code,
          leaf: item.level >= 1,
          disabled: item.name === '市辖区' ? true : false,
        }));
      }
      this.getList()
    },
    async getList(){
      if(!this.search.areaCode) this.search.areaCode = this.userInfo.area_code
      const params = {...this.search}
      if(this.timeway == 2) {
        params.startTime = this.dateRange && this.dateRange[0] || '';
        params.endTime = this.dateRange && this.dateRange[1] || '';
      } else {
        params.startTime = this.search.startTime
        params.endTime = '';
      }
      params.areaCode = Array.isArray(params.areaCode) ? params.areaCode[0] : params.areaCode;
      const res = await getoproAbnormalStatistics(params)
      this.defendProductList = res.data.defendProductList
      this.alertStats = res.data.abnormalList
      this.penaltyStats = res.data.abnormalList
      this.countList = this.defendProductList.reduce((acc, cur) => acc + cur.count, 0)
      this.abnormalCount = this.alertStats.reduce((acc, cur) => acc + cur.abnormalCount, 0)
      this.abnormalEmployeeCount = this.alertStats.reduce((acc, cur) => acc + cur.abnormalEmployeeCount, 0)
      this.punishmentCount = this.penaltyStats.reduce((acc, cur) => acc + cur.punishmentCount, 0)
      this.initChart1()
      this.initChart2()
      this.initChart3()
    },
    initChart1() {
      try {
        const chart = echarts.init(this.$refs.chart1)
        const option = {
          title: { text: '未佩戴防护用品预警情况分布' },
          tooltip:{ trigger: 'axis' },
          legend: { data: ['未佩戴单位数', '未佩戴人数'],top:20},
          xAxis: { 
            data: this.alertStats.map(item => item.areaName),
            axisLabel: { interval: 0, rotate: 30 }
          },
          yAxis: {},
          series: [{ 
            name: '未佩戴单位数', 
            type: 'bar', 
            data: this.alertStats.map(item => item.abnormalCount) 
          },{
            name: '未佩戴人数', 
            type: 'bar', 
            data: this.alertStats.map(item => item.abnormalEmployeeCount) 
          }]
        }
        chart.setOption(option)
      } catch (error) {
        console.error('初始化图表失败:', error)
      }
    },
    initChart2() {
      try {
        const chart = echarts.init(this.$refs.chart2)
        const option = {
          title: { 
            text: '各危害因素种类防护用品数量占比',
          },
          tooltip: {
            trigger: 'item'
          },
          series: [{ 
            type: 'pie', 
            radius: '50%',
            data: this.defendProductList.map(item => ({ 
              name: item.product, 
              value: item.count 
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }]
        }
        chart.setOption(option)
      } catch (error) {
        console.error('初始化饼图失败:', error)
      }
    },
    initChart3() {
      try {
        const chart = echarts.init(this.$refs.chart3)
        const option = {
          title: { text: '监督处罚情况' },
          tooltip:{ trigger: 'axis' },
          legend: { data: ['处罚单位数']},
          tooltip: {
            trigger: 'axis'
          },
          xAxis: { 
            data: this.penaltyStats.map(item => item.areaName),
            axisLabel: { interval: 0, rotate: 30 }
          },
          yAxis: {},
          series: [{ 
            name: '处罚单位数', 
            type: 'bar', 
            data: this.penaltyStats.map(item => item.punishmentCount)
          }]
        }
        chart.setOption(option)
        window.addEventListener('resize', () => chart.resize())
      } catch (error) {
        console.error('初始化折线图失败:', error)
      }
    },
    onSearch(){
      this.getList()
     },
     onReset(){
      this.search={
        startTime:'',
        endTime:'',
        areaCode:this.userInfo.area_code,
      }
      this.dateRange= []
      this.getList()
     },
     changeWay(){
      this.search.startTime = ''
      this.search.endTime = ''
      this.dateRange=[]
     }
  }
};
</script>

<style lang="scss" scoped>
.content {
  padding: 10px 20px;
}
.dr-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    .dr-title-left {
      font-size: 16px;
      font-weight: 500;
      border-left: 6px solid #409eff;
      display: flex;
      height: 24px;
      line-height: 24px;
      .dr-title-text {
        margin-left: 10px;
      }
    }
    .dr-title-divider {
      flex: 1;
      padding: 0 10px;
      el-divider {
      }
    }
  }


.metric-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.card {
  background: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 增加了垂直偏移量和模糊半径，调整了颜色 */
}

.metric-value {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.metric-number {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
}

.metric-change {
  color: #4CAF50;
  font-size: 12px;
}


.chart-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-col {
  flex: 1;
  background: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 增加了垂直偏移量和模糊半径，调整了颜色 */
}

.chart-container {
  height: 300px;
}
</style>