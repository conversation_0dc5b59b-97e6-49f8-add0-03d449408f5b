<template>
  <div class="content">
    <div ref="chartRef" style="width: 100%; height: 400px; margin-top: 20px"></div>
  </div>
</template>

<script>
import {statisticByYear} from '@/api/index'
import * as echarts from 'echarts'
export default {
  data() {
    return {
      formData:{
        year: '',
        areaCode: '',
        disease: '',
        administerAreaCode:''
      },
      tableData: []
    };
  },
  created() {
  },
  mounted() {
    this.getList()
  },
  methods: {
    async getList(){
      const res = await statisticByYear(this.formData)
      this.tableData = res.data.data
      this.initEcharts()
    },
    initEcharts(){
      // 初始化ECharts实例
      const chart = echarts.init(this.$refs.chartRef);
      // 配置ECharts选项
      const option = {
        title: {
          text: '职业病鉴定人数统计'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          bottom: 0,
          data: ['职业病鉴定人数']
        },
        xAxis: {
          type: 'category',
          data: this.tableData && this.tableData.map((item) => item.year)
        },
        yAxis: {
          type: 'value'
        },
        series: {
          name: '职业病鉴定人数',
          type: 'line',
          data: this.tableData && this.tableData.map((item) => item.count)
        }
      }
      // 使用配置项和数据显示图表
      chart.setOption(option)
    }
  } 
}
</script>

<style lang="scss" scoped>
.content {
  padding: 20px;
}

</style>
