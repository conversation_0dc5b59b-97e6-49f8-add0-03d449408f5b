<template>
  <div>
    <el-row>
      <el-col :span="24"
        ><div class="grid-content bg-purple">
          <el-table size="medium" :data="courseList" border style="width: 100%">
            <el-table-column fixed label="封面" width="120">
              <template slot-scope="scope">
                <el-image
                  style="width: 90px; height: 60px"
                  :src="scope.row.cover"
                  fit="scale-down"
                ></el-image>
              </template>
            </el-table-column>
            <el-table-column fixed prop="name" label="课程名" >
            </el-table-column>
            <el-table-column prop="authorID.name" label="创建人" width="90">
            </el-table-column>
            <el-table-column show-overflow-tooltip label="分类" >
              <template slot-scope="scope">
                <span>{{ joinClassification(scope.row.classification) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="章节数" width="70" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.videoInfos.length + scope.row.documents.length }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="views" label="查看" width="90" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.views }} 次</span>
              </template>
            </el-table-column>
            <el-table-column prop="likes" label="点赞数" width="80" align="center">
            </el-table-column>
            <!-- <el-table-column prop="credit" label="学分" width="60">
            </el-table-column> -->
            <el-table-column prop="classHours" label="学时" width="60" align="center">
            </el-table-column>
            <el-table-column prop="commentLength" label="评论数" width="80" align="center">
            </el-table-column>
            <el-table-column label="发布" width="100" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.complete ? "已发布" : "暂未发布" }}</span>
              </template>
            </el-table-column>
            <el-table-column label="对外开放" width="150" align="center">
              <template slot-scope="scope">
                <span>{{
                  scope.row.allowToOpen
                    ? dateFormat(
                        "YYYY-mm-dd HH:MM",
                        new Date(scope.row.openTime)
                      )
                    : "暂未开放"
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="updateTima" label="更新时间" width="150" align="center">
              <template slot-scope="scope">
                <span>{{
                  dateFormat("YYYY-mm-dd HH:MM", new Date(scope.row.updateTima))
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="210" align="center">
              <template slot-scope="scope">
                <el-button size="small" @click="edit(scope.row)" type="primary"
                  >编辑</el-button
                >
                <el-button
                  type="danger"
                  @click="deleteCourse(scope.row)"
                  size="small"
                  >删除</el-button
                >
                <el-button
                  :disabled="!scope.row.powerStatus"
                  type="warning"
                  @click="editPower(scope.row)"
                  size="small"
                  >课程权限</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div></el-col
      >
      <el-col :span="8" :offset="8">
        <div class="grid-content bg-purple">
          <!-- <el-pagination
            @current-change="currentChange"
            background
            layout="prev, pager, next"
            :total="count"
          >
          </el-pagination> -->
          <el-pagination
            @size-change="pageSizeChange"
            @current-change="currentChange"
            :current-page="current"
            :page-sizes="[5, 10, 30, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="count"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>

    <div>
      <el-dialog
        title="请输入课程名称"
        :visible.sync="deleteDialog"
        width="30%"
      >
        <span>避免误操作，请输入想要删除课程的名称</span>
        <el-input
          size="mini"
          v-model="input"
          placeholder="请输入内容"
        ></el-input>
        <el-button size="mini" type="primary" plain>主要按钮</el-button>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { findPlanQuote, deleteCourse } from "@/api/courses";

export default {
  props: {
    courseList: Array,
    count: Number,
  },
  data() {
    return {
      current: 1,
      pageSize: 10,
      deleteDialog: false,
      checkList:[],
    };
  },
  created() {
    this.$parent.$parent.$parent.getList(1);
  },
  inject: ["reload"],
  methods: {
    joinClassification(array) {
      let result = "";
      const len = array.length;
      for (let index = 0; index < len; index++) {
        const element = array[index].name;
        result += element;
        if (index < len - 1) result += "➤";
      }
      return result;
    },
    dateFormat(fmt, date) {
      let ret;
      const opt = {
        "Y+": date.getFullYear().toString(), // 年
        "m+": (date.getMonth() + 1).toString(), // 月
        "d+": date.getDate().toString(), // 日
        "H+": date.getHours().toString(), // 时
        "M+": date.getMinutes().toString(), // 分
        "S+": date.getSeconds().toString(), // 秒
        // 有其他格式化字符需求可以继续添加，必须转化成字符串
      };
      for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt);
        if (ret) {
          fmt = fmt.replace(
            ret[1],
            ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
          );
        }
      }
      return fmt;
    },
    edit(row) {
      console.log(row);
      this.$router.push({
        name: "courseForm",
        params: { id: row._id },
      });
    },
    editPower(row) {
      this.$router.push({
        name: "power",
        params: { id: row._id },
      });
    },
    currentChange(val) {
      console.log("页面", val,this.checkList);
      this.current = val;
      this.$parent.$parent.$parent.getList({
        current: this.current,
        pageSize: this.pageSize,
      });
    },
    pageSizeChange(val) {
      console.log("页面大小", val);
      this.pageSize = val;
      this.$parent.$parent.$parent.getList({
        current: this.current,
        pageSize: this.pageSize,
      });
    },
    deleteCourse(row) {
      console.log(row);
      this.$confirm(
        "若有培训计划引用此课程，将导致删除失败, 是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          // 查询这个课程是否有被引用
          this.$axios
            .get(findPlanQuote, {
              courseID: row._id,
            })
            .then(({ data }) => {
              if (data.data.count) {
                this.$message({
                  type: "info",
                  message: `该课程被${data.data.count}个培训引用，无法删除`,
                });
              } else {
                this.$prompt("为避免误操作，请输入想要删除课程的名称", "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  inputValidator: (value) => {
                    if (value === row.name) {
                      return true;
                    }
                    return false;
                  },
                  inputPlaceholder: "请输入名称",
                })
                  .then(({ value }) => {
                    if (value === row.name) {
                      this.$axios
                        .get(deleteCourse, {
                          courseID: row._id,
                        })
                        .then(({ data }) => {
                          this.$message({
                            type: "info",
                            message: "删除成功",
                          });
                          this.reload();
                        });
                    }
                  })
                  .catch(() => {
                    this.$message({
                      type: "info",
                      message: "已取消删除",
                    });
                  });
              }
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
};
</script>