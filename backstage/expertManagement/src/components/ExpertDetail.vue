<template>
    <el-dialog :close-on-click-modal="false" title="专家详情" top="6vh" width="70%" :visible.sync="currentIsShow">
        <div class="form-container">
            <div style="margin-bottom: 25px;">
                <el-row>
                    <table style="width:100%;border:1px solid #ddd;">
                        <tr>
                            <th class="left-tittle table-label" colspan="4">
                                <span style="color:red;">*</span>专家姓名
                            </th>
                            <th colspan="4" class="th-input">
                                <span>{{ expertData.name }}</span>
                            </th>
                            <th class="left-tittle table-label" colspan="4" style="text-align: right;">
                                专家类别
                            </th>
                            <th colspan="4" class="th-input">
                                <span>{{ expertData.category_id }}</span>
                            </th>
                            <th class="left-tittle table-label" colspan="4" style="text-align: right;">
                                所属地区
                            </th>
                            <th colspan="4" class="th-input">
                                <span>{{ expertData.area_name &&  Array.isArray(expertData.area_name) ? expertData.area_name.join('/') : ''}}</span>
                            </th>
                        </tr>
                        <tr>
                            <th class="left-tittle table-label" colspan="4">
                                <span style="color:red;">*</span>专家级别
                            </th>
                            <th colspan="4" class="th-input">
                                <span>{{ expertData.level }}</span>
                            </th>
                            <th class="left-tittle table-label" colspan="4" style="text-align: right;">
                                出生日期
                            </th>
                            <th colspan="4" class="th-input">
                                <span>{{ handleTime(expertData.birthday) }}</span>
                            </th>
                            <th class="left-tittle table-label" colspan="4" style="text-align: right;">
                                <span style="color:red;">*</span>证件类型
                            </th>
                            <th colspan="4" class="th-input">
                                <span>{{ expertData.id_type }}</span>
                            </th>
                        </tr>
                        <tr>
                            <th class="left-tittle table-label" colspan="4">
                                <span style="color:red;">*</span>证件号码
                            </th>
                            <th colspan="4" class="th-input">
                                <span>{{ expertData.id_number }}</span>
                            </th>
                            <th class="left-tittle table-label" colspan="4" style="text-align: right;">
                                联系电话
                            </th>
                            <th colspan="4" class="th-input">
                                <span>{{ expertData.phone }}</span>
                            </th>
                            <th class="left-tittle table-label" colspan="4" style="text-align: right;">
                                专业
                            </th>
                            <th colspan="4" class="th-input">
                                <span>{{ expertData.major }}</span>
                            </th>
                        </tr>
                        <tr>
                            <th class="left-tittle table-label" colspan="4">
                                工作单位
                            </th>
                            <th colspan="4" class="th-input">
                                <span>{{ expertData.work_unit }}</span>
                            </th>
                            <th class="left-tittle table-label" colspan="4" style="text-align: right;">
                                学历信息
                            </th>
                            <th colspan="4" class="th-input">
                                <span>{{ expertData.education }}</span>
                            </th>
                            <th class="left-tittle table-label" colspan="4" style="text-align: right;">
                                <span style="color:red;">*</span>专家状态
                            </th>
                            <th colspan="4" class="th-input">
                                <span>{{ expertData.state }}</span>
                            </th>
                        </tr>
                        <tr>
                            <th class="left-tittle table-label" colspan="4">
                                受聘时间
                            </th>
                            <th colspan="4" class="th-input">
                                <span>{{ expertData.employment_date }}</span>
                            </th>
                        </tr>
                    </table>
                </el-row>
            </div>
        </div>

        <TitleTag titleName="资质证书信息"></TitleTag>
        <div class="certificate-container">
            <el-table :data="expertData.certificates" tooltip-effect="light" style="width: 100%"
                @selection-change="handleSelect" stripe border ref="multipleTable"
                header-cell-style="background-color: #f5f7fa; color: #606266;height:46px">
                <el-table-column type="index" width="50" label="序号" align="center"></el-table-column>
                <el-table-column label="证书名称" align="center" prop="name"></el-table-column>
                <el-table-column label="证书类型" align="center" prop="type"></el-table-column>
                <el-table-column label="证书编号" align="center" prop="number"></el-table-column>
                <el-table-column label="获得时间" align="center" prop="get_time"></el-table-column>
                <el-table-column label="颁发机构" align="center" prop="issuing_agency"></el-table-column>
                <el-table-column label="有效期至" align="center" prop="valid_time"></el-table-column>
                <el-table-column label="专业类别" align="center" prop="professional"></el-table-column>
                <el-table-column label="附件" width="150px" align="left" fixed="right">
                    <template slot-scope="scope">
                        <!-- <el-button size="mini" plain  type="text" @click="toOpenFile(scope.row.annex)">查看</el-button> -->
                        <el-button size="mini" plain type="primary" @click="toOpenFile(scope.row.annex)">查看</el-button>
                        <el-button size="mini" plain type="success"
                            @click="toDownloadFile(scope.row.annex, scope.row.name)">下载</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <div class="el-grant-the-staff-foot footer-distance custom-btn" style="margin-top: 20px;">
            <el-button @click="cancel" size="middle">关 闭</el-button>
        </div>
    </el-dialog>
</template>

<script>
import moment from 'moment'
export default {
    name: 'expertDetail',
    props: {
        show: {
            type: Boolean,
            default: false
        },
        expertData: {

        }
    },
    data() {
        return {

        }
    },
    computed: {
        currentIsShow: {
            get() {
                return this.show
            },
            set(val) {
                this.$emit('update:show', val)
            }
        }
    },
    methods: {
        async toDownloadFile(url, name) {
            const a = document.createElement('a');
            a.href = url
            a.download = name + new Date().getTime();
            a.click();
        },
        toOpenFile(url) {
            window.open(url, '_blank')
        },
        cancel() {
            this.currentIsShow = false
        },
        handleTime(data){
            console.log(data,'==========进来了')
            return data ? moment(data).format('YYYY-MM-DD') : ''
        }
    }
}
</script>

<style scoped lang="scss">
.custom-btn {
    width: 100%;
    text-align: right;
}

table {
    table-layout: fixed;
    background: #fff;
    width: 97%;
    margin: 0 auto;
    border-collapse: collapse;

    td {
        padding: 5px 0;
    }

    th {
        font-weight: normal;
        border: 1px solid #e5e5e5;
        // border:1px solid #504e4e;
        padding: 3px 0;
    }

    tr {
        border: 1px solid #e5e5e5;
        width: 100%;
        font-size: 12px;
    }

    .left-tittle {
        background: #F8FCFF;
        // background: #5eabe6;
    }

    .center-tittle {
        background: #F8FCFF;
    }

    .table-label {
        text-align: right;
        padding-right: 5px;
        color: #333;
        font-weight: 500;
        font-size: 12px;
        height: 36px;
    }

    .th-input {
        text-align: center;
        padding: 2px 4px;

        span {
            color: #333;
            font-weight: 500;
        }
    }

    .th-radio {
        text-align: center;
        padding-left: 6px;
        padding-right: 6px;
    }

    .input-width {
        width: 200px;
        background-color: bisque;
    }

    .health-check-th {
        padding: 11px 28px;
        text-align: center;
    }
}
</style>
