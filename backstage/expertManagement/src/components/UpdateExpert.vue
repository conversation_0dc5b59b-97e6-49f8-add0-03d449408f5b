<template>
  <el-dialog :close-on-click-modal="false" title="更新专家信息" top="6vh" width="85%" :visible.sync="currentIsShow">
    <TitleTag titleName="基本信息"></TitleTag>
    <div class="form-container">
      <el-form :model="expertData" :rules="rules" ref="ruleForm" label-width="auto" size="small" :inline="true">
        <el-form-item label="专家姓名" prop="name">
          <el-input style="width: 200px" v-model="expertData.name" placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="专家类别" prop="category_id">
          <el-select v-model="expertData.category_id" 
                     placeholder="请选择">
            <el-option :label="item.classification_name" 
                       :value="item.id" 
                       v-for="item in expertClassifierOptions"
                       :key="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="专家级别" prop="level">
          <el-select v-model="expertData.level" style="width: 200px" placeholder="请选择">
            <el-option v-for="(item, index) in jiBieList" 
              :key="index" 
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

         <el-form-item label="所属地区" prop="area_name">
          <el-cascader @visible-change='visibleChange' 
                       style="width:100%" 
                       v-model="expertData.area_code"
                       :props="workAdd" 
                       clearable 
                       collapse-tags ref="regAddCas"
                      :placeholder="expertData.area_name ? expertData.area_name.join('/') : '请选择地区'">
          </el-cascader>
        </el-form-item>

        <el-form-item label="证件类型" prop="id_type">
          <el-select v-model="expertData.id_type" 
                     style="width: 200px" 
                     placeholder="请选择">
            <el-option :label="item.name" 
              :value="item.id" 
              v-for="item in IDtypeOptions" 
              :key="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="证件号码" prop="id_number">
          <el-input style="width: 200px" v-model.trim="expertData.id_number" placeholder="请输入" @input="handleIdNumberInput"></el-input>
        </el-form-item>

        <el-form-item label="出生日期" prop="birthday">
          <el-date-picker style="width: 200px" 
                          v-model="expertData.birthday" 
                          type="date" 
                          value-format="yyyy-MM-dd"
                          formar="yyyy-MM-dd" 
                          placeholder="选择日期">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="专家性别" prop="birthday">
          <el-select v-model="expertData.gender" style="width: 200px" placeholder="请选择">
            <el-option label="男" value="M"></el-option>
            <el-option label="女" value="F"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="联系电话" prop="phone">
          <el-input style="width: 200px" v-model.trim="expertData.phone" placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="专业" prop="major">
          <el-input style="width: 200px" v-model.trim="expertData.major" placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="工作单位" prop="work_unit">
          <el-input style="width: 200px" v-model.trim="expertData.work_unit" placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="学历信息" prop="education">
          <el-select v-model="expertData.education" style="width: 200px" placeholder="请选择">
            <el-option v-for="(item, index) in levelList" :key="index" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="专家状态" prop="state">
          <el-select v-model="expertData.state" style="width: 200px" placeholder="请选择">
            <el-option v-for="(item, index) in statusList" 
              :key="index" :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="受聘时间" prop="employment_date">
          <el-date-picker style="width: 200px" 
            v-model="expertData.employment_date" 
            type="date"
            value-format="yyyy-MM-dd" 
            formar="yyyy-MM-dd" 
            placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>

    <div class="el-grant-the-staff-foot footer-distance custom-btn">
      <el-button @click="cancel" size="middle">取 消</el-button>
      <el-button @click="confirm" size="middle" type="primary">确认更新</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getAreaCode } from '@/api'
import axios from 'axios'
import TitleTag from './TitleTag.vue'
export default {
  name: 'updateExpert',
  components: {
    TitleTag
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    expertData: {
      default: () => {
        return {}
      }
    },
    areaCodeList: {
      type: Array,
      default: () => {
        return []
      }
    },
    jiBieList: {
      type: Array,
      default: () => {
        return []
      }
    },
    IDtypeOptions: {
      type: Array,
      default: () => {
        return []
      }
    },
    expertClassifierOptions: {
      type: Array,
      default: () => {
        return []
      }
    },
    statusList: {
      type: Array,
      default: () => {
        return []
      }
    },
    levelList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    // 自定义电话号码校验规则
    const validatePhone = (rule, value, callback) => {
      // 如果值为空，直接通过校验（因为不是必填项）
      if (!value) {
        return callback();
      }
      // 简单的电话号码正则表达式，可以根据需求调整
      const phoneReg = /^1[3456789]\d{9}$/;
      if (!phoneReg.test(value)) {
        callback(new Error('请输入有效的电话号码'));
      } else {
        callback();
      }
    }
    return {
      rules: {
        name: [{ required: true, message: "请输入企业名称", trigger: "blur" }],
        level: [{ required: true, message: "请选择专家级别", trigger: "blur" }],
        id_number: [
          { required: true, message: "请输入证件号码", trigger: "blur" },
          { 
            validator: (rule, value, callback) => {
              if (this.expertData.id_type === 1) {
                const reg = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
                if (!reg.test(value)) {
                  return callback(new Error("请输入有效的18位身份证号"));
                }
              }
              callback(); // 非身份证类型或格式正确时通过
            },
            trigger: ["blur", "change"] // 输入完成或证件类型切换时触发
          }
        ],
        employment_date: [{ required: false, message: "请选择受聘时间", trigger: "blur" }],
        state: [{ required: true, message: "请选择专家状态", trigger: "blur" }],
        category_id: [{ required: true, message: "请选择专家类别", trigger: "blur" }],
        phone: [
          // { required: false, message: '电话号码不能为空', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        id_type: [{ required: true, message: "请选择证件类型", trigger: "blur" }],
        districts: [
          { required: true, message: "请选择工作场所", trigger: "blur" },
        ]
      },
      workAdd: {
        lazy: true,
        checkStrictly: true,
        emitPath: false,
        value: 'area_code', // 使用area_code作为参数值
        lazyLoad(node, resolve) {
          if (node.level > 0) {
            node = node.data;
          }
          const url = "/api/address/list";
          axios({
            method: "get",
            url: url,
            params: node,
          }).then((response) => {
            try {
              const districts = response.data.data;
              const a = districts[0];
              let nodes = districts.map((item) => ({
                value: item.name,
                label: item.name,
                id: item.id,
                area_code: item.area_code,
                parent_code: item.parent_code,
                lat: item.lat,
                lon: item.lng,
                merger_name: item.merger_name,
                short_name: item.short_name,
                leaf: item.level >= 2,
              }));

              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          });
        }
      },
      districtListProps: {
        multiple: false,
        checkStrictly: true,
        lazy: true,
        // emitPath:false,
        lazyLoad(node, resolve) {
          console.log(123444444)
          const { level } = node;
          const params = {
            level,
          };
          if (level) {
            params.code = node.data.code;
          }
          getAreaCode(params).then((response) => {
            console.log(123456)
            try {
              const districts = Array.from(response.data);
              let nodes = districts.map((item) => ({
                value: item.code,
                label: item.name,
                code: item.code,
                // leaf: item.level >= 2,
                leaf: item.hasChildren ? item.level >= 2 : item.level >= 3,
                disabled: item.name === '市辖区' ? true : false,
              }));
              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          });
        }
      }
    }
  },
  computed: {
    currentIsShow: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  created() {

  },
  methods: {
    handleIdNumberInput() {
      // 仅当证件类型为身份证
      if (this.expertData.id_type == 1) {
        const idNumber = this.expertData.id_number;
        if (idNumber && /^\d{17}[\dXx]$/.test(idNumber)) {
          const birthStr = idNumber.substring(6, 14);
          const year = birthStr.substring(0, 4);
          const month = birthStr.substring(4, 6);
          const day = birthStr.substring(6, 8);
          const birthday = `${year}-${month}-${day}`;
          this.expertData.birthday = birthday;

          const genderCode = idNumber.charAt(16); // 取倒数第二位
          this.expertData.gender = genderCode % 2 === 1 ? 'M' : 'F'; // 奇数→男（M），偶数→女（F）
        } else {
          this.expertData.birthday = null;
        }
      }
    },
    confirm() {
      this.$confirm("是否确认更新?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          if (this.expertData.certificates) {
            delete this.expertData.certificates
          }
          this.$emit('confirm', this.expertData)
          this.currentIsShow = false
        })
        .catch(() => { })
    },
    cancel() {
      this.currentIsShow = false
    }
  }
}
</script>

<style scoped lang="scss">
.custom-btn {
  width: 100%;
  text-align: right;
}

.certificate-container {
  margin-bottom: 20px;
}
</style>
