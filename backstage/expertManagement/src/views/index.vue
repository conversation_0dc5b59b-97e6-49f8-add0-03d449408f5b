<template>
  <div :class="classObj" class="adminUser">
    <div class="main-container">
      <div class="serveEnterprise">
        <div class="search">
          <el-form ref="form" :model="formSearch" :inline="true" size="mini" label-width="auto">
            <el-form-item label="所属地区">
              <el-cascader @visible-change="visibleChange" 
                            style="width: 100%" 
                            v-model="formSearch.area_code"
                            :props="workAdd" 
                            clearable 
                            collapse-tags 
                            ref="regAddCas" 
                            placeholder="请选择地区"/>
            </el-form-item>

            <el-form-item label="专家类别">
              <el-select v-model="formSearch.category_id" placeholder="请选择">
                <el-option :label="item.classification_name" 
                           :value="item.id" 
                           v-for="item in expertClassifierOptions"
                           :key="item.id">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="专家级别">
              <el-select v-model="formSearch.level" placeholder="请选择">
                <el-option 
                  v-for="(item, index) in jiBieList" 
                  :key="index" :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="关键字查询">
              <el-input v-model="formSearch.keyWord" placeholder="支持姓名、手机号码"></el-input>
            </el-form-item>

            <el-form-item label="专家状态">
              <el-select v-model="formSearch.state" placeholder="请选择">
                <el-option v-for="(item, index) in statusList" 
                           :key="index" 
                           :label="item.label"
                           :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="onSubmit">查询</el-button>
              <el-button @click="reset">重置</el-button>
            </el-form-item>

          </el-form>
        </div>

        <div class="header">
          <div></div>

          <div class="operate">
            <el-button type="primary" size="small" icon="el-icon-plus" @click="addEnterprise">新建</el-button>
            <el-button type="primary" size="small" icon="el-icon-upload2" @click="exportData" plain>导出</el-button>
          </div>
        </div>

        <div class="table">
          <el-table :data="tableData" 
                    tooltip-effect="light" 
                    style="width: 100%" 
                    @selection-change="handleSelect" stripe
                    border 
                    ref="multipleTable" 
                    header-cell-style="background-color: #f5f7fa; color: #606266;height:46px">
            <el-table-column type="index" width="50" label="序号" align="center"></el-table-column>
            <el-table-column label="专家姓名" align="center" prop="name" width="100"></el-table-column>
            <el-table-column label="专家级别" align="center" width="100">
              <template slot-scope="scope">
                {{ scope.row.levelName }}
              </template>
            </el-table-column>
            <el-table-column prop="birthday" label="出生日期" align="center" width="100"></el-table-column>
            <el-table-column prop="id_number" label="身份证号" align="center" min-width="150">
              <template slot-scope="scope">
                {{ scope.row.id_number | handleIdNumber }}
              </template>
            </el-table-column>
            <el-table-column prop="gender" label="性别" align="center" width="60"></el-table-column>
            <el-table-column prop="phone" label="联系电话" align="center" width="100">
              <template slot-scope="scope">
                {{ scope.row.phone | handlePhone }}
              </template>
            </el-table-column>
            <el-table-column prop="major" label="专业" align="center" show-overflow-tooltip width="120"></el-table-column>
            <el-table-column prop="work_unit" label="工作单位" align="center" show-overflow-tooltip min-width="140"></el-table-column>
            <el-table-column prop="education" label="学历信息" align="center" show-overflow-tooltip min-width="120"></el-table-column>
            <el-table-column prop="state" label="专家状态" align="center" width="100">
              <template slot-scope="scope">
                {{ scope.row.state | handleState }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="300px" align="left" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" plain type="primary" @click="toExpertDetail(scope.row.id)">查看</el-button>
                <el-button size="mini" plain type="warning" @click="getDetail(scope.row.id)">专家信息维护</el-button>

                <el-button size="mini" plain type="info" @click="matainCertificate(scope.row.id)">资质证书信息维护</el-button>
                <!-- 推荐条件:状态为正常、休假、出差且level为非兵团级 -->
                <el-button v-show="isDivision &&
                  (scope.row.state == 1 ||
                    scope.row.state == 2 ||
                    scope.row.state == 3) &&
                  scope.row.level == 2
                  " size="mini" type="success" plain @click="expertRecommand(scope.row.id)">专家推荐</el-button>

                <!-- 解聘条件: -->
                <el-button v-show="isDivision &&
                  (scope.row.state == 1 ||
                    scope.row.state == 2 ||
                    scope.row.state == 3)
                  " size="mini" type="danger" plain @click="expertFire(scope.row.id)">解聘</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination">
            <el-pagination @size-change="handleSizeChange" 
                           @current-change="handleCurrentChange"
                          :current-page.sync="pageInfo.curPage" 
                          :page-sizes="[10, 20, 50, 100]" 
                          :page-size.sync="pageInfo.pageSize"
                          background 
                          layout="total,sizes, prev, pager, next" 
                          :total="totalCount">
            </el-pagination>
          </div>

          <el-dialog :title="isCreated ? '添加证书' : '新建专家'" 
                     :visible.sync="addDialogVisible" 
                     :append-to-body="true"
                     :close-on-click-modal="false" 
                     top="6vh" 
                     :width="isCreated ? '85%' : '70%'">
            <el-form v-show="!isCreated" 
                     width="100%" 
                     :model="enterpriseForm" 
                     :rules="rules" ref="ruleForm"
                     label-width="auto" 
                     size="small" 
                     :inline="true">
              <el-form-item label="专家姓名" prop="name">
                <el-input style="width: 200px" v-model="enterpriseForm.name" placeholder="请输入"></el-input>
              </el-form-item>

              <el-form-item label="专家类别" prop="category_id">
                <el-select v-model="enterpriseForm.category_id" placeholder="请选择">
                  <el-option :label="item.classification_name" 
                             :value="item.id" 
                             v-for="item in expertClassifierOptions"
                             :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="专家级别" prop="level">
                <el-select v-model="enterpriseForm.level" style="width: 200px" placeholder="请选择">
                    <el-option v-for="(item, index) in jiBieList" 
                               :key="index" :label="item.label"
                               :value="item.value">
                    </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="所属地区" prop="area_code">
                <el-cascader @visible-change="visibleChange" 
                             style="width: 100%" 
                             v-model="enterpriseForm.area_code"
                             :props="workAdd" 
                             clearable 
                             collapse-tags 
                             ref="regAddCas" 
                             placeholder="请选择地区">
                </el-cascader>
              </el-form-item>

              <el-form-item label="证件类型" prop="id_type">
                <el-select v-model="enterpriseForm.id_type" style="width: 200px" placeholder="请选择">
                  <el-option :label="item.name" 
                             :value="item.id" 
                             v-for="item in IDtypeOptions"
                             :key="item.id">
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="证件号码" prop="id_number">
                <el-input style="width: 200px" v-model.trim="enterpriseForm.id_number" placeholder="请输入" @input="handleIdNumberInput" ></el-input>
              </el-form-item>

              <el-form-item label="出生日期" prop="birthday">
                <el-date-picker style="width: 200px" 
                                v-model="enterpriseForm.birthday" 
                                type="date"
                                value="yyyy-MM-dd HH:mm:ss" 
                                format="yyyy-MM-dd" 
                                placeholder="选择日期">
                </el-date-picker>
              </el-form-item>

              <el-form-item label="专家性别" prop="birthday">
                <el-select v-model="enterpriseForm.gender" style="width: 200px" placeholder="请选择">
                  <el-option label="男" value="M"></el-option>
                  <el-option label="女" value="F"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="联系电话" prop="phone">
                <el-input style="width: 200px" v-model.trim="enterpriseForm.phone" placeholder="请输入"></el-input>
              </el-form-item>

              <el-form-item label="专业" prop="major">
                <el-input style="width: 200px" v-model.trim="enterpriseForm.major" placeholder="请输入"></el-input>
              </el-form-item>

              <el-form-item label="工作单位" prop="work_unit">
                <el-input style="width: 200px" v-model.trim="enterpriseForm.work_unit" placeholder="请输入"></el-input>
              </el-form-item>

              <el-form-item label="学历信息" prop="education">
                <el-select v-model="enterpriseForm.education" style="width: 200px" placeholder="请选择">
                  <el-option v-for="(item, index) in levelList" 
                             :key="index" 
                             :label="item.name"
                             :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="专家状态" prop="state">
                <el-select v-model="enterpriseForm.state" 
                           style="width: 200px" 
                           placeholder="请选择">
                  <el-option v-for="(item, index) in createStateList" 
                             :key="index" 
                             :label="item.label"
                             :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="受聘时间" prop="employment_date">
                <el-date-picker style="width: 200px" 
                                v-model="enterpriseForm.employment_date" 
                                type="date"
                                value="yyyy-MM-dd HH:mm:ss" 
                                format="yyyy-MM-dd" 
                                placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-form>

            <div slot="footer" class="dialog-footer">
              <el-button @click="cancelNew" style="margin-right: 10px">取 消</el-button>
              <el-button type="primary" @click="confirmNew('ruleForm')">确 定</el-button>
            </div>
          </el-dialog>
        </div>
      </div>

      <UpdateExpert :show.sync="isShowEdit" 
                    :expertData="expertDetail" 
                    @confirm="confirmUpdate" 
                    @cancel="cancelUpdate"
                    :areaCodeList="areaCodeList" 
                    :jiBieList="jiBieList" 
                    :IDtypeOptions="IDtypeOptions" 
                    :statusList="statusList"
                    :levelList="levelList" 
                    :expertClassifierOptions="expertClassifierOptions"/>
      
      <RemarkMessage :show.sync="isShowRemark" 
                     :id="id" 
                     :dealwithtype="dealwithtype" 
                     :handleType="handleType"
                     @recommand="recommand" 
                     @dismiss="dismiss" 
                     @cancel="cancel"/>
      
      <DeclarationDialog :show.sync="isShowDeclaration" 
                         :id="currentId" 
                         @confirm="confirmExpertDeclarationReview"/>
      
      <ExpertDetail :show.sync="isShowExpertDetail" 
                    :expertData="detailData" />

      <UpdateCertificate :show.sync="isShowUpdateCertificate" 
                         :certificateData="expertDetail.certificates"
                         :id="expertDetail.id" 
                         @updateList="updateList"/>
    </div>
  </div>
</template>

<script>
import {
  addEnterprise,
  getList,
  addData,
  getEducationalQualifications,
  getAreaCode,
  expertDetail,
  expertClassifier,
  getIDtype,
  deleteCertificate,
  certificateList,
  updateExpert,
  expertRecommend,
  expertDismissal,
  createCertificate,
  expertDeclarationReview,
} from "@/api/index.js";
import XLSX from "xlsx";
import moment from "moment";
import { saveAs } from "file-saver";
import { Message } from "element-ui";
import UpdateExpert from "../components/UpdateExpert.vue";
import RemarkMessage from "../components/RemarkMessage.vue";
import DeclarationDialog from "../components/DeclarationDialog.vue";
import ExpertDetail from "../components/ExpertDetail.vue";
import axios from "axios";
import UpdateCertificate from "../components/UpdateCertificate";
import { initEvent } from "@root/publicMethods/events";

export default {
  components: {
    UpdateExpert,
    RemarkMessage,
    DeclarationDialog,
    ExpertDetail,
    UpdateCertificate,
  },
  data() {
    // 自定义电话号码校验规则
    const validatePhone = (rule, value, callback) => {
      // 如果值为空，直接通过校验（因为不是必填项）
      if (!value) {
        return callback();
      }
      // 简单的电话号码正则表达式，可以根据需求调整
      const phoneReg = /^1[3456789]\d{9}$/;
      if (!phoneReg.test(value)) {
        callback(new Error("请输入有效的电话号码"));
      } else {
        callback();
      }
    };
    return {
      isShowUpdateCertificate: false,
      levelList: [],
      areaCodeList: [],
      dialogVisible: false,
      currentId: "",
      isCity: false,
      form: {
        person: "",
      },
      superId: "",
      enterpriseForm: {
        name: "",
        category_id: null,
        area_code: "",
        level: "",
        birthday: null,
        id_type: null,
        id_number: null,
        phone: "",
        major: "",
        work_unit: "",
        education: null,
        state: null
      },
      rules: {
        name: [{ required: true, message: "请输入专家姓名", trigger: "blur" }],
        id_number: [
          { required: true, message: "请输入证件号码", trigger: "blur" },
          { 
            validator: (rule, value, callback) => {
              if (this.enterpriseForm.id_type === 1) {
                const reg = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
                if (!reg.test(value)) {
                  return callback(new Error("请输入有效的18位身份证号"));
                }
              }
              callback(); // 非身份证类型或格式正确时通过
            },
            trigger: ["blur", "change"] // 输入完成或证件类型切换时触发
          }
        ],
        level: [{ required: true, message: "请选择专家级别", trigger: "blur" }],
        employment_date: [
          { required: false, message: "请选择受聘时间", trigger: "blur" },
        ],
        state: [{ required: true, message: "请选择专家状态", trigger: "blur" }],
        id_type: [
          { required: true, message: "请选择证件类型", trigger: "blur" },
        ],
        category_id: [
          { required: true, message: "请选择专家类别", trigger: "blur" },
        ],
        phone: [
          // { required: false, message: '电话号码不能为空', trigger: 'blur' },
          { validator: validatePhone, trigger: "blur" },
        ],
        districts: [
          { required: true, message: "请选择工作场所", trigger: "blur" },
        ],
      },
      totalCount: 0,
      tableData: [],
      searchParams: {
        curPage: 1,
        limit: 10,
        keyWords: "",
        districts: "",
      },
      districts: "",
      addDialogVisible: false,

      loading: false,
      selectTableData: [],
      formSearch: {
        area_code: null,
        category_id: null,
        level: null,
        state: null,
        keyWord: "",
      },
      pageInfo: {
        curPage: 1,
        pageSize: 10,
      },
      jiBieList: [
        { label: "兵团级", value: 1 },
        { label: "师市级", value: 2 },
        { label: "团镇级", value: 3 },
      ],
      // 1正常 0已注销 2出差 3休假 4已解聘5解聘申请中 6专家推荐
      statusList: [
        { label: "正常", value: 1 },
        { label: "出差", value: 2 },
        { label: "休假", value: 3 },
        { label: "已解聘", value: 4 },
        { label: "解聘申请中", value: 5 },
        { label: "专家推荐", value: 6 },
        { label: "已注销", value: 0 },
      ],
      createStateList: [
        { label: "正常", value: 1 },
        { label: "出差", value: 2 },
        { label: "休假", value: 3 },
      ],
      detailDialogVisible: true,
      expertClassifierOptions: [],
      IDtypeOptions: [],
      user_id: null,
      cerficateData: [],
      expertDetail: {
        id: null,
        certificates: [],
      }, // 专家详情
      isShowEdit: false,
      isDivision: false,
      isShowRemark: false,
      id: null,
      dealwithtype: null,
      handleType: null,
      isCreated: false, // 是否已经创建过专家
      tempFile: null,
      isShowDeclaration: false,
      regAddLength: null,
      isShowExpertDetail: false,
      detailData: null,
      statusOptions: [
        { label: "已注销", value: 0 },
        { label: "正常", value: 1 },
        { label: "出差", value: 2 },
        { label: "休假", value: 3 },
        { label: "已解聘", value: 4 },
        { label: "解聘中", value: 5 },
        { label: "专家推荐", value: 6 },
      ],
      sidebarOpened: false,
      device: "desktop",

      workAdd: {
        lazy: true,
        checkStrictly: true,
        emitPath: false,
        value: "area_code", // 使用area_code作为参数值
        lazyLoad(node, resolve) {
          if (node.level > 0) {
            node = node.data;
          }
          const url = "/api/address/list";
          axios({
            method: "get",
            url: url,
            params: node,
          }).then((response) => {
            try {
              const districts = response.data.data;
              const a = districts[0];
              let nodes = districts.map((item) => ({
                value: item.name,
                label: item.name,
                id: item.id,
                area_code: item.area_code,
                parent_code: item.parent_code,
                lat: item.lat,
                lon: item.lng,
                merger_name: item.merger_name,
                short_name: item.short_name,
                leaf: item.level >= 2,
              }));

              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          });
        },
      },
    };
  },

  async created() {
    await this.getEducationLevel(); // 学历
    // await this.getAreaCodeList()     // 所属区域
    await this.getExpertClassifier();
    await this.getIDtype();
    this.getList();
    initEvent(this); // 初始化 hideSidebar & openSidebar
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    },
  },
  methods: {
    handleIdNumberInput() {
      // 仅当证件类型为身份证 根据身份证号自动填充出生日期和性别
      if (this.enterpriseForm.id_type == 1) {
        const idNumber = this.enterpriseForm.id_number;
        if (idNumber && /^\d{17}[\dXx]$/.test(idNumber)) {
          const birthStr = idNumber.substring(6, 14);
          const year = birthStr.substring(0, 4);
          const month = birthStr.substring(4, 6);
          const day = birthStr.substring(6, 8);
          const birthday = `${year}-${month}-${day}`;
          this.enterpriseForm.birthday = birthday;

          const genderCode = idNumber.charAt(16); // 取倒数第二位
          this.enterpriseForm.gender = genderCode % 2 === 1 ? 'M' : 'F'; // 奇数→男（M），偶数→女（F）
        } else {
          this.enterpriseForm.birthday = null;
        }
      }
    },
    updateList() {
      this.formSearch = this.$options.data()["formSearch"];
      this.pageInfo = this.$options.data()["pageInfo"];
      this.getList();
    },
    async toDownloadFile(url, name) {
      const a = document.createElement("a");
      a.href = url;
      a.download = name + new Date().getTime();
      a.click();
    },
    toOpenFile(url) {
      window.open(url, "_blank");
    },
    async toExpertDetail(id) {
      const res = await expertDetail({ id });
      if (res.status === 200 && res.data) {
        this.expertDetail = res.data;
        this.expertDetail.id_type =
          this.expertDetail.id_type || this.expertDetail.id_type === 0
            ? Number(this.expertDetail.id_type)
            : null;
        this.expertDetail.education =
          this.expertDetail.education || this.expertDetail.education === 0
            ? Number(this.expertDetail.education)
            : null;
        this.detailData = this.handleDetail(this.expertDetail);
        this.isShowExpertDetail = true;
      }
    },
    async matainCertificate(id) {
      const res = await expertDetail({ id });
      if (res.status === 200 && res.data) {
        this.expertDetail = res.data;
        this.isShowUpdateCertificate = true;
      }
    },
    handletoPhone(val) {
      let reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
      let phone = val.replace(reg, "$1****$2");
      return phone;
    },
    handletoIdNumber(val) {
      const reg = /^(\d{6})\d{8}(\d{4})$/;
      return val.replace(reg, "$1********$2");
    },
    handleDetail(data) {
      this.expertClassifierOptions.forEach((item) => {
        if (item.id == data.category_id) {
          data.category_id = item.classification_name;
        }
      });
      this.jiBieList.forEach((item) => {
        if (item.value == data.level) {
          data.level = item.label;
        }
      });
      this.areaCodeList.forEach((item) => {
        if (item.code == data.area_code) {
          data.area_code = item.name;
        }
      });
      this.IDtypeOptions.forEach((item) => {
        if (item.id == data.id_type) {
          data.id_type = item.name;
        }
      });
      this.levelList.forEach((item) => {
        if (item.id == data.education) {
          data.education = item.name;
        }
      });
      this.statusOptions.forEach((item) => {
        if (item.value == data.state) {
          data.state = item.label;
        }
      });
      data.employment_date = data.employment_date ? moment(data.employment_date).format("YYYY-MM-DD") : ''
      data.id_number = this.handletoIdNumber(data.id_number);
      data.phone = this.handletoPhone(data.phone);
      return data;
    },
    async addSuccess(response) {
      if (response.status === 200 && response.data) {
        // 获取资质证书列表
        this.$message.success("添加成功,可继续添加。");
        await this.getCertificateList();
      }
    },
    async getDeleteCertificate(id) {
      const res = await deleteCertificate({ id });
      if (res.status === 200 && res.data) {
        this.$message.success("删除成功。");
      }
    },
    async getCertificateList() {
      if (!this.user_id) {
        this.$message.warning("专家ID不存在,请先创建专家。");
        return;
      }
      const res = await certificateList({ user_id: this.user_id });
      if (res.status === 200 && res.data && Array.isArray(res.data)) {
        this.cerficateData = res.data;
      }
    },
    checkoutUpload(item) { },
    // 获取证件类型
    async getIDtype() {
      const res = await getIDtype();
      if (res && res.status == 200 && res.data) {
        this.IDtypeOptions = res.data;
      }
    },
    async getExpertClassifier() {
      const res = await expertClassifier();
      if (res.status === 200 && res.data) {
        this.expertClassifierOptions = res.data;
      }
    },
    handleEducation(val) {
      let education = "";
      if (this.levelList && Array.isArray(this.levelList)) {
        this.levelList.forEach((item) => {
          if (val == item.id) {
            education = item.name;
          }
        });
        return education;
      }
    },
    // 获取学历数据
    async getEducationLevel() {
      try {
        const res = await getEducationalQualifications();
        if (res.status === 200 && res.data && Array.isArray(res.data)) {
          this.levelList = res.data;
        }
      } catch (error) {
        Message({
          type: "danger",
          message: error,
        });
      }
    },
    // 获取所属区域编码数据
    async getAreaCodeList() {
      const res = await getAreaCode();
      if (res.status === 200 && res.data && Array.isArray(res.data)) {
        this.areaCodeList = res.data;
      }
    },
    addSecondList() {
      // 判断上次是否有正在添加的操作，若有则不允许再次添加
      if (this.cerficateData.length > 0) {
        let flagValue = false;
        this.cerficateData.forEach((item) => {
          if (!item.id) {
            // 则说明有
            flagValue = true;
          }
        });
        if (flagValue) {
          Message({
            type: "warning",
            message: "您有未添加完成的证书，请按顺序依次添加。",
          });
          return;
        }
      }
      this.tempFile = "";
      this.cerficateData.push({
        name: "",
        type: "",
        user_id: this.user_id,
        disabled: false,
        get_time: null,
        issuing_agency: "",
        valid_time: null,
        professional: null,
        number: null,
      });
    },
    deletePersonMenu(id) {
      this.$confirm("确定要删除这一项吗?", "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const res = await deleteCertificate({ id });
          if (res.status === 200 && res.data) {
            this.getCertificateList();
            this.$message.success("删除成功。");
          }
        })
        .catch(() => { });
    },
    handleChange(value) {
      console.log(value);
    },
    handleSelect(selection) {
      this.selectTableData = selection;
    },

    async addEnterprise() {
      this.addDialogVisible = true;
    },

    cancelNew() {
      this.enterpriseForm = this.$options.data()["enterpriseForm"];
      this.user_id = null;
      this.addDialogVisible = false;
      this.getList();
    },

    //确定新增企业
    confirmNew(formName) {
      try {
        this.$refs[formName].validate(async (valid) => {
          if (valid) {
            const reg =
              /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
            if (this.enterpriseForm.id_type == 1) {
              if (
                !this.enterpriseForm.id_number ||
                !reg.test(this.enterpriseForm.id_number)
              ) {
                this.$message.warning("请填写正确的身份证号。");
                return;
              }
            }
            const res = await addData(this.enterpriseForm);
            if (res && res.status === 200 && res.data) {
              this.expertDetail = this.$options.data()["expertDetail"];
              this.user_id = res.data.id;
              this.expertDetail.id = res.data.id;
              this.addDialogVisible = false;
              this.isShowUpdateCertificate = true;
              this.isCreated = true;
              this.$message.success("新增成功。");
              this.getList();
            }
          } else {
            return false;
          }
        });
      } catch (error) {
        this.$message.error("出错了,请联系管理员。");
      }
    },

    async handleSizeChange(val) {
      this.formSearch.curPage = this.pageInfo.curPage;
      this.formSearch.pageSize = this.pageInfo.pageSize;
      this.getList();
    },
    async handleCurrentChange(val) {
      this.formSearch.curPage = this.pageInfo.curPage;
      this.formSearch.pageSize = this.pageInfo.pageSize;
      this.getList();
    },
    async getList() {
      try {
        let params = {
          ...this.pageInfo,
        };
        if (this.formSearch.area_code) {
          params.area_code = this.formSearch.area_code;
        }
        if (this.formSearch.category_id) {
          params.category_id = this.formSearch.category_id;
        }
        if (this.formSearch.level) {
          params.level = this.formSearch.level;
        }
        if (this.formSearch.state) {
          params.state = this.formSearch.state;
        }
        if (this.formSearch.keyWord) {
          params.keyWord = this.formSearch.keyWord;
        }
        const res = await getList(params);
        if (res && res.data && res.data.list && Array.isArray(res.data.list)) {
          this.tableData = this.handleLevelToNum(res.data.list);
          this.totalCount = res.data.total;
          this.isDivision =
            res.data.superUserInfo.regAdd.length > 1 ? true : false; // 大于1为师市级，则可推荐
          this.regAddLength = res.data.superUserInfo.regAdd.length;
        }
      } catch (error) {
        Message({
          type: "danger",
          message: error,
        });
      }
    },
    // 处理专家级别
    handleLevelToNum(list) {
      list.forEach((item) => {
        if (item.level == "兵团级") {
          item.level = 1;
          item.levelName = "兵团级";
        }
        if (item.level == "师市级") {
          item.level = 2;
          item.levelName = "师市级";
        }
        if (item.level == "团镇级") {
          item.level = 3;
          item.levelName = "团镇级";
        }
      });
      return list;
    },
    async onSubmit() {
      this.pageInfo.curPage = 1;
      this.getList();
    },
    async reset() {
      this.formSearch = this.$options.data()["formSearch"];
      this.pageInfo = this.$options.data()["pageInfo"];
      this.getList();
    },
    // 专家推荐
    async expertRecommand(id) {
      this.$confirm("确认推荐吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const res = await expertRecommend({ expert_id: id, operate: 1 });
          if (res.status == 200) {
            this.getList();
          }
          this.$message({
            type: "success",
            message: "操作成功，请等待审核。",
          });
        })
        .catch(() => { });
    },
    // 专家解聘
    async expertFire(id) {
      this.$confirm("确认解聘吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const res = await expertDismissal({ expert_id: id, operate: 1 });
          if (res.status === 200) {
            this.$message.success("操作成功。");
            this.getList();
          }
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        })
        .catch(() => { });
    },
    async recommand(val) {
      const res = await expertRecommend(val);
      if (res.status === 200 && res.message == "操作成功") {
        this.$message.success("操作成功。");
        this.getList();
      }
    },
    async dismiss(val) {
      const res = await expertDismissal(val);
      if (res.status === 200 && res.message == "操作成功") {
        this.$message.success("操作成功。");
        this.getList();
      }
    },
    async cancel() { },

    async getDetail(id) {
      const res = await expertDetail({ id });
      if (res.status === 200 && res.data) {
        this.expertDetail = res.data;
        this.expertDetail.area_code = Number(this.expertDetail.area_code);
        this.expertDetail.id_type =
          this.expertDetail.id_type || this.expertDetail.id_type === 0
            ? Number(this.expertDetail.id_type)
            : null;
        this.expertDetail.education =
          this.expertDetail.education || this.expertDetail.education === 0
            ? Number(this.expertDetail.education)
            : null;
        this.isShowEdit = true;
      }
    },
    async confirmUpdate(val) {
      delete val.area_name;
      const res = await updateExpert(val);
      if (res.status === 200 && res.data) {
        this.getList();
        this.$message.success("更新成功。");
      }
    },
    async cancelUpdate() { },
    async exportData() {
      try {
        let params = {
          curPage: 1,
          pageSize: 2000,
        };
        if (this.formSearch.area_code) {
          params.area_code = this.formSearch.area_code;
        }
        if (this.formSearch.category_id) {
          params.category_id = this.formSearch.category_id;
        }
        if (this.formSearch.level) {
          params.level = this.formSearch.level;
        }
        if (this.formSearch.state) {
          params.state = this.formSearch.state;
        }
        if (this.formSearch.keyWord) {
          params.keyWord = this.formSearch.keyWord;
        }
        const res = await getList(params);
        if (res && res.data && res.data.list && Array.isArray(res.data.list)) {
          let downloadData = [];
          res.data.list.forEach((item) => {
            downloadData.push({
              "专家姓名": item.name ? item.name : "",
              "专家级别": item.level ? item.level : "",
              "出生日期": item.birthday ? item.birthday : "",
              "身份证号": item.id_number ? item.id_number : "",
              "联系电话": item.phone ? item.phone : "",
              "专业": item.major ? item.major : "",
              "工作单位": item.work_unit ? item.work_unit : "",
              "学历信息": item.education ? item.education : "",
              "专家状态": item.state ? this.dealwithState(item.state) : "",
            });
          });

          const ws = XLSX.utils.json_to_sheet(downloadData); // 将 JSON 数据转换为工作表
          const wb = XLSX.utils.book_new(); // 创建一个新的工作簿
          XLSX.utils.book_append_sheet(wb, ws, "Sheet1"); // 将工作表添加到工作簿中
          const wbout = XLSX.write(wb, { type: "binary", bookType: "xlsx" }); // 将工作簿转换为二进制字符串
          const blob = new Blob([s2ab(wbout)], {
            type: "application/octet-stream",
          }); // 将二进制字符串转换为 Blob 对象
          saveAs(blob, moment() + ".xlsx"); // 保存文件
          // 辅助函数：将字符串转换为 ArrayBuffer
          function s2ab(s) {
            const buf = new ArrayBuffer(s.length);
            const view = new Uint8Array(buf);
            for (let i = 0; i < s.length; i++) view[i] = s.charCodeAt(i) & 0xff;
            return buf;
          }
        }
      } catch (error) {
        Message({
          type: "danger",
          message: error,
        });
      }
    },
    dealwithLevel(val) {
      switch (val) {
        case 1:
          return "兵团级";
        case 2:
          return "师市级";
        case 3:
          return "团镇级";
        default:
          return "";
      }
    },
    dealwithState(val) {
      switch (val) {
        case 0:
          return "已注销";
        case 1:
          return "正常";
        case 2:
          return "出差";
        case 3:
          return "休假";
        case 4:
          return "已解聘";
        case 5:
          return "解聘申请中";
        case 6:
          return "专家推荐";
        default:
          return "";
      }
    },
    uploadFile(options) {
      this.tempFile = options.file;
    },
    async confirmCreate(data) {
      try {
        if (!this.user_id) {
          this.$message.warning(
            "用户ID不存在，请确认新建专家是否成功或联系管理员。"
          );
          return;
        }
        if (!this.tempFile) {
          this.$message.warning("附件不存在，请上传对应附件。");
          return;
        }
        if (!data.name) {
          this.$message.warning("证书名称不存在，请填写对应名称。");
          return;
        }
        if (!data.number) {
          this.$message.warning("文件编号不存在，请上传对应编号。");
          return;
        }
        if (data.valid_time && data.get_time) {
          if (
            moment(data.valid_time).valueOf() < moment(data.get_time).valueOf()
          ) {
            this.$message.warning("有效时间应晚于获得时间。");
            return;
          }
        }

        const formData = new FormData();
        formData.append("name", data.name);
        formData.append("number", data.number);
        formData.append("user_id", this.user_id);
        if (data.type) {
          formData.append("type", data.type); // 证书类型
        }
        if (data.professional) {
          formData.append("professional", data.professional); // 专业类别
        }
        if (data.issuing_agency) {
          formData.append("issuing_agency", data.issuing_agency); // 发证机构
        }
        if (data.get_time) {
          formData.append("get_time", data.get_time);
        }
        if (data.valid_time) {
          formData.append("valid_time", data.valid_time);
        }
        formData.append("annex", this.tempFile);
        const res = await createCertificate(formData);
        if (res.status == 200 && res.data) {
          this.$message.success("创建成功。");
          this.getCertificateList();
        }
      } catch (error) {
        this.$message.warning(error);
      }
    },
    async checkDeclaration(id) {
      this.currentId = id;
      this.isShowDeclaration = true;
    },
    async confirmExpertDeclarationReview(data) {
      const res = await expertDeclarationReview(data);
      if (res.status === 200 && res.data) {
        this.$message.success("审核成功。");
        this.getList();
      }
    },
  },

  filters: {
    handleLevel(val) {
      switch (val) {
        case 1:
          return "兵团级";
        case 2:
          return "师市级";
        case 3:
          return "团镇级";
        default:
          return "";
      }
    },
    handleState(val) {
      switch (val) {
        case 0:
          return "已注销";
        case 1:
          return "正常";
        case 2:
          return "出差";
        case 3:
          return "休假";
        case 4:
          return "已解聘";
        case 5:
          return "解聘申请中";
        case 6:
          return "专家推荐";
        default:
          return "";
      }
    },
    handlePhone(val) {
      let reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
      let phone = val.replace(reg, "$1****$2");
      return phone;
    },
    handleIdNumber(val) {
      const reg = /^(\d{6})\d{8}(\d{3}[0-9Xx])$/;
      return val.replace(reg, "$1********$2");
    }
  }
}
</script>

<style lang="scss" scoped>
.serveEnterprise {
  padding: 15px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-top: 15px;
}

.num {
  font-size: 18px;
  color: #1691e0;
  font-weight: bolder;
}

.table {
  margin-top: 10px;
}

.pagination {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

::v-deep .el-table__fixed,
::v-deep .el-table__fixed-right {
  height: 100% !important;
}

.excel-upload-input {
  display: none;
  z-index: -9999;
}
</style>
