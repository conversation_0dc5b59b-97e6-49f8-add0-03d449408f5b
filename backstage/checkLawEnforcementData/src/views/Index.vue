<template>
  <div class="content">
    <div>
      <el-select v-model="timeway" placeholder="请选择时间筛选方式">
        <el-option label="时间段" :value="1"></el-option>
        <el-option label="时间点" :value="2"></el-option>
      </el-select>
      &nbsp;
      <el-date-picker v-show="timeway === 1" v-model="dateRange" type="daterange" value="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" range-separator="至"
        start-placeholder="开始日期" end-placeholder="结束日期">
      </el-date-picker>

      <el-date-picker v-show="timeway === 2" v-model="sureTime" value="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss" type="date" placeholder="选择日期">
      </el-date-picker>

      &nbsp;
      <el-cascader
        class="custom-cascader"
        :options="regionOptions"
        v-model="selectedRegion"
        :props="districtListProps"
        clearable
        collapse-tags
        ref="regAddCas"
        placeholder="请选择地区"
        @change="onRegionChange"
      />

      &nbsp;

      <el-button type="primary" @click="onSearch">查询</el-button>
      <el-button @click="onReset">重置</el-button>
    </div>
    <el-divider></el-divider>

    <el-row :gutter="20">
      <!-- 监督执法开展情况 -->
      <el-col :span="12">
        <div class="grid-content bg-purple">
          <div style="width: 100%;border-bottom: 1px solid #ededed;padding: 8px 6px;font-weight: bolder;">
            监督执法开展情况
          </div>
          <ProjectRecordData class="full" :distributionData="countData"></ProjectRecordData>
        </div>
      </el-col>

      <el-col :span="12">
        <!-- 监督检查行政执法地点分布情况 -->
        <div class="grid-content bg-purple">
          <div style="width: 100%;border-bottom: 1px solid #ededed;padding: 8px 6px;font-weight: bolder;">
            监督检查行政执法地点分布情况
          </div>
          <TD_map class="full" :regions="regionOptions" :recordProjectCount="recordProjectCount"></TD_map>
        </div>
      </el-col>

      
    </el-row>
    <br>

    <el-row :gutter="20">
      <!-- 监督检查项目情况 -->
      <el-col :span="12">
        <div class="grid-content bg-purple">
          <div style="width: 100%;border-bottom: 1px solid #ededed;padding: 8px 6px;font-weight: bolder;">
            监督检查项目情况
          </div>
          <ProjectCategoryChart
            class="full" 
            :stats="projectStats.stats"
            :ranking="projectStats.ranking"
            :panel-height="400"
            :left-width="120"
            chart-title="各区域检查项目排名"
          />
        </div>
      </el-col>
    

      <el-col :span="12">
        <!-- 行政执法结果情况 -->
        <div class="grid-content bg-purple">
          <div style="width: 100%;border-bottom: 1px solid #ededed;padding: 8px 6px;font-weight: bolder;">
            行政执法结果情况
          </div>
          <ProjectNatureStatistics 
          class="full" :natureData="recordCountRankTableData" >
          </ProjectNatureStatistics>
        </div>
      </el-col>
    </el-row>
    <br>
  </div>
</template>

<script>
import ProjectCategoryChart from '@/components/ProjectCategoryChart'
import ProjectRecordData from '@/components/ProjectRecordData'
import ProjectNatureStatistics from '@/components/ProjectNatureStatistics'
import TD_map from '@/components/TD_map'
import {
  getRegionList,
  getCarryOut,
  getLocation,
  getProject,
  getResults,
} from '@/api'
export default {
  data() {
    const pad = n => (n < 10 ? '0' + n : '' + n)
    const d = new Date()
    const todayStr = `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} 00:00:00`
    return {
      chart: null, // 存储图表实例
      countData: [],
      timeway: 2,
      dateRange: [],
      sureTime: todayStr,
      // 选中路径（emitPath: true 时为数组）
      selectedRegion: [],
      // 级联选项
      regionOptions: [],
      // 你的业务查询用的最终区域码
      areaCode: null,

      // Cascader 配置
      districtListProps: {
        value: 'value',
        label: 'label',
        children: 'children',
        emitPath: true,        // 返回完整路径
        checkStrictly: false,  // 仅允许选中叶子，如需父级也可选，改为 true
        expandTrigger: 'hover'
      },
      projectStats: { stats: { total: 0, passed: 0, passRate: 0 }, ranking: [] },
      recordCountRankTableData:{}
    }
  },
  components: {
    ProjectCategoryChart,
    ProjectRecordData,
    ProjectNatureStatistics,
    TD_map
  },
  computed: {

  },

  watch: {

  },

  async mounted() {
    this.fetchRegionList()
    this.inintData()
  },
  beforeDestroy() {
   
  },
  methods: {

    onSearch() {
      this.inintData()
    },

    onReset() {
      const pad = n => (n < 10 ? '0' + n : '' + n)
      const d = new Date()
      const todayStr = `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} 00:00:00`
      this.areaCode = this.$options.data()['areaCode']
      this.selectedRegion = this.$options.data()['selectedRegion']
      this.timeway = this.$options.data()['timeway']
      this.dateRange = this.$options.data()['dateRange']
      this.sureTime = todayStr
      this.inintData()
    },

    inintData() {
      this.fetchCarryOut()
      this.fetchProject()
      this.fetchResults()
    },

    async fetchRegionList() {
      try {
        const { data } = await getRegionList()
        let regionData = Array.isArray(data) ? data : [data]

        // 如果第一层是全国并且有 children，就直接取 children
        if (regionData.length === 1 && regionData[0].label === '全国' && regionData[0].children) {
          regionData = regionData[0].children
        }

        this.regionOptions = regionData
      } catch (e) {
        console.error('getRegionList error', e)
      }
    },
    onRegionChange(val) {
      // 取最后一级编码作为查询条件
      this.areaCode = Array.isArray(val) ? val[val.length - 1] : val
      // 如需级联路径全量一起传，直接用 this.selectedRegion
    },
    async fetchCarryOut() {
      try {
        let params={
          areaCode:this.areaCode,
        }
        if(this.timeway===2){
          params.sureTime=this.sureTime
        }else{
          params.dateRange=this.dateRange
        }
        console.log(params,'params');
        
        const { data } = await getCarryOut(params)
        this.countData = data || []
      } catch (e) {
        console.error('getCarryOut error', e)
      }
    },


    extractValues(data) {
    const result = [];

  function traverse(nodes) {
    nodes.forEach(node => {
      if (node.value) {
        result.push(node.value);
      }
      if (Array.isArray(node.children)) {
        traverse(node.children); // 递归遍历子节点
      }
    });
  }

  traverse(data);
  return result;                            
},  
    async fetchProject() {
      try {
        let params = { areaCode: this.areaCode };
        if (this.timeway === 2) {
          params.sureTime = this.sureTime;
        } else {
          params.dateRange = this.dateRange;
        }
        const res = await getProject(params);
        const data = res && res.data;
        const list = (data && Array.isArray(data.list)) ? data.list : [];
        this.recordProjectCount = list;

        // 统计
        this.projectStats = this.buildProjectStats(list, this.regionOptions);
      } catch (e) {
        console.error('getProject error', e)
      }
    },

    buildProjectStats(list, regionOptions) {
      // total：条数
      const total = Array.isArray(list) ? list.length : 0;

      // passed：每条数据 records 全部 isWarn !== '1' 记为合格
      let passed = 0;
      for (let i = 0; i < list.length; i++) {
        const recs = Array.isArray(list[i].records) ? list[i].records : [];
        let ok = true;
        for (let j = 0; j < recs.length; j++) {
          if (String(recs[j].isWarn) === '1') { ok = false; break; }
        }
        if (ok) passed++;
      }

      const passRate = total ? (passed / total) * 100 : 0;

      // ranking：各区域统计
      const regionMap = this.flattenRegions(regionOptions); // { code: label }
      const counter = {};
      for (const code in regionMap) {
        if (regionMap.hasOwnProperty(code)) counter[code] = 0;
      }
      for (let i = 0; i < list.length; i++) {
        const zc = list[i].zonecode;
        if (zc && regionMap[zc] !== undefined) {
          counter[zc] = (counter[zc] || 0) + 1;
        }
      }
      const ranking = Object.keys(counter)
      .map(code => ({ name: regionMap[code], count: counter[code] || 0 }))
      .sort((a, b) => b.count - a.count)

      return {
        stats: { total, passed, passRate },
        ranking
      };
    },

    flattenRegions(nodes) {
      const map = {};
      const stack = Array.isArray(nodes) ? nodes.slice() : [];
      while (stack.length) {
        const n = stack.pop();
        if (!n) continue;
        if (n.value && n.label) {
          map[n.value] = n.label;
        }
        if (n.children && Array.isArray(n.children) && n.children.length) {
          for (let i = 0; i < n.children.length; i++) stack.push(n.children[i]);
        }
      }
      return map;
    },

    async fetchResults() {
      try {
        let params={
          areaCode:this.areaCode,
        }
        if(this.timeway===2){
          params.sureTime=this.sureTime
        }else{
          params.dateRange=this.dateRange
        }
        const { data } = await getResults(params)
        this.recordCountRankTableData = data || []
      } catch (e) {
        console.error('getResults error', e)
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.progress-container {
  margin: 5px 0px;
  // display: flex;
  // justify-content: space-between;
  // align-items: center;
  // color: #fff;
  font-size: 12px;
  font-weight: bolder;
}

.progress-name {
  position: absolute;
  left: 0;
  top: 0;
  transform: translateY(-50%);
}

.name-container {
  width: 95%;
  border-radius: 10px 10px;
  padding: 3px 5px;
}

.bg-purple {
  // background: #d3dce6;
  // border: 2px solid #ededed;
  box-shadow: 0 0 10px #cfcfcf;
}

.bg-purple-light {
  background: #e5e9f2;
}

.grid-content {
  padding: 6px 6px;
  border-radius: 8px;
  height: 400px;
  // overflow: auto;
  display: flex;flex-direction: column;
}

.grid-content-small {
  padding: 6px 6px;
  border-radius: 8px;
  height: 480px;
  overflow: auto;
}
.full{
  flex: 1;
  padding: 20px;
}
.content {
  padding: 10px 20px;
}

.dr-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .dr-title-left {
    font-size: 16px;
    font-weight: 500;
    border-left: 6px solid #409eff;
    display: flex;
    height: 24px;
    line-height: 24px;

    .dr-title-text {
      margin-left: 10px;
    }
  }

  .dr-title-divider {
    flex: 1;
    padding: 0 10px;

    el-divider {}
  }
}

.metric-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.card {
  background: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  /* 增加了垂直偏移量和模糊半径，调整了颜色 */
}

.metric-value {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.metric-number {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
}

.metric-change {
  color: #4CAF50;
  font-size: 12px;
}


.chart-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-col {
  flex: 1;
  background: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  /* 增加了垂直偏移量和模糊半径，调整了颜色 */
}

.chart-container {
  height: 300px;
}

.recordName {
  width: 200px;
  padding: 15px 15px;
}

.recordCount {
  color: #fff;
  font-size: 20px;
  font-weight: bolder;
}


.scroll-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #ededed;
  font-size: 14px;
  color: #606266;
}


.auto-scroll-container {
  width: 100%;
  height: calc(100% - 10px);
  position: relative;
  overflow: hidden;

  .scroll-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .scroll-list {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    transition: transform 0s linear; // 基础状态无过渡
  }

  // 滚动激活状态
  .scroll-list.scroll-active {
    transition: transform 0.1s linear; // 滚动时启用过渡
  }

  .scroll-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    border-bottom: 1px solid #ededed;
    font-size: 14px;
    color: #606266;
    height: 48px; // 固定高度确保计算准确
    box-sizing: border-box;
  }
}

.common-count-style {
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: space-around;
  align-items: center;
  h4{
    width: 200px;
    text-align: left;
  }
}

.success-container{
  width: 100%;
  background-color: #67C23A;
  width:10vw;
  height: 46px;
  border-radius: 12px 12px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.fail-container{
  width: 100%;
  background-color: #E6A23C;
  width:10vw;
  height: 46px;
  border-radius: 12px 12px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.description-font{
  font-size: 16px;
  color:#444;
  font-weight: bolder;
}

.description-count{
  font-size: 30px;
  color:#fff;
  font-weight: bolder;
}
</style>