<template>
    <div class="distribution-container">
        <el-card shadow="hover" class="kpi-card">
            <div class="kpi-title">开展监督检查用人单位数</div>
            <div class="kpi-value">{{ total }}</div>
        </el-card>
        <div ref="pie" class="pie"></div>
    </div>
  </template>
  
  <script>
  import * as echarts from 'echarts'
  
  export default {
    name: 'DistributionStatistics',
    props: {
      // { total, confirmCount, confirmRate }
      distributionData: {
        type: Object,
        default: () => ({ total: 0, confirmCount: 0, confirmRate: 0 }),
        required: true
      }
    },
    data() {
      return { chart: null }
    },
    computed: {
      total() {
        const v = this.distributionData && this.distributionData.total
        return typeof v === 'number' ? v : 0
      },
      confirmCount() {
        const v = this.distributionData && this.distributionData.confirmCount
        return typeof v === 'number' ? v : 0
      },
      confirmRate() {
        const v = this.distributionData && this.distributionData.confirmRate
        return typeof v === 'number' ? v : 0
      },
      unconfirmCount() {
        const v = this.total - this.confirmCount
        return v > 0 ? v : 0
      }
    },
    watch: {
      distributionData: {
        handler() { this.render() },
        deep: true,
        immediate: true
      }
    },
    mounted() {
      this.init()
      window.addEventListener('resize', this.resize)
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.resize)
      if (this.chart) this.chart.dispose()
    },
    methods: {
      init() {
        if (!this.chart) this.chart = echarts.init(this.$refs.pie)
        this.render()
      },
      render() {
        if (!this.chart) return
        const option = {
          title: { text: '监督检查项目完成率', left: 'center', top: 8, textStyle: { fontSize: 14, fontWeight: 'bold' } },
          tooltip: { trigger: 'item', formatter: '{b}: {c} ({d}%)' },
          legend: { bottom: 0, data: ['已开展', '未开展'] },
          series: [
            {
              name: '开展情况',
              type: 'pie',
              radius: ['50%', '70%'],
              center: ['50%', '50%'],
              avoidLabelOverlap: false,
              itemStyle: { borderRadius: 6, borderColor: '#fff', borderWidth: 2 },
              label: { show: false },
              emphasis: { label: { show: true, fontSize: 14, fontWeight: 'bold' } },
              labelLine: { show: false },
              data: [
                { value: this.confirmCount, name: '已开展' },
                { value: this.unconfirmCount, name: '未开展' }
              ],
              color: ['#5BA0FF', '#E6EAF2']
            }
          ],
          graphic: [
            { type: 'text', left: 'center', top: '48%', style: { text: `${this.confirmRate}%`, textAlign: 'center', fill: '#333', fontSize: 18, fontWeight: 'bold' } },
            { type: 'text', left: 'center', top: '60%', style: { text: '完成率', textAlign: 'center', fill: '#666', fontSize: 12 } }
          ]
        }
        this.chart.setOption(option, true)
      },
      resize() { if (this.chart) this.chart.resize() }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .distribution-container {
    width: 100%;
    height: 300px;
    display: flex;
    gap: 16px;
    align-items: stretch;
  }
  .kpi-card {
    width: 240px;
    min-width: 200px;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    padding: 16px;
    box-sizing: border-box;
    background-image:linear-gradient(to right, #5BA0FF,#3588fc);
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
  }
  .kpi-title {
    font-size: 20px;
    color: #fff;
    font-weight: bold;
    margin-bottom: 8px;
  }
  .kpi-value {
    font-size: 100px;
    font-weight: 700;
    color: #fff;
    line-height: 1.2;
  }
  .pie {
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: center;
  }
  </style>