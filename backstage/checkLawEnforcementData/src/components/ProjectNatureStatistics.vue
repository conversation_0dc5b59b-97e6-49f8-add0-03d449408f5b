<template>
    <div class="nature-panel">
      <div class="left-col">
        <el-card class="mini-card mini--blue" shadow="hover">
          <div class="mini-title">责令暂停导致职业病危害事故作业的用人单位数</div>
          <div class="mini-value">{{ zelingZanTing }}</div>
        </el-card>
  
        <el-card class="mini-card mini--purple" shadow="hover">
          <div class="mini-title">封存造成职业病危害事故的材料和设备数</div>
          <div class="mini-value">{{ fengCun }}</div>
        </el-card>
      </div>
  
      <div class="right-col">
        <div class="chart-title">执行行政执法用人单位占比</div>
        <div ref="ring" class="ring"></div>
      </div>
    </div>
  </template>
  
  <script>
  import * as echarts from 'echarts'
  
  export default {
    name: 'ProjectNatureStatistics',
    props: {
      natureData: {
        type: [Object, Array],
        default: () => ({})
      }
    },
    data() {
      return { chart: null }
    },
    computed: {
      stats() {
      const sumKeys = function(arr) {
        const acc = { total: 0, zelingZanTing: 0, fengCun: 0, executeCount: 0 };
        for (let i = 0; i < arr.length; i++) {
          const it = arr[i] || {};
          acc.total         += Number(it.total) || 0;
          acc.zelingZanTing += Number(it.zelingZanTing) || 0;
          acc.fengCun       += Number(it.fengCun) || 0;
          acc.executeCount  += Number(it.executeCount) || 0;
        }
        return acc;
      };

      let src = {};
      if (Array.isArray(this.natureData)) {
        if (this.natureData.length === 1 && typeof this.natureData[0] === 'object') {
          src = this.natureData[0];
        } else {
          src = sumKeys(this.natureData);
        }
      } else if (this.natureData && typeof this.natureData === 'object') {
        src = this.natureData;
      }

      const total         = Number(src.total) || 0;
      const zelingZanTing = Number(src.zelingZanTing) || 0;
      const fengCun       = Number(src.fengCun) || 0;
      const executeCount  = Number(src.executeCount) || 0;

      const er = (typeof src.executeRate === 'number')
        ? src.executeRate
        : (typeof src.executeRate === 'string')
          ? parseFloat(src.executeRate)
          : (total ? (executeCount / total) * 100 : 0);
      const executeRate = Math.max(0, Math.min(100, isNaN(er) ? 0 : er));

      return { total, zelingZanTing, fengCun, executeCount, executeRate };
    },
    total() { return this.stats.total },
    zelingZanTing() { return this.stats.zelingZanTing },
    fengCun() { return this.stats.fengCun },
    executeCount() { return this.stats.executeCount },
    executeRate() { return this.stats.executeRate },
    unExecuteCount() { return Math.max(0, this.total - this.executeCount) },
  },      
    watch: {
      stats: { handler() { this.render(); }, deep: true }
    },
    mounted() {
      this.$nextTick(() => {
        this.init()
        setTimeout(() => this.resize(), 0)
      })
      window.addEventListener('resize', this.resize)
    },
    activated() { this.resize() },
    beforeDestroy() {
      window.removeEventListener('resize', this.resize)
      if (this.chart) this.chart.dispose()
    },
    methods: {
      init() {
        const el = this.$refs.ring
        if (!el) return
        const tryInit = () => {
          if (!this.chart) this.chart = echarts.init(el)
          this.render()
          this.resize()
        }
        if ((el.offsetWidth || 0) === 0 || (el.offsetHeight || 0) === 0) {
          setTimeout(tryInit, 50)
        } else {
          tryInit()
        }
      },
      render() {
        if (!this.chart) return
        const done = this.executeRate
        const option = {
          tooltip: { show: false },
          series: [
            // 背景环
            {
              type: 'pie',
              radius: ['70%', '86%'],
              center: ['50%', '50%'],
              silent: true,
              label: { show: false },
              data: [{ value: 100, name: 'bg', itemStyle: { color: '#ECEFF5' } }],
              z: 1
            },
            // 进度环：执行 vs 未执行
            {
              type: 'pie',
              radius: ['70%', '86%'],
              center: ['50%', '50%'],
              label: { show: false },
              data: [
                { value: done, name: '已执行', itemStyle: { color: '#409EFF' } },
                { value: 100 - done, name: '未执行', itemStyle: { color: 'transparent' } },
              ],
              z: 2,
              hoverAnimation: false
            }
          ],
          graphic: [
            {
              type: 'text',
              left: 'center',
              top: '42%',
              style: {
                text: `${done.toFixed(1)}%`,
                textAlign: 'center',
                fill: '#1F2D3D',
                fontSize: 26,
                fontWeight: 700
              }
            },
            {
              type: 'text',
              left: 'center',
              top: '64%',
              style: {
                text: `已执行 ${this.executeCount} / ${this.total}`,
                textAlign: 'center',
                fill: '#77839A',
                fontSize: 12
              }
            }
          ]
        }
        this.chart.setOption(option, true)
      },
      resize() { if (this.chart) this.chart.resize() }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .nature-panel {
    width: 100%;
    min-height: 260px;
    display: grid;
    grid-template-columns: 200px 1fr; /* 左窄右宽 */
    gap: 12px;
    align-items: stretch;
  }
  
  .left-col {
    display: grid;
    grid-template-rows: 1fr 1fr;
    gap: 12px;
  }
  
  .mini-card {
    border-radius: 12px;
    overflow: hidden;
    color: #fff;
    border: none;
  }
  
  /* 去掉 el-card 默认内边距，使用自己的 */
  .mini-card ::v-deep .el-card__body {
    padding: 16px 18px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .mini--blue   { background: linear-gradient(135deg, #3a7bd5 0%, #409EFF 100%); }
  .mini--purple { background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%); }
  
  .mini-title {
    font-size: 16px;
    opacity: 0.95;
    letter-spacing: .5px;
  }
  
  .mini-value {
    margin-top: 6px;
    font-size: 42px;
    font-weight: 800;
    line-height: 1;
    text-shadow: 0 2px 8px rgba(0,0,0,0.12);
  }
  
  .right-col {
    border-radius: 12px;
    border: 1px solid #edf2f7;
    background: #fff;
    box-shadow: 0 4px 12px rgba(31, 45, 61, 0.04);
    padding: 8px 8px 0 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .chart-title {
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    color: #1f2d3d;
    padding-top: 6px;
  }
  
  .ring {
    width: 100%;
    height: 240px;
    display: flex;
    justify-content: center;
  }
  </style>