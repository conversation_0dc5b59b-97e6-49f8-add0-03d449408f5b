<template>
  <!-- 地图区域 -->
  <div class="map-container">
      <div id="map" class="map" v-loading="loading.map" element-loading-text="加载地图数据..."></div>
      <el-dialog
        title="用人单位详情"
        :visible.sync="showDetails"
        width="70%">
        <el-table :data="unitDetails" border style="width: 100%;" header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px">
            <el-table-column prop="unitName" label="单位名称"></el-table-column>
            <el-table-column prop="location" label="详细地址"></el-table-column>
            <el-table-column prop="industry" label="所属行业">
                <template slot-scope="scope">
                    {{ scope.row.industry.join('、') }}
                </template>
            </el-table-column>
            <el-table-column prop="employeeCount" label="从业人数"></el-table-column>
            <el-table-column prop="hazardFactors" label="危害因素"></el-table-column>
        </el-table>
      </el-dialog>
  </div>
</template>

<script>
import axios from 'axios';
export default {
props: {
  // 父组件传入的行政区数组
  regions: { type: Array, default: () => [] },
  recordProjectCount: { type: Array, default: () => [] }
},
data() {
  return {
    tdtMap: null,
    tdtToken: '925af82a336ce5cec50e8529f2267a3f',
    isInitialized: false,
    pendingRegions: null,
    center: [87.613831, 43.825135],
    zoomLevel: 11,

    markerObjects: [],
    loading: { map: false },

    // 经纬度缓存，避免重复调用 API
    geoCache: {},
    infoWindow: null, 
  };
},
watch: {
  regions: {
    immediate: true,
    deep: true,
    handler(val) {
      if (!val || !val.length) return;
      if (!this.isInitialized) {
        this.pendingRegions = val;      // 先缓存
        return;
      }
      this.addMarkersFromRegions(val);
    }
  }
},
mounted() {
  this.loadTiandituScript();
},
methods: {
  // 加载天地图 API
  loadTiandituScript() {
    return new Promise((resolve, reject) => {
      if (window.T) {
        this.initTiandituMap();
        resolve(window.T);
        return;
      }
      const tdtScript = document.createElement('script');
      tdtScript.type = 'text/javascript';
      tdtScript.src = `https://api.tianditu.gov.cn/api?v=4.0&tk=${this.tdtToken}`;
      tdtScript.onerror = reject;
      tdtScript.onload = () => {
        this.initTiandituMap();
        resolve(window.T);
      };
      document.head.appendChild(tdtScript);
    });
  },

  // 初始化地图
  initTiandituMap() {
    if (this.tdtMap) return;
    const el = this.$refs.mapRef ||   document.getElementById('map');
    if (!el || !el.offsetWidth || !el.offsetHeight) {
      setTimeout(() => this.initTiandituMap(), 50);
      return;
    }
    this.tdtMap = new window.T.Map(el, { projection: 'EPSG:4326' });
    this.tdtMap.centerAndZoom(new T.LngLat(this.center[0], this.center[1]), this.zoomLevel);
    this.isInitialized = true;

    if (this.pendingRegions) {
      const r = this.pendingRegions;
      this.pendingRegions = null;
      this.addMarkersFromRegions(r);      // 现在打点
    }
        // 点击地图其他地方关闭信息窗口
    this.tdtMap.addEventListener('click', () => {
      console.log(1111);
      
      if (this.infoWindow) {
        this.tdtMap.closeInfoWindow();
        this.infoWindow = null;
      }
    });
  },

  // 调用天地图地理编码 API 获取经纬度
  async getLatLngByName(name) {
  if (this.geoCache[name]) return this.geoCache[name];
  const url = `https://api.tianditu.gov.cn/geocoder?ds={"keyWord":"${encodeURIComponent(name)}"}&tk=${this.tdtToken}`;
  try {
    const res = await axios.get(url);
    // console.log(res,'res');
    const { lon, lat } = res.data.location;
    const coords = [lon, lat];
    this.geoCache[name] = coords; // 缓存
    return coords;
  } catch (err) {
    console.error("地理编码失败:", err);
  }
  return null;
},

  // 遍历行政区数组并添加标注
  async addMarkersFromRegions(regions) {
  if (!this.tdtMap) return;
  this.clearMapOverlays();

  const traverse = async (nodes, parentLabel = '') => {
    for (const node of nodes) {
      // 处理 parentLabel 为空或 undefined 的情况
      const safeParent = parentLabel || '';
      const fullName = safeParent
        ? `${safeParent} ${node.label}`
        : node.label;

      // 调用天地图获取坐标
      const coords = await this.getLatLngByName(fullName);
      if (coords) {
        const pos = new window.T.LngLat(coords[0], coords[1]);
        const marker = new window.T.Marker(pos);
        this.tdtMap.addOverLay(marker);

        const label = new window.T.Label({
          text: node.label,
          position: pos,
          offset: new window.T.Point(0, 16)
        });
        this.tdtMap.addOverLay(label);

        this.markerObjects.push(marker, label);

        marker.addEventListener('click', () => {

          const zoneCode = node.value;
          const allData = this.recordProjectCount;
          const zoneData = allData.filter(item => item.zonecode === zoneCode);

          const superviseCount = zoneData.length;
          const supervisePercent = allData.length
            ? ((superviseCount / allData.length) * 100).toFixed(2)
            : 0;

          const lawEnforcementCount = zoneData.filter(
            item => item.lawEnforcement === '执行行政执法'
          ).length;
          const lawEnforcementPercent = superviseCount
            ? ((lawEnforcementCount / superviseCount) * 100).toFixed(2)
            : 0;

          const suspendCount = zoneData.filter(
            item => item.lawEnforcement === '责令暂停'
          ).length;

          const content = `
            <div style="font-size:14px;">
              <b>${node.label}</b><br/>
              区域监督检查用人单位数：${superviseCount}<br/>
              区域监督检查用人单位数占比：${supervisePercent}%<br/>
              区域行政执法用人单位数：${lawEnforcementCount}<br/>
              区域行政执法用人单位数占比： ${lawEnforcementPercent}%<br/>
              区域责令暂停作业用人单位数：${suspendCount}
            </div>
          `;

          if (this.infoWindow) {
            this.tdtMap.closeInfoWindow();
          }
          this.infoWindow = new window.T.InfoWindow(content, { offset: new window.T.Point(0, -30) });
          this.tdtMap.openInfoWindow(this.infoWindow, pos);
        });
      }

      if (node.children && node.children.length) {
        await traverse(node.children, fullName);
      }
    }
  };

  await traverse(regions);

  // 点击地图空白处关闭 infoWindow
  this.tdtMap.addEventListener('click', () => {
    if (this.infoWindow) {
      this.tdtMap.closeInfoWindow();
      this.infoWindow = null;
    }
  });
},



  // 清空标注
  clearMapOverlays() {
    this.markerObjects.forEach(marker => {
      if (marker && this.tdtMap) {
        this.tdtMap.removeOverLay(marker);
      }
    });
    this.markerObjects = [];
  },

  handleMapClick(e) {
    console.log('地图点击位置:', e.lnglat.lng, e.lnglat.lat);
    console.log(this.recordProjectCount,'recordProjectCount');

  }
},
beforeDestroy() {
  if (this.tdtMap) {
    this.tdtMap.removeEventListener('click', this.handleMapClick);
    this.clearMapOverlays();
    this.tdtMap = null;
  }
}
};
</script>

<style lang="scss" scoped>
.map-container {
min-height: 260px;
.map {
  width: 100%;
  height: 100%;
}
}
</style>

<style>
.tdt-control-copyright,
.tdt-control-logo {
display: none !important;
visibility: hidden !important;
}
</style>
