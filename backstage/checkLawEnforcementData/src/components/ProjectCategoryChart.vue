<template>
  <div class="chart-panel" :style="panelStyle">
    <div class="left-column">
      <div class="kpi-card kpi--blue">
        <div class="kpi-title">检查用人单位数</div>
        <div class="kpi-value">{{ (stats && stats.total) ? stats.total : 0 }}</div>
      </div>
      <div class="kpi-card kpi--green">
        <div class="kpi-title">合格数</div>
        <div class="kpi-value">{{ (stats && stats.passed) ? stats.passed : 0 }}</div>
      </div>
      <div class="kpi-card kpi--purple">
        <div class="kpi-title">合格率</div>
        <div class="kpi-value">{{ formatPercent((stats && stats.passRate) ? stats.passRate : 0) }}</div>
      </div>
    </div>

    <div class="right-column">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'ProjectCategoryChart',
  props: {
    stats: { type: Object, required: true },     // { total, passed, passRate }
    ranking: { type: Array, required: true },    // [{ name, count }]
    chartTitle: { type: String, default: '各区域检查项目排名' },
    panelHeight: { type: Number, default: 560 }, // 整体高度（可在父组件传更大）
    leftWidth: { type: Number, default: 200 }    // 左侧宽度（可在父组件传更窄/更宽）
  },
  data() {
    return { chart: null };
  },
  computed: {
    panelStyle() {
      return {
        height: this.panelHeight + 'px',
        gridTemplateColumns: `${this.leftWidth}px 1fr`
      }
    }
  },
  watch: {
    ranking: { handler() { console.log(this);
     this.updateChart(); }, deep: true },
    stats: { handler() { this.updateChart(); }, deep: true }
  },
  mounted() {
    this.chart = echarts.init(this.$refs.chartRef);
    window.addEventListener('resize', this.handleResize);
    this.$nextTick(() => {
      this.updateChart();
    });
  },
  beforeDestroy() {
    if (this.chart) this.chart.dispose();
  },
  methods: {
    formatPercent(v) {
      const n = typeof v === 'number' ? v : 0;
      return n.toFixed(1) + '%';
    },
    handleResize() {
      if (this.chart) this.chart.resize();
    },
    updateChart() {
      if (!this.chart) return;

      const list = Array.isArray(this.ranking) ? this.ranking.slice() : [];
      const total = list.length;
      const showCount = 10; // 默认显示前10条
      const endPercent = total > showCount ? (showCount / total) * 100 : 100;

      // 不截断，全部展示；允许 0 也显示
      list.sort((a, b) => (b.count || 0) - (a.count || 0));
      const names = list.map(i => (i && i.name) ? i.name : '');
      const counts = list.map(i => (i && typeof i.count === 'number') ? i.count : 0);

      const options = {
        title: {
          text: this.chartTitle,
          left: 'center',
          top: 6,
          textStyle: { fontSize: 14, fontWeight: 600 }
        },
        grid: { top: 48, left: 100, right: 28, bottom: 40 },
        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.1],
          axisLine: { lineStyle: { color: '#A0AEC0' } },
          splitLine: { lineStyle: { color: '#EDF2F7' } }
        },
        yAxis: {
          type: 'category',
          data: names,
          inverse: true, 
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: { color: '#4A5568' }
        },
        // 支持纵向滚动，确保「所有区域」可查看
        dataZoom: [
  { type: 'slider', yAxisIndex: 0, right: 2, start: 0, end: endPercent, height: '80%' },
  { type: 'inside', yAxisIndex: 0, start: 0, end: endPercent }
],
        series: [
          {
            name: '数量',
            type: 'bar',
            data: counts,
            barWidth: 14,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                { offset: 0, color: '#3B82F6' },
                { offset: 1, color: '#60A5FA' }
              ]),
              borderRadius: [8, 8, 8, 8]
            },
            label: { show: true, position: 'right', color: '#2D3748' }
          }
        ],
        // 当区域为 0 时展示空态
        graphic: (!names.length) ? [{
          type: 'text',
          left: 'center',
          top: 'middle',
          style: { text: '暂无数据', fill: '#A0AEC0', fontSize: 14 }
        }] : []
      };

      this.chart.setOption(options, true);
    }
  }
};
</script>

<style scoped>
.chart-panel {
  width: 100%;
  /* min-height: 420px; */
  display: grid;
  grid-template-columns: 200px 1fr; /* 会被 :style 覆盖 */
  gap: 12px;
  overflow: hidden;
}
.left-column {
  display: grid;
  grid-template-rows: 1fr 1fr 1fr;
  gap: 12px;
}
.kpi-card {
  border-radius: 12px;
  padding: 12px 14px;
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 0;
}
.kpi--blue   { background: linear-gradient(135deg, #3a7bd5 0%, #409EFF 100%); }
.kpi--green  { background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%); }
.kpi--purple { background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%); }
.kpi-title { font-size: 12px; opacity: .95; letter-spacing: .5px; }
.kpi-value { margin-top: 4px; font-size: 28px; font-weight: 800; line-height: 1; text-shadow: 0 2px 8px rgba(0,0,0,0.12); }

.right-column { width: 100%; height: 100%; }
.chart-container { width: 120%; height: 100%; }
</style>