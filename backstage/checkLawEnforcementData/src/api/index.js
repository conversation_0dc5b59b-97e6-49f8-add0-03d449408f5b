import request from '@root/publicMethods/request';

// 获取所有区域
export function getRegionList() {
  return request({
    url: '/manage/checkLawEnforcementData/getRegionList',
    method: 'get',
  });
}

// 获取监督执法开展情况
export function getCarryOut(params) {
  return request({
    url: '/manage/checkLawEnforcementData/getCarryOut',
    method: 'get',
    params
  });
}
// 监督检查行政执法地点分布情况
export function getLocation(params) {
  return request({
    url: '/manage/checkLawEnforcementData/getLocation',
    method: 'get',
    params
  });
}
// 监督检查项目情况
export function getProject(params) {
  return request({
    url: '/manage/checkLawEnforcementData/getProject',
    method: 'get',
    params
  });
}
// 行政执法结果情况
export function getResults(params) {
  return request({
    url: '/manage/checkLawEnforcementData/getResults',
    method: 'get',
    params
  });
}