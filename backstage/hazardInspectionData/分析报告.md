# 危害项目检查数据页：前后端接口实现分析报告

本文面向页面 `backstage/hazardInspectionData/src/views/Index.vue`，梳理其前端调用的接口、后端路由与控制器/服务实现、数据来源模型、参数契约与已知问题，并给出对齐建议。

## 1. 前端接口清单（调用方）

- 地图标记点数据
  - GET `/api/hazardInspectionData/mapMarkers`
  - 参数：`hazardFactors?: string[]`、`region?: string`、`industry?: string(JSON)`、`startDate?: any`、`endDate?: any`
- 危害因素分类统计
  - GET `/api/hazardInspectionData/hazardFactorStats`
  - 参数同上
- 行业分布统计
  - GET `/api/hazardInspectionData/industryStats`
  - 参数同上
- 区域分布统计
  - GET `/api/hazardInspectionData/regionStats`
  - 参数同上
- 单位详情
  - GET `/api/hazardInspectionData/unitDetail`
  - 参数：`id: string`
- 地区级联（懒加载）
  - GET `/api/adminorgGov/address/list`
  - 参数：`level: number`，`parent_code?: string`（按级联懒加载）
- 行业分类（级联选项）
  - GET `/api/adminorgGov/getIndustryCategory`

以上前端定义位于：`backstage/hazardInspectionData/src/api/index.js`

## 2. 后端路由与控制器实现（被调方）

路由入口：`app/router/api.js`

- 职业病危害项目检查数据 API（本页核心）
  - GET `/api/hazardInspectionData/mapMarkers` → `controller.api.hazardStatistics.getMapMarkers`
  - GET `/api/hazardInspectionData/hazardFactorStats` → `controller.api.hazardStatistics.getHazardFactorStats`
  - GET `/api/hazardInspectionData/industryStats` → `controller.api.hazardStatistics.getIndustryStats`
  - GET `/api/hazardInspectionData/regionStats` → `controller.api.hazardStatistics.getRegionStats`
  - GET `/api/hazardInspectionData/unitDetail` → `controller.api.hazardStatistics.getUnitDetail`
  - GET `/api/hazard/updateStatistics` → `controller.api.hazardStatistics.updateStatistics`（手工触发预计算）

- 行政端公共 API（作为本页辅助：地区与行业）
  - GET `/api/adminorgGov/address/list` → 插件 `egg-jk-adminorg` 暴露，控制器 `addressList`
  - GET `/api/adminorgGov/getIndustryCategory` → 插件 `egg-jk-adminorg` 暴露，控制器 `getIndustryCategory`

## 3. 控制器与服务层逻辑

### 3.1 `app/controller/api/hazardStatistics.js`

- getMapMarkers / getHazardFactorStats / getIndustryStats / getRegionStats
  - 读取 `ctx.query` 为筛选参数
  - 调用 `ctx.service.hazardStatisticsService.getStatistics(params)`
  - 分别返回 `stats.mapData` / `stats.hazardFactorsStats` / `stats.industryStats` / `stats.regionStats`

- getUnitDetail
  - 校验入参 `id`
  - `Adminorg.findById(id)` 获取企业
  - 行业名称：根据 `enterprise.industryCategory` 数组的首元素编码到 `IndustryCategory` 表查询 `label`，组装为数组
  - 危害因素：从 `enterprise.checkResult.*` 摘取 `{name, point, exceed}` 组成数组
  - 在 `Employee` 表统计在岗人数 `employeeCount`
  - 返回：`{ id, name, address, industry: string[], employeeCount, hazardFactors }`

- updateStatistics
  - 手工触发：调用服务 `calculateAndUpdateStatistics({ timeFrame: 'all' })`

### 3.2 `app/service/hazardStatisticsService.js`

- 数据预计算与缓存表：`HazardStatistics`
- calculateAndUpdateStatistics({ timeFrame = 'all', filter = {} })
  - 计算时间点 `getTimePoint(timeFrame)`
  - 构建 `baseQuery = buildBaseQuery(filter)`
  - 并发计算：
    - 企业数量 `calculateEnterpriseCount`
    - 危害因素 `calculateHazardFactorsStats`
    - 行业分布 `calculateIndustryStats`（聚合 + 超标企业统计）
    - 区域分布 `calculateRegionStats`（聚合 + 超标企业统计）
    - 地图数据 `generateMapData`（从企业工作地址 `workAddress[*].point` 提取，经纬度缺失时会尝试地理编码并回写）
  - `HazardStatistics.findOneAndUpdate(..., { upsert: true })` 按 `timeFrame + timePoint + filterConditions` 落库

- getStatistics(filter = {})
  - 先在 `HazardStatistics` 中按 `timeFrame = 'all'` 与 `filterConditions.regionCode/industryCode` 查找最近一条
  - 若不存在或超过 24 小时，调用 `calculateAndUpdateStatistics({ timeFrame: 'all', filter })` 重新计算
  - 若传入 `hazardFactors`，对 `hazardFactorsStats` 做内存筛选

- buildBaseQuery(filter)
  - 支持：时间范围 `startDate/endDate` → `reportTime`；区域 `regionCode`；行业 `industryCode`

### 3.3 行政端辅助接口（行业/地区）

插件配置：`lib/plugin/egg-jk-adminorg/config/config.default.js`

- GET `/api/adminorgGov/address/list` → `addressList`
  - 依据监管账号会话限制可见区域
  - 懒加载参数：`parent_code`（缺省代表初始化层级）
  - 过滤 `直辖区`、`市辖区`；补充 `hasChildren`

- GET `/api/adminorgGov/getIndustryCategory` → `getIndustryCategory`
  - 从 `service.industryCategory.find` 拉取并返回

## 4. 数据来源与模型（核心关联）

- `Adminorg`：企业主体，含 `industryCategory`、`workAddress`、`checkResult`、`reportTime` 等
- `IndustryCategory`：行业分类字典（树/平铺，包含 `value`、`label` 等）
- `District`：行政区划字典（`name`、`area_code`、`parent_code`、`level`）
- `Employee`：劳动者表，用于统计 `employeeCount`
- `HazardStatistics`：统计缓存表（预计算结果承载体）

## 5. 参数契约与对齐建议

当前前端与服务层在部分参数命名/类型上存在错位，影响筛选与缓存命中：

- 区域参数
  - 前端：`region`（传 `area_code`）
  - 服务层：`buildBaseQuery` 期望 `regionCode`
  - 建议：统一改为 `regionCode`（前端出参和服务 `getStatistics` 内部映射二选一）

- 行业参数
  - 前端：`industry`（`JSON.stringify(cascader选中路径数组[])`）
  - 服务层：`buildBaseQuery` 期望 `industryCode`（单一编码）
  - 建议：前端改为传单个行业编码字段 `industryCode`（或服务在 `getStatistics` 中解析 `industry`→`industryCode`）

- 日期参数
  - 前端：`dateRange: [start, end]`（可能为 `Date` 对象）
  - 服务：按 `startDate/endDate` 解析为 `new Date(...)`
  - 建议：前端明确格式化为 `YYYY-MM-DD` 或 ISO 字符串，并命名为 `startDate/endDate`

对齐后的好处：
1) `getStatistics` 查询 `HazardStatistics` 可命中已有分区/行业缓存；
2) 预计算落库的 `filterConditions.regionCode/industryCode` 不再为空；
3) `buildBaseQuery` 才能按区域/行业真正过滤原始企业数据；

## 6. 已知问题（前后端协作需修复）

- 地图标记点点击事件的 ID 不一致
  - 生成数据：`mapData` 使用 `enterpriseId` 字段；无 `_id`
  - 前端点击逻辑：`if (marker._id) this.loadUnitDetail(marker.enterpriseId)` 导致永远不触发详情加载
  - 建议：统一使用 `enterpriseId`，判空与取值一致

- 行业字段类型展示不一致
  - `mapData.industry` 为数组（行业名列表），信息窗以字符串输出；详情表以数组 `join('、')`
  - 建议：统一为数组，在信息窗处做 `Array.isArray ? join('、') : String(...)`

- 行业与区域筛选未真正参与服务端筛选
  - 原因：服务仅识别 `regionCode/industryCode`，而前端传 `region/industry`
  - 影响：每次查询都可能触发重算，且结果未按筛选过滤

- 日期未格式化
  - 影响：不同环境下 `new Date(前端值)` 解析不稳定

- 天地图密钥硬编码
  - 建议：迁移至配置或环境变量

## 7. 定时任务与数据鲜度

- `app/schedule/hazard.js` 定时执行 `calculateAndUpdateStatistics({ timeFrame: 'all' })`，即使前端不触发，也会有基础缓存
- `getStatistics` 带有 24 小时鲜度校验，过期自动重算

## 8. 建议的最小改动（供实施参考）

前端：
1) 统一点击事件 ID 字段：判断与传参均用 `enterpriseId`
2) `getFilterParams()` 输出改为：`regionCode`、`industryCode`、`startDate/endDate`（并对日期进行格式化）
3) 信息窗行业字段展示兼容数组

后端（任选其一，建议同时）：
1) 在 `getStatistics(filter)` 入口处做参数归一化：`filter.regionCode ||= filter.region`；`filter.industryCode ||= parseIndustry(filter.industry)`
2) `calculateAndUpdateStatistics` 按规范将 `filterConditions.regionCode/industryCode` 落库，便于缓存命中

---

附：相关源码位置
- 前端页面：`backstage/hazardInspectionData/src/views/Index.vue`
- 前端 API：`backstage/hazardInspectionData/src/api/index.js`
- 路由：`app/router/api.js`
- 控制器：`app/controller/api/hazardStatistics.js`
- 服务：`app/service/hazardStatisticsService.js`
- 定时任务：`app/schedule/hazard.js`
- 行政端行业/地区：`lib/plugin/egg-jk-adminorg/*`

