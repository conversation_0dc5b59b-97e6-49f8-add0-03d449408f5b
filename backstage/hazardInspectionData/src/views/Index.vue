<template>
  <div class="dashboard-container">
    <!-- 筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="危害因素:">
          <el-select v-model="filterForm.hazardFactors" multiple placeholder="选择危害因素" size="small">
            <el-option v-for="item in hazardFactorOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="区域:">
          <el-cascader
            v-model="filterForm.region"
            style="width: 100%"
            :props="districtListProps"
            @change="handleRegionChange"
            clearable
            size="small"
            placeholder="选择区域"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="行业:">
          <el-cascader
            style="width: 100%"
            placeholder="请选择行业类别"
            size="small"
            v-model="filterForm.industry"
            :options="industryCategoryOptions"
            :props="{ multiple: true, checkStrictly: true }"
            collapse-tags
            @change="handleIndustryChange"
            clearable
          ></el-cascader>
        </el-form-item>
        <el-form-item label="时间:">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="applyFilter" :loading="isFilterLoading">应用筛选</el-button>
          <el-button size="small" @click="resetFilter">清空筛选</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 左侧地图区域 -->
      <div class="map-container">
        <div id="map" class="map" v-loading="loading.map" element-loading-text="加载地图数据..."></div>
        <el-dialog
          title="用人单位详情"
          :visible.sync="showDetails"
          width="70%">
          <el-table :data="unitDetails" border style="width: 100%;" header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px">
            <el-table-column prop="unitName" label="单位名称"></el-table-column>
            <el-table-column prop="location" label="详细地址"></el-table-column>
            <el-table-column prop="industry" label="所属行业">
              <template slot-scope="scope">
               {{ scope.row.industry.join('、') }}
              </template>
            </el-table-column>
            <el-table-column prop="employeeCount" label="从业人数"></el-table-column>
            <el-table-column prop="hazardFactors" label="危害因素"></el-table-column>
          </el-table>
        </el-dialog>
      </div>

      <!-- 右侧统计图表区域 -->
      <div class="stats-container">
        <!-- 危害因素分类统计 -->
        <div class="stats-card">
          <div class="dr-title">
            <span class="dr-title-left">
              <span class="dr-title-text">危害因素分类统计</span>
            </span>
            <span class="dr-title-divider"><el-divider></el-divider></span>
          </div>
          <el-table 
            v-loading="loading.hazardFactors"
            element-loading-text="加载中..."
            :data="hazardFactorStats" 
            border 
            style="width: 100%;" 
            header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
          >
            <el-table-column prop="hazardType" label="危害因素类别"></el-table-column>
            <el-table-column prop="checkCount" label="检查单位数"></el-table-column>
            <el-table-column prop="exceedCount" label="超标单位数"></el-table-column>
            <el-table-column prop="percentage" label="类别占比"></el-table-column>
            <el-table-column prop="ranking" label="区域排名"></el-table-column>
          </el-table>
        </div>

        <!-- 行业分布统计 -->
        <div class="stats-card">
          <div class="dr-title">
            <span class="dr-title-left">
              <span class="dr-title-text">行业分布统计</span>
            </span>
            <span class="dr-title-divider"><el-divider></el-divider></span>
          </div>
          <el-table 
            v-loading="loading.industry"
            element-loading-text="加载中..."
            :data="industryStats" 
            border 
            style="width: 100%;" 
            header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
          >
            <el-table-column prop="industry" label="行业类型"></el-table-column>
            <el-table-column prop="checkCount" label="检查单位数"></el-table-column>
            <el-table-column prop="exceedCount" label="超标单位数"></el-table-column>
            <el-table-column prop="percentage" label="行业占比"></el-table-column>
            <el-table-column prop="ranking" label="行业排名"></el-table-column>
          </el-table>
        </div>

        <!-- 区域分布统计 -->
        <div class="stats-card">
          <div class="dr-title">
            <span class="dr-title-left">
              <span class="dr-title-text">区域分布统计</span>
            </span>
            <span class="dr-title-divider"><el-divider></el-divider></span>
          </div>
          <el-table 
            v-loading="loading.region"
            element-loading-text="加载中..."
            :data="regionStats" 
            border 
            style="width: 100%;" 
            header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
          >
            <el-table-column prop="region" label="所属区域"></el-table-column>
            <el-table-column prop="checkCount" label="检查单位数"></el-table-column>
            <el-table-column prop="exceedCount" label="超标单位数"></el-table-column>
            <el-table-column prop="percentage" label="区域占比"></el-table-column>
            <el-table-column prop="ranking" label="区域排名"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { 
  getMapMarkers,
  getHazardFactorStats,
  getIndustryStats,
  getRegionStats,
  getUnitDetail,
  getDistrictList,
  getIndustryCategory,
  getAllStats,
} from '@/api';

export default {
  data() {
    return {
      // 当前时间
      currentTime: new Date().toLocaleString(),
      // 时间更新定时器
      timeInterval: null,
      // 筛选表单
      filterForm: {
        hazardFactors: [],
        region: [],
        selectedRegionObj: null,
        industry: [],
        dateRange: []
      },
      // 筛选选项
      hazardFactorOptions: [
        { value: 'dust', label: '粉尘类' },
        { value: 'chemical', label: '化学物质类' },
        { value: 'physical', label: '物理因素类' },
        { value: 'radiation', label: '放射性因素类' },
        { value: 'biological', label: '生物因素类' }
      ],
      industryOptions: [
        { value: 'manufacturing', label: '制造业' },
        { value: 'mining', label: '采矿业' },
        { value: 'chemical', label: '化工业' },
        { value: 'construction', label: '建筑业' }
      ],
      // 行业分类选项（级联选择器用）
      industryCategoryOptions: [],
      // 危害因素分类统计
      hazardFactorStats: [],
      // 行业分布统计
      industryStats: [],
      // 区域分布统计
      regionStats: [],
      // 用人单位详情
      unitDetails: [],
      showDetails: false,
      
      // 天地图相关属性
      tdtMap: null, // 天地图实例
      tdtToken: '0e1e12da092c60073c8fb11f9477020b', // 天地图API密钥（实际开发中应从环境变量或配置中获取）
      infoWindows: [], // 信息窗口数组
      isInitialized: false, // 地图初始化状态
      // center: [86.21, 41.17], // 地图中心点坐标（新疆区域）
      center: [87.613831, 43.825135], // 地图中心点坐标（新疆区域）
      zoomLevel: 10, // 地图缩放级别
      
      // 标记点数据
      markerData: [],
      // 天地图标记点对象
      markerObjects: [],
      // 信息窗口对象
      infoWindowObjects: [],
      // {{ AURA: Add - 兵团各师市固定点位（长期存在） }}
      fixedMarkers: [
        { name: '第一师', lng: 81.29, lat: 40.55 },
        { name: '第二师', lng: 85.69, lat: 41.86 },
        { name: '第三师', lng: 79.08, lat: 39.87 },
        { name: '第四师', lng: 81.03, lat: 43.94 },
        { name: '第五师', lng: 82.38, lat: 44.85 },
        { name: '第六师', lng: 87.55, lat: 44.17 },
        { name: '第七师', lng: 84.83, lat: 44.70 },
        { name: '第八师', lng: 86.09, lat: 44.31 },
        { name: '第九师', lng: 82.99, lat: 46.76 },
        { name: '第十师', lng: 87.84, lat: 47.34 },
        { name: '第十一师', lng: 87.65, lat: 43.75 },
        { name: '第十二师', lng: 87.38, lat: 43.99 },
        { name: '第十三师', lng: 93.75, lat: 42.80 },
        { name: '兵地融合发展草湖项目区', lng: 87.38, lat: 43.99 }
      ],
      // 固定点自定义图标（橙色圆点）
      fixedMarkerIcon: 'data:image/svg+xml;utf8,' + encodeURIComponent(
        `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
           <circle cx="9" cy="9" r="7" fill="#ff6a00" stroke="#ffffff" stroke-width="2" />
         </svg>`
      ),
      // 固定点位对象集合（不随筛选清除）
      fixedMarkerObjects: [],
      // 固定点名称标签对象集合（不随筛选清除）
      fixedLabelObjects: [],
      
      // 数据加载状态
      loading: {
        map: false,
        hazardFactors: false,
        industry: false,
        region: false
      },
      
      // 筛选加载状态
      isFilterLoading: false,
      
      // 地区级联选择器配置
      districtListProps: {
        checkStrictly: true,
        lazy: true,
        lazyLoad(node, resolve) {
          const { level } = node;
          const params = {
            level,
          };
          if (level) {
            params.parent_code = node.value.area_code;
          }
          
          console.log('Cascader loading node:', node);
          console.log('Cascader request params:', params);
          
          // 调用获取地区列表的API
          getDistrictList(params).then(({ data }) => {
            try {
              console.log('Cascader API response:', data);
              const nodes = Array.from(data.docs).map((item) => ({
                value: item,
                label: item.name,
                leaf: item.level >= 3,
                disabled: item.name === "市辖区" ? true : false,
              }));
              console.log('Cascader resolved nodes:', nodes);
              resolve(nodes);
            } catch (e) {
              console.error('Cascader error processing data:', e);
              resolve([]);
            }
          }).catch(error => {
            console.error('获取地区列表失败:', error);
            resolve([]);
          });
        },
      },
    }
  },
  mounted() {
    // 加载天地图API脚本
    this.loadTiandituScript();
    
    // 每秒更新当前时间
    this.timeInterval = setInterval(() => {
      this.currentTime = new Date().toLocaleString();
    }, 1000);
    
    // 加载初始数据
    this.loadAllData();
    
    // 加载行业分类数据
    this.loadIndustryCategories();
  },
  methods: {
    // 加载所有数据
    async loadAllData() {
      // 优先尝试使用单接口加载
      const params = this.getFilterParams();
      try {
        const res = await getAllStats(params);
        if (res && res.success && res.data) {
          // map
          this.markerData = Array.isArray(res.data.mapData) ? res.data.mapData : [];
          if (this.isInitialized) this.addMarkers();
          // hazard
          this.hazardFactorStats = Array.isArray(res.data.hazardFactors) ? res.data.hazardFactors.map(item => {
            const percentage = item.total > 0 ? ((item.exceed / item.total) * 100).toFixed(2) + '%' : '0%';
            return {
              hazardType: item.name,
              checkCount: item.total,
              exceedCount: item.exceed,
              percentage,
              ranking: '-',
              unit: item.unit || '',
            };
          }) : [];
          // industry
          this.industryStats = Array.isArray(res.data.industry) ? res.data.industry.map(item => ({
            industry: item.name,
            checkCount: item.count,
            exceedCount: item.exceedCount || 0,
            percentage: (typeof item.percentage === 'number') ? (item.percentage.toFixed(2) + '%') : '-',
            ranking: item.ranking || '-',
          })) : [];
          // region
          this.regionStats = Array.isArray(res.data.region) ? res.data.region.map(item => ({
            region: item.name,
            checkCount: item.count,
            exceedCount: item.exceedCount || 0,
            percentage: (typeof item.percentage === 'number') ? (item.percentage.toFixed(2) + '%') : '-',
            ranking: item.ranking || '-',
          })) : [];
          return;
        }
      } catch (e) {
        console.warn('getAllStats 调用失败，降级为多接口:', e);
      }
      // 降级：并发调用多个接口
      await Promise.all([
        this.loadMapMarkers(),
        this.loadHazardFactorStats(),
        this.loadIndustryStats(),
        this.loadRegionStats()
      ]);
    },
    
    // 加载行业分类数据
    loadIndustryCategories() {
      getIndustryCategory().then(res => {
        if (res.data) {
          this.industryCategoryOptions = res.data;
        } else {
          console.error('加载行业分类数据失败');
        }
      }).catch(error => {
        console.error('加载行业分类数据失败:', error);
      });
    },
    
    // 加载地图标记点数据
    async loadMapMarkers() {
      this.loading.map = true;
      try {
        const params = this.getFilterParams();
        const res = await getMapMarkers(params);
        console.log('地图数据:', res);
        if (res.data && res.success) {
          // 直接使用API返回的数组格式
          this.markerData = res.data || [];
          
          console.log('处理后的地图数据:', this.markerData);
          
          // 如果地图已初始化，更新标记点
          if (this.isInitialized) {
            this.addMarkers();
          }
        } else {
          this.$message.error((res.data && res.message) || '加载地图数据失败');
        }
      } catch (error) {
        console.error("加载地图数据失败:", error);
        this.$message.error('加载地图数据失败');
      } finally {
        this.loading.map = false;
      }
    },
    
    // 加载危害因素分类统计
    async loadHazardFactorStats() {
      this.loading.hazardFactors = true;
      try {
        const params = this.getFilterParams();
        const res = await getHazardFactorStats(params);
        console.log('危害因素统计数据:', res);
        if (res.success && res.data) {
          // 转换字段名以匹配表格的预期
          this.hazardFactorStats = res.data.map(item => {
            // 计算百分比
            const percentage = item.total > 0 ? ((item.exceed / item.total) * 100).toFixed(2) + '%' : '0%';
            
            return {
              hazardType: item.name, // 将name映射为hazardType
              checkCount: item.total, // 将total映射为checkCount
              exceedCount: item.exceed, // 将exceed映射为exceedCount
              percentage, // 计算百分比
              ranking: '-', // 暂无排名数据
              unit: item.unit // 保留unit字段
            };
          });
        } else {
          this.$message.error((res.message) || '加载危害因素统计失败');
        }
      } catch (error) {
        console.error("加载危害因素统计失败:", error);
        this.$message.error('加载危害因素统计失败');
      } finally {
        this.loading.hazardFactors = false;
      }
    },
    
    // 加载行业分布统计
    async loadIndustryStats() {
      this.loading.industry = true;
      try {
        const params = this.getFilterParams();
        const res = await getIndustryStats(params);
        console.log('行业分布统计数据:', res);
        
        if (res.success && res.data) {
          // 转换字段名以匹配表格的预期
          this.industryStats = res.data.map(item => {
            return {
              industry: item.name, // 将name映射为industry
              checkCount: item.count, // 将count映射为checkCount
              exceedCount: 0, // 暂无超标数据
              percentage: '-', // 暂无百分比数据
              ranking: '-' // 暂无排名数据
            };
          });
        } else {
          this.$message.error((res.message) || '加载行业分布统计失败');
        }
      } catch (error) {
        console.error("加载行业分布统计失败:", error);
        this.$message.error('加载行业分布统计失败');
      } finally {
        this.loading.industry = false;
      }
    },
    
    // 加载区域分布统计
    async loadRegionStats() {
      this.loading.region = true;
      try {
        const params = this.getFilterParams();
        const res = await getRegionStats(params);
        console.log('区域分布统计数据:', res);
        
        if (res.success && res.data) {
          // 转换字段名以匹配表格的预期
          this.regionStats = res.data.map(item => {
            return {
              region: item.name, // 将name映射为region
              checkCount: item.count, // 将count映射为checkCount
              exceedCount: 0, // 暂无超标数据
              percentage: '-', // 暂无百分比数据
              ranking: '-' // 暂无排名数据
            };
          });
        } else {
          this.$message.error((res.message) || '加载区域分布统计失败');
        }
      } catch (error) {
        console.error("加载区域分布统计失败:", error);
        this.$message.error('加载区域分布统计失败');
      } finally {
        this.loading.region = false;
      }
    },
    
    // 获取单位详情
    async loadUnitDetail(id) {
      try {
        const res = await getUnitDetail({id});
        console.log('单位详情数据:', res);
        
        if (res.success && res.data) {
          // 转换为表格需要的格式
          this.unitDetails = [{
            unitName: res.data.name,
            location: res.data.address,
            industry: res.data.industry,
            employeeCount: res.data.employeeCount || '-',
            hazardFactors: res.data.hazardFactors ? 
              res.data.hazardFactors.map(f => f.name).join(', ') : 
              '-'
          }];
          this.showDetails = true;
        } else {
          this.$message.error((res.message) || '加载单位详情失败');
        }
      } catch (error) {
        console.error("加载单位详情失败:", error);
        this.$message.error('加载单位详情失败');
      }
    },
    
    // 获取筛选参数
    getFilterParams() {
      const params = {};
      // 危害因素
      if (this.filterForm.hazardFactors && this.filterForm.hazardFactors.length > 0) {
        params.hazardFactors = this.filterForm.hazardFactors;
      }
      // 区域（传 area_code，后端将归一为 regionCode）
      if (this.filterForm.selectedRegionObj) {
        params.regionCode = this.filterForm.selectedRegionObj.area_code;
      }
      // 行业 - 处理级联多选（后端可解析 JSON 并提取末级编码集合）
      if (this.filterForm.industry && this.filterForm.industry.length > 0) {
        params.industry = JSON.stringify(this.filterForm.industry);
      }
      // 日期范围（格式化为 YYYY-MM-DD）
      if (this.filterForm.dateRange && this.filterForm.dateRange.length === 2) {
        const [start, end] = this.filterForm.dateRange;
        const fmt = d => {
          if (!d) return '';
          const date = (d instanceof Date) ? d : new Date(d);
          const y = date.getFullYear();
          const m = `${date.getMonth() + 1}`.padStart(2, '0');
          const dd = `${date.getDate()}`.padStart(2, '0');
          return `${y}-${m}-${dd}`;
        };
        params.startDate = fmt(start);
        params.endDate = fmt(end);
      }
      return params;
    },
    
    // 加载天地图API脚本
    loadTiandituScript() {
      return new Promise((resolve, reject) => {
        if (window.T) {
          this.initTiandituMap();
          resolve(window.T);
          return;
        }
        
        // 加载天地图主API
        const tdtScript = document.createElement('script');
        tdtScript.type = 'text/javascript';
        tdtScript.src = `https://api.tianditu.gov.cn/api?v=4.0&tk=${this.tdtToken}`;
        tdtScript.onerror = (e) => {
          console.error('天地图API加载失败', e);
          reject(e);
        };
        
        tdtScript.onload = () => {
          console.log('天地图API加载成功');
          this.initTiandituMap();
          resolve(window.T);
        };
        
        document.head.appendChild(tdtScript);
      });
    },
    
    // 初始化天地图
    initTiandituMap() {
      if (this.tdtMap) return;
      
      try {
        console.log('初始化天地图...');
        
        // 创建地图实例
        this.tdtMap = new T.Map('map');
        
        // 设置显示地图的中心点和级别
        this.tdtMap.centerAndZoom(new T.LngLat(this.center[0], this.center[1]), this.zoomLevel);
        
        // 添加地图类型控件
        this.tdtMap.addControl(new T.Control.MapType());
        
        // 添加缩放控件
        this.tdtMap.addControl(new T.Control.Zoom());
        
        // 添加比例尺控件
        this.tdtMap.addControl(new T.Control.Scale());
        
        // 添加地图点击事件
        this.tdtMap.addEventListener('click', this.handleMapClick);
        
        this.isInitialized = true;
        console.log('天地图初始化完成');
        
        // 添加标记点
        this.addMarkers();
        // 添加固定点位（兵团各师市）
        this.addFixedMarkers();
      } catch (error) {
        console.error("天地图初始化失败:", error);
      }
    },
    
    // 添加标记点
    addMarkers() {
      // 清除现有标记点
      this.clearMapOverlays();
      
      if (!Array.isArray(this.markerData) || this.markerData.length === 0) {
        console.warn('没有可用的标记点数据');
        return;
      }
      
      console.log('开始添加标记点...');
      
      // 添加新的标记点
      this.markerData.forEach((marker, index) => {
        try {
          // 检查必要的坐标信息
          if (!marker.location || !marker.location.longitude || !marker.location.latitude) {
            console.warn(`标记点 #${index} 缺少经纬度信息:`, marker);
            return; // 跳过此次循环
          }
          
          // 创建标记点，从location对象中获取经纬度
          const markerObj = new T.Marker(new T.LngLat(marker.location.longitude, marker.location.latitude));
          
          // 创建信息窗口
          const infoWindow = new T.InfoWindow();
          
          // 格式化危害因素信息
          let hazardFactorsHtml = '';
          if (Array.isArray(marker.hazardFactors) && marker.hazardFactors.length > 0) {
            hazardFactorsHtml = marker.hazardFactors.map(hf => 
              `${hf.name}${hf.value ? ': ' + hf.value + (hf.unit ? hf.unit : '') : ''}`
            ).join(', ');
          } else {
            hazardFactorsHtml = '无';
          }
          
          infoWindow.setContent(`
            <div style="padding: 8px; max-width: 300px;">
              <h4 style="margin: 0 0 5px 0; border-bottom: 1px solid #ccc; padding-bottom: 5px;">${marker.name || '未命名'}</h4>
              <p style="margin: 5px 0;"><strong>详细地址:</strong> ${marker.address || '未知地址'}</p>
              <p style="margin: 5px 0;"><strong>所属行业:</strong> ${Array.isArray(marker.industry) ? marker.industry.join('、') : (marker.industry || '未知行业')}</p>
              <p style=\"margin: 5px 0;\"><strong>从业人数:</strong> ${typeof marker.employeeCount === 'number' ? marker.employeeCount : (marker.employeeCount || '-')}</p>
              <p style="margin: 5px 0;"><strong>危害因素:</strong> ${hazardFactorsHtml}</p>
            </div>
          `);
          
          // 添加点击事件
          markerObj.addEventListener('click', () => {
            // 加载并显示单位详情
            console.log('marker:', marker);
            if (marker && marker.enterpriseId) {
              this.loadUnitDetail(marker.enterpriseId);
            } else {
              console.warn('标记点缺少 enterpriseId，无法加载详情');
            }
            
            // 打开信息窗口
            markerObj.openInfoWindow(infoWindow);
          });
          
          // 添加悬停事件
          markerObj.addEventListener('mouseover', () => {
            markerObj.openInfoWindow(infoWindow);
          });
          
          // 将标记点添加到地图
          this.tdtMap.addOverLay(markerObj);
          
          // 保存标记点和信息窗口对象，以便后续管理
          this.markerObjects.push(markerObj);
          this.infoWindowObjects.push(infoWindow);
        } catch (error) {
          console.error(`添加标记点 #${index} 失败:`, error, marker);
        }
      });
      console.log(`成功添加 ${this.markerObjects.length} 个标记点,中心点:`, this.center);
    },
    
    // 清除地图覆盖物
    clearMapOverlays() {
      // 清除标记点
      this.markerObjects.forEach(marker => {
        if (marker && this.tdtMap) {
          this.tdtMap.removeOverLay(marker);
        }
      });
      this.markerObjects = [];
      
      // 清除信息窗口（天地图API中信息窗口会随标记点一起清除）
      this.infoWindowObjects = [];
    },

    // {{ AURA: Add - 添加兵团各师市固定点位，不随筛选清除 }}
    addFixedMarkers() {
      if (!this.tdtMap || !Array.isArray(this.fixedMarkers)) return;
      // 已添加则不重复添加
      if (this.fixedLabelObjects.length > 0) return;
      try {
        this.fixedMarkers.forEach((fm) => {
          if (!fm || !fm.lng || !fm.lat) return;
          // 始终展示的名称标签（作为区域标识）
          const label = new T.Label({
            text: fm.name,
            position: new T.LngLat(fm.lng, fm.lat),
            offset: new T.Point(12, -12),
          });
          // 简单样式，便于区分
          if (label.setBackgroundColor) label.setBackgroundColor('#ffffffcc');
          if (label.setBorderColor) label.setBorderColor('#409EFF');
          if (label.setFontColor) label.setFontColor('#303133');
          this.tdtMap.addOverLay(label);
          this.fixedLabelObjects.push(label);
        });
      } catch (e) {
        console.error('添加固定点位失败:', e);
      }
    },
    
    // 处理地图点击事件
    handleMapClick(e) {
      console.log('地图点击位置:', e.lnglat.lng, e.lnglat.lat);
      
      // 可以在这里添加点击地图显示详情的逻辑
      // 例如，找到最近的标记点并显示其详情
    },
    
    // 应用筛选条件
    async applyFilter() {
      console.log('应用筛选条件:', this.filterForm);
      
      this.isFilterLoading = true;
      try {
        // 重新加载所有数据
        await this.loadAllData();
      } finally {
        this.isFilterLoading = false;
      }
    },
    
    // 重置筛选条件
    resetFilter() {
      this.filterForm = {
        hazardFactors: [],
        region: [],
        selectedRegionObj: null,
        industry: [],
        dateRange: []
      };
      
      // 应用清空后的筛选条件
      this.applyFilter();
    },
    
    // 显示用人单位详情
    showUnitDetails(marker) {
      // 通过API加载单位详情
      this.loadUnitDetail(marker.enterpriseId || marker.id);
    },
    
    // 处理区域变更事件
    handleRegionChange(value) {
      if (value && value.length > 0) {
        // 获取最后一级选中的地区对象
        const selectedRegion = value[value.length - 1];
        // 将地区对象存储到筛选参数中，便于后续API调用
        this.filterForm.selectedRegionObj = selectedRegion;
      } else {
        this.filterForm.selectedRegionObj = null;
      }
    },
    
    // 处理行业变更事件
    handleIndustryChange(value) {
      if (value && value.length > 0) {
        // 将行业对象存储到筛选参数中，便于后续API调用
        this.filterForm.industry = value;
      } else {
        this.filterForm.industry = [];
      }
    }
  },
  beforeDestroy() {
    // 清理资源
    if (this.tdtMap) {
      // 移除事件监听
      this.tdtMap.removeEventListener('click', this.handleMapClick);
      
      // 清除覆盖物
      this.clearMapOverlays();
      
      // 清空地图实例引用
      this.tdtMap = null;
    }
    
    // 清除定时器
    clearInterval(this.timeInterval);
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f0f2f5;
}

.dashboard-header {
  height: 60px;
  background-color: #409eff;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  
  .logo {
    display: flex;
    align-items: center;
    
    img {
      height: 40px;
      margin-right: 10px;
    }
    
    .title {
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    
    .time {
      margin-right: 20px;
    }
  }
}

.filter-container {
  padding: 10px 20px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .filter-form {
    display: flex;
    flex-wrap: wrap;
  }
}

.main-content {
  flex: 1;
  display: flex;
  padding: 20px;
  height: calc(100vh - 130px);
  overflow: hidden;
  
  .map-container {
    flex: 7;
    margin-right: 20px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    
    .map {
      width: 100%;
      height: 100%;
    }
  }
  
  .stats-container {
    flex: 3;
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow-y: auto;
    
    .stats-card {
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      padding: 15px;
    }
  }
}

.dr-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 10px;
  
  .dr-title-left {
    font-size: 16px;
    font-weight: 500;
    border-left: 6px solid #409eff;
    display: flex;
    height: 24px;
    line-height: 24px;
    
    .dr-title-text {
      margin-left: 10px;
    }
  }
  
  .dr-title-divider {
    flex: 1;
    padding: 0 10px;
  }
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    
    .map-container {
      margin-right: 0;
      margin-bottom: 20px;
      height: 50%;
    }
    
    .stats-container {
      height: 50%;
    }
  }
}
</style>

<style>
/* 隐藏天地图logo和版权信息 */
.tdt-control-copyright {
  display: none !important;
  visibility: hidden !important;
}

.tdt-control-logo {
  display: none !important;
  visibility: hidden !important;
}
</style>