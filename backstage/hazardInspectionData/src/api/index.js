import request from '@root/publicMethods/request';

// 获取在线监测列表
export function getonlineMonitoringList() {
  // 获取所有单位
  return request({
    url: '/manage/onlineMonitoring/statistics',
    method: 'get',
  });
}

// 获取地图标记点数据
export function getMapMarkers(params) {
  return request({
    url: '/api/hazardInspectionData/mapMarkers',
    method: 'post',
    data: params,
  });
}

// 获取危害因素分类统计
export function getHazardFactorStats(params) {
  return request({
    url: '/api/hazardInspectionData/hazardFactorStats',
    method: 'post',
    data: params,
  });
}

// 获取行业分布统计
export function getIndustryStats(params) {
  return request({
    url: '/api/hazardInspectionData/industryStats',
    method: 'post',
    data: params,
  });
}

// 获取区域分布统计
export function getRegionStats(params) {
  return request({
    url: '/api/hazardInspectionData/regionStats',
    method: 'post',
    data: params,
  });
}

// 获取单位详情
export function getUnitDetail(params) {
  return request({
    url: '/api/hazardInspectionData/unitDetail',
    method: 'get',
    params,
  });
}

// 获取地区列表

export function getDistrictList(params) {
  return request({
    url: '/api/adminorgGov/address/list',
    params,
    method: 'get',
  });
}


// 一次性获取全部统计与地图数据
export function getAllStats(params) {
  return request({
    url: '/api/hazardInspectionData/allStats',
    method: 'post',
    data: params,
  });
}


export function getdDstrictsData(dataType, year, adcode, listType, viewType) {
  return request({
    url: '/manage/getdDstrictsData',
    method: 'post',
    data: {
      dataType,
      year,
      adcode,
      listType,
      viewType,
    },
  });
}
export function getIndustryCategory() {
  return request({
    method: 'get',
    url: '/api/adminorgGov/getIndustryCategory',
  });
}
