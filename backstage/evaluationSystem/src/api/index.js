import request from "@root/publicMethods/request";

export function addTarget(data) {
  return request({
    url: "/manage/evaluationSystem/addTarget",
    method: "post",
    data,
  });
}

// getTargets
export function getTargets(params) {
  return request({
    url: "/manage/evaluationSystem/getTargets",
    method: "get",
    params,
  });
}

export function removeTarget(data) {
  return request({
    url: "/manage/evaluationSystem/removeTarget",
    method: "post",
    data,
  });
}

export function addTargetContent(data) {
  return request({
    url: "/manage/evaluationSystem/addTargetContent",
    method: "post",
    data,
  });
}

export function getTargetContents(params) {
  return request({
    url: "/manage/evaluationSystem/getTargetContents",
    method: "get",
    params,
  });
}

// updateContent
export function updateContent(data) {
  return request({
    url: "/manage/evaluationSystem/updateContent",
    method: "post",
    data,
  });
}

export function removeContent(data) {
  return request({
    url: "/manage/evaluationSystem/removeContent",
    method: "post",
    data,
  });
}

export function addBasicItem(data) {
  return request({
    url: "/manage/evaluationSystem/addBasicItem",
    method: "post",
    data,
  });
}

// getAllModels

export function getAllModels(params) {
  return request({
    url: "/manage/evaluationSystem/getAllModels",
    method: "get",
    params,
  });
}

// addAccessItem
export function addAccessItem(data) {
  return request({
    url: "/manage/evaluationSystem/addAccessItem",
    method: "post",
    data,
  });
}

// removeAccessItem
export function removeAccessItem(data) {
  return request({
    url: "/manage/evaluationSystem/removeAccessItem",
    method: "post",
    data,
  });
}

// updateAccessItem
export function updateAccessItem(data) {
  return request({
    url: "/manage/evaluationSystem/updateAccessItem",
    method: "post",
    data,
  });
}

export const getAssessmentModelListApi = (params) => {
  return request({
    url: "/manage/evaluationSystem/getAssessmentModelList",
    method: "get",
    params,
  });
};

export const getAssessmentModelApi = (params) => {
  return request({
    url: "/manage/evaluationSystem/getAssessmentModel",
    method: "get",
    params,
  });
};

export const addOrEditAssessmentModelApi = (data) => {
  return request({
    url: "/manage/evaluationSystem/addOrEditAssessmentModel",
    method: "post",
    data,
  });
};

export const enableAssessmenModelApi = (data) => {
  return request({
    url: "/manage/evaluationSystem/enableAssessmenModel",
    method: "put",
    data,
  });
};

export const deleteAssessmentModelApi = (params) => {
  return request({
    url: "/manage/evaluationSystem/deleteAssessmentModel",
    method: "delete",
    params,
  });
};

export const checkAssessmentModelSameNameApi = (data) => {
  return request({
    url: "/manage/evaluationSystem/checkAssessmentModelSameName",
    method: "post",
    data,
  });
};
