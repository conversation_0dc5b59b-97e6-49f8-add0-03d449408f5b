<script>
import _ from "lodash";

import { nanoid } from 'nanoid'

import { getAssessmentModelApi, getTargetContents, addOrEditAssessmentModelApi } from "@/api/index"

import AssessmentTable from "../../components/assessment-table/index.vue"

export default {
  name: "LookAssessmentModelView",
  components: { AssessmentTable },
  data() {
    return {
      title: "评估模型",
      assessmentModel: {
        model_name: "",
        year: "",
        release_status: "0",
        basic_conditions: [],
        assessment_info: []
      },
      assessmentModelRules: {
        // year: [
        //   { required: true, message: '请选择年度', trigger: 'change' }
        // ],
      },
      basicConditionsDialogVisible: false,
      basicCondition: {
        name: ""
      },
      basicConditionRules: {
        name: [
          { required: true, message: '请输入基本条件', trigger: 'blur' }
        ],
      },
      assessmentInfo: {
        indicator_id: null,
        score: 0,
        review_mode: [],
        score_criteria: "",
        indicator_type: null
      },
      assessmentInfoRules: {
        indicator_id: [
          { required: true, message: '请选择指标', trigger: 'change' }
        ],
        score: [
          { required: true, message: '请输入分值', trigger: 'blur' }
        ],
        review_mode: [
          { type: 'array', required: true, message: '请选择复核方式', trigger: 'change' }
        ],
        score_criteria: [
          { required: true, message: '请输入赋分标准', trigger: 'blur' }
        ]
      },
      assessmentInfoDialogVisible: false,
      indictorList: []
    }
  },
  created() {
    const { id } = this.$route.query
    if (id) { // 编辑
      this.getAssessmentModel(id)
    }
    this.getIndictorList()
  },
  methods: {
    goBack() {
      this.$router.push({
        name: "index",
        query: {
          activeName: "model"
        }
      })
    },
    handleAddBasicCondition() {
      this.basicConditionsDialogVisible = true
      this.basicCondition = {
        name: ""
      }
    },
    basicConditionDialogConfirm() {
      // 校验表单
      this.$refs.basicConditionFormRef.validate((valid) => {
        if (valid) {

          this.assessmentModel.basic_conditions.push({ ..._.cloneDeep(this.basicCondition), nanoid: nanoid() })

          this.basicConditionsDialogVisible = false
        } else {
          return false;
        }
      });
    },
    basicConditionDialogCancel() {
      this.basicConditionsDialogVisible = false
    },
    handleBasicConditionsDialogClose() {
      // 移除校验结果 
      this.$refs.basicConditionFormRef.clearValidate()
      // 重置数据
      this.basicCondition = {
        name: ""
      }
    },
    handleDeleteBasicCondition(row) {
      this.$confirm("确定删除该基本条件吗？")
        .then(_ => {
          const index = this.assessmentModel.basic_conditions.findIndex(item => item.nanoid === row.nanoid);
          if (index !== -1) {
            this.assessmentModel.basic_conditions.splice(index, 1);
          }
          done();
        })
        .catch(_ => { });
    },
    async getAssessmentModel(id) {
      try {
        const { data } = await getAssessmentModelApi({ id })
        this.assessmentModel = data
        this.assessmentModel.basic_conditions.forEach(item => {
          item.nanoid = nanoid()
        })
      } catch (error) {
        this.$message.error("数据获取失败")
      }
    },
    assessmentInfoDialogConfirm() {
      // 校验表单
      this.$refs.assessmentInfoFormRef.validate((valid) => {
        if (valid) {

          if (!this.assessmentInfo.edit) { // 新增
            const assessmentInfo = _.cloneDeep(this.assessmentInfo)
            const indictorList = _.cloneDeep(this.indictorList)

            assessmentInfo.indicator_id = assessmentInfo.indicator_id[assessmentInfo.indicator_id.length - 1]

            indictorList.forEach(item1 => {
              if (!item1.children) return;
              item1.children.forEach(item2 => {
                if (!item2.children) return;
                item2.children.forEach(item3 => {
                  if (item3.id === assessmentInfo.indicator_id) {
                    console.log("success")

                    item3.item = assessmentInfo
                  }
                })
              })
            })
            const res = this.filterTreeData(indictorList)[0]
            let flag = true

            this.assessmentModel.assessment_info.forEach(item => {
              if (item.id === res.id) {
                flag = false
                item.children.push(res.children[0])
              }
            })

            if (flag) {
              this.assessmentModel.assessment_info.push(res)
            }
          } else {
            const assessmentInfo = _.cloneDeep(this.assessmentInfo)
            console.log("assessmentInfo", assessmentInfo)
            assessmentInfo.indicator_id = assessmentInfo.indicator_id[assessmentInfo.indicator_id.length - 1]

            this.assessmentModel.assessment_info.forEach(item1 => {
              item1.children.forEach(item2 => {
                item2.children.forEach(item3 => {
                  if (item3.id === assessmentInfo.indicator_id) {
                    item3.item = assessmentInfo
                  }
                })
              })
            })
          }

          this.assessmentInfoDialogVisible = false
        } else {
          return false;
        }
      });
    },
    assessmentInfoDialogCancel() {
      this.assessmentInfoDialogVisible = false;
    },
    handleAssessmentInfoDialogClose() {
      // 移除校验结果 
      this.$refs.assessmentInfoFormRef.clearValidate()
      // 重置数据
      this.assessmentInfo = {
        indicator_id: null,
        score: 0,
        review_mode: [],
        score_criteria: "",
        indicator_type: null
      }
    },
    handleAddAssessmentInfo() {
      this.assessmentInfoDialogVisible = true;
    },
    async getIndictorList() {
      try {
        const { data } = await getTargetContents()
        this.indictorList = data
      } catch (error) {
        this.$message.error("指标数据获取失败")
      }
    },
    filterTreeData(data) {
      // 深拷贝原始数据避免污染原数据
      const clonedData = JSON.parse(JSON.stringify(data));

      return clonedData.filter((level1) => {
        // 处理一级节点
        level1.children = level1.children.filter((level2) => {
          // 处理二级节点
          level2.children = level2.children.filter(
            (level3) => level3.item, // 过滤三级节点
          );
          return level2.children.length > 0; // 保留有子节点的二级节点
        });
        return level1.children.length > 0; // 保留有子节点的一级节点
      });
    },
    handleEditAssessmentInfo(val) {
      const res = _.cloneDeep(val.item);
      res.edit = true
      this.indictorList.forEach(item1 => {
        item1.children.forEach(item2 => {
          item2.children.forEach(item3 => {
            if (item3.id === res.indicator_id) {
              res.indicator_id = [item1.id, item2.id, item3.id]
            }
          })
        })
      })
      console.log(res)
      this.assessmentInfo = res
      this.assessmentInfoDialogVisible = true
    },
    handleDeleteAssessmentInfo(val) {
      this.$confirm(`确定删除该评估信息吗？`)
        .then(_ => {
          this.assessmentModel.assessment_info.forEach(item1 => {
            item1.children.forEach(item2 => {
              item2.children.forEach(item3 => {
                if (item3.id = val.id) {
                  delete item3.item
                }
              })
            })
          });
          this.assessmentModel.assessment_info = this.filterTreeData(this.assessmentModel.assessment_info)
        })
        .catch(_ => { });

    },
    save() {
      this.$refs.assessmentModelFormRef.validate((valid) => {
        if (valid) {
          if (this.assessmentModel.basic_conditions.length > 0 && this.assessmentModel.assessment_info.length > 0) {
            this.submit()
          } else {
            this.$message.warning("请将基本条件、评估信息表单填写完整")
          }
        } else {
          return false;
        }
      });
    },
    release() {
      this.$refs.assessmentModelFormRef.validate((valid) => {
        if (valid) {

          if (this.assessmentModel.basic_conditions.length > 0 && this.assessmentModel.assessment_info.length > 0) {
            this.$confirm(`发布后将不能删除模型。确定发布该评估模型吗？`)
              .then(_ => {
                this.submit('发布')
              })
              .catch(_ => { });
          } else {
            this.$message.warning("请将基本条件、评估信息表单填写完整")
          }

        } else {
          return false;
        }
      });
    },
    async submit(type) {
      // 处理数据
      const item = []
      const assessmentModel = _.cloneDeep(this.assessmentModel)
      assessmentModel.assessment_info.forEach(item1 => {
        item1.children.forEach(item2 => {
          item2.children.forEach(item3 => {
            if (item3.item) {
              item.push(item3.item)
            }
          })
        })
      })
      assessmentModel.assessment_info = item
      if (type === "发布") {
        assessmentModel.release_status = "1"
      }
      try {
        await addOrEditAssessmentModelApi(assessmentModel)
        this.goBack()
      } catch (error) {
        console.log(error)
        this.$message.error("提交失败")
      }
    }
  },
}
</script>

<template>
  <div class="look-assessment-view">
    <el-page-header @back="goBack" :content="title"></el-page-header>
    <el-form :model="assessmentModel" :rules="assessmentModelRules" ref="assessmentModelFormRef">
      <div class="mode-form" style="display: flex;">
        <el-form-item label="年度" prop="year">
          <el-date-picker v-model="assessmentModel.year" type="year" value-format="yyyy" format="yyyy"
            placeholder="选择年度" disabled>
          </el-date-picker>
        </el-form-item>
        <el-form-item label="模型名称" prop="model_name">
          <el-input v-model="assessmentModel.model_name" disabled></el-input>
        </el-form-item>
      </div>
    </el-form>
    <div class="basic_conditions">
      <div class="title">
        <span class="content">基本条件</span>
      </div>
      <el-table :data="assessmentModel.basic_conditions" border style="width: 100%; margin-top: 20px" stripe
        header-cell-style="background-color: #f5f7fa; color: #606266;height:46px; text-algin: center">
        <el-table-column type="index" label="序号" align="center" width="180">
        </el-table-column>
        <el-table-column prop="name" label="基本条件" align="center">
        </el-table-column>
      </el-table>
    </div>
    <div class="assessment_info">
      <div class="title">
        <span class="content">评估信息</span>
      </div>
      <AssessmentTable :tableData="assessmentModel.assessment_info" hidden @edit="handleEditAssessmentInfo"
        @delete="handleDeleteAssessmentInfo" />
    </div>

    <!-- 基本条件弹窗 -->
    <el-dialog title="新增基本条件" :visible.sync="basicConditionsDialogVisible" @close="handleBasicConditionsDialogClose">
      <el-form :model="basicCondition" :rules="basicConditionRules" ref="basicConditionFormRef">
        <el-form-item label="活动名称" prop="name">
          <el-input v-model="basicCondition.name" type="textarea" maxlength="500" show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="basicConditionDialogCancel">取 消</el-button>
        <el-button type="primary" @click="basicConditionDialogConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 评估信息弹窗 -->
    <el-dialog :title="assessmentInfo.id ? '编辑评估信息' : '新增评估信息'" :visible.sync="assessmentInfoDialogVisible"
      destroy-on-close @close="handleAssessmentInfoDialogClose">
      <el-form :model="assessmentInfo" :rules="assessmentInfoRules" ref="assessmentInfoFormRef" label-width="80px">
        <el-form-item label="选择指标" prop="indicator_id">
          <el-cascader v-model="assessmentInfo.indicator_id" :options="indictorList"
            :props="{ value: 'id', label: 'name', emitPath: 'false' }"></el-cascader>
        </el-form-item>
        <el-form-item label="分值" prop="score">
          <el-input-number v-model="assessmentInfo.score" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="复核方式" prop="review_mode">
          <el-checkbox-group v-model="assessmentInfo.review_mode">
            <el-checkbox label="资料审查"></el-checkbox>
            <el-checkbox label="现场勘察"></el-checkbox>
            <el-checkbox label="访谈"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="赋分标准" prop="score_criteria">
          <el-input v-model="assessmentInfo.score_criteria" type="textarea"></el-input>
        </el-form-item>
        <el-form-item label="指标类型" prop="indicator_type">
          <el-select v-model="assessmentInfo.indicator_type" placeholder="请选择">
            <el-option label="存在职业危害因素企业的特有指标" value="存在职业危害因素企业的特有指标"></el-option>
            <el-option label="企业内部设置食堂或餐厅" value="企业内部设置食堂或餐厅"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="assessmentInfoDialogCancel">取 消</el-button>
        <el-button type="primary" @click="assessmentInfoDialogConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
@import url("./index.moudle.scss")
</style>