.add-edit-assessment-view {
  width: 100%;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 24px;

  .mode-form {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;

    .el-form-item {
      display: flex;
    }
  }

  .basic_conditions {
  }

  .assessment_info {
    display: flex;
    flex-direction: column;
    gap: 24px;
    min-height: 300px;
  }

  .basic_conditions,
  .assessment_info {
    display: flex;
    flex-direction: column;

    .title {
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .content {
        padding-left: 8px;
      }

      &::before {
        content: "";
        display: block;
        width: 2px;
        height: 100%;
        background-color: #7ab5f1;
        position: absolute;
      }
    }
  }

  & > .operate {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
  }
}
