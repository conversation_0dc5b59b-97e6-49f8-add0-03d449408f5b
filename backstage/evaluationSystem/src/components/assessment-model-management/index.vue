<script>
import { getAssessmentModelListApi, enableAssessmenModelApi, deleteAssessmentModelApi } from "@/api/index"

export default {
  name: "AssessmentModelManagenemtComp",
  data() {
    return {
      assessmentModelList: [],
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  created() {
    this.getAssessmentModelList()
  },
  methods: {
    async getAssessmentModelList() {
      try {
        const { data } = await getAssessmentModelListApi({
          page: this.pagination.page,
          pageSize: this.pagination.pageSize
        })
        this.assessmentModelList = data.records
        this.pagination.total = data.total
      } catch (error) {
        this.$message.error("列表数据获取失败")
      }
    },
    handleSizeChange() {
      this.getAssessmentModelList()
    },
    handleCurrentChange() {
      this.getAssessmentModelList()
    },
    // 新增模型
    handleAddAssessmentModel() {
      this.$router.push({
        name: "addEditAssessmentModel",
      })
    },
    // 编辑模型
    handleEditAssessmentModel(row) {
      this.$router.push({
        name: "addEditAssessmentModel",
        query: {
          id: row.id
        }
      })
    },
    // 查看模型
    handleLookAssessmentModel(row) {
      this.$router.push({
        name: "lookAssessmentModel",
        query: {
          id: row.id
        }
      })
    },
    // 启用评估模型
    async enableAssessmenModel(row) {
      try {
        await enableAssessmenModelApi({
          id: row.id,
          release_status: '1'
        })
        this.getAssessmentModelList()
      } catch (error) {
        this.$message.error("操作失败")
      }
    },
    async disabledAssessmenModel(row) {
      try {
        await enableAssessmenModelApi({
          id: row.id,
          release_status: '2'
        })
        this.getAssessmentModelList()
      } catch (error) {
        this.$message.error("操作失败")
      }
    },

    // 删除评估模型
    async deleteAssessmentModel(row) {
      this.$confirm(`确定删除该评估模型吗？`)
        .then(async _ => {
          try {
            await deleteAssessmentModelApi({
              id: row.id
            })
            this.getAssessmentModelList()
          } catch (error) {
            this.$message.error("删除失败")
          }
        })
        .catch(_ => { });
    }
  }
}
</script>

<template>
  <div class="assessment-model-management">
    <div class="operate">
      <el-button type="primary" @click="handleAddAssessmentModel">新增模型</el-button>
    </div>
    <el-table style="width: 100%; margin-top: 20px" :data="assessmentModelList"
      :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333333' }" border>
      <el-table-column type="index" label="序号" width="100">
      </el-table-column>
      <el-table-column prop="model_name" label="模型名称" :show-overflow-tooltip="true" min-width="150">
      </el-table-column>
      <el-table-column prop="year" label="年度" :show-overflow-tooltip="true" min-width="110">
      </el-table-column>
      <el-table-column prop="release_time_format" label="发布时间" min-width="110">
      </el-table-column>
      <el-table-column prop="create_time_format" label="创建时间" min-width="110">
      </el-table-column>
      <el-table-column prop="release_status" label="状态" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.release_status === '0'">已保存</el-tag>
          <el-tag v-else-if="scope.row.release_status === '1'" type="success">已发布</el-tag>
          <el-tag v-else-if="scope.row.release_status === '2'" type="info">已禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" :show-overflow-tooltip="true" width="220">
        <template slot-scope="scope">
          <el-button v-if="scope.row.release_status === '0'" type="primary" size="mini"
            @click="handleEditAssessmentModel(scope.row)">编辑</el-button>
          <el-button v-if="scope.row.release_status != '0'" type="primary" size="mini"
            @click="handleLookAssessmentModel(scope.row)">查看</el-button>
          <el-button v-if="scope.row.release_status === '1'" type="warning" size="mini"
            @click="disabledAssessmenModel(scope.row)">禁用</el-button>
          <el-button v-if="scope.row.release_status === '2'" type="success" size="mini"
            @click="enableAssessmenModel(scope.row)">启用</el-button>
          <el-button v-if="scope.row.release_status === '0'" type="danger" size="mini"
            @click="deleteAssessmentModel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page.sync="pagination.page" :page-sizes="[5, 10, 15, 20]" :page-size="pagination.pageSize"
        layout="sizes, prev, pager, next" :total="pagination.total">
      </el-pagination>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import url("./index.moudle.scss")
</style>