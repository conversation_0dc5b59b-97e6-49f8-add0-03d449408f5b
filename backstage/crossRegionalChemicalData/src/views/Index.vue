<template>
  <div class="content">
    <!-- 1.毒物检测情况 -->
    <div class="dr-title">
      <span class="dr-title-left">
        <span class="dr-title-text">毒物检测情况</span>
      </span>
      <span class="dr-title-divider"><el-divider></el-divider></span>
      
    </div>
    <div style="margin-bottom: 10px;">
      <span>采样时间：</span>
      <span class="dr-title-btn">
        <!-- 采样时间范围筛选器 -->
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          @change="handleDateChange"
          style="width: 320px;"
        ></el-date-picker>
      </span>
    </div>
    <div class="chart-row">
      <div class="chart-col">
        <div ref="chart1" class="chart-container"></div>
      </div>
      <div class="chart-col">
        <div ref="chart2" class="chart-container"></div>
      </div>
    </div>

    <div class="chart-row">
      <div class="chart-col">
        <div ref="chart3" class="chart-container"></div>
      </div>
      <div class="chart-col">
        <div ref="chart4" class="chart-container"></div>
      </div>
    </div>

    <div class="chart-row">
      <div class="chart-col">
        <div ref="chart5" class="chart-container"></div>
      </div>
    </div>

    <!-- 2.远程诊疗情况 -->
    <div class="dr-title">
      <span class="dr-title-left">
        <span class="dr-title-text">远程诊疗情况</span>
      </span>
      <span class="dr-title-divider"><el-divider></el-divider></span>
      <span class="dr-title-btn"></span>
    </div>
    <el-descriptions border :column="2">
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="毒物信息数据库统计情况"
        >{{ telemedicineData.poisonInformation }}</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="中毒病例数据库统计情况"
        >{{ telemedicineData.casesOfPoisoning }}</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="远程会诊统计数情况"
        >{{ telemedicineData.remoteConsultation }}</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="远程门诊统计数情况"
        >{{ telemedicineData.teleclinics }}</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="现场救治指导统计分析情况"
        >{{ telemedicineData.treatmentGuidance }}</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="化学中毒患者管理情况"
        >{{ telemedicineData.poisonedPatients }}</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="中毒病例远程监测情况"
        >{{ telemedicineData.remoteMonitoring }}</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="化学中毒救治专家库管理情况"
        >{{ telemedicineData.expertPool }}</el-descriptions-item
      >
    </el-descriptions>

    <!-- 3.药品储备情况 -->
    <div class="dr-title">
      <span class="dr-title-left">
        <span class="dr-title-text">药品储备情况</span>
      </span>
      <span class="dr-title-divider"><el-divider></el-divider></span>
      <span class="dr-title-btn"></span>
    </div>
    <el-descriptions border :column="2">
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="化学中毒特效解毒药剂的生产储备和应急储备配置情况"
        >-</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="各省市药品储备的科研、情报、生产、储运和使用的情况"
        >-</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="解毒药剂的生产储备和应急储备情况"
        >-</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="药物救治方案和化学中毒救治专家的储备情况"
        >-</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="生产技术能力和交通转运能力的储备情况"
      >
        {{ medicineData.productionPlan }}</el-descriptions-item
      >
    </el-descriptions>

    <!-- 4.人才分布情况 -->
    <div class="dr-title">
      <span class="dr-title-left">
        <span class="dr-title-text">人才分布情况</span>
      </span>
      <span class="dr-title-divider"><el-divider></el-divider></span>
      <span class="dr-title-btn"></span>
    </div>
    <el-descriptions border :column="2">
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="化学中毒救治课程体系完善情况"
        >100%</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="化学中毒救治技术培训情况"
        >100%</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="化学中毒突发事件管理情况"
        >100%</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="化学中毒救治知识培训评估情况"
        >{{ talentData.trainingEvaluation }}</el-descriptions-item
      >
    </el-descriptions>

    <!-- 5.科研情况 -->
    <div class="dr-title">
      <span class="dr-title-left">
        <span class="dr-title-text">科研情况</span>
      </span>
      <span class="dr-title-divider"><el-divider></el-divider></span>
      <span class="dr-title-btn"></span>
    </div>
    <el-descriptions border :column="2">
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="各区域科研投入、科研产出和科研影响力情况"
        >-</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="化学中毒救治相关标准及政策法规、相关专著拟定与管理情况"
        >{{ researchData.policyManagement }}</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="化学灾害医学救援和化学中毒救治学术会议举办情况"
        >{{ researchData.academicConferences }}</el-descriptions-item
      >
    </el-descriptions>

    <!-- 6.应急演练情况 -->
    <div class="dr-title">
      <span class="dr-title-left">
        <span class="dr-title-text">应急演练情况</span>
      </span>
      <span class="dr-title-divider"><el-divider></el-divider></span>
      <span class="dr-title-btn"></span>
    </div>
    <el-descriptions border :column="2">
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="化学灾害医学应急演练预案情况"
        >{{ drillData.emergencyDrills }}</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="化学灾害医学应急救援队伍演练组织情况"
        >{{ drillData.drillOrganization }}</el-descriptions-item
      >
      <el-descriptions-item
        :labelStyle="{ width: '25%' }"
        :contentStyle="{ width: '25%' }"
        label="化学灾害医学应急救援队伍演练总结分析留档情况"
        >{{ drillData.drillOrganization }}</el-descriptions-item
      >
    </el-descriptions>
  </div>
</template>

<script>
import {
  getToxicDbs,
  getCaseDbs,
  getApplyLists,
  getConsultLists,
  getRemoteLives,
  getPoisonPatientManages,
  getRemotePoisoings,
  getExpertManages,
  getSampleCollections,
  getProductionPlans,
  getTrainingEvaluations,
  getScientificResearchs,
  getAcademicConferences,
  getEmergencyPlans,
  getOrganiseDrills,
} from '@/api';
import * as echarts from 'echarts';
export default {
  data() {
    return {
      dateRange: [],
      
      // 1.远程诊疗数据（新疆）
      telemedicineData: {
        poisonInformation: 0,
        casesOfPoisoning: 0,
        remoteConsultation: 0,
        teleclinics: 0,
        treatmentGuidance: 0,
        poisonedPatients: 0,
        remoteMonitoring: 0,
        expertPool: 0,
      },

      // 2.毒物检测数据（新疆）
      detectionData: {
        collectionData: [],
        transportationData: [],
        sampleSaveWayResult: [],
        sampleFeedbackStatusResult: [],
        sampleDetectWayResult: [],
      },

      // 3.药品储备数据（新疆）
      medicineData: {
        productionPlan: 0,
      },

      // 4.人才分布数据（新疆）
      talentData: {
        trainingEvaluation: 0,
      },

      // 5.科研数据（新疆）
      researchData: {
        policyManagement: 0,
        academicConferences: 0,
      },

      // 6.应急演练数据（新疆）
      drillData: {
        emergencyDrills: 0,
        drillOrganization: 0,
      },
    };
  },
  mounted() {
    // 初始化加载数据
    this.loadAllData();
  },
  methods: {
    async loadAllData(startTime = undefined, endTime = undefined) {
      // 毒物检测图表数据（带时间参数）
      await this.getSampleCollectionsList(startTime, endTime);
      
      // 其他数据加载
      this.getToxicDbsList();
      this.getCaseDbsList();
      this.getApplyListsList();
      this.getConsultListsList();
      this.getRemoteLivesList();
      this.getPoisonPatientManagesList();
      this.getRemotePoisoingsList();
      this.getExpertManagesList();
      this.getProductionPlansList();
      this.getTrainingEvaluationsList();
      this.getScientificResearchsList();
      this.getAcademicConferencesList();
      this.getContingencyManagementList();
      this.getOrganiseDrillsList();
    },

    handleDateChange(range) {
      const [startTime, endTime] = range || [];
      this.loadAllData(startTime, endTime);
    },

    // 修改：毒物检测数据接口，增加时间参数
    async getSampleCollectionsList(startTime = undefined, endTime = undefined) {
      try {
        // 传递时间参数到接口
        const { data } = await getSampleCollections({
          startTime,
          endTime
        });
        
        if (data.code === 200) {
          // 1. 检测目的数据
          this.detectionData.collectionData = data.data.sampleCollectionResult;

          // 2. 运输方式数据（处理null值）
          this.detectionData.transportationData = data.data.sampleTransportResult.map(item => ({
            ...item,
            transportWay: item.transportWay !== null && item.transportWay !== undefined
              ? item.transportWay
              : '未知',
          }));

          // 3. 贮存方式数据
          this.detectionData.sampleSaveWayResult = data.data.sampleSaveWayResult;

          // 4. 反馈状态数据
          this.detectionData.sampleFeedbackStatusResult = data.data.sampleFeedbackStatusResult.map(
            item => ({
              ...item,
              feedbackStatus: item.feedbackStatus === 'feedbacked' ? '已反馈' : '未反馈',
            })
          );

          // 5. 检测方法数据
          this.detectionData.sampleDetectWayResult = data.data.sampleDetectWayResult.map(item => ({
            ...item,
            totalRecordCount: Number(item.totalRecordCount),
          }));

          // 重新初始化所有图表
          this.initChart1();
          this.initChart2();
          this.initChart3();
          this.initChart4();
          this.initChart5();
        } else {
          this.$message.error(data.message);
        }
      } catch (error) {
        this.$message.error('获取数据失败');
      }
    },

    // 以下方法保持不变
    getToxicDbsList() {
      try {
        getToxicDbs().then(({ data }) => {
          if (data.code === 200) {
            this.telemedicineData.poisonInformation = data.data;
          } else {
            this.$message.error(data.message);
          }
        });
      } catch (error) {
        this.$message.error('获取数据失败');
      }
    },

    getCaseDbsList() {
      try {
        getCaseDbs().then(({ data }) => {
          if (data.code === 200) {
            this.telemedicineData.casesOfPoisoning = data.data;
          } else {
            this.$message.error(data.message);
          }
        });
      } catch (error) {
        this.$message.error('获取数据失败');
      }
    },

    getApplyListsList() {
      try {
        getApplyLists().then(({ data }) => {
          if (data.code === 200) {
            this.telemedicineData.remoteConsultation = data.data;
          } else {
            this.$message.error(data.message);
          }
        });
      } catch (error) {
        this.$message.error('获取数据失败');
      }
    },

    getConsultListsList() {
      try {
        getConsultLists().then(({ data }) => {
          if (data.code === 200) {
            this.telemedicineData.teleclinics = data.data;
          } else {
            this.$message.error(data.message);
          }
        });
      } catch (error) {
        this.$message.error('获取数据失败');
      }
    },

    getRemoteLivesList() {
      try {
        getRemoteLives().then(({ data }) => {
          if (data.code === 200) {
            this.telemedicineData.treatmentGuidance = data.data;
          } else {
            this.$message.error(data.message);
          }
        });
      } catch (error) {
        this.$message.error('获取数据失败');
      }
    },

    getPoisonPatientManagesList() {
      try {
        getPoisonPatientManages().then(({ data }) => {
          if (data.code === 200) {
            this.telemedicineData.poisonedPatients = data.data;
          } else {
            this.$message.error(data.message);
          }
        });
      } catch (error) {
        this.$message.error('获取数据失败');
      }
    },

    getRemotePoisoingsList() {
      try {
        getRemotePoisoings().then(({ data }) => {
          if (data.code === 200) {
            this.telemedicineData.remoteMonitoring = data.data;
          } else {
            this.$message.error(data.message);
          }
        });
      } catch (error) {
        this.$message.error('获取数据失败');
      }
    },

    getExpertManagesList() {
      try {
        getExpertManages().then(({ data }) => {
          if (data.code === 200) {
            this.telemedicineData.expertPool = data.data;
          } else {
            this.$message.error(data.message);
          }
        });
      } catch (error) {
        this.$message.error('获取数据失败');
      }
    },

    getProductionPlansList() {
      try {
        getProductionPlans().then(({ data }) => {
          if (data.code === 200) {
            this.medicineData.productionPlan = data.data;
          } else {
            this.$message.error(data.message);
          }
        });
      } catch (error) {
        this.$message.error('获取数据失败');
      }
    },

    getTrainingEvaluationsList() {
      try {
        getTrainingEvaluations().then(({ data }) => {
          if (data.code === 200) {
            this.talentData.trainingEvaluation = data.data;
          } else {
            this.$message.error(data.message);
          }
        });
      } catch (error) {
        this.$message.error('获取数据失败');
      }
    },

    getScientificResearchsList() {
      try {
        getScientificResearchs().then(({ data }) => {
          if (data.code === 200) {
            this.researchData.policyManagement = data.data;
          } else {
            this.$message.error(data.message);
          }
        });
      } catch (error) {
        this.$message.error('获取数据失败');
      }
    },

    getAcademicConferencesList() {
      try {
        getAcademicConferences().then(({ data }) => {
          if (data.code === 200) {
            this.researchData.academicConferences = data.data;
          } else {
            this.$message.error(data.message);
          }
        });
      } catch (error) {
        this.$message.error('获取数据失败');
      }
    },

    getContingencyManagementList() {
      try {
        getEmergencyPlans().then(({ data }) => {
          if (data.code === 200) {
            this.drillData.emergencyDrills = data.data;
          } else {
            this.$message.error(data.message);
          }
        });
      } catch (error) {
        this.$message.error('获取数据失败');
      }
    },

    getOrganiseDrillsList() {
      try {
        getOrganiseDrills().then(({ data }) => {
          if (data.code === 200) {
            this.drillData.drillOrganization = data.data;
          } else {
            this.$message.error(data.message);
          }
        });
      } catch (error) {
        this.$message.error('获取数据失败');
      }
    },

    initChart1() {
      try {
        var chart = echarts.init(this.$refs.chart1);
        const option = {
          title: {
            text: '生物样品采集管理情况',
          },
          tooltip: {
            trigger: 'axis',
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: ['经常性职业卫生监督', '抽样调查', '全面职业卫生普查', '科学研究'],
            axisLabel: {
              rotate: 30,
              interval: 0,
              align: 'right',
              fontSize: 12,
            },
          },
          yAxis: {
            type: 'value',
          },
          series: [
            {
              name: '样本数',
              data: this.detectionData.collectionData
                .sort((item1, item2) => item1.detectPurpose - item2.detectPurpose)
                .map(item => item.totalSampleCount),
              type: 'bar',
            },
          ],
        };
        chart.setOption(option);

        window.addEventListener('resize', () => {
          chart.resize();
        });
      } catch (error) {
        console.error('初始化柱状图失败:', error);
      }
    },

    initChart2() {
      try {
        const chart = echarts.init(this.$refs.chart2);
        const pieData = this.detectionData.sampleSaveWayResult.length
          ? this.detectionData.sampleSaveWayResult.map(item => ({
              name: item.saveWay || '未知方式',
              value: item.totalSampleCount,
            }))
          : [{ name: '暂无数据', value: 1 }];

        const option = {
          title: {
            text: '生物样品贮存管理情况',
            left: 'center',
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)',
          },
          legend: {
            orient: 'vertical',
            left: 10,
            data: pieData.map(item => item.name),
          },
          series: [
            {
              name: '贮存方式',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2,
              },
              label: {
                show: false,
                position: 'center',
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 16,
                  fontWeight: 'bold',
                },
              },
              labelLine: {
                show: true,
              },
              data: pieData,
            },
          ],
        };
        chart.setOption(option);
      } catch (error) {
        console.error('初始化贮存方式图表失败:', error);
      }
    },

    initChart3() {
      const transportWayToName = {
        1: '快递',
        2: '专车',
        3: '自送',
      };
      try {
        var chart = echarts.init(this.$refs.chart3);
        const option = {
          title: {
            text: '生物样品转运管理情况',
          },
          tooltip: {
            trigger: 'item',
          },
          series: [
            {
              type: 'pie',
              radius: '70%',
              data: this.detectionData.transportationData
                .filter(item => item.transportWay !== null)
                .map(item => ({
                  value: item.totalSampleCount,
                  name: transportWayToName[item.transportWay],
                })),
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
            },
          ],
        };
        chart.setOption(option);
      } catch (error) {
        console.error('初始化饼图失败:', error);
      }
    },

    initChart4() {
      try {
        const chartContainer = this.$refs.chart4;
        if (!chartContainer) return;

        setTimeout(() => {
          const chart = echarts.init(chartContainer);

          const yAxisData = this.detectionData.sampleDetectWayResult.map(
            item => item.detectWay || '未知方法'
          );
          const seriesData = this.detectionData.sampleDetectWayResult.map(
            item => item.totalRecordCount
          );
          const dataLength = yAxisData.length;

          const getDynamicColor = index => {
            const hue = (index * (360 / dataLength)) % 360;
            return `hsl(${hue}, 70%, 60%)`;
          };

          const option =
            dataLength > 0
              ? {
                  title: {
                    text: '生物样品检测管理情况',
                    left: 'center',
                    top: 10,
                  },
                  tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'shadow' },
                  },
                  grid: {
                    left: '12%',
                    right: '8%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true,
                  },
                  xAxis: {
                    type: 'value',
                    nameTextStyle: {
                      fontSize: 12,
                      padding: [0, 0, 10, 0],
                    },
                    axisLabel: {
                      fontSize: 11,
                    },
                  },
                  yAxis: {
                    type: 'category',
                    data: yAxisData,
                    axisLabel: {
                      interval: 0,
                      rotate: 15,
                      fontSize: 11,
                    },
                  },
                  series: [
                    {
                      name: '检测方法',
                      type: 'bar',
                      data: seriesData,
                      barWidth: 30,
                      itemStyle: {
                        color: params => getDynamicColor(params.dataIndex),
                      },
                      label: {
                        show: true,
                        position: 'right',
                        formatter: '{c}',
                        fontSize: 11,
                      },
                    },
                  ],
                }
              : {
                  title: {
                    text: '生物样品检测管理情况',
                    left: 'center',
                  },
                  graphic: {
                    type: 'text',
                    left: 'center',
                    top: 'center',
                    text: '暂无检测方法数据',
                    style: { fontSize: 16, fill: '#999' },
                  },
                };

          chart.setOption(option);

          setTimeout(() => {
            chart.resize();
          }, 100);

          window.addEventListener('resize', () => {
            chart.resize();
          });

          this.$on('hook:destroyed', () => {
            window.removeEventListener('resize', () => {
              chart.resize();
            });
          });
        }, 300);
      } catch (error) {
        console.error('初始化检测方法图表失败:', error);
      }
    },

    initChart5() {
      try {
        const chart = echarts.init(this.$refs.chart5);
        const yAxisData = this.detectionData.sampleFeedbackStatusResult.map(
          item => item.feedbackStatus
        );
        const seriesData = this.detectionData.sampleFeedbackStatusResult.map(
          item => item.totalSampleCount
        );

        const option = {
          title: {
            text: '生物样品分析反馈情况',
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          legend: {
            data: ['反馈数量'],
          },
          xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01],
            name: '样本数量',
          },
          yAxis: {
            type: 'category',
            data: yAxisData,
          },
          series: [
            {
              name: '反馈数量',
              type: 'bar',
              data: seriesData,
              barWidth: 40,
              itemStyle: {
                color: function (params) {
                  return params.dataIndex === 0 ? '#1089E7' : '#F8B448';
                },
              },
              label: {
                show: true,
                position: 'right',
                formatter: '{c}',
              },
            },
          ],
        };
        chart.setOption(option);
      } catch (error) {
        console.error('初始化反馈状态图表失败:', error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 10px 20px;
}
.dr-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 15px;
  
  .dr-title-left {
    font-size: 16px;
    font-weight: 500;
    border-left: 6px solid #409eff;
    display: flex;
    height: 24px;
    line-height: 24px;
    
    .dr-title-text {
      margin-left: 10px;
    }
  }
  
  .dr-title-divider {
    flex: 1;
    padding: 0 10px;
  }
  
  .dr-title-btn {
    padding-right: 10px;
  }
}

.chart-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-col {
  flex: 1;
  background: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.chart-container {
  height: 300px;
}

/* 其他原有样式保持不变 */
.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  .grid-content {
    line-height: 36px;
    padding-left: 10px;
    border-right: 1px solid black;
    border-bottom: 1px solid black;
  }
}
.bg-purple-dark {
  background: rgb(245, 247, 250);
}
.bg-purple {
  background: rgb(242, 242, 242);
}
.bg-purple-light {
  background: white;
}
.grid-content {
  min-height: 36px;
}
.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}
</style>
