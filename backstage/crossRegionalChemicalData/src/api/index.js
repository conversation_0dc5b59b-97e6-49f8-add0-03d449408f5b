import request from '@root/publicMethods/request';

// 获取毒物信息数据库统计
export function getToxicDbs() {
  return request({
    url: '/manage/crossRegionalChemicalData/getToxicDb',
    method: 'get',
  });
}

// 获取中毒病例数据库统计情况
export function getCaseDbs() {
  return request({
    url: '/manage/crossRegionalChemicalData/getCaseDb',
    method: 'get',
  });
}

// 获取远程会诊统计数情况
export function getApplyLists() {
  return request({
    url: '/manage/crossRegionalChemicalData/getApplyList',
    method: 'get',
  });
}

// 获取远程门诊统计数情况
export function getConsultLists() {
  return request({
    url: '/manage/crossRegionalChemicalData/getConsultList',
    method: 'get',
  });
}

// 获取现场救治指导统计分析情况
export function getRemoteLives() {
  return request({
    url: '/manage/crossRegionalChemicalData/getRemoteLive',
    method: 'get',
  });
}

// 获取化学中毒患者管理情况
export function getPoisonPatientManages() {
  return request({
    url: '/manage/crossRegionalChemicalData/getPoisonPatientManage',
    method: 'get',
  });
}

// 获取中毒病例远程监测情况
export function getRemotePoisoings() {
  return request({
    url: '/manage/crossRegionalChemicalData/getRemotePoisoing',
    method: 'get',
  });
}

// 获取化学中毒救治专家库管理情况
export function getExpertManages() {
  return request({
    url: '/manage/crossRegionalChemicalData/getExpertManage',
    method: 'get',
  });
}

// ---------------------------
// 获取化学毒物检测统计情况
export function getSampleCollections(params) {
  return request({
    url: '/manage/crossRegionalChemicalData/getSampleCollection',
    method: 'get',
    params,
  });
}

// 获取生产技术能力和交通转运能力的储备情况
export function getProductionPlans() {
  return request({
    url: '/manage/crossRegionalChemicalData/getProductionPlan',
    method: 'get',
  });
}

// 获取化学中毒救治知识培训评估情况
export function getTrainingEvaluations() {
  return request({
    url: '/manage/crossRegionalChemicalData/getTrainingEvaluation',
    method: 'get',
  });
}

// 获取化学中毒救治相关标准及政策法规、相关专著拟定与管理情况
export function getScientificResearchs() {
  return request({
    url: '/manage/crossRegionalChemicalData/getScientificResearch',
    method: 'get',
  });
}

// 化学灾害医学救援和化学中毒救治学术会议举办情况
export function getAcademicConferences() {
  return request({
    url: '/manage/crossRegionalChemicalData/getAcademicConferences',
    method: 'get',
  });
}

// 获取化学灾害医学应急演练预案情况
export function getEmergencyPlans() {
  return request({
    url: '/manage/crossRegionalChemicalData/getContingencyManagement',
    method: 'get',
  });
}

// 获取化学灾害医学应急救援队伍演练组织情况
export function getOrganiseDrills() {
  return request({
    url: '/manage/crossRegionalChemicalData/getOrganiseDrills',
    method: 'get',
  });
}

//  获取毒物检测数据库样本转运情况
// export function getSampleTransports() {
//   return request({
//     url: '/manage/crossRegionalChemicalData/getSampleTransport',
//     method: 'get',
//   });
// }

