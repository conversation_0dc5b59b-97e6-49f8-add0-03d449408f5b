<template>
  <el-row>
    <!-- <el-alert center type="success" :closable="false">
      <template slot="title">
        共有 <B>{{ totleCount || 0 }}</B> 项签约合同（灰底的为未注册机构）
      </template>
    </el-alert> -->

    <el-table
      default-expand-all
      align="center"
      tooltip-effect="dark"
      style="width: 100%"
      row-key="_id"
      ref="multipleTable"
      header-cell-style="background-color: #FAFAFA;height:60px"
      v-loading="loading"
      :data="contractList"
      :row-class-name="tableRowClassName"
      :max-height="tableHeight"
    >
      <el-table-column
        prop="projectName"
        label="项目名称"
        fixed
        min-width="120px"
      >
        <template slot-scope="scope">
          <div>
            {{ scope.row.projectName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="EnterpriseID.cname"
        label="用人单位名称"
        fixed
        min-width="110px"
      >
      </el-table-column>
      <el-table-column
        prop="serviceOrgId.name"
        label="机构名称"
        fixed
        min-width="110px"
      >
        <template slot-scope="scope">
          <div style="color: #409eff"">
            {{ scope.row.serviceOrgId && scope.row.serviceOrgId.name }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="contact"
        align="center"
        label="单位联系人"
        min-width="100px"
      >
        <template slot-scope="scope">
          <div>
            {{ scope.row.contact }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="contactPhone"
        align="center"
        label="单位联系电话"
        min-width="120px"
      >
        <template slot-scope="scope">
          <div>
            {{ maskPhoneNumber(scope.row.contactPhone) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="serviceOrgContact"
        align="center"
        label="机构联系人"
        min-width="100px"
      >
        <template slot-scope="scope">
          <div>
            {{ scope.row.serviceOrgContact }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="serviceOrgPhone"
        align="center"
        label="机构联系电话"
        min-width="120px"
      >
        <template slot-scope="scope">
          <div>
            {{ maskPhoneNumber(scope.row.serviceOrgPhone) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="applyTime"
        align="center"
        label="申请日期"
        min-width="100px"
      >
        <template slot-scope="scope">
          <div>
            {{ doMomentmonthD(scope.row.applyTime) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="appointmentDate"
        align="center"
        label="预约日期"
        min-width="100px"
      >
        <template slot-scope="scope">
          <div>
            {{ doMomentmonthD(scope.row.appointmentDate) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        align="center"
        label="预约状态"
        min-width="100px"
      >
        <template slot-scope="scope">
          <div>
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="reason"
        align="center"
        label="预约备注"
        min-width="150px"
      >
        <template slot-scope="scope">
          <div>
            {{ scope.row.reason || '-' }}
          </div>
        </template>
      </el-table-column>
    </el-table>
  </el-row>
</template>

<script>
import moment from "moment";
// import { goCountersign,checkPhoneLog } from '@/api';

export default {
  props: {
    contractList: Array,
    value: Object,
    searchParams: Object,
    totleCount: Number,
    loading: Boolean,
  },
  data() {
    return {
      tableRowDetail: { ...this.value },
      // loading: false,
    };
  },
  watch: {
    // 监听 tableRowDetail 的变化并通过 $emit 更新父组件数据
    tableRowDetail: {
      handler(newValue) {
        this.$emit("input", newValue);
      },
      deep: true, // 深度监听对象的变化
    },
  },
  methods: {
    // 点击电话号码显示真实号码
    showPhone(phone) {
      phone.show = !phone.show;
    },
    // 判断机构是否是自主增加服务区域来判断这一行的显示
    tableRowClassName({ row, rowIndex }) {
      if (row.companyIn) {
        return "cancel-row";
      }
      return "";
    },

    showOrgDetail(row) {
      this.tableRowDetail.show = true;
      this.tableRowDetail.type = "serviceOrg";
      this.tableRowDetail.data = row.serviceOrgId;

      console.log("siisi", this.tableRowDetail.data);
      if (row.serviceOrgId && row.serviceOrgId.qualifies && row.serviceOrgId.qualifies[0] && new Date(row.serviceOrgId.qualifies[0].validTime) < new Date()) {
        this.$alert("职业卫生技术服务机构资质证书已过期", "提示", {
          confirmButtonText: "确定",
        });
      }
    },
    showEntrustDetail(row) {
      this.tableRowDetail.show = true;
      this.tableRowDetail.type = "entrust";
      this.tableRowDetail.data = row;
    },
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'accepted': 'success',
        'rejected': 'danger',
        'canceled': 'info'
      };
      return statusMap[status] || 'info';
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待审核',
        'accepted': '已接受',
        'rejected': '已拒绝',
        'canceled': '已取消'
      };
      return statusMap[status] || '未知';
    },
    // 手机号脱敏
    maskPhoneNumber(phoneNum) {
      if (!phoneNum) return '';
      const reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
      if (phoneNum.toString().match(reg)) {
        return phoneNum.toString().replace(reg, "$1****$2");
      }
      return phoneNum;
    },
  },
  computed: {
    //将时间 转换成 简化的
    doMomentyear(nowTime) {
      return function (nowTime) {
        return moment(nowTime).format("YYYY");
      };
    },
    //将时间 转换成 简化的
    doMomentmonthD(nowTime) {
      return function (nowTime) {
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format("YYYY-MM-DD");
      };
    },
    tableHeight() {
      const innerHeight =
        window.innerHeight ||
        document.documentElement.clientHeight ||
        document.body.clientHeight;
      return innerHeight - 220;
    },
  },
};
</script>
<style scoped>
.el-icon-circle-close {
  color: #f5f5f5;
}
::v-deep .el-table .cancel-row {
  background: rgb(204, 204, 204);
}
.orgName:hover {
  cursor: pointer;
  color: #409eff;
}
.el-message-box {
  z-index: 9999;
}
</style>
