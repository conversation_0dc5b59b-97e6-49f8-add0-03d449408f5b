<template>
  <div class="dr-toolbar">
    <div class="dr-filter-row">
      <div class="dr-searchInput">
        <span class="dr-searchInput_title">用人单位地区：</span>
        <div class="dr-searchInput_main">
          <el-cascader
            :props="districtListProps"
            @change="regaddChangeOptionFunc"
            v-model="searchParams.regAddr"
            clearable
            ref="regAddCas"
            size="small"
            placeholder="选择用人单位地区"
          >
            <template slot-scope="{ data }">
              <span>{{ data.label }}</span>
            </template>
          </el-cascader>
        </div>
      </div>
      <div class="dr-searchInput">
        <span class="dr-searchInput_title">项目名称：</span>
        <div class="dr-searchInput_main">
          <el-input
            size="small"
            v-model="projectName"
            suffix-icon="el-icon-search"
            placeholder="请输入项目名称"
          ></el-input>
        </div>
      </div>
      <div class="dr-searchInput">
        <span class="dr-searchInput_title">预约日期：</span>
        <div class="dr-searchInput_main">
          <el-date-picker
            v-model="appointmentDate"
            type="date"
            size="small"
            placeholder="选择预约日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @change="appointmentDateChange"
          ></el-date-picker>
        </div>
      </div>
      <div class="dr-searchBtn">
        <el-button type="primary" @click="handleSearch" size="mini" icon="el-icon-search">查询</el-button>
        <el-button size="mini" @click="handleReset" icon="el-icon-refresh">重置</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import { getDistrictList } from "@/api";
export default {
  props: {
    searchParams: Object,
  },
  data() {
    return {
      year: "", // 选择年份
      visible: true,
      projectName: "", // 项目名称
      appointmentDate: "", // 预约日期
      district: [],
      districtListProps: {
        checkStrictly: true,
        lazy: true,
        value: 'area_code',
        lazyLoad(node, resolve) {
          const { level } = node;
          const params = {
            level,
          };
          if (level) {
            params.parent_code = node.value.area_code;
          }
          getDistrictList(params).then(({ data }) => {
            try {
              const nodes = Array.from(data.docs).map((item) => ({
                value: item,
                label: item.name,
                leaf: item.level >= 3,
                area_code: item.area_code,
                disabled: item.name === "市辖区" ? true : false,
              }));
              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          });
        },
      },
    };
  },
  methods: {
    // 选择注册地
    regaddChangeOptionFunc(v) {
      console.log(v, 222222);
      this.searchParams.regAddr = v || [];
      this.searchParams.curPage = 1;
      this.$refs.regAddCas.dropDownVisible = false;
    },
    // 预约日期变化
    appointmentDateChange(val) {
      this.searchParams.appointmentDate = val;
      this.searchParams.curPage = 1;
    },
    // 搜索按钮点击
    handleSearch() {
      this.searchParams.projectName = this.projectName;
      this.$emit('search');
    },
    // 重置按钮点击
    handleReset() {
      this.projectName = "";
      this.appointmentDate = "";
      this.$emit('reset');
    },
  },
};
</script>
<style lang="scss" scoped>
.dr-toolbar{
  width: 100%;
  height: 100%;
  padding: 15px;
  background: #fafafa;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}
.dr-filter-row {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
  width: 100%;
  box-sizing: border-box;
}
.dr-searchInput {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 200px;
  max-width: 300px;
}
.dr-searchInput_title {
  white-space: nowrap;
  margin-right: 8px;
  color: #606266;
  font-weight: 500;
  font-size: 14px;
  min-width: 70px;
  flex-shrink: 0;
}
.dr-searchInput_main {
  flex: 1;
  min-width: 120px;
}
.dr-searchBtn {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
  margin-left: auto;
}
</style>
