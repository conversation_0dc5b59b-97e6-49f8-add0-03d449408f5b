import request from '@root/publicMethods/request';

// 获取签约列表
export function getAllContract(data) {
  return request({
    url: '/manage/adminServiceOrg/getAllContract',
    data,
    method: 'post',
  });
}

// 获取预约列表
export function getServiceAppointmentList(params) {
  return request({
    url: '/manage/serviceAppointment/getList',
    params,
    method: 'get',
  });
}

// 获取地址信息
export function getDistrictList(params) {
  return request({
    url: '/api/adminorgGov/address/list',
    params,
    method: 'get',
  });
}

// 获取资质信息
export function getQualifies(data) {
  return request({
    url: '/manage/adminServiceOrg/getQualifies',
    data,
    method: 'post',
  });
}
