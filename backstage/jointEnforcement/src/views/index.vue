<template>
  <div class="serveEnterprise">
   


    <!-- 数据表格 -->
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="信息报送和结果查询" name="first">
         <!-- 搜索表单 -->
        <TitleTag titleName="查询条件"></TitleTag>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item label="案件信息">
            <el-input
              v-model="searchForm.caseInformation"
              placeholder="请输入案件信息"
            ></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.state" placeholder="请选择数据特性">
              <el-option label="待反馈" :value="0"></el-option>
              <el-option label="已反馈" :value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="接受/反馈部门" >
            <el-select
              v-model="searchForm.AcceptingDepartment"
              placeholder="请选择接受/反馈部门"
            >
              <el-option
                v-for="item in Departmentlist"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-form>
        <div style="margin-bottom: 15px">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="addDataRules"
            >新增报送</el-button
          >
        </div>
        <el-table
          :data="tableData"
          tooltip-effect="light"
          style="width: 100%"
          stripe
          border
          header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
          align="center"
        >
          <el-table-column
            prop="caseInformation"
            label="案件信息"
            align="center"
            width="150"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="enforcementOrg"
            label="执法机构"
            align="center"
            width="120"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="enforcementTime"
            label="执法时间"
            align="center"
            width="120"
            show-overflow-tooltip
          >
          <template slot-scope="scope">
              {{ handleTime(scope.row.enforcementTime) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="enforcementResult"
            label="执法结果"
            align="center"
            width="120"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="punish"
            label="处罚措施"
            align="center"
            width="120"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="material"
            label="相关材料"
            align="center"
            width="120"
            show-overflow-tooltip
          >
          <template slot-scope="scope">
              <el-link type="primary" v-for="(item,index) in scope.row.material" :key="item"  @click="handleopen(item)">查看</el-link>
            </template>
        </el-table-column>
          <el-table-column
            prop="submitDepartment.cname"
            label="报送部门"
            align="center"
            width="200"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <el-tag type="primary">{{
                scope.row.submitDepartment.cname
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="submitBy"
            label="报送人"
            align="center"
            width="120"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="AcceptingDepartment.cname"
            label="接受/反馈部门"
            align="center"
            width="200"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <el-tag type="primary">{{
                scope.row.AcceptingDepartment.cname
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="feedbackBy"
            label="反馈人"
            align="center"
            width="120"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            label="反馈时间"
            align="center"
            prop="feedbackTime"
            width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ handleTime(scope.row.feedbackTime) }}
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" width="120" prop="state">
            <template slot-scope="scope">
              <el-tag type="info" v-show="scope.row.state === 0">待反馈</el-tag>
              <el-tag type="success" v-show="scope.row.state === 1"
                >已反馈</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            align="center"
            width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ handleTime(scope.row.createdAt) }}
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            width="160"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="modify(scope.row)"
                style="color: #909399"
                v-if="scope.row.state === 0"
                >编辑</el-button
              >

              <el-button
                type="text"
                @click="remove(scope.row._id)"
                style="color: #f56c6c"
                >删除</el-button
              >
              <el-button
                type="text"
                @click="showdetail(scope.row)"
                style="color: #e6a23c"
                >查看详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            @size-change="getList"
            @current-change="getList"
            :current-page.sync="searchForm.pageNum"
            :page-size.sync="searchForm.pageSize"
            :page-sizes="[10, 20, 30, 50, 100]"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="结果反馈" name="second">
        <TitleTag titleName="查询条件"></TitleTag>
        <el-form :inline="true" :model="feedbacksearchForm" class="demo-form-inline">
          <el-form-item label="案件信息">
            <el-input
              v-model="feedbacksearchForm.caseInformation"
              placeholder="请输入案件信息"
            ></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="feedbacksearchForm.state" placeholder="请选择数据特性">
              <el-option label="待反馈" :value="0"></el-option>
              <el-option label="已反馈" :value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="报送部门" >
            <el-select
              v-model="feedbacksearchForm.submitDepartment"
              placeholder="请选择报送部门"
            >
              <el-option
                v-for="item in Departmentlist"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table
          :data="tableDatafeedback"
          tooltip-effect="light"
          style="width: 100%"
          stripe
          border
          header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
          align="center"
        >
          <el-table-column
            prop="caseInformation"
            label="案件信息"
            align="center"
            width="150"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="enforcementOrg"
            label="执法机构"
            align="center"
            width="120"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="enforcementTime"
            label="执法时间"
            align="center"
            width="120"
            show-overflow-tooltip
          >
          <template slot-scope="scope">
              <span>{{ handleTime(scope.row.enforcementTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="enforcementResult"
            label="执法结果"
            align="center"
            width="120"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="punish"
            label="处罚措施"
            align="center"
            width="120"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="material"
            label="相关材料"
            align="center"
            width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <el-link type="primary" v-for="(item,index) in scope.row.material" :key="item"  @click="handleopen(item)">查看</el-link>
            </template>
        </el-table-column>
          <el-table-column
            prop="submitDepartment.cname"
            label="报送部门"
            align="center"
            width="200"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <el-tag type="primary">{{
                scope.row.submitDepartment.cname
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="submitBy"
            label="报送人"
            align="center"
            width="120"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="AcceptingDepartment.cname"
            label="接受/反馈部门"
            align="center"
            width="200"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <el-tag type="primary">{{
                scope.row.AcceptingDepartment.cname
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="feedbackBy"
            label="反馈人"
            align="center"
            width="120"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            label="反馈时间"
            align="center"
            prop="feedbackTime"
            width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ handleTime(scope.row.feedbackTime) }}
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" width="120" prop="state">
            <template slot-scope="scope">
              <el-tag type="info" v-show="scope.row.state === 0">待反馈</el-tag>
              <el-tag type="success" v-show="scope.row.state === 1"
                >已反馈</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            align="center"
            prop="createdAt"
            width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ handleTime(scope.row.createdAt) }}
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            width="160"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="showfeedback(scope.row)"
                style="color: #e6a23c"
                v-if="scope.row.state===0"
                >反馈</el-button
              >
              <el-button
                type="text"
               
                style="color: #67C23A"
                v-else
                >已反馈</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            @size-change="getfeedbackList"
            @current-change="getfeedbackList"
            :current-page.sync="feedbacksearchForm.pageNum"
            :page-size.sync="feedbacksearchForm.pageSize"
            :page-sizes="[10, 20, 30, 50, 100]"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="feedbacktotal"
          >
          </el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>

    
    <ModifyFunctionInfo
      :show.sync="isShowModify"
      :itemData="itemData"
      @confirm="handleTableData"
      :Departmentlist="Departmentlist"
    />
    <AddDataRule
      :show.sync="isShowAddRule"
      @confirm="confirmAdd"
      :Departmentlist="Departmentlist"
    ></AddDataRule>

    <Detail :show.sync="showDetail" :detail-data="currentDetail"></Detail>

    <Feedback :show.sync="isShowFeedback"
    @confirm="confirmFeedback" :feedbackData="feedbackData"></Feedback>
  </div>
</template>

<script>
import TitleTag from "@/components/TitleTag.vue";
import ModifyFunctionInfo from "@/components/ModifyFunctionInfo.vue";
import AddDataRule from "@/components/AddDataRule.vue";
import Detail from "@/components/Detail.vue";
import Feedback from "@/components/Feedback.vue"
import moment from "moment";
import {
  api_getDepartmentlist,
  getUserSession,
  add,
  update,
  deleteitem,
  api_getlist,
  feedback,
  feedbackwithfile
} from "@/api";
import { mapActions } from "vuex"; // 添加这行

export default {
  components: {
    TitleTag,
    ModifyFunctionInfo,
    AddDataRule,
    Detail,
    Feedback
  },
  data() {
    return {
      // 我的数据
      activeName: "first",
      userinfo: {},
      Departmentlist: [],
      tableData: [], // 表格数据
      tableDatafeedback: [], // 表格数据
      total: null,
      searchForm: {
        caseInformation: "",
        state: "",
        submitDepartment: "",
        AcceptingDepartment: "",
        pageNum: 1,
        pageSize: 10,
      },
      feedbacktotal: null,
      feedbacksearchForm: {
        caseInformation: "",
        state: "",
        submitDepartment: "",
        AcceptingDepartment: "",
        pageNum: 1,
        pageSize: 10,
      },
      itemData: {
        AcceptingDepartment: {
          _id: "",
        },
      },
      showDetail: false,
      isShowModify: false,
      isShowAddRule: false,
      isShowFeedback: false,
      currentDetail: {},
      feedbackData: {
        AcceptingDepartment: {
          _id: "",
        },
        enforcementOrg:'', //执法机构
        enforcementTime:'', //执法时间
        enforcementResult:'', //执法结果
        punish:'', //处罚
        material:[], //材料
      },
    };
  },
  created() {
    this.getDepartmentlist();
    this.getUserInfo();
  },
  mounted() {
    
  },
  methods: {
    ...mapActions("index", ["setUserInfo"]), // 映射 Vuex action
    async getUserInfo() {
      const res = await getUserSession();
      if (res.status === 200) {
        this.setUserInfo(res.data.userInfo); // 假设返回数据中包含 userinfo
        this.getList();
      }
    },
    async getDepartmentlist() {
      const res = await api_getDepartmentlist();

      if (res.status === 200) {
        this.Departmentlist = res.data.map((item) => {
          return {
            label: item.cname,
            value: item._id,
          };
        });
      }
    },
    
    async getList() {
      // this.searchForm.submitDepartment=this.$store.state.index.userinfo._id
      const res = await api_getlist(this.searchForm);
      console.log(res, "res");
      if (res.status === 200) {
        this.tableData = res.data.resList;
        this.total = res.data.total;
      }
    },
    async getfeedbackList() {
      // this.feedbacksearchForm.AcceptingDepartment=this.$store.state.index.userinfo._id
      const res = await api_getlist(this.feedbacksearchForm);
      if (res.status === 200) {
        this.tableDatafeedback = res.data.resList;
        this.feedbacktotal = res.data.total;
      }
    },
    handleTime(val) {
      if(val !=undefined){
        return moment(val).format("YYYY-MM-DD");
      }
    },

    addDataRules() {
      this.isShowAddRule = true;
    },
    showfeedback(row) {
      console.log(row);
      
      this.feedbackData.caseInformation=row.caseInformation
      this.feedbackData.AcceptingDepartment=row.AcceptingDepartment
      this.feedbackData._id=row._id
      this.isShowFeedback = true;
    },
    async confirmAdd(val) {
      console.log(val, "val");
      console.log(this.$store.state, "val");
      let data = { ...val };
      data.submitBy = this.$store.state.index.userinfo.name;
      data.submitDepartment = this.$store.state.index.userinfo._id;
      data.state = 0;
      const res = await add(data);
      console.log(res, "res");

      if (res.status === 200) {
        this.$message.success("添加成功");
        this.getList();
      }
    },
    async modify(value) {
      console.log(value);
      this.itemData = JSON.parse(JSON.stringify(value));
      this.isShowModify = true;
    },
    remove(_id) {
      this.$confirm("确认删除吗, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const res = await deleteitem({
            _id,
          });
          console.log(res, "res");
          if (res.status === 200) {
            this.$message.success("删除成功");
            this.getList();
          }
        })
        .catch(() => {});
    },
    async handleTableData(val) {
      let data = { ...val };
      data.AcceptingDepartment = data.AcceptingDepartment._id;
      console.log(data, "data");

      const res = await update({
        _id: data._id,
        caseInformation: data.caseInformation,
        AcceptingDepartment: data.AcceptingDepartment,
      });
      console.log(res, "res");

      if (res.status === 200) {
        this.$message.success("更新成功");
        this.getList();
      }
    },
    showdetail(row) {
      this.currentDetail = JSON.parse(JSON.stringify(row));
      this.showDetail = true;
    },

    // 搜索按钮点击事件
    onSearch() {
      // if(this.activeName=='first' &&  this.searchForm.AcceptingDepartment ===this.$store.state.index.userinfo._id){
      //   this.$message.error("接受部门不能为自己");
      //   return; // 阻止查询
      // }else if(this.activeName=='second' &&  this.searchForm.feedbacksearchForm ===this.$store.state.index.userinfo._id){
      //   this.$message.error("报送部门不能为自己");
      //   return; // 阻止查询
      // }
      if(this.activeName=='first'){
        this.getList()
      }else{
        this.getfeedbackList()
      }
    },
    reset() {
     if(this.activeName=='first'){
      this.searchForm = {
        caseInformation: "",
        state: "",
        submitDepartment: "",
        AcceptingDepartment: "",
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
     }else{
      this.feedbacksearchForm = {
        caseInformation: "",
        state: "",
        submitDepartment: "",
        AcceptingDepartment: "",
        pageNum: 1,
        pageSize: 10,
      };
      this.getfeedbackList();
     }
    },
    handleClick(item){
      console.log(item,'item');
      if(item.index==0){
        this.getList()
      }else{
        this.getfeedbackList()
      }
    },
    async confirmFeedback(val){
      console.log(val,'val');
      try {
       
        if(val.material.length>0){
          const formData = new FormData();
          // 添加必填字段
          formData.append('feedbackData', JSON.stringify(val)); 
          // 添加文件（如果有）
          val.material.forEach(file => {
            formData.append('material', file.raw);
          });

          const res = await feedbackwithfile(formData);
          if (res.status === 200) {
            this.$message.success('反馈提交成功');
            this.feedbackData={
              AcceptingDepartment: {
                _id: "",
              },
              enforcementOrg:'', //执法机构
              enforcementTime:'', //执法时间
              enforcementResult:'', //执法结果
              punish:'', //处罚
              material:[], //材料
            }
            this.getfeedbackList()
          }
        }else{
          // 调用API
          const res = await feedback(val);
          if (res.status === 200) {
            this.$message.success('反馈提交成功');
            this.feedbackData={
              AcceptingDepartment: {
                _id: "",
              },
              enforcementOrg:'', //执法机构
              enforcementTime:'', //执法时间
              enforcementResult:'', //执法结果
              punish:'', //处罚
              material:[], //材料
            }
            this.getfeedbackList()
          }
        }
      } catch (error) {
        this.$message.error('提交失败: ' + error.message);
      }
    },
    handleopen(url){
      window.open(url)
    }
  },
};
</script>

<style scoped>
.demo-form-inline {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

.serveEnterprise {
  /* margin-left: 210px; */
  padding: 20px;
}
</style>