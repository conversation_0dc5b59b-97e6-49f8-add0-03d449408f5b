<template>
  <div class="content">
    <el-form inline :model="query">
      <el-form-item label="诊断机构">
          <el-input v-model="query.institutionName" placeholder="请输入机构名称" />
      </el-form-item>
      <el-form-item label="劳动者名称">
          <el-input v-model="query.workerName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="开始时间">
        <el-date-picker
          v-model="query.startDate"
          type="date"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          placeholder="请选择开始日期"
        />
      </el-form-item>
      <el-form-item label="结束时间">
        <el-date-picker
          v-model="query.endDate"
          type="date"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          placeholder="请选择开始日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" @click="fetchData">查询</el-button>
        <el-button size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="tableData"
      style="width: 100%"
      header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    >
      <el-table-column type="index" label="序号"></el-table-column>
      <el-table-column prop="workerName" label="劳动者名称" show-overflow-tooltip></el-table-column>
      <el-table-column prop="empName" label="用人单位名称" show-overflow-tooltip min-width="150"></el-table-column>
      <el-table-column prop="empCreditCode" label="用人单位统一社会信用代码" show-overflow-tooltip min-width="150"></el-table-column>
      <el-table-column prop="workEmpName" label="用工单位名称" min-width="150"></el-table-column>
      <el-table-column prop="workEmpCreditCode" label="用工单位统一社会信用代码" show-overflow-tooltip min-width="150"></el-table-column>
      <el-table-column prop="institutionName" label="诊断机构" show-overflow-tooltip min-width="150"></el-table-column>
      <el-table-column prop="institutionAreaName" label="机构所在地区" show-overflow-tooltip min-width="150"></el-table-column>
      <el-table-column prop="applicationDate" label="申请时间" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ formatDate(scope.row.applicationDate) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="success" @click="downloadArchive(scope.row)">下载</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="margin:10px 0;text-align:center">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="query.pageNum"
        :page-size="query.pageSize"
        :page-sizes="[10, 20, 30, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="query.total">
    </el-pagination>
    </div>
  </div>
</template>

<script>
import {getemployerList,getUserSession} from '@/api/index.js'
import moment from 'moment'
export default {
  data() {
    return {
      query: {
        institutionName: '',
        administerArea:'',
        workerName: '',
        startDate:'',
        endDate:'',
        diseaseCodeList:'',
        pageNum:1,
        pageSize:10,
        total:0
      },
      tableData: []
    };
  },
  async created() {
    try {
      const res = await getUserSession()
      if(res.data.userInfo) {
        this.query.administerArea = res.data.userInfo.area_code      
        this.getData()
      } else {
        console.error('获取区域码失败')
        this.$message.error('获取用户信息失败')
      }
    } catch (error) {
      console.error('获取用户会话失败:', error)
      this.$message.error('获取用户信息失败')
    }
  },
  methods: {
    async getData() {
      const res = await getemployerList(this.query)
      this.tableData = res.data.data.list || []
      this.query.pageNum = res.data.data.pageNum;      
      this.query.pageSize = res.data.data.pageSize;      
      this.query.total = res.data.data.total;      
    },
    formatDate(date) {
      return moment(date).format("YYYY-MM-DD");
    },
    fetchData(){
      this.getData()
    },
    resetQuery() {
      this.query.workerName = ''
      this.query.startDate = ''
      this.query.endDate = ''
      this.query.institutionName = ''
      this.getData()
    },
    downloadArchive(row) {
      // alert(`正在下载档案`);
        // 根据筛选条件生成报表数据
        const reportData = [
          {
            劳动者名称: row.workerName,
            用人单位名称: row.empName,
            用人单位统一社会信用代码: row.empCreditCode,
            用工单位名称: row.workEmpName,
            用工单位统一社会信用代码: row.workEmpCreditCode,
            申请时间: this.formatDate(row.applicationDate)
          }
        ]
        // 模拟报表下载
        const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: "application/json" });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "职业病诊断档案报表.xlsx";
        a.click();
        window.URL.revokeObjectURL(url);
    },
    handleSizeChange(val){
      this.query.pageSize = val;
      this.fetchData();
    },
    handleCurrentChange(val){
      this.query.pageNum = val;
      this.fetchData();
    }
  } 
}
</script>

<style lang="scss" scoped>
.content {
  padding: 20px;
}

</style>
