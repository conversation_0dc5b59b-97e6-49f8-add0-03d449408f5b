<template>
  <a-modal
    title="系统运行时间"
    :visible="modalVisible"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    okText="提 交"
    cancelText="取 消"
    width="500px"
    @cancel="handleCancel"
  >
  <div class="formContain" v-if="modalVisible">
    <el-form ref="ruleForm" :model="formData" label-width="80px" :rules="rules">
      <el-form-item label="限制日期" prop="datePeriod">
        <el-date-picker
          v-model="formData.datePeriod"
          type="daterange"
          range-separator="至"
          @change="handleTimeChange"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="运行时间" prop="runTime">
        <el-time-picker
          is-range
          format="HH:mm"
          v-model="formData.runTime"
          @change="handleTimeChange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          placeholder="选择时间范围">
        </el-time-picker>
      </el-form-item>
      <el-form-item label="平台" prop="platform">
        <el-checkbox-group v-model="formData.platform">
          <el-checkbox v-for="item in platforms" :label="item.value">{{item.label}}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="通知公告" prop="notice">
        <el-input type="textarea" v-model="formData.notice" autosize />
        <div style="position: absolute; right: 0"><el-link :underline="false" type="info" @click="generaNotice">生成模板</el-link></div>
      </el-form-item>

    </el-form>
  </div>
  </a-modal>
</template>

<script>
import { saveOperationPlan, findOperationPlanConfig } from '../../api/systemConfig.js'
import moment from 'moment';

export default {
  props: {
    parameterId: String,
    modalVisible: Boolean,
  },
  data() {
    let checkTimeArr = (rule, value, callback) => {
      console.log(31213, value)
      if (!value || value.some(e => !e)) {
        callback(new Error("请选择日期段"))
      }
      callback()
    }
    return {
      confirmLoading: false,
      platforms: [
        { label: '机构端', value: 'jc' },
        { label: '体检端', value: 'tj' },
        { label: '监管端', value: 'jg' },
        { label: '企业端', value: 'qy' },
      ],
      formData: {
        datePeriod: ['', ''], // 日期段
        runTime: [new Date(), new Date()], // 启用时间
        platform: [], // 平台
        notice: '', // 通知
      },
      rules: {
        datePeriod: [
          { required: true, message: '请选择日期段', trigger: 'blur', validator: checkTimeArr },
        ],
        runTime: [
          { required: true, message: '请选择时间', trigger: 'blur', validator: checkTimeArr },
        ],
        platform: [
          { required: true, message: '请选择平台', trigger: 'blur' },
        ],
        notice: [
          { required: true, message: '请输入通知', trigger: 'change' },
        ],
      },
    }
  },
  watch: {
    async modalVisible(newVal, val) {
      if (newVal) {
        const res = await findOperationPlanConfig({ parameterId: this.parameterId })

        if (res && res.data) {
          const formData = JSON.parse(JSON.stringify(res.data.formData))
          if (formData) {
            formData.runTime = formData.runTime.map(e => {
              const date = new Date()
              const [ h, m ] = e.split(':')
              date.setHours(h)
              date.setMinutes(m)
              return date
            })
            this.formData = formData
          } else {
            this.formData = {
              datePeriod: ['', ''], // 日期段
              runTime: [new Date(), new Date()], // 启用时间
              platform: [], // 平台
              notice: '', // 通知
            }
          }
        }
      }
    }
  },
  methods: {
    handleTimeChange (e) {
      this.formData.notice = ''
    },
    generaNotice() {
      const datePeriod = this.formData.datePeriod.map(item => moment(item).format('YYYY-MM-DD')).join('至')
      const runTime = this.formData.runTime.map(item => moment(item).format('HH:mm')).join('-')

      this.formData.notice = `接上级通知，${datePeriod}期间，杭州企卫通服务平台仅在每天${runTime}之间启用，其它时间系统将进行维护。`
    },
    handleCancel() {
      this.$emit('cancel')
    },
    async handleOk() {
      this.$refs.ruleForm.validate(async valid => {
        if (valid) {
          const newFormData = {
            datePeriod: this.formData.datePeriod, // 日期段
            runTime: this.formData.runTime.map(e => moment(e).format('HH:mm')), // 启用时间
            platform: this.formData.platform, // 平台
            notice: this.formData.notice, // 通知
            _id: this.formData._id, // 通知
          } 

          // this.confirmLoading = true
          const res = await saveOperationPlan({
            formData: newFormData,
          })

          if (res) {
            this.$message({
              message: res.data.message,
              type: res.data.status,
            });
          }
          this.confirmLoading = false

          this.$emit('ok')
        } else {
          console.log('error submit!!');
          return false;
        }
      });
      
    },
  }
}
</script>

<style scoped lang="scss">
.formContain {
  height: 100%;
}

.form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .form_item_head {
    color: #606266;
    font-weight: bold;
    padding: 0 0 10px 0;
  }

  .form_item {
    padding: 10px;
  }
}
</style>