<template>
  <div class="content">
    <el-form inline :model="query">
      <el-form-item label="年份">
        <el-date-picker
          v-model="formData.year"
          type="year"
          value-format="yyyy"
          placeholder="请选择年份"
        ></el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="地区">
        <ElCascader
          style="width: 100%"
          v-model="formData.areaCode"
          placeholder="请选择地区"
          clearable
          :options="areaList"
          :props="{
            label: 'dictLabel',
            value: 'dictCode',
            children: 'children',
            checkStrictly: false,
            emitPath: false
          }"
          filterable
        />
      </el-form-item> -->
      <el-form-item label="病种">
        <ElCascader
          style="width: 100%"
          v-model="formData.disease"
          placeholder="请选择职业病类别"
          clearable
          filterable
          :options="categoryList"
          :props="{
            label: 'dictLabel',
            value: 'dictCode',
            children: 'children',
            checkStrictly: false,
            emitPath: false
          }"
        />
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" @click="fetchData">查询</el-button>
        <el-button size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      style="width: 100%"
      :data="tableData"
      :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333333' }"
    >
      <el-table-column type="index" label="序号" min-width="50" />
      <el-table-column prop="diseaseCategory" label="职业病病种" min-width="120" />
      <el-table-column
        v-for="year in uniqueYears"
        :key="year"
        :prop="'counts.' + year"
        :label="year"
        align="center"
      />
      <el-table-column prop="total" label="合计" width="120" />
    </el-table>
    </div>
  </div>
</template>

<script>
import {statisticByDiseaseAndYear,getDictDataByType} from '@/api/index'
export default {
  data() {
    return {
      formData:{
        year: '',
        areaCode: '',
        disease: '',
        administerAreaCode:''
      },
      oneDimensionalArray:[],
      areaList:[]
    };
  },
  created() {
    this.getCategoryList()
    this.fetchData()
  },
  computed: {
    uniqueYears() {
      return Array.from(new Set(this.oneDimensionalArray.map((item) => item.year))).sort(
        (a, b) => a - b
      )
    },
    tableData(){
      const processedData = []
      const diseaseCategoryMap = {}

      this.oneDimensionalArray &&
        this.oneDimensionalArray.forEach((item) => {
          const diseaseCategoryKey = item.diseaseCategory || '未指定' // 处理结论为null的情况

          if (!diseaseCategoryMap[diseaseCategoryKey]) {
            diseaseCategoryMap[diseaseCategoryKey] = {
              diseaseCategory: diseaseCategoryKey,
              counts: {},
              total: 0
            }
            this.uniqueYears.forEach((year) => {
              diseaseCategoryMap[diseaseCategoryKey].counts[year] = 0
            })
          }

          diseaseCategoryMap[diseaseCategoryKey].counts[item.year] += item.count
          diseaseCategoryMap[diseaseCategoryKey].total += item.count
        })


        // 将结论映射转换为数组格式，以便el-table使用
        for (const key in diseaseCategoryMap) {
          processedData.push({
            ...diseaseCategoryMap[key],
            // 将年份计数和合计转换为数字类型，以确保正确显示
            ...this.uniqueYears.reduce(
              (acc, year) => ({
                ...acc,
                ['counts.' + year]: Number(diseaseCategoryMap[key].counts[year])
              }),
              {}
            ),
            total: Number(diseaseCategoryMap[key].total)
          })
        }

        return processedData
    }
  },
  methods: {
    // 获取职业病类别
    getCategoryList(){
      getDictDataByType({dictType:'occupational_disease'}).then(res=>{
        this.categoryList = res.data
      })
      // getDictDataByType({dictType:'area'}).then(res2=>{
      //   this.areaList = res2.data
      // })
    },
    async fetchData() {
      const res = await statisticByDiseaseAndYear(this.formData)
      this.oneDimensionalArray = res.data.data
    },
    resetQuery() {
      this.formData = {
        year: '',
        areaCode: '',
        disease: '',
        administerAreaCode:''
      };
      this.fetchData();
    }
  } 
}
</script>

<style lang="scss" scoped>
.content {
  padding: 20px;
}

</style>
