{"name": "protectionAcceptFileData", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --port 9688", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@jiaminghi/data-view": "^2.10.0", "@sentry/browser": "^5.20.1", "@sentry/integrations": "^5.20.0", "axios": "^0.19.2", "core-js": "^3.6.4", "driver.js": "^0.9.8", "fuse.js": "^3.4.5", "js-cookie": "^2.2.1", "moment": "^2.29.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "pl-table": "^2.7.5", "sass": "^1.85.1", "single-spa-vue": "^1.5.4", "validator": "^11.1.0", "vue": "^2.6.11", "vue-axios": "^2.1.5", "vue-count-to": "^1.0.13", "vue-i18n": "^8.14.0", "vue-router": "^3.1.6", "vuex": "^3.1.3", "xlsx": "^0.16.2"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "^3.9.0", "@vue/cli-service": "^3.9.0", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "element-ui": "^2.15.0", "eslint": "5.15.3", "eslint-plugin-vue": "^6.2.2", "file-saver": "^2.0.5", "html-webpack-plugin": "3.2.0", "html2canvas": "^1.0.0-rc.7", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "runjs": "^4.3.2", "sass-loader": "^7.3.1", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "terser-webpack-plugin": "^3.0.3", "uglifyjs-webpack-plugin": "^2.2.0", "vue-cli-plugin-single-spa": "^1.0.0-alpha.1", "vue-template-compiler": "^2.6.10"}}