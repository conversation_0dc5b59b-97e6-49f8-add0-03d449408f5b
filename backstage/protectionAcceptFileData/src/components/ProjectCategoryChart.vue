<template>
  <div class="project-category-chart">
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'ProjectCategoryChart',
  props: {
    chartData: {
      type: Array,
      required: true
    },
    chartTitle: {
      type: String,
      default: '建设项目数类别占比'
    },
    chartRadius: {
      type: Array,
      default: () => ['40%', '65%']
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    chartData: {
      handler: 'updateChart',
      deep: true
    }
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartRef);
      window.addEventListener('resize', () => {
        if (this.chart) {
          this.chart.resize();
        }
      });
      this.updateChart();
    },

    updateChart() {
      // if (!this.chart || !this.chartData || this.chartData.length === 0) {
      //   this.renderEmptyState();
      //   return;
      // }

      const useScrollLegend = this.chartData.length > 6;

      const options = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          type: useScrollLegend ? 'scroll' : 'plain',
          orient: 'horizontal',
          bottom: useScrollLegend ? 0 : '5%',
          left: 'center',
          itemWidth: 12,
          itemHeight: 12,
          textStyle: {
            fontSize: 12
          },
          pageButtonItemGap: 5,
          pageIconSize: 10,
          pageTextStyle: {
            fontSize: 10
          }
        },
        series: [
          {
            name: this.chartTitle,
            type: 'pie',
            radius: this.chartRadius,
            center: ['50%', useScrollLegend ? '40%' : '50%'],
            data: this.chartData,
            // label: {
            //   show: true,
            //   fontSize: 12,
            //   color: '#333',
            //   formatter: function(params) {
            //     const name = params.name;
            //     if (name.length > 8) {
            //       return name.slice(0, 8) + '\n' + name.slice(8) + ': ' + params.percent.toFixed(1) + '%';
            //     }
            //     return name + ': ' + params.percent.toFixed(1) + '%';
            //   },
            //   position: 'inside',
            //   overflow: 'truncate',
            //   width: '80%'
            // },
            labelLine: {
              show: true,
              length: 10,
              length2: 15,
              lineStyle: {
                width: 1
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                fontWeight: 'bold',
                overflow: 'none'
              }
            },
            color: [
              '#3B82F6', // 中蓝
              '#60A5FA', // 亮蓝
              '#93C5FD', // 淡蓝
              '#BFDBFE',  // 浅蓝
              '#1E40AF', // 深蓝
            ]
          }
        ]
      };

      this.chart.setOption(options);
    },

    renderEmptyState() {
      this.chart.setOption({
        series: []
      });
    }
  }
};
</script>

<style scoped>
.project-category-chart {
  width: 100%;
  height: 85%;
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 280px;
}

::v-deep .ec-label {
  line-height: 1.2 !important;
}
</style>