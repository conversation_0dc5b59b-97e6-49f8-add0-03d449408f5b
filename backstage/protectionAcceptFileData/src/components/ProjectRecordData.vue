<template>
    <div class="distribution-container">
        <!-- 标题栏 -->
        <div class="distribution-header">
            <span class="title">职业病防护设施方案备案分布情况</span>
            <span class="total-count">总数: {{ totalCount || 0 }}</span>
        </div>

        <!-- 进度条列表 -->
        <div class="progress-list">
            <dv-scroll-ranking-board :config="config" style="width:100%;height:100%;" />
        </div>
    </div>
</template>

<script>
export default {
    name: 'DistributionStatistics',
    props: {
        // 接收父组件传入的分布数据
        distributionData: {
            type: Array,
            default: () => [],
            required: true
        }
    },
    computed: {
        // 计算总数
        totalCount() {
            return this.distributionData.reduce((sum, item) => sum + (item.value || 0), 0);
        },
        config(){
            return {
                data:this.distributionData
            }
        }
    },
    methods: {
        // 获取随机颜色（基于索引确保颜色稳定）
        getRandomColor(index) {
            // 预设颜色列表（避免随机色过于杂乱）
            const colorPalette = [
                "#5BA0FF", "#64B5FF", "#6EC0FF", "#78CAFF", "#82D4FF",
                "#8CDDFF", "#96E7FF", "#A0F0FF", "#A9F5FF", "#B3E6FF",
                "#BDEBFF", "#C7F0FF", "#D1F5FF", "#DBFAFF", "#E5FEFF"
            ];
            // 基于索引循环取色，确保同一项颜色固定
            return colorPalette[index % colorPalette.length];
        }
    }
};
</script>

<style lang="scss" scoped>
.distribution-container {
    width: 100%;
    height: 100%;
    padding: 2px 10px;
    box-sizing: border-box;
}

.distribution-header {
    width: 100%;
    padding: 8px 6px;
    border-bottom: 1px solid #ededed;
    font-weight: bolder;
    position: relative;
    margin-bottom: 15px;

    .title {
        font-size: 14px;
        color: #333;
    }

    .total-count {
        position: absolute;
        right: 12px;
        font-size: 16px;
        color: #666;
        font-weight: bolder;
    }
}

.progress-list {
    height: calc(100% - 50px);
    /* 减去标题栏高度 */
    overflow-y: auto;
    padding-right: 5px;
}

.progress-item {
    margin: 8px 0;
    position: relative;

    ::v-deep .el-progress {
        width: 95%;
        /* 预留计数空间 */
    }

    .item-count {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        font-size: 12px;
        font-weight: bolder;
        color: #333;
        width: 40px;
        text-align: center;
    }
}

/* 滚动条样式优化 */
.progress-list::-webkit-scrollbar {
    width: 6px;
}

.progress-list::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 3px;
}

::v-deep .info-name{
    color: #333!important;
}

::v-deep .ranking-value{
    color: #333!important;
}
</style>