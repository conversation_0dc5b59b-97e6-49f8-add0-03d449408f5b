<template>
    <div class="project-nature-container">
        <!-- 标题栏 -->
        <div class="nature-header">
            <span class="title">职业病防护设施方案备案项目性质情况</span>
        </div>

        <!-- 数据表格 -->
        <dv-scroll-board 
            :config="{
                header: ['地区', '新建', '改建','扩建','技术改造','技术引进','共计','占比'],
                data:this.formData,
                index:true,
                columnWidth:[60,240]
            }" 
            style="width:100%;height:100%" />
    </div>
</template>

<script>
export default {
    name: 'ProjectNatureStatistics',
    props: {
        // 接收项目性质数据
        natureData: {
            type: Array,
            default: () => [],
            required: true
        }
    },
    computed:{
        totalCount(){
            if(!this.natureData || this.natureData.length <= 0){
                return []
            }
            let totalCount = 0
            for(let i=0;i<this.natureData.length;i++){
                const item = JSON.parse(JSON.stringify(this.natureData[i]))
                const total = item.newCount + item.changeCount + item.addCount + item.tChangeCount + item.tImport || 0
                totalCount = totalCount + total
            }
            return totalCount
        },
        formData(){
            if(!this.natureData || this.natureData.length <= 0){
                return []
            }
            const data = []
            for(let i=0;i<this.natureData.length;i++){
                const obj = []
                const item = JSON.parse(JSON.stringify(this.natureData[i]))
                const total = item.newCount + item.changeCount + item.addCount + item.tChangeCount + item.tImport || 0
                let  percent = this.totalCount && this.totalCount !== 0 ? ((total/this.totalCount)*100).toFixed(2) + '%' : '0%'
                obj.push(item.name,item.newCount,item.changeCount,item.addCount,item.tChangeCount,item.tImport,total,percent)
                data.push(obj)
            }
            return data
        }
    }
}
</script>

<style lang="scss" scoped>
.project-nature-container {
    width: 100%;
    height: 100%;
    //   border: 2px solid #ededed;
    //   border-radius: 8px;
    padding: 2px 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.nature-header {
    width: 100%;
    padding: 8px 6px;
    border-bottom: 1px solid #ededed;
    font-weight: bolder;
    margin-bottom: 10px;

    .title {
        font-size: 14px;
        color: #333;
    }
}
</style>