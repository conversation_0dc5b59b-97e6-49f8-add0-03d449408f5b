<template>
    <div class="risk-classification-container">
        <!-- 标题栏 -->
        <div class="risk-header">
            <span class="title">职业病防护设施方案备案职业病危害风险分类情况</span>
        </div>

        <!-- 数据表格 -->
         <dv-scroll-board 
            :config="{
                header: ['地区', '一般', '较重','严重','共计','占比'],
                data:this.formData,
                index:true,
                columnWidth:[60,240]
            }" 
            style="width:100%;height:100%" />
    </div>
</template>

<script>
export default {
    name: 'RiskClassificationStatistics',
    props: {
        // 接收风险分类数据
        riskData: {
            type: Array,
            default: () => [],
            required: true
        }
    },
    computed:{
        totalCount(){
            if(!this.riskData || this.riskData.length <= 0){
                return []
            }
            let totalCount = 0
            for(let i=0;i<this.riskData.length;i++){
                const item = JSON.parse(JSON.stringify(this.riskData[i]))
                const total = item.normalCount + item.badCount + item.moreBadCount || 0
                totalCount = totalCount + total
            }
            return totalCount
        },

        formData(){
            if(!this.riskData || this.riskData.length <= 0){
                return []
            }
            const data = []
            for(let i=0;i<this.riskData.length;i++){
                const obj = []
                const item = JSON.parse(JSON.stringify(this.riskData[i]))
                const total = item.normalCount + item.badCount + item.moreBadCount || 0
                let  percent = this.totalCount && this.totalCount !== 0 ? ((total/this.totalCount)*100).toFixed(2) + '%' : '0%'
                // if(i === this.riskData.length-1){
                //     percent = 
                // }
                obj.push(item.name,item.normalCount,item.badCount,item.moreBadCount,total,percent)
                data.push(obj)
            }
            return data
        }
    }
};
</script>

<style lang="scss" scoped>
.risk-classification-container {
    width: 100%;
    height: 100%;
    padding: 2px 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.risk-header {
    width: 100%;
    padding: 8px 6px;
    border-bottom: 1px solid #ededed;
    font-weight: bolder;
    margin-bottom: 10px;

    .title {
        font-size: 14px;
        color: #333;
    }
}
</style>