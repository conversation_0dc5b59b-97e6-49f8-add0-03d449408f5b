<template>
  <div class="content">
    <div>
      <el-select v-model="timeway" placeholder="请选择时间筛选方式">
        <el-option label="时间段" :value="1"></el-option>
        <el-option label="时间点" :value="2"></el-option>
      </el-select>
      &nbsp;
      <el-date-picker v-show="timeway === 1" v-model="dateRange" type="daterange" value="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" range-separator="至"
        start-placeholder="开始日期" end-placeholder="结束日期">
      </el-date-picker>

      <el-date-picker v-show="timeway === 2" v-model="sureTime" value="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss" type="date" placeholder="选择日期">
      </el-date-picker>

      &nbsp;
      <el-cascader class="custom-cascader" v-model="areaCode" :props="districtListProps" clearable collapse-tags
        ref="regAddCas" placeholder="请选择地区">
      </el-cascader>

      &nbsp;

      <el-button type="primary" @click="onSearch">查询</el-button>
      <el-button @click="onReset">重置</el-button>
    </div>
    <el-divider></el-divider>

    <el-row :gutter="20">
      <!-- 职业病防护设施方案备案分布情况 -->
      <el-col :span="8">
        <div class="grid-content bg-purple">
          <ProjectRecordData :distributionData="countData"></ProjectRecordData>
        </div>
      </el-col>

      <el-col :span="8">
        <div class="grid-content bg-purple">
          <div style="width: 100%;border-bottom: 1px solid #ededed;padding: 8px 6px;font-weight: bolder;">
            建设项目数类别占比
          </div>
          <!-- 引入项目类别占比组件 -->
          <ProjectCategoryChart 
            :chart-data="projectCategoryPieData"
            chart-title="建设项目数类别占比"
          />
        </div>
      </el-col>

      <el-col :span="8">
        <div class="grid-content bg-purple">
          <div style="width: 100%;border-bottom: 1px solid #ededed;padding: 8px 6px;font-weight: bolder;">
            验收单位地区分布
          </div>

          <div style="width:100%;height: 85%;overflow: auto;">
            <div class="auto-scroll-container">
              <div class="scroll-wrapper" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
                <div class="scroll-list" :class="{
                  'scroll-active': checkUnitData.length > 0 && isScrolling
                }" ref="scrollList">
                  <!-- 原始列表 -->
                  <div class="scroll-item" v-for="(item, index) in checkUnitData" :key="index">
                    <div>{{ item.name }}</div>
                    <div>{{ item.count }}</div>
                  </div>
                  <!-- 复制列表用于无缝衔接 -->
                  <div class="scroll-item" v-for="(item, index) in checkUnitData" :key="`copy-${index}`">
                    <div>{{ item.name }}</div>
                    <div>{{ item.count }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </div>
      </el-col>
    </el-row>
    <br>

    <el-row :gutter="20">
      <el-col :span="8">
        <div class="grid-content bg-purple">
          <PersonnelDuty :personnel-list="dutyPersonnelList"></PersonnelDuty>
          <div style="width: 100%;border-bottom: 1px solid #ededed;padding: 8px 6px;font-weight: bolder;">
            参与人员职责分工
          </div>
          <div ref="chartDom" class="chart-dom" style="width: 100%;height: 300px;"></div>
        </div>
      </el-col>
    

      <el-col :span="16">
        <!-- 职业病防护设施方案备案项目性质情况 -->
        <div class="grid-content bg-purple">
          <ProjectNatureStatistics :natureData="recordCountRankTableData" @getAreaDistribution="getTypeRecordAreaCount">
          </ProjectNatureStatistics>
        </div>
      </el-col>
    </el-row>
    <br>

    <el-row :gutter="20">
      <el-col :span="8">
        <div class="grid-content-small bg-purple" style="display: flex;justify-content: space-around;">
          <div style="width: 100%;height: 100%;display: flex;flex-direction: column;">
            <div class="common-count-style">
              <h4>各备案结果的建设项目数</h4>
              <dv-decoration-2 style="width:300px;height:5px;" />
            </div>
            <div class="common-count-style" style="margin-bottom: 6px;">
              <div class="success-container">
                <div class="description-font">备案成功</div>
                <div class="description-count">{{ recordTypeCount.successCount }}</div>
              </div>

              <div class="fail-container">
                <div class="description-font">备案失败</div>
                <div class="description-count">{{ recordTypeCount.failCount }}</div>
              </div>
            </div>

            <div class="common-count-style">
              <h4>落实整改建设项目数</h4>
              <dv-decoration-2 style="width:300px;height:5px;" />
            </div>
            <div class="common-count-style" style="margin-bottom: 6px;">
              <div class="success-container">
                <div class="description-font">已落实</div>
                <div class="description-count">{{ dealCount.hasDealCount }}</div>
              </div>

              <div class="fail-container">
                <div class="description-font">未落实</div>
                <div class="description-count">{{ dealCount.waitDealCount || 0 }}</div>
              </div>
            </div>

            <div class="common-count-style">
              <h4>各评审意见建设项目数</h4>
              <dv-decoration-2 style="width:300px;height:5px;" />
            </div>
            <div class="common-count-style" style="margin-bottom: 6px;">
              <div class="success-container">
                <div class="description-font">同意</div>
                <div class="description-count">{{ preCheckCount.agreeCount }}</div>
              </div>
              <div class="fail-container">
                <div class="description-font">拒绝</div>
                <div class="description-count">{{ preCheckCount.refuseCount }}</div>
              </div>
            </div>

            <div class="common-count-style">
              <h4>汇报答疑数</h4>
              <dv-decoration-2 style="width:300px;height:5px;" />
            </div>
            <div class="common-count-style" style="margin-bottom: 6px;">
              <dv-digital-flop :config="{
                number: [answerAndReportCount || 0],
                content: '{nt}个',
                formatter
              }" style="width:100%;height:50px;" />
            </div>
          </div>
        </div>
      </el-col>

      <el-col :span="16">
        <!-- 职业病防护设施方案备案职业病危害风险分类情况 -->
        <div class="grid-content-small bg-purple">
          <RiskClassificationStatistics :riskData="recordProjectRiskCount"
            @getAreaDistribution="getRiskRecordAreaCount"></RiskClassificationStatistics>
        </div>
      </el-col>

      <!-- <el-col :span="8">
        <div class="grid-content-small bg-purple">
          <div style="width: 100%;border-bottom: 1px solid #ededed;padding: 8px 6px;font-weight: bolder;">
            汇报答疑数
          </div>
          <div
            style="width: 100%;height: 80%;display: flex;align-items: center;justify-content: center;font-size: 42px;color:#333;font-weight: bolder;">
            <i class="el-icon-service"></i>{{ answerAndReportCount }}
          </div>
        </div>
      </el-col> -->

    </el-row>

    <ProjectTypeRange :show.sync="isShowAreaData" :tableData="areaRangeData"></ProjectTypeRange>
  </div>
</template>

<script>
import moment from 'moment'
import * as echarts from 'echarts'
import {
  getAreaRecordCountRank,
  getTypeRecordCount,
  getTypeRecordAreaCount,
  getRiskRecordCount,
  getRiskRecordAreaCount,
  getContructProjectCategory,
  getDictionaryByKey,
  checkUnitDistribution,
  checkPeopleAndFY,
  recordTypeCount,
  implementRectification,
  prevaluteCheckCount,
  getDistrictList,
  answerAndReportCount
} from '@/api'
import ProjectRecordData from '@/components/ProjectRecordData.vue'
import ProjectNatureStatistics from '@/components/ProjectNatureStatistics.vue'
import RiskClassificationStatistics from '@/components/RiskClassificationStatistics.vue'
import ProjectCategoryChart from '@/components/ProjectCategoryChart.vue'
import ProjectTypeRange from '@/components/ProjectTypeRange.vue'
export default {
  data() {
    return {
      answerAndReportCount: 0,
      isScrolling: false, // 是否处于滚动状态
      scrollAnimation: null, // 存储动画帧

      areaCode: null,
      chart: null, // 存储图表实例
      countData: [],
      timeway: null,
      dateRange: [],
      sureTime: '',

      recordCountRankTableData: [],
      recordProjectCount: [],
      recordProjectRiskCount: [],

      areaRangeData: [],
      isShowAreaData: false,

      projectCategoryPieData: null,
      projectCategoryOptions: [],
      checkUnitData: [],
      recordTypeCount: {
        successCount: 0,
        failCount: 0
      },
      dealCount: {
        hasDealCount: 0,
        waitDealCount: 0
      },
      preCheckCount: {
        agreeCount: 0,
        refuseCount: 0
      },
      districtListProps: {
        emitPath: false,
        lazy: true,
        lazyLoad(node, resolve) {
          const { level } = node;
          const params = {
            level,
          };
          if (level) {
            params.parent_code = node.data.area_code;
          }
          // 需要请求地区数据
          getDistrictList(params).then((response) => {
            try {
              const districts = Array.from(response.data.docs);
              let nodes = districts.map((item) => ({
                value: item.area_code,
                label: item.name,
                area_code: item.area_code,
                leaf: item.level >= 1,
                // leaf: item.hasChildren ? item.level >= 2 : item.level >= 3,
                disabled: item.name === '市辖区' ? true : false,
              }));
              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          })
        }
      }
    }
  },
  components: {
    ProjectTypeRange,
    ProjectRecordData,
    ProjectNatureStatistics,
    RiskClassificationStatistics,
    ProjectCategoryChart
  },
  computed: {
    // 计算所有地区的count总和
    totalCount() {
      return this.countData.reduce((sum, item) => sum + item.count, 0);
    },

    recordTypeCountConfig(){
      return {
        data:this.recordTypeCount,
        color:[
          '#3B82F6', // 中蓝
          '#60A5FA', // 亮蓝
          '#93C5FD', // 淡蓝
          '#BFDBFE',  // 浅蓝
          '#1E40AF', // 深蓝
        ]
      }
    }
  },

  watch: {
    checkUnitData: {
      handler() {
        // 数据变化时重新初始化滚动
        if (this.scrollAnimation) {
          cancelAnimationFrame(this.scrollAnimation);
        }
        this.$nextTick(() => {
          if (this.checkUnitData.length > 0) {
            this.isScrolling = true;
            this.startScroll();
          } else {
            this.isScrolling = false;
          }
        });
      },
      deep: true
    }
  },

  async mounted() {
    this.chart = echarts.init(this.$refs.chartDom)
    window.addEventListener('resize', this.handleResize)
    this.inintData()

    this.$nextTick(() => {
      this.startScroll();
    })
  },
  beforeDestroy() {
    if (this.scrollAnimation) {
      cancelAnimationFrame(this.scrollAnimation);
    }
  },
  methods: {
    formatter (number) {
      const numbers = number.toString().split('').reverse()
      const segs = []
      while (numbers.length) segs.push(numbers.splice(0, 3).join(''))
      return segs.join(',').split('').reverse().join('')
    },
    async fAnswerAndReportCount() {
      const params = {
        areaCode: this.areaCode
      }
      // 按照时间段查询
      if (this.timeway == '1' && this.dateRange) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }
      // 按照时间点
      if (this.timeway == '2' && this.sureTime) {
        const timeRange = this.getFullDayRange(this.sureTime)
        params.startTime = timeRange.startTime
        params.endTime = timeRange.endTime
      }
      const res = await answerAndReportCount(params)
      if (res.status == 200) {
        this.answerAndReportCount = res.data
      }
    },
    // 鼠标进入 - 停止滚动
    handleMouseEnter() {
      this.isScrolling = false;
    },
    // 鼠标离开 - 开始滚动
    handleMouseLeave() {
      if (this.checkUnitData.length > 0) {
        this.isScrolling = true;
        this.startScroll();
      }
    },
    // 启动滚动
    startScroll() {
      if (this.checkUnitData.length === 0) return;

      const scrollList = this.$refs.scrollList;
      const itemHeight = 48; // 每个item的固定高度(含padding)
      const visibleCount = Math.floor(scrollList.parentElement.offsetHeight / itemHeight);

      // 数据量小于可见数量时不滚动
      if (this.checkUnitData.length <= visibleCount) {
        this.isScrolling = false;
        return;
      }

      // 重置滚动位置
      scrollList.style.transform = 'translateY(0)';

      // 计算总滚动距离和时间
      const totalDistance = this.checkUnitData.length * itemHeight;
      const duration = this.checkUnitData.length * 1000; // 每个item滚动1秒

      // 动画函数
      const animate = (startTime) => {
        if (!this.isScrolling) return;

        const currentTime = Date.now() - startTime;
        const progress = (currentTime % duration) / duration;
        const translateY = -progress * totalDistance;

        scrollList.style.transform = `translateY(${translateY}px)`;
        this.scrollAnimation = requestAnimationFrame(() => animate(startTime));
      };

      // 启动动画
      this.scrollAnimation = requestAnimationFrame(() => animate(Date.now()));
    },

    getFullDayRange(date) {
      const momentDate = moment(date);
      return {
        startTime: momentDate.startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        endTime: momentDate.endOf('day').format('YYYY-MM-DD HH:mm:ss')
      };
    },

    onSearch() {
      this.inintData()
    },

    onReset() {
      this.areaCode = this.$options.data()['areaCode']
      this.timeway = this.$options.data()['timeway']
      this.dateRange = this.$options.data()['dateRange']
      this.sureTime = this.$options.data()['sureTime']
      this.inintData()
    },

    async inintData() {
      await this.getProjectCategoryOptions()
      this.fRecordTypeCount()
      this.fImplementRectification()
      this.fPrevaluteCheckCount()
      this.checkPeopleAndFY()
      this.getAreaRecordCountRank()
      this.getTypeRecordCounts()
      this.getRiskRecordCount()
      this.checkUnitDistribution()
      await this.getContructProjectCategory()
      this.fAnswerAndReportCount()
    },
    // 评审意见
    async fPrevaluteCheckCount() {
      const params = {
        areaCode: this.areaCode
      }
      // 按照时间段查询
      if (this.timeway == '1' && this.dateRange) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }
      // 按照时间点
      if (this.timeway == '2' && this.sureTime) {
        const timeRange = this.getFullDayRange(this.sureTime)
        params.startTime = timeRange.startTime
        params.endTime = timeRange.endTime
      }
      const res = await prevaluteCheckCount(params)
      if (res.status === 200) {
        this.preCheckCount = res.data
      }
    },
    // 获取整改措施落实情况统计
    async fImplementRectification() {
      const params = {
        areaCode: this.areaCode
      }
      // 按照时间段查询
      if (this.timeway == '1' && this.dateRange) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }
      // 按照时间点
      if (this.timeway == '2' && this.sureTime) {
        const timeRange = this.getFullDayRange(this.sureTime)
        params.startTime = timeRange.startTime
        params.endTime = timeRange.endTime
      }
      // 按照时间段查询
      if (this.timeway == '1' && this.dateRange) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }
      // 按照时间点
      if (this.timeway == '2' && this.sureTime) {
        const timeRange = this.getFullDayRange(this.sureTime)
        params.startTime = timeRange.startTime
        params.endTime = timeRange.endTime
      }
      const res = await implementRectification(params)
      if (res.status === 200) {
        this.dealCount = res.data
      }
    },
    async fRecordTypeCount() {
      // 添加第地区和时间限制
      const params = {}
      if (this.areaCode) {
        params.areaCode = this.areaCode
      }
      // 按照时间段查询
      if (this.timeway == '1' && this.dateRange) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }
      // 按照时间点
      if (this.timeway == '2' && this.sureTime) {
        const timeRange = this.getFullDayRange(this.sureTime)
        params.startTime = timeRange.startTime
        params.endTime = timeRange.endTime
      }
      const res = await recordTypeCount(params)
      if (res.status === 200) {
        this.recordTypeCount = res.data
      }
    },
    async checkPeopleAndFY() {
      const params = {
        areaCode: this.areaCode
      }
      // 按照时间段查询
      if (this.timeway == '1' && this.dateRange) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }
      // 按照时间点
      if (this.timeway == '2' && this.sureTime) {
        const timeRange = this.getFullDayRange(this.sureTime)
        params.startTime = timeRange.startTime
        params.endTime = timeRange.endTime
      }
      const result = await checkPeopleAndFY(params)
      if (result.status === 200) {
        // this.peoppleData = result.data
        this.renderChart(result.data.xAxisData, result.data.seriesData);
      }
    },

    renderChart(xData, yData) {
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' } // 鼠标悬停时显示阴影指示器
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%', // 底部留足空间，防止x轴标签被截断
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xData, // 人员类型（如"工程师"、"技术员"）
          axisLabel: {
            rotate: 45, // 标签旋转45度，避免文字重叠
            interval: 0, // 强制显示所有标签
            fontSize: 12
          },
          // name: '人员类型',
          nameLocation: 'top',
          nameGap: 30 // 名称与轴的距离
        },
        yAxis: {
          type: 'value',
          name: '数量',
          nameLocation: 'middle',
          nameGap: 50 // 名称与轴的距离
        },
        color: [
          '#337ab7', '#5cb85c', '#5bc0de', '#f0ad4e', '#d9534f',
          '#7b68ee', '#00fa9a', '#ff6347', '#ba55d3', '#1e90ff'
        ],
        series: [
          {
            name: '人员数量',
            type: 'bar',
            data: yData, // 对应类型的数量
            barWidth: '50px', // 柱子宽度
            itemStyle: {
              // 柱状图颜色（可自定义渐变）
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#409eff' },
                { offset: 1, color: '#6aa8ff' }
              ])
            },
            // 柱子上方显示数值
            label: {
              show: true,
              position: 'top',
              fontSize: 12
            }
          }
        ]
      };

      // 设置图表配置项
      this.chart.setOption(option);
    },

    // 获取验收单位地址分布
    async checkUnitDistribution() {
      const params = {
        areaCode: this.areaCode
      }
      // 按照时间段查询
      if (this.timeway == '1' && this.dateRange) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }
      // 按照时间点
      if (this.timeway == '2' && this.sureTime) {
        const timeRange = this.getFullDayRange(this.sureTime)
        params.startTime = timeRange.startTime
        params.endTime = timeRange.endTime
      }
      const res = await checkUnitDistribution(params)
      if (res.status === 200) {
        this.checkUnitData = res.data
      }
    },
    formatText(val) {
      return val
    },

    // 获得地区排名（图一）
    async getAreaRecordCountRank() {
      const params = {
        areaCode: this.areaCode
      }
      // 按照时间段查询
      if (this.timeway == '1' && this.dateRange) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }
      // 按照时间点
      if (this.timeway == '2' && this.sureTime) {
        const timeRange = this.getFullDayRange(this.sureTime)
        params.startTime = timeRange.startTime
        params.endTime = timeRange.endTime
      }
      const res = await getAreaRecordCountRank(params)
      if (res.status == 200) {
        this.countData = res.data
      }
    },


    async getTypeRecordCounts() {
      const params = {
        areaCode: this.areaCode
      }
      // 按照时间段查询
      if (this.timeway == '1' && this.dateRange) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }
      // 按照时间点
      if (this.timeway == '2' && this.sureTime) {
        const timeRange = this.getFullDayRange(this.sureTime)
        params.startTime = timeRange.startTime
        params.endTime = timeRange.endTime
      }
      const res = await getTypeRecordCount(params)
      if (res.status == 200) {
        this.recordCountRankTableData = res.data
      }
    },

    async getTypeRecordAreaCount(val) {
      let params = {
        projectType: val
      }
      const res = await getTypeRecordAreaCount(params)
      if (res.status == 200) {
        this.areaRangeData = res.data
        this.isShowAreaData = true
      }
    },

    async getRiskRecordCount() {
      const params = {
        areaCode: this.areaCode
      }
      // 按照时间段查询
      if (this.timeway == '1' && this.dateRange) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }
      // 按照时间点
      if (this.timeway == '2' && this.sureTime) {
        const timeRange = this.getFullDayRange(this.sureTime)
        params.startTime = timeRange.startTime
        params.endTime = timeRange.endTime
      }
      const res = await getRiskRecordCount(params)
      if (res.status == 200) {
        this.recordProjectRiskCount = res.data
      }
    },

    async getRiskRecordAreaCount(val) {
      let params = {
        cpHazardLevel: val
      }
      const res = await getRiskRecordAreaCount(params)
      if (res.status == 200) {
        this.areaRangeData = res.data
        this.isShowAreaData = true
      }
    },

    async getContructProjectCategory() {
      const params = {
        areaCode: this.areaCode
      }
      // 按照时间段查询
      if (this.timeway == '1' && this.dateRange) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }
      // 按照时间点
      if (this.timeway == '2' && this.sureTime) {
        const timeRange = this.getFullDayRange(this.sureTime)
        params.startTime = timeRange.startTime
        params.endTime = timeRange.endTime
      }
      const res = await getContructProjectCategory(params)
      if (res.status == 200) {
        console.log(res, '=====resres')
        this.projectCategoryPieData = this.handleProjectCategoryPieData(res.data)
      }
    },

    async getProjectCategoryOptions() {
      let params = {
        key: 'construct_project_type'
      }
      const res = await getDictionaryByKey(params)
      if (res.status == 200) {
        this.projectCategoryOptions = res.data
      }
    },

    handleProjectCategory(val) {
      let formatString = ''
      this.projectCategoryOptions.forEach(item => {
        if (item.value === val) {
          formatString = item.name
        }
      })
      return formatString
    },

    handleProjectCategoryPieData(data) {
      console.log(data, 'data============', this.projectCategoryOptions)
      let tempArr = []
      for (let i = 0; i < data.length; i++) {
        let item = data[i]
        for (let j = 0; j < this.projectCategoryOptions.length; j++) {
          if (item.name == this.projectCategoryOptions[j].value) {
            item.name = this.projectCategoryOptions[j].name
            tempArr.push(item)
            break
          }
        }
      }
      console.log(tempArr, '======arr======')
      return tempArr
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.progress-container {
  margin: 5px 0px;
  // display: flex;
  // justify-content: space-between;
  // align-items: center;
  // color: #fff;
  font-size: 12px;
  font-weight: bolder;
}

.progress-name {
  position: absolute;
  left: 0;
  top: 0;
  transform: translateY(-50%);
}

.name-container {
  width: 95%;
  border-radius: 10px 10px;
  padding: 3px 5px;
}

.bg-purple {
  // background: #d3dce6;
  border: 2px solid #ededed;
}

.bg-purple-light {
  background: #e5e9f2;
}

.grid-content {
  padding: 6px 6px;
  border-radius: 8px;
  height: 400px;
  overflow: auto;
}

.grid-content-small {
  padding: 6px 6px;
  border-radius: 8px;
  height: 480px;
  overflow: auto;
}

.content {
  padding: 10px 20px;
}

.dr-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .dr-title-left {
    font-size: 16px;
    font-weight: 500;
    border-left: 6px solid #409eff;
    display: flex;
    height: 24px;
    line-height: 24px;

    .dr-title-text {
      margin-left: 10px;
    }
  }

  .dr-title-divider {
    flex: 1;
    padding: 0 10px;

    el-divider {}
  }
}

.metric-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.card {
  background: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  /* 增加了垂直偏移量和模糊半径，调整了颜色 */
}

.metric-value {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.metric-number {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
}

.metric-change {
  color: #4CAF50;
  font-size: 12px;
}


.chart-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-col {
  flex: 1;
  background: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  /* 增加了垂直偏移量和模糊半径，调整了颜色 */
}

.chart-container {
  height: 300px;
}

.recordName {
  width: 200px;
  padding: 15px 15px;
}

.recordCount {
  color: #fff;
  font-size: 20px;
  font-weight: bolder;
}


.scroll-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #ededed;
  font-size: 14px;
  color: #606266;
}


.auto-scroll-container {
  width: 100%;
  height: calc(100% - 10px);
  position: relative;
  overflow: hidden;

  .scroll-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .scroll-list {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    transition: transform 0s linear; // 基础状态无过渡
  }

  // 滚动激活状态
  .scroll-list.scroll-active {
    transition: transform 0.1s linear; // 滚动时启用过渡
  }

  .scroll-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    border-bottom: 1px solid #ededed;
    font-size: 14px;
    color: #606266;
    height: 48px; // 固定高度确保计算准确
    box-sizing: border-box;
  }
}

.common-count-style {
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: space-around;
  align-items: center;
  h4{
    width: 200px;
    text-align: left;
  }
}

.success-container{
  width: 100%;
  background-color: #67C23A;
  width:10vw;
  height: 46px;
  border-radius: 12px 12px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.fail-container{
  width: 100%;
  background-color: #E6A23C;
  width:10vw;
  height: 46px;
  border-radius: 12px 12px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.description-font{
  font-size: 16px;
  color:#444;
  font-weight: bolder;
}

.description-count{
  font-size: 30px;
  color:#fff;
  font-weight: bolder;
}
</style>