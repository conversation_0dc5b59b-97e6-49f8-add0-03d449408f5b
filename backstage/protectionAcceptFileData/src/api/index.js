import request from '@root/publicMethods/request';

export function getAreaRecordCountRank(params) {
  return request({
    url: '/manage/jgContructProjectManage/getAreaRecordCountRank',
    method: 'get',
    params
  });
}

export function getTypeRecordCount(params) {
  return request({
    url: '/manage/jgContructProjectManage/getTypeRecordCountes',
    method: 'get',
    params
  });
}


export function getTypeRecordAreaCount(params) {
  return request({
    url: '/manage/jgContructProjectManage/getTypeRecordAreaCount',
    method: 'get',
    params
  });
}


export function getRiskRecordCount(params) {
  return request({
    url: '/manage/jgContructProjectManage/getRiskRecordCount',
    method: 'get',
    params
  });
}


export function getRiskRecordAreaCount(params) {
  return request({
    url: '/manage/jgContructProjectManage/getRiskRecordAreaCount',
    method: 'get',
    params
  });
}


export function getContructProjectCategory(params) {
  return request({
    url: '/manage/jgContructProjectManage/getContructProjectCategory',
    method: 'get',
    params
  });
}

export function getDictionaryByKey(params) {
  return request({
    url: '/api/adminorgGov/getDictionaryByKey',
    params,
    method: 'get',
  });
}


export function checkUnitDistribution(params) {
  return request({
    url: '/manage/jgContructProjectManage/checkUnitDistribution',
    params,
    method: 'get',
  })
}


export function checkPeopleAndFY(params) {
  return request({
    url: '/manage/jgContructProjectManage/checkPeopleAndFY',
    params,
    method: 'get',
  })
}


export function recordTypeCount(params) {
  return request({
    url: '/manage/jgContructProjectManage/recordTypeCount',
    params,
    method: 'get',
  })
}

// 建设项目整改落实统计
export function implementRectification(params) {
  return request({
    url: '/manage/jgContructProjectManage/implementRectification',
    params,
    method: 'get',
  })
}


// 建设项目数评审意见建设项目数
export function prevaluteCheckCount(params) {
  return request({
    url: '/manage/jgContructProjectManage/prevaluteCheckCount',
    params,
    method: 'get',
  })
}

export function getDistrictList(params) {
  return request({
    url: '/api/adminorgGov/address/list',
    params,
    method: 'get',
  });
}

// 汇报答疑数
export function answerAndReportCount(params) {
  return request({
    url: '/manage/jgContructProjectManage/answerAndReportCount',
    params,
    method: 'get',
  });
}