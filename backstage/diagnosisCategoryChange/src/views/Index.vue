<template>
  <div class="content">
    <div ref="chartRef" style="width: 100%; height: 400px; margin-top: 20px"></div>
  </div>
</template>

<script>
import {statisticDiagnosisByDiseaseAndYear,getUserSession} from '@/api/index'
import * as echarts from 'echarts'
export default {
  data() {
    return {
      formData:{
        yearBegin: '',
        yearEnd: '',
        areaCode: '',
        disease: '',
        administerAreaCode:''
      },
      tableData:[]
    };
  },
  async created() {
    const res = await getUserSession();
    if (!res.data.userInfo) {
      throw new Error('获取用户信息失败');
    }
    this.formData.administerAreaCode = res.data.userInfo.area_code;
    this.getList()
  },
  mounted() {
  },
  methods: {
    async getList(){
      const res = await statisticDiagnosisByDiseaseAndYear(this.formData)
      this.tableData = res.data.data || []
      this.initEcharts()
    },
    initEcharts(){
      // 数据转换
      const categories = Array.from(new Set(this.tableData && this.tableData.map((item) => item.year))).sort(
        (a, b) => a - b
      )
      const seriesData = this.tableData && this.tableData.reduce((acc, item) => {
        if (!acc[item.diseaseCategory]) {
          acc[item.diseaseCategory] = { name: item.diseaseCategory, data: categories.map(() => 0) }
        }
        acc[item.diseaseCategory].data[categories.indexOf(item.year)] = item.count
        return acc
      }, {})

      const series = Object.values(seriesData).map((item) => ({
        ...item,
        type: 'bar',
        stack: 'total',
        // 调整柱子宽度和间距
        barCategoryGap: '50%', // 柱子之间的类别间距
        barGap: '20%', // 同一系列的柱子之间的间距
        barMaxWidth: '60px' // 每个柱子的最大宽度
      }))
      // 初始化ECharts实例
      const chart = echarts.init(this.$refs.chartRef);
      // 配置ECharts选项
      const option = {
        title: {
          text: '历年职业病诊断变化情况'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: series.map((item) => item.name),
          top: 'bottom'
        },
        xAxis: {
          type: 'category',
          data: categories
        },
        yAxis: {
          type: 'value',
        },
        series: series
      }
      // 使用配置项和数据显示图表
      chart.setOption(option)
    }
  } 
}
</script>

<style lang="scss" scoped>
.content {
  padding: 20px;
}

</style>
