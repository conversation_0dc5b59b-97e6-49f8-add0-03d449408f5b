<template>
  <div class="expert-container">
    <div :class="classObj">
      <div class="main-container">
        <div class="search">
          <el-form
            ref="form"
            :model="formSearch"
            :inline="true"
            size="mini"
            label-width="auto"
          >
            
            <el-form-item label="姓名">
              <el-input
                v-model="formSearch.name"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
            <el-form-item label="联系电话">
              <el-input
                v-model="formSearch.phone"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
            <el-form-item label="专家级别">
              <el-select v-model="formSearch.level" placeholder="请选择">
                <el-option
                  v-for="item in jiBieList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
              <!-- <el-input
                v-model="formSearch.level"
                placeholder="请输入"
              ></el-input> -->
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSubmit">查询</el-button>
              <el-button @click="reset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="table">
          <el-table
            :data="tableData"
            style="width: 100%"
            stripe
            border
            ref="multipleTable"
            header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
          >
            <el-table-column type="index" width="50" label="序号" align="center"></el-table-column>
            <el-table-column label="分类" align="center" prop="category" min-width="200">
            </el-table-column>
            <el-table-column label="姓名" align="center" prop="name">
            </el-table-column>
            <el-table-column label="性别" align="center" prop="gender">
            </el-table-column>
            <el-table-column label="级别" align="center" prop="level">
            </el-table-column>
            <el-table-column label="身份证号" align="center" prop="id_number" width="180">
              <template slot-scope="scope">
                {{ scope.row.id_number | handleIdNumber }}
              </template>
            </el-table-column>
            <el-table-column label="联系电话" align="center" prop="phone">
              <template slot-scope="scope">
                {{ scope.row.phone | handlePhone }}
              </template>
            </el-table-column>
            <el-table-column label="年龄" align="center" prop="age">
            </el-table-column>
            <el-table-column
              label="学历"
              align="center"
              prop="education"
            >
            </el-table-column>
            <el-table-column
              label="聘任时间"
              align="center"
              prop="employment_date"
            >
            </el-table-column>
            <el-table-column label="解聘时间" align="center" prop="dismissa_date">
            </el-table-column>
          </el-table>

          <div class="pagination">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page.sync="pageInfo.curPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size.sync="pageInfo.pageSize"
              background
              layout="total,sizes, prev, pager, next"
              :total="totalCount"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { initEvent } from "@root/publicMethods/events";

import {
  getAllAdminorg,
  getDistrictList,
  getCurrentPerson,
  allocationOrg,
  addEnterprise,
  expertStatisticsByMobility
} from "@/api/index.js";
import XLSX from "xlsx";
export default {
  data() {
    return {
      sidebarOpened: true,
      jiBieList: [
        { label: "兵团级", value: 1 },
        { label: "师市级", value: 2 },
        { label: "团镇级", value: 3 },
      ],
      dialogVisible: false,
      currentId: "",
      isCity: false,
      form: {
        person: "",
      },

      totalCount: null,
      tableData: [],
      districts: "",
      addDialogVisible: false,

      loading: false,
      selectTableData: [],
      formSearch: {
        name:'',
        phone:''
      },
      pageInfo:{
        curPage:1,
        pageSize:10
      }
    };
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    },
  },
  mounted() {
    initEvent(this);
  },
  methods: {
    async getList() {
      let params = {
        ...this.pageInfo
      }
      if(this.formSearch.name){
        params.name = this.formSearch.name
      }
      if(this.formSearch.phone){
        params.phone = this.formSearch.phone
      }
      if(this.formSearch.level){
        params.level = this.formSearch.level
      }
      const res = await expertStatisticsByMobility(params)
      if(res.status === 200 && res.data && res.data.list){
        this.tableData = res.data.list
        this.totalCount = res.data.total
      }
    },
    handleSizeChange(){
      this.getList()
    },
    handleCurrentChange(){
      this.getList()
    },
    onSubmit(){
      this.pageInfo.curPage = 1
      this.getList()
    },
    reset(){
      this.pageInfo = this.$options.data()['pageInfo']
      this.formSearch = this.$options.data()['formSearch']
      this.getList()
    }
  },
  filters:{
    handlePhone(val){
      let reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
      let phone = val.replace(reg, '$1****$2');
      return phone;
    },
    handleIdNumber(val) {
      const reg = /^(\d{6})\d{8}(\d{3}[0-9Xx])$/;
      return val.replace(reg, "$1********$2");
    }
  },
  async created() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.expert-container {
  padding: 20px 15px;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-top: 15px;
}
.num {
  font-size: 18px;
  color: #1691e0;
  font-weight: bolder;
}
.table {
  margin-top: 10px;
}
.pagination {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
::v-deep .el-table__fixed,
::v-deep .el-table__fixed-right {
  height: 100% !important;
}

.excel-upload-input {
  display: none;
  z-index: -9999;
}
</style>
