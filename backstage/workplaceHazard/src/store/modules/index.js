// import {
//   addData,
//   deleteItems,
//   editData,
//   getList,
// } from '@/api/index';

const state = {
  searchParams: { // 搜索参数
    keyWord: null,
    status: null, // []
    createdAt: null,
  },
  list: [], // table列表数据
  pageInfo: { // 分页信息
    curPage: 1,
    pageSize: 10,
    total: 0,
  },
  formData: {}, // 表单数据
  dialogVisible: false, // 弹窗/表单是否显示
};

const mutations = {
  setData(state, data) {
    state.list = data.list;
    state.pageInfo.total = data.total;
  },
  // 处理修改的搜索参数
  changeSearchParams(state, searchParams = {}) {
    const keys = Object.keys(searchParams);
    if (keys.length === 0) {
      state.searchParams = {
        keyWord: null,
        status: null,
        createdAt: null,
      };
    } else {
      keys.forEach(key => {
        state.searchParams[key] = searchParams[key];
      });
    }
    state.pageInfo.curPage = 1;
  },
  // 修改表单弹窗状态及表单数据初始化
  setForm(state, { operate, formData }) {
    switch (operate) {
      case 'view':
        state.dialogVisible = true;
        state.formData = {
          ...formData,
          title: '查看',
        };
        break;
      case 'edit':
        state.dialogVisible = true;
        state.formData = {
          ...formData,
          title: '编辑',
        };
        break;
      case 'add':
        state.dialogVisible = true;
        state.formData = {
          title: '新建',
          name: '',
          status: null,
          img: '',
        };
        break;
      default:
        state.dialogVisible = false;
        state.formData = {};
    }
  },
};

const actions = {
  // 修改搜索参数
  async changeSearchParams({ commit, dispatch }, searchParams = {}) {
    commit('changeSearchParams', searchParams);
    dispatch('getList');
  },
  // 获取企业审核列表
  async getList({
    commit,
  }) {
    const query = {
      ...state.searchParams,
      curPage: state.pageInfo.curPage,
      pageSize: state.pageInfo.pageSize,
    };
    console.log('query====', query);
    // const result = await getList(query);
    const result = { // 模拟数据
      data: {
        list: [{ _id: 1, name: 'name', createdAt: '2025-01-01', status: 1, img: 'https://element.eleme.io/static/theme-index-blue.c38b733.png' }],
        total: 1,
      },
    };
    console.log('列表返回===', result);
    commit('setData', result.data);
  },
  // 修改表单弹窗状态及表单数据初始化
  async setForm({ commit }, { operate, formData }) {
    commit('setForm', { operate, formData });
  },
  // 删除多条数据
  async deleteItems({ dispatch }, ids = []) {
    console.log('删除多条数据===', ids);
    // await deleteData(ids);
    dispatch('getList');
  },
  // 新建数据
  async addData({ dispatch }, data) {
    delete data.title;
    console.log('新建数据===', data);
    // await addData(data);
    dispatch('getList');
  },
  // 编辑数据
  async editData({ dispatch }, data) {
    delete data.title;
    console.log('编辑数据===', data);
    // await editData(data);
    dispatch('getList');
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
