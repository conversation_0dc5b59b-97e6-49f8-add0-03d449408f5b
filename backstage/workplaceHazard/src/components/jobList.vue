<template>
  <div class="container">
    <!-- 新增按钮 -->
    <el-button type="primary" @click="dialogVisible = true"
      >新增岗位环节</el-button
    >

    <!-- 弹窗表单 -->
    <el-dialog
      title="新增岗位环节"
      :visible.sync="dialogVisible"
      width="50%"
      @closed="resetForm"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="岗位环节名称" prop="jobNameDisplay">
          <el-autocomplete
            v-model="form.jobNameDisplay"
            :fetch-suggestions="querySearchAsync"
            placeholder="请输入搜索岗位名称"
            clearable
            style="width: 100%"
            @select="handleSelect"
            value-key="jobName"
          >
            <template slot-scope="{ item }">
              <div>{{ item.jobName }} 
                <!-- ({{ item._id }}) -->
              </div>
            </template>
          </el-autocomplete>
        </el-form-item>

        <el-form-item label="是否重点岗位" prop="ifExistJob">
          <el-radio-group v-model="form.ifExistJob">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      border
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="jobNo" label="岗位环节名称">
        <template slot-scope="scope">
          {{ getJobName(scope.row.jobNo) }} 
          <!-- ({{ scope.row.jobNo }}) -->
        </template>
      </el-table-column>
      <el-table-column prop="ifExistJob" label="是否重点岗位">
        <template slot-scope="scope">
          <el-tag :type="scope.row.ifExistJob ? 'danger' : 'success'">
            {{ scope.row.ifExistJob ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="danger"
            plain
            @click="handleDelete(scope.$index)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import { getJobList } from '@/api';
import { debounce } from 'lodash';

export default {
  name: 'firstMessage',
  components: {
    TitleTag,
  },
  props: {
    tableData:{
      type:Array,
      default:[]
    }
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        jobNo: '', // 实际存储的编码值
        jobNameDisplay: '', // 显示的名称
        ifExistJob: null,
      },
      rules: {
        jobNameDisplay: [
          { required: true, message: '请选择岗位环节编码', trigger: 'change' },
        ],
        ifExistJob: [
          { required: true, message: '请选择是否重点岗位', trigger: 'change' },
        ],
      },
      // tableData: [],
      jobList: [], // 存储从后端获取的岗位列表
    };
  },
  async created(){
    // 获取岗位列表
    await this.fetchJobList()
  },
  methods: {
    // 远程搜索方法
    querySearchAsync: debounce(function(queryString, cb) {
      if (!queryString) {
        cb([]);
        return;
      }
      
      this.fetchJobList(queryString).then(() => {
        cb(this.jobList);
      });
    }, 500),

    // 选择选项时的处理
    handleSelect(item) {
      this.form.jobNo = item._id; // 存储编码
      this.form.jobNameDisplay = item.jobName; // 显示名称
      console.log(this.form.jobNo,'this.form.jobNo0000')
    },

    // 获取岗位列表
    async fetchJobList(keyword) {
      try {
        const params = { keyword };
        const res = await getJobList(params);
        if (res.status === 200) {
          this.jobList = res.data.list || [];
        }
      } catch (error) {
        console.error('获取岗位列表失败:', error);
        this.jobList = [];
      }
    },

    // 根据编码获取岗位名称
    getJobName(jobNo) {
      const item = this.jobList.find(item => item._id === jobNo);
      return item ? item.jobName : '未知岗位';
    },

    // 提交表单
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // 添加到表格数据
          this.tableData.push({
            jobNo: this.form.jobNo,
            ifExistJob: this.form.ifExistJob,
          });
          this.$emit('keyJobInvestigation', this.tableData);
          this.$message.success('新增成功');
          this.dialogVisible = false;
        }
      });
    },

    // 删除行
    handleDelete(index) {
      this.$confirm('确定要删除该条数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.tableData.splice(index, 1);
          this.$emit('keyJobInvestigation', this.tableData);
          this.$message.success('删除成功');
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },

    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields();
      this.form = {
        jobNo: '',
        jobNameDisplay: '',
        ifExistJob: null,
      };
    },
  },
};
</script>

<style scoped>
.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}
.container {
  padding: 20px;
}
</style>