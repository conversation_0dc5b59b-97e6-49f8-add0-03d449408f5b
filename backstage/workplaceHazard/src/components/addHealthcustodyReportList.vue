<template>
  <div class="container">
    <!-- 新增按钮 -->
    <el-button type="primary" @click="showDialog" :disabled="!ifhea"
      >新增体检总结报告</el-button
    >

    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      border
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
      style="width: 100%; margin-top: 20px"
    >
      <!-- <el-table-column
        prop="uuid"
        label="唯一标识"
        width="180"
      ></el-table-column> -->
      <el-table-column
        prop="unitName"
        label="检查机构名称"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="creditCode"
        label="社会信用代码"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="ifExistCustodyReport"
        label="是否存在体检"
        width="120"
      >
        <template #default="scope">
          {{ scope.row.ifExistCustodyReport ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="reportNo" label="报告编号"></el-table-column>
      <el-table-column prop="fileName" label="附件名称"></el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.$index, scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增弹窗 -->
    <el-dialog
      title="新增体检总结报告"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="180px">
        <!-- <el-form-item label="体检总结报告唯一标识" prop="uuid">
          <el-input v-model="form.uuid" placeholder="请输入UUID" maxlength="32"></el-input>
        </el-form-item> -->

        <el-form-item label="检查机构名称" prop="unitName">
          <el-input
            v-model="form.unitName"
            placeholder="请输入检查机构名称"
            maxlength="50"
          ></el-input>
        </el-form-item>

        <el-form-item label="检查机构社会信用代码" prop="creditCode">
          <el-input
            v-model="form.creditCode"
            placeholder="请输入社会信用代码"
            maxlength="18"
          ></el-input>
        </el-form-item>

        <el-form-item label="是否存在体检" prop="ifExistCustodyReport">
          <el-radio-group v-model="form.ifExistCustodyReport">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          label="体检总结报告编号"
          prop="reportNo"
          :rules="
            form.ifExistCustodyReport
              ? [{ required: true, message: '请输入报告编号', trigger: 'blur' }]
              : []
          "
        >
          <el-input
            v-model="form.reportNo"
            placeholder="请输入报告编号"
            maxlength="50"
            :disabled="!form.ifExistCustodyReport"
          ></el-input>
        </el-form-item>

        <el-form-item
          label="体检总结报告附件名称"
          prop="fileName"
          :rules="
            form.ifExistCustodyReport
              ? [{ required: true, message: '请输入附件名称', trigger: 'blur' }]
              : []
          "
        >
          <el-input
            v-model="form.fileName"
            placeholder="请输入附件名称"
            maxlength="50"
            :disabled="!form.ifExistCustodyReport"
          ></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';

export default {
  name: 'firstMessage',
  components: {
    TitleTag,
  },
  props: {
    tableData: {
      type: Array,
      default: [],
    },
    ifhea: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    // 社会信用代码校验函数
    const validateCreditCode = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入社会信用代码'));
      }
      // 社会信用代码正则表达式：18位，包含数字和大写字母
      const reg = /^[0-9A-Z]{18}$/;
      if (!reg.test(value)) {
        callback(new Error('请输入正确的18位社会信用代码（数字和大写字母）'));
      } else {
        callback();
      }
    };
    return {
      dialogVisible: false,
      // tableData: [
      //   // 示例数据
      //   {
      //     uuid: '123e4567-e89b-12d3-a456-************',
      //     unitName: '北京市第一医院',
      //     creditCode: '91110105MA01X12345',
      //     ifExistCustodyReport: true,
      //     reportNo: '2023-001',
      //     fileName: '体检报告2023-001.pdf',
      //   },
      // ],
      form: {
        // uuid: '',
        unitName: '',
        creditCode: '',
        ifExistCustodyReport: false,
        reportNo: '',
        fileName: '',
      },
      rules: {
        // uuid: [
        //   { required: true, message: '请输入唯一标识', trigger: 'blur' },
        //   { max: 32, message: '长度不能超过32个字符', trigger: 'blur' },
        // ],
        unitName: [
          { required: true, message: '请输入检查机构名称', trigger: 'blur' },
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' },
        ],
        creditCode: [
          { required: true, message: '请输入社会信用代码', trigger: 'blur' },
          { max: 18, message: '长度不能超过18个字符', trigger: 'blur' },
          { validator: validateCreditCode, trigger: 'blur' },
        ],
        ifExistCustodyReport: [
          { required: true, message: '请选择是否存在体检', trigger: 'change' },
        ],
        reportNo: [
          { required: true, message: '请输入报告编号', trigger: 'blur' },
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' },
        ],
        fileName: [
          { required: true, message: '请输入附件名称', trigger: 'blur' },
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' },
        ],
      },
    };
  },
  async created() {},
  watch: {},
  async mounted() {},
  methods: {
    showDialog() {
      this.dialogVisible = true;
      this.resetForm();
    },

    resetForm() {
      this.form = {
        // uuid: '',
        unitName: '',
        creditCode: '',
        ifExistCustodyReport: false,
        reportNo: '',
        fileName: '',
      };
      // 清除验证
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate();
        }
      });
    },

    submitForm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // 添加到表格
          this.tableData.push({
            ...this.form,
          });
          this.$emit('healthcustodyReport', this.tableData);
          // 关闭弹窗
          this.dialogVisible = false;

          this.$message.success('新增成功');
        } else {
          this.$message.error('请填写完整信息');
          return false;
        }
      });
    },

    handleEdit(index, row) {
      // 编辑功能实现
      this.dialogVisible = true;
      this.form = { ...row };
    },
    handleDelete(index, row) {
      this.$confirm('确定要删除这条记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.tableData.splice(index, 1);
          this.$emit('healthcustodyReport', this.tableData);

          this.$message.success('删除成功');
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },
  },
  computed: {},
};
</script>
<style scoped>
.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}
.container {
  padding: 20px;
}
</style>
