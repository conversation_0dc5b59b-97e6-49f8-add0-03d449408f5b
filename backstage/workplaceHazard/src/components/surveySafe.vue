<template>
  <div>
    <TitleTag titleName="防护设施&用品"></TitleTag>
    <el-row>
      <el-col :span="24">
        <table
          style="width: 100%; border: 1px solid #ddd"
          class="table-container"
        >
          <!-- 防尘设施部分 -->
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>防尘设施-设置情况
            </th>
            <th colspan="8" class="th-input">
              <span>{{ convertValue('situation', baseInfo.fcssSituation) }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red" v-if="baseInfo.fcssSituation !== 3">*</span>防尘设施-防护效果
            </th>
            <th colspan="8" class="th-input">
              <span>
                {{ baseInfo.fcssSituation === 3 ? '无' : convertValue('effect', baseInfo.fcssEffect) }}
              </span>
            </th>
          </tr>

          <!-- 防毒设施部分 -->
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>防毒设施-设置情况
            </th>
            <th colspan="8" class="th-input">
              <span>{{ convertValue('situation', baseInfo.fdssSituation) }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red" v-if="baseInfo.fdssSituation !== 3">*</span>防毒设施-防护效果
            </th>
            <th colspan="8" class="th-input">
              <span>
                {{ baseInfo.fdssSituation === 3 ? '无' : convertValue('effect', baseInfo.fdssEffect) }}
              </span>
            </th>
          </tr>

          <!-- 防噪声设施部分 -->
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>防噪声设施-设置情况
            </th>
            <th colspan="8" class="th-input">
              <span>{{ convertValue('situation', baseInfo.fzsssSituation) }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red" v-if="baseInfo.fzsssSituation !== 3">*</span>防噪声设施-防护效果
            </th>
            <th colspan="8" class="th-input">
              <span>
                {{ baseInfo.fzsssSituation === 3 ? '无' : convertValue('effect', baseInfo.fzsssEffect) }}
              </span>
            </th>
          </tr>

          <!-- 防尘口罩部分 -->
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>防尘口罩-发放情况
            </th>
            <th colspan="8" class="th-input">
              <span>{{ convertValue('distribution', baseInfo.fckzDistributionSituation) }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red" v-if="baseInfo.fckzDistributionSituation === 1">*</span>防尘口罩-佩戴情况
            </th>
            <th colspan="8" class="th-input">
              <span>
                {{ baseInfo.fckzDistributionSituation !== 1 ? '无' : convertValue('wear', baseInfo.fckzWearSituation) }}
              </span>
            </th>
          </tr>

          <!-- 防毒口罩部分 -->
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>防毒口罩或面罩-发放情况
            </th>
            <th colspan="8" class="th-input">
              <span>{{ convertValue('distribution', baseInfo.fdkzDistributionSituation) }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red" v-if="baseInfo.fdkzDistributionSituation === 1">*</span>防毒口罩或面罩-佩戴情况
            </th>
            <th colspan="8" class="th-input">
              <span>
                {{ baseInfo.fdkzDistributionSituation !== 1 ? '无' : convertValue('wear', baseInfo.fdkzWearSituation) }}
              </span>
            </th>
          </tr>

          <!-- 防噪声耳塞部分 -->
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>防噪声耳塞或耳罩-发放情况
            </th>
            <th colspan="8" class="th-input">
              <span>{{ convertValue('distribution', baseInfo.fzyesDistributionSituation) }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red" v-if="baseInfo.fzyesDistributionSituation === 1">*</span>防噪声耳塞或耳罩-佩戴情况
            </th>
            <th colspan="8" class="th-input">
              <span>
                {{ baseInfo.fzyesDistributionSituation !== 1 ? '无' : convertValue('wear', baseInfo.fzyesWearSituation) }}
              </span>
            </th>
          </tr>

          <!-- 警示标识部分 -->
          <tr>
            <th class="left-tittle table-label" colspan="8">
              <span style="color: red">*</span>粉尘职业病危害警示标识及警示说明
            </th>
            <th colspan="8" class="th-input">
              <span>{{ convertValue('sign', baseInfo.signDust) }}</span>
            </th>
            <th class="left-tittle table-label" colspan="8">
              <span style="color: red">*</span>化学职业病危害警示标识及警示说明
            </th>
            <th colspan="8" class="th-input">
              <span>{{ convertValue('sign', baseInfo.signChemistry) }}</span>
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="8">
              <span style="color: red">*</span>物理职业病危害警示标识及警示说明
            </th>
            <th colspan="16" class="th-input">
              <span>{{ convertValue('sign', baseInfo.signPhysics) }}</span>
            </th>
          </tr>
        </table>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import TitleTag from './TitleTag.vue';
import { getSafeguardDetail } from '@/api';
export default {
  components: { TitleTag },
  data() {
    return {
      baseInfo: {
        // 防尘设施
        fcssSituation: 3,
        fcssEffect: undefined,
        // 防毒设施
        fdssSituation: 3,
        fdssEffect: undefined,
        // 防噪声设施
        fzsssSituation: 3,
        fzsssEffect: undefined,
        // 防尘口罩
        fckzDistributionSituation: 2,
        fckzWearSituation: undefined,
        // 防毒口罩
        fdkzDistributionSituation: 2,
        fdkzWearSituation: undefined,
        // 防噪声耳塞
        fzyesDistributionSituation: 2,
        fzyesWearSituation: undefined,
        // 警示标识
        signDust: 3,
        signChemistry: 3,
        signPhysics: 3,
        // 基础关联字段
        workspaceMonitoringRecordId: '',
        jcOrgId: '',
        serviceOrgId: '',
        EnterpriseID: ''
      },
      taskId: '',
      provincialWorkspaceMonitoringId: ''
    };
  },

  async created() {
    const {
      id,
      taskId,
      jcOrgId,
      serviceOrgId,
      EnterpriseID,
      provincialWorkspaceMonitoringId,
    } = this.$route.query;
    this.taskId = taskId;
    this.baseInfo.workspaceMonitoringRecordId = id;
    this.baseInfo.jcOrgId = jcOrgId;
    this.baseInfo.serviceOrgId = serviceOrgId;
    this.baseInfo.EnterpriseID = EnterpriseID;
    this.provincialWorkspaceMonitoringId = provincialWorkspaceMonitoringId;
    this.getSafeguardDetail(id);
  },
  methods: {
    async getSafeguardDetail(id) {
      const res = await getSafeguardDetail({
        workspaceMonitoringRecordId: id,
      });
      if (res.status === 200) {
        // 防尘设施
        this.baseInfo.fcssSituation = res.data.fcssSituation || 3;
        this.baseInfo.fcssEffect = res.data.fcssSituation === 3 
          ? undefined 
          : res.data.fcssEffect;

        // 防毒设施
        this.baseInfo.fdssSituation = res.data.fdssSituation || 3;
        this.baseInfo.fdssEffect = res.data.fdssSituation === 3 
          ? undefined 
          : res.data.fdssEffect;

        // 防噪声设施
        this.baseInfo.fzsssSituation = res.data.fzsssSituation || 3;
        this.baseInfo.fzsssEffect = res.data.fzsssSituation === 3 
          ? undefined 
          : res.data.fzsssEffect;

        // 防尘口罩
        this.baseInfo.fckzDistributionSituation = res.data.fckzDistributionSituation || 2;
        this.baseInfo.fckzWearSituation = res.data.fckzDistributionSituation !== 1 
          ? undefined 
          : res.data.fckzWearSituation;

        // 防毒口罩
        this.baseInfo.fdkzDistributionSituation = res.data.fdkzDistributionSituation || 2;
        this.baseInfo.fdkzWearSituation = res.data.fdkzDistributionSituation !== 1 
          ? undefined 
          : res.data.fdkzWearSituation;

        // 防噪声耳塞
        this.baseInfo.fzyesDistributionSituation = res.data.fzyesDistributionSituation || 2;
        this.baseInfo.fzyesWearSituation = res.data.fzyesDistributionSituation !== 1 
          ? undefined 
          : res.data.fzyesWearSituation;

        // 警示标识
        this.baseInfo.signDust = res.data.signDust || 3;
        this.baseInfo.signChemistry = res.data.signChemistry || 3;
        this.baseInfo.signPhysics = res.data.signPhysics || 3;
      }
    },
    // 转换数值为显示文本
    convertValue(type, value) {
      const map = {
        // 设施设置情况：1-有 2-部分有 3-无
        situation: { 1: '有', 2: '部分有', 3: '无' },
        // 防护效果：1-有效 2-部分有效 3-无效
        effect: { 1: '有效', 2: '部分有效', 3: '无效' },
        // 发放情况：1-有 2-无
        distribution: { 1: '有', 2: '无' },
        // 佩戴情况：1-有 2-部分 3-无
        wear: { 1: '有', 2: '部分', 3: '无' },
        // 警示标识：1-有 2-部分有 3-无（同设施设置）
        sign: { 1: '有', 2: '部分有', 3: '无' }
      };
      return map[type][value] || '无';
    }
  },
};
</script>

<style lang="scss" scoped>
.table-container {
  table-layout: fixed;
  background: #fff;
  width: 100%;
  margin: 0 auto;
  border-collapse: collapse;

  td {
    padding: 5px 0;
  }

  th {
    font-weight: normal;
    border: 1px solid #e5e5e5;
    padding: 3px 0;
  }

  tr {
    border: 1px solid #e5e5e5;
    width: 100%;
    font-size: 14px;
  }

  .left-tittle {
    background: #f5f7fa;
  }

  .table-label {
    text-align: right;
    padding-right: 5px;
    color: #000;
    font-size: 14px;
    height: 42px;
  }

  .th-input {
    text-align: center;
    padding: 2px 4px;

    span {
      color: #333;
    }
  }
}

.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}
</style>