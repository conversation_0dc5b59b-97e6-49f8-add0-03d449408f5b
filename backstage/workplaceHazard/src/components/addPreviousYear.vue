<template>
  <div class="container">
    <el-button
      type="primary"
      @click="showAddDialog"
      :disabled="!ifat"
      style="margin-bottom: 20px"
      >新增工作场所检测</el-button
    >

    <el-table
      :data="formattedTableData"
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
      border
      style="width: 100%"
      row-key="id"
    >
      <el-table-column
        prop="workplaceName"
        label="工作场所名称"
      ></el-table-column>

      <el-table-column label="上年度检测岗位明细">
        <el-table-column
          v-for="(job, index) in jobColumns"
          :key="index"
          :prop="job.prop"
          :label="job.label"
        >
          <template #default="scope">
            <div v-for="(item, idx) in scope.row.jobList" :key="idx">
              {{ item[job.prop] }}
            </div>
          </template>
        </el-table-column>
      </el-table-column>

      <el-table-column label="检测危害因素明细">
        <el-table-column
          v-for="(factor, index) in factorColumns"
          :key="index"
          :prop="factor.prop"
          :label="factor.label"
        >
          <template #default="scope">
            <template v-if="factor.prop === 'factorName'">
              <div
                v-for="(item, idx) in scope.row.detectionFactorList"
                :key="idx"
              >
                {{ item.factorName }}
              </div>
            </template>
            <template v-else-if="factor.prop === 'jobIfQualifiedText'">
              <div
                v-for="(item, idx) in scope.row.detectionFactorList"
                :key="idx"
              >
                {{ item.jobIfQualifiedText }}
              </div>
            </template>
            <template v-else-if="factor.prop === 'workplaceIfQualifiedText'">
              <div
                v-for="(item, idx) in scope.row.detectionFactorList"
                :key="idx"
              >
                {{ item.workplaceIfQualifiedText }}
              </div>
            </template>
            <template v-else>
              <div
                v-for="(item, idx) in scope.row.detectionFactorList"
                :key="idx"
              >
                {{ item[factor.prop] }}
              </div>
            </template>
          </template>
        </el-table-column>
      </el-table-column>

      <el-table-column label="操作" width="150">
        <template #default="scope">
          <!-- <el-button size="small" @click="handleEdit(scope.$index, scope.row)"
            >编辑</el-button
          > -->
          <el-button
            size="small"
            type="danger"
            @click="handleDelete(scope.$index, scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增弹窗 -->
    <el-dialog
      :visible.sync="dialogVisible"
      :title="
        editingIndex === undefined ? '新增工作场所检测' : '编辑工作场所检测'
      "
      width="80%"
    >
      <el-form
        :model="newWorkplace"
        label-width="180px"
        :rules="rules"
        ref="workplaceForm"
      >
        <el-form-item label="工作场所名称" prop="workplaceName">
          <el-input
            v-model="newWorkplace.workplaceName"
            placeholder="请输入工作场所名称(不能重复)"
          ></el-input>
        </el-form-item>

        <el-divider>上年度检测岗位明细</el-divider>
        <el-table :data="newWorkplace.jobList" border>
          <el-table-column
            prop="originalReportJobName"
            label="原报告中岗位名称"
          >
            <template #default="scope">
              <el-input v-model="scope.row.originalReportJobName"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="jobNo" label="标化后岗位环节编码">
            <template #default="scope">
              <el-autocomplete
                v-model="scope.row.jobNameDisplay"
                :fetch-suggestions="(query, cb) => querySearchAsync(query, cb)"
                placeholder="输入岗位名称搜索"
                clearable
                style="width: 100%"
                @select="(item) => handleSelect(item, scope)"
                :loading="scope.row._loading || jobSearchLoading"
                value-key="label"
              >
                <template #default="{ item }">
                  <div>
                    <span>{{ item.jobName }}</span>
                    <span style="color: #909399; margin-left: 8px"
                      >({{ item.cnCsjcNo }})</span
                    >
                  </div>
                </template>
              </el-autocomplete>
              <div
                v-if="!scope.row.jobNo"
                style="color: #f56c6c; font-size: 12px"
              >
                请选择岗位编码
              </div>
              <input type="hidden" v-model="scope.row.jobNo" />
            </template>
          </el-table-column>
          <el-table-column prop="jobNameOther" label="标化后其他岗位名称">
            <template #default="scope">
              <el-input
                v-model="scope.row.jobNameOther"
                :disabled="scope.row.jobNo === '20070231'"
                placeholder="请输入标化后其他岗位名称"
              ></el-input>
              <div
                v-if="
                  scope.row.jobNo === '20070231' &&
                  scope.row.jobNameOther !== scope.row.originalReportJobName
                "
                style="color: #f56c6c; font-size: 12px"
              >
                必须等于原报告中岗位名称
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button
                size="small"
                type="danger"
                @click="removeJob(scope.$index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-button @click="addJob">添加岗位</el-button>

        <el-divider>检测危害因素明细</el-divider>
        <el-table :data="newWorkplace.detectionFactorList" border>
          <el-table-column prop="factorId" label="危害因素编码">
            <template #default="scope">
              <el-select
                v-model="scope.row.factorId"
                placeholder="请选择"
                clearable
                filterable
                :filter-method="filterFactor"
                style="width: 100%"
              >
                <el-option
                  v-for="item in filteredFactorOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                  <template v-if="item.name.length > 30">
                    <el-tooltip :content="item.name" placement="top">
                      <span>{{ item.name.substring(0, 30) + '...' }}</span>
                    </el-tooltip>
                  </template>
                  <template v-else>
                    <span>{{ item.name }}</span>
                  </template>
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="ctwaSampleTag" label="Ctwa检测值标记">
            <template #default="scope">
              <el-select
                v-model="scope.row.ctwaSampleTag"
                placeholder="请选择"
                :required="isFieldRequired(scope.row.factorId, 'ctwa')"
              >
                <el-option label="=" :value="1"></el-option>
                <el-option label="<" :value="2"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="ctwaCheckValue" label="Ctwa检测值">
            <template #default="scope">
              <el-input
                v-model="scope.row.ctwaCheckValue"
                :required="isFieldRequired(scope.row.factorId, 'ctwa')"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="cstelSampleTag" label="Cstel检测值标记">
            <template #default="scope">
              <el-select
                v-model="scope.row.cstelSampleTag"
                placeholder="请选择"
                :required="isFieldRequired(scope.row.factorId, 'cstel')"
              >
                <el-option label="=" :value="1"></el-option>
                <el-option label="<" :value="2"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="cstelCheckValue" label="Cstel检测值">
            <template #default="scope">
              <el-input
                v-model="scope.row.cstelCheckValue"
                :required="isFieldRequired(scope.row.factorId, 'cstel')"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="noiseSampleTag" label="等效声级检测值标记">
            <template #default="scope">
              <el-select
                v-model="scope.row.noiseSampleTag"
                placeholder="请选择"
                :required="isFieldRequired(scope.row.factorId, 'noise')"
              >
                <el-option label="=" :value="1"></el-option>
                <el-option label="<" :value="2"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="noiseCheckValue" label="等效声级检测值">
            <template #default="scope">
              <el-input
                v-model="scope.row.noiseCheckValue"
                :required="isFieldRequired(scope.row.factorId, 'noise')"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="macSampleTag" label="Mac检测值标记">
            <template #default="scope">
              <el-select
                v-model="scope.row.macSampleTag"
                placeholder="请选择"
                :required="isFieldRequired(scope.row.factorId, 'mac')"
              >
                <el-option label="=" :value="1"></el-option>
                <el-option label="<" :value="2"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="macCheckValue" label="Mac检测值">
            <template #default="scope">
              <el-input
                v-model="scope.row.macCheckValue"
                :required="isFieldRequired(scope.row.factorId, 'mac')"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="jobIfQualified" label="岗位合格标记">
            <template #default="scope">
              <el-select
                v-model="scope.row.jobIfQualified"
                placeholder="请选择"
              >
                <el-option label="超标" :value="0"></el-option>
                <el-option label="未超标" :value="1"></el-option>
                <el-option label="未评判" :value="2"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="workplaceIfQualified" label="工作场所合格标记">
            <template #default="scope">
              <el-select
                v-model="scope.row.workplaceIfQualified"
                placeholder="请选择"
                :required="
                  isFieldRequired(scope.row.factorId, 'workplaceQualified')
                "
              >
                <el-option label="超标" :value="0"></el-option>
                <el-option label="未超标" :value="1"></el-option>
                <el-option label="未评判" :value="2"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注说明">
            <template #default="scope">
              <el-input v-model="scope.row.remark"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button
                size="small"
                type="danger"
                @click="removeFactor(scope.$index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-button @click="addFactor">添加危害因素</el-button>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import { getJobList, getFactorListNation } from '@/api';
import { debounce } from 'lodash';

export default {
  name: 'firstMessage',
  components: {
    TitleTag,
  },
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
    ifat: {
      type: Boolean,
      default: false,
    },
    taskId: {
      type: String,
    },
  },
  data() {
    return {
      dialogVisible: false,
      editingIndex: undefined,
      newWorkplace: {
        workplaceName: '',
        jobList: [],
        detectionFactorList: [],
      },
      jobColumns: [
        { prop: 'originalReportJobName', label: '原报告中岗位名称' },
        { prop: 'jobNo', label: '标化后岗位环节编码' },
        { prop: 'jobNameOther', label: '标化后其他岗位名称' },
      ],
      factorColumns: [
        { prop: 'factorName', label: '危害因素名称' },
        { prop: 'ctwaCheckValue', label: 'Ctwa检测值' },
        { prop: 'cstelCheckValue', label: 'Cstel检测值' },
        { prop: 'noiseCheckValue', label: '等效声级检测值' },
        { prop: 'macCheckValue', label: 'Mac检测值' },
        { prop: 'jobIfQualifiedText', label: '岗位合格标记' },
        { prop: 'workplaceIfQualifiedText', label: '工作场所合格标记' },
        { prop: 'remark', label: '备注说明' },
      ],
      jobNoOptions: [],
      filteredFactorOptions: [],
      factorOptions: [],
      jobSearchLoading: false,
      rules: {
        workplaceName: [
          { required: true, message: '请输入工作场所名称', trigger: 'blur' },
          { validator: this.validateWorkplaceName, trigger: 'blur' },
          { max: 300, message: '长度不能超过300个字符', trigger: 'blur' },
        ],
        jobList: {
          originalReportJobName: [
            {
              required: true,
              message: '请输入原报告中岗位名称',
              trigger: 'blur',
            },
            { max: 100, message: '长度不能超过100个字符', trigger: 'blur' },
          ],
          jobNameOther: [
            {
              validator: (rule, value, callback) => {
                const job = this.newWorkplace.jobList[rule.field.split('.')[1]];
                if (job && job.jobNo === '20070231' && !value) {
                  callback(
                    new Error('当选择"其他"时，必须填写标化后其他岗位名称')
                  );
                } else if (
                  job &&
                  job.jobNo === '20070231' &&
                  value !== job.originalReportJobName
                ) {
                  callback(
                    new Error('标化后其他岗位名称必须等于原报告中岗位名称')
                  );
                } else {
                  callback();
                }
              },
              trigger: 'blur',
            },
            { max: 100, message: '长度不能超过100个字符', trigger: 'blur' },
          ],
        },
        detectionFactorList: {
          factorId: [
            {
              required: true,
              message: '请选择危害因素编码',
              trigger: 'change',
            },
          ],
          ctwaCheckValue: [
            {
              validator: (rule, value, callback) => {
                const factor =
                  this.newWorkplace.detectionFactorList[
                    rule.field.split('.')[1]
                  ];
                if (this.isFieldRequired(factor.factorId, 'ctwa') && !value) {
                  callback(new Error('Ctwa检测值不能为空'));
                } else {
                  callback();
                }
              },
              trigger: 'blur',
            },
          ],
          cstelCheckValue: [
            {
              validator: (rule, value, callback) => {
                const factor =
                  this.newWorkplace.detectionFactorList[
                    rule.field.split('.')[1]
                  ];
                if (this.isFieldRequired(factor.factorId, 'cstel') && !value) {
                  callback(new Error('Cstel检测值不能为空'));
                } else {
                  callback();
                }
              },
              trigger: 'blur',
            },
          ],
          noiseCheckValue: [
            {
              validator: (rule, value, callback) => {
                const factor =
                  this.newWorkplace.detectionFactorList[
                    rule.field.split('.')[1]
                  ];
                if (this.isFieldRequired(factor.factorId, 'noise') && !value) {
                  callback(new Error('等效声级检测值不能为空'));
                } else {
                  callback();
                }
              },
              trigger: 'blur',
            },
          ],
          macCheckValue: [
            {
              validator: (rule, value, callback) => {
                const factor =
                  this.newWorkplace.detectionFactorList[
                    rule.field.split('.')[1]
                  ];
                if (this.isFieldRequired(factor.factorId, 'mac') && !value) {
                  callback(new Error('Mac检测值不能为空'));
                } else {
                  callback();
                }
              },
              trigger: 'blur',
            },
          ],
          workplaceIfQualified: [
            {
              validator: (rule, value, callback) => {
                const factor =
                  this.newWorkplace.detectionFactorList[
                    rule.field.split('.')[1]
                  ];
                if (
                  this.isFieldRequired(factor.factorId, 'workplaceQualified') &&
                  value === undefined
                ) {
                  callback(new Error('请选择工作场所合格标记'));
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
          remark: [
            { max: 2000, message: '长度不能超过2000个字符', trigger: 'blur' },
          ],
        },
      },
    };
  },
  computed: {
    formattedTableData() {
      return this.tableData.map((item) => {
        return {
          ...item,
          jobIfQualifiedText: this.getQualifiedText(item.jobIfQualified),
          workplaceIfQualifiedText: this.getQualifiedText(
            item.workplaceIfQualified
          ),
          detectionFactorList: item.detectionFactorList.map((factor) => {
            const factorObj =
              this.factorOptions.find((f) => f.id === factor.factorId) || {};
            return {
              ...factor,
              factorName: factorObj.name || '未知危害因素',
              jobIfQualifiedText: this.getQualifiedText(factor.jobIfQualified),
              workplaceIfQualifiedText: this.getQualifiedText(
                factor.workplaceIfQualified
              ),
            };
          }),
        };
      });
    },
  },
  async created() {
    this.fetchFactorList();
  },
  methods: {
    isFieldRequired(factorId, fieldType) {
      if (!factorId) return false;
      const factor = this.factorOptions.find((item) => item.id === factorId);
      if (!factor) return false;
      const factorName = factor.name;

      switch (fieldType) {
        case 'ctwa':
        case 'cstel':
          return !(factorName.includes('噪声') || factorName.includes('甲醛'));
        case 'noise':
          return factorName.includes('噪声');
        case 'mac':
          return factorName.includes('甲醛');
        case 'workplaceQualified':
          return !factorName.includes('噪声');
        default:
          return false;
      }
    },

    getQualifiedText(value) {
      const map = {
        0: '超标',
        1: '未超标',
        2: '未评判',
      };
      return map[value] || '未知';
    },

    async fetchFactorList() {
      try {
        // const params = { taskId: this.taskId };
        const res = await getFactorListNation();

        this.factorOptions = res.data.flatMap((category) => {
          return category.children.map((child) => ({
            category: category._id,
            id: child._id,
            name: child.chineseName.join('、'),
            aliases: child.chineseName.slice(1),
          }));
        });

        this.filteredFactorOptions = [...this.factorOptions];
      } catch (error) {
        console.error('获取监测因素列表失败:', error);
        this.$message.error('获取监测因素列表失败');
      }
    },

    filterFactor(query) {
      if (!query) {
        this.filteredFactorOptions = [...this.factorOptions];
        return;
      }

      const lowerQuery = query.toLowerCase();
      this.filteredFactorOptions = this.factorOptions.filter((item) => {
        if (item.name.toLowerCase().includes(lowerQuery)) {
          return true;
        }

        if (item.aliases) {
          return item.aliases.some((alias) =>
            alias.toLowerCase().includes(lowerQuery)
          );
        }

        return false;
      });
    },

    querySearchAsync: debounce(function (queryString, cb) {
      if (!queryString) {
        cb([]);
        return;
      }

      this.jobSearchLoading = true;

      this.fetchJobList(queryString)
        .then((list) => {
          const results = list.map((item) => ({
            value: item.cnCsjcNo,
            label: item.jobName,
            ...item,
          }));
          cb(results);
        })
        .catch(() => cb([]))
        .finally(() => {
          this.jobSearchLoading = false;
        });
    }, 500),

    handleSelect(item, scope) {
      scope.row.jobNo = item.cnCsjcNo;
      scope.row.jobNameDisplay = item.jobName;

      if (scope.row.jobNo === '20070231') {
        scope.row.jobNameOther = scope.row.originalReportJobName;
      }

      this.$set(this.newWorkplace.jobList, scope.$index, scope.row);
    },

    async fetchJobList(keyword) {
      try {
        const params = { keyword };
        const res = await getJobList(params);
        return (res.data && res.data.list) || [];
      } catch (error) {
        console.error('获取岗位列表失败:', error);
        this.$message.error('获取岗位列表失败');
        return [];
      }
    },

    getJobName(jobNo) {
      const item = this.jobList.find((item) => item.cnCsjcNo === jobNo);
      return item ? item.jobName : '未知岗位';
    },

    showAddDialog() {
      this.dialogVisible = true;
      this.editingIndex = undefined;
      this.newWorkplace = {
        workplaceName: '',
        jobList: [],
        detectionFactorList: [],
      };
      this.$nextTick(() => {
        if (this.$refs.workplaceForm) {
          this.$refs.workplaceForm.clearValidate();
        }
      });
    },

    addJob() {
      this.newWorkplace.jobList.push({
        originalReportJobName: '',
        jobNo: '',
        jobNameDisplay: '',
        jobNameOther: '',
        _loading: false,
      });
    },

    removeJob(index) {
      this.newWorkplace.jobList.splice(index, 1);
    },

    addFactor() {
      this.newWorkplace.detectionFactorList.push({
        factorId: '',
        ctwaSampleTag: null,
        ctwaCheckValue: '',
        cstelSampleTag: null,
        cstelCheckValue: '',
        noiseSampleTag: null,
        noiseCheckValue: '',
        macSampleTag: null,
        macCheckValue: '',
        jobIfQualified: null,
        workplaceIfQualified: null,
        remark: '',
      });
    },

    removeFactor(index) {
      this.newWorkplace.detectionFactorList.splice(index, 1);
    },

    validateWorkplaceName(rule, value, callback) {
      const errors = [];

      if (!value) {
        errors.push('请输入工作场所名称');
      } else if (value.length > 300) {
        errors.push('长度不能超过300个字符');
      } else if (
        this.tableData.some(
          (item, index) =>
            item.workplaceName === value &&
            (this.editingIndex === undefined || index !== this.editingIndex)
        )
      ) {
        errors.push('工作场所名称不能重复');
      }

      if (errors.length > 0) {
        callback(new Error(errors[0]));
      } else {
        callback();
      }
    },

    validateFactors() {
      const errors = [];

      this.newWorkplace.detectionFactorList.forEach((factor, index) => {
        const factorObj = this.factorOptions.find(
          (item) => item.id === factor.factorId
        );
        const factorName = factorObj
          ? factorObj.name
          : `第${index + 1}个危害因素`;

        if (!factor.factorId) {
          errors.push(`${factorName}未选择危害因素编码`);
        }

        if (
          this.isFieldRequired(factor.factorId, 'ctwa') &&
          !factor.ctwaCheckValue
        ) {
          errors.push(`${factorName}必须填写Ctwa检测值`);
        }

        if (
          this.isFieldRequired(factor.factorId, 'cstel') &&
          !factor.cstelCheckValue
        ) {
          errors.push(`${factorName}必须填写Cstel检测值`);
        }

        if (
          this.isFieldRequired(factor.factorId, 'noise') &&
          !factor.noiseCheckValue
        ) {
          errors.push(`${factorName}必须填写等效声级检测值`);
        }

        if (
          this.isFieldRequired(factor.factorId, 'mac') &&
          !factor.macCheckValue
        ) {
          errors.push(`${factorName}必须填写Mac检测值`);
        }

        if (
          this.isFieldRequired(factor.factorId, 'workplaceQualified') &&
          (factor.workplaceIfQualified === null ||
            factor.workplaceIfQualified === undefined)
        ) {
          errors.push(`${factorName}必须选择工作场所合格标记`);
        }
      });

      if (errors.length > 0) {
        this.$message.error(errors[0]);
        return false;
      }

      return true;
    },

    handleEdit(index, row) {
      this.dialogVisible = true;
      this.editingIndex = index;
      this.newWorkplace = {
        ...row,
        jobList: row.jobList.map((job) => ({
          ...job,
          jobNameDisplay:
            this.getJobName(job.jobNo) || job.originalReportJobName,
        })),
        detectionFactorList: row.detectionFactorList.map((factor) => ({
          ...factor,
        })),
      };
    },

    submitForm() {
      this.$refs.workplaceForm.validate((valid) => {
        if (!valid) {
          this.$message.error('请填写完整信息');
          return false;
        }

        if (!this.validateFactors()) {
          return false;
        }

        const jobErrors = [];
        this.newWorkplace.jobList.forEach((job, index) => {
          if (!job.jobNo) {
            jobErrors.push(`第${index + 1}个岗位未选择标化编码`);
          }
          if (
            job.jobNo === '20070231' &&
            job.jobNameOther !== job.originalReportJobName
          ) {
            jobErrors.push(
              `第${index + 1}个岗位的标化后名称必须等于原报告中名称`
            );
          }
        });

        if (jobErrors.length > 0) {
          this.$message.error(jobErrors[0]);
          return false;
        }

        if (this.editingIndex !== undefined) {
          this.tableData.splice(this.editingIndex, 1, {
            id: this.tableData[this.editingIndex].id,
            ...this.newWorkplace,
            detectionFactorList: this.newWorkplace.detectionFactorList.map(
              (factor) => {
                const factorObj =
                  this.factorOptions.find(
                    (item) => item.id === factor.factorId
                  ) || {};
                return {
                  ...factor,
                  factorName: factorObj.name || '未知危害因素',
                };
              }
            ),
          });
          this.$message.success('修改成功');
        } else {
          const newItem = {
            id:
              this.tableData.length > 0
                ? Math.max(...this.tableData.map((item) => item.id)) + 1
                : 1,
            ...this.newWorkplace,
            detectionFactorList: this.newWorkplace.detectionFactorList.map(
              (factor) => {
                const factorObj =
                  this.factorOptions.find(
                    (item) => item.id === factor.factorId
                  ) || {};
                return {
                  ...factor,
                  factorName: factorObj.name || '未知危害因素',
                };
              }
            ),
          };
          this.tableData.push(newItem);
          this.$message.success('新增成功');
        }

        this.$emit('detectionResult', this.tableData);
        this.dialogVisible = false;
        this.editingIndex = undefined;
      });
    },

    handleDelete(index, row) {
      this.$confirm('确定删除该工作场所检测信息吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.tableData.splice(index, 1);
          this.$emit('detectionResult', this.tableData);
          this.$message.success('删除成功');
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
}

.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}

.el-table .cell {
  white-space: pre-line;
}

.el-form-item.is-required .el-form-item__label:before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.el-form-item__error {
  padding-top: 2px;
}

.el-select[required] .el-input__inner::after,
.el-input[required] .el-input__inner::after {
  content: '*';
  color: red;
  margin-left: 4px;
}
</style>
