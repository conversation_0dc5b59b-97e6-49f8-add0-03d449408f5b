<template>
  <div class="container">
    <!-- 新增按钮 -->
    <el-button type="primary" @click="showDialog" :disabled="!ifhea">
      新增体检因素统计
    </el-button>

    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
      border
      style="width: 100%; margin-top: 20px"
    >
      <!-- 表格列保持不变 -->
      <el-table-column prop="ifheaFactor" label="是否检查" width="100">
        <template #default="scope">
          {{ scope.row.ifheaFactor ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="factortype" label="因素类型" width="120">
        <template #default="scope">
          {{ getTypeLabel(scope.row.factortype) }}
        </template>
      </el-table-column>
      <el-table-column prop="factorId" label="监测因素" min-width="150">
        <template #default="scope">
          {{ getFactorName(scope.row.factorId) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="checkMonitorPeoples"
        label="体检人数"
      ></el-table-column>
      <el-table-column
        prop="checkShouldPeoples"
        label="应复查人数"
      ></el-table-column>
      <el-table-column
        prop="checkActualPeoples"
        label="实际复查人数"
      ></el-table-column>
      <el-table-column prop="unusualNum" label="异常人数"></el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleEdit(scope.$index, scope.row)"
          >
            编辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.$index)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="180px">
        <!-- 表单内容保持不变 -->
        <el-form-item label="是否检查" prop="ifheaFactor">
          <el-radio-group
            v-model="form.ifheaFactor"
            @change="handleIfheaFactorChange"
          >
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="因素类型" prop="factortype">
          <el-select
            v-model="form.factortype"
            placeholder="请选择因素类型"
            @change="handleTypeChange"
            style="width: 100%"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="监测因素" prop="factorId">
          <el-select
            v-model="form.factorId"
            placeholder="请选择监测因素"
            clearable
            filterable
            :filter-method="filterFactor"
            multiple
            style="width: 100%"
          >
            <el-option
              v-for="item in filteredFactorOptions"
              :key="item._id"
              :label="item.chineseName.join('、')"
              :value="item._id"
            >
              <template v-if="item.chineseName.join('、').length > 30">
                <el-tooltip
                  :content="item.chineseName.join('、')"
                  placement="top"
                >
                  <span>{{
                    item.chineseName.join('、').substring(0, 30) + '...'
                  }}</span>
                </el-tooltip>
              </template>
              <template v-else>
                <span>{{ item.chineseName.join('、') }}</span>
              </template>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 其他表单项保持不变 -->
        <el-form-item
          label="体检人数"
          prop="checkMonitorPeoples"
          :rules="
            form.ifheaFactor
              ? [
                  {
                    required: true,
                    message: '请输入体检人数',
                    trigger: 'blur',
                  },
                  { validator: validatePeopleNum, trigger: 'blur' },
                ]
              : []
          "
        >
          <el-input-number
            v-model="form.checkMonitorPeoples"
            :min="0"
            :max="999999"
            :disabled="!form.ifheaFactor"
            placeholder="请输入体检人数"
          ></el-input-number>
        </el-form-item>

        <el-form-item
          label="应复查人数"
          prop="checkShouldPeoples"
          :rules="
            form.ifheaFactor
              ? [
                  {
                    required: true,
                    message: '请输入应复查人数',
                    trigger: 'blur',
                  },
                  { validator: validateShouldNum, trigger: 'blur' },
                ]
              : []
          "
        >
          <el-input-number
            v-model="form.checkShouldPeoples"
            :min="0"
            :max="Math.min(999999, form.checkMonitorPeoples || 999999)"
            :disabled="!form.ifheaFactor"
            placeholder="请输入应复查人数"
          ></el-input-number>
        </el-form-item>

        <el-form-item
          label="实际复查人数"
          prop="checkActualPeoples"
          :rules="
            form.ifheaFactor
              ? [
                  {
                    required: true,
                    message: '请输入实际复查人数',
                    trigger: 'blur',
                  },
                  { validator: validateActualNum, trigger: 'blur' },
                ]
              : []
          "
        >
          <el-input-number
            v-model="form.checkActualPeoples"
            :min="0"
            :max="Math.min(999999, form.checkShouldPeoples || 999999)"
            :disabled="!form.ifheaFactor"
            placeholder="请输入实际复查人数"
          ></el-input-number>
        </el-form-item>

        <el-form-item
          label="异常人数"
          prop="unusualNum"
          :rules="
            form.ifheaFactor
              ? [
                  {
                    required: true,
                    message: '请输入异常人数',
                    trigger: 'blur',
                  },
                  { validator: validateUnusualNum, trigger: 'blur' },
                ]
              : []
          "
        >
          <el-input-number
            v-model="form.unusualNum"
            :min="0"
            :max="Math.min(999999, form.checkMonitorPeoples || 999999)"
            :disabled="!form.ifheaFactor"
            placeholder="请输入异常人数"
          ></el-input-number>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import { getFactorListNation } from '@/api';

export default {
  name: 'addSupervisionItemList',
  components: {
    TitleTag,
  },
  props: {
    tableData: {
      type: Array,
      default: [],
    },
    ifhea: {
      type: Boolean,
      default: false,
    },
    taskId: {
      type: String,
    },
    fatherFactorOptions: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '新增体检因素统计',
      editIndex: -1,
      form: {
        ifheaFactor: false,
        factortype: '',
        factorId: [],
        checkMonitorPeoples: null,
        checkShouldPeoples: null,
        checkActualPeoples: null,
        unusualNum: null,
      },
      rules: {
        factortype: [
          { required: true, message: '请选择因素类型', trigger: 'change' },
        ],
        factorId: [
          {
            required: true,
            message: '请选择监测因素',
            trigger: 'change',
            type: 'array',
          },
        ],
      },
      typeOptions: [
        { value: 1, label: '粉尘因素' },
        { value: 2, label: '化学因素' },
        { value: 3, label: '物理因素' },
      ],
      factorOptions: [],
      filteredFactorOptions: [],
      factorNameMap: new Map(),
      // 新增：缓存所有因素ID，用于验证
      allFactorIds: new Set(),
    };
  },
  async created() {
    await this.fetchFactorList();
    this.$watch(
      'fatherFactorOptions',
      () => {
        this.fetchFactorList();
      },
      { deep: true }
    );
  },
  methods: {
    async fetchFactorList() {
      try {
        this.factorOptions = this.fatherFactorOptions || [];
        this.filteredFactorOptions = this.getAllFactors();
        this.buildFactorNameMap();
        this.buildAllFactorIds(); // 构建所有因素ID的集合
      } catch (error) {
        console.error('获取监测因素列表失败:', error);
        this.factorOptions = [];
        this.filteredFactorOptions = [];
        this.buildFactorNameMap();
        this.buildAllFactorIds();
      }
    },

    buildFactorNameMap() {
      this.factorNameMap.clear();
      this.factorOptions.forEach(option => {
        if (option && option.children && Array.isArray(option.children)) {
          option.children.forEach(child => {
            if (
              child &&
              child._id &&
              child.chineseName &&
              Array.isArray(child.chineseName)
            ) {
              this.factorNameMap.set(child._id, child.chineseName.join('、'));
            }
          });
        }
      });
    },

    // 新增：构建所有因素ID的集合，用于验证
    buildAllFactorIds() {
      this.allFactorIds.clear();
      this.factorOptions.forEach(option => {
        if (option && option.children && Array.isArray(option.children)) {
          option.children.forEach(child => {
            if (child && child._id) {
              this.allFactorIds.add(child._id);
            }
          });
        }
      });
    },

    getAllFactors() {
      return this.factorOptions.flatMap(category =>
        category && category.children && Array.isArray(category.children)
          ? category.children.map(child => ({
              ...child,
              category: category._id,
            }))
          : []
      );
    },

    getFactorName(factorIds) {
      if (!factorIds) return '';

      const ids = Array.isArray(factorIds) ? factorIds : [factorIds];

      if (this.factorNameMap.size === 0) {
        return '';
      }

      const names = [];
      ids.forEach(id => {
        const name = this.factorNameMap.get(id);
        if (name) {
          names.push(name);
        } else if (id) {
          names.push(`未知因素(${id})`);
        }
      });

      return names.join(', ');
    },

    filterFactor(query) {
      if (!query) {
        this.filterByType(this.form.factortype);
        return;
      }

      const lowerQuery = query.toLowerCase();
      const allFactors = this.getAllFactors();

      this.filteredFactorOptions = allFactors.filter(item => {
        if (
          this.form.factortype &&
          item.category !== this.getCategoryName(this.form.factortype)
        ) {
          return false;
        }

        const mainName = item.chineseName[0] || '';
        if (mainName.toLowerCase().includes(lowerQuery)) {
          return true;
        }

        return item.chineseName
          .slice(1)
          .some(alias => alias.toLowerCase().includes(lowerQuery));
      });
    },

    filterByType(factortype) {
      const typeMap = {
        1: '粉尘',
        2: '化学',
        3: '物理',
      };
      const typeName = typeMap[factortype];

      if (!typeName) {
        this.filteredFactorOptions = this.getAllFactors();
        return;
      }

      const category = this.factorOptions.find(
        item => item && item._id === typeName
      );
      this.filteredFactorOptions =
        category && category.children && Array.isArray(category.children)
          ? category.children.map(child => ({
              ...child,
              category: category._id,
            }))
          : [];
    },

    showDialog() {
      this.dialogTitle = '新增体检因素统计';
      this.editIndex = -1;
      this.resetForm();
      this.dialogVisible = true;
    },

    // 修复编辑回显核心方法
    handleEdit(index, row) {
      this.dialogTitle = '编辑体检因素统计';
      this.editIndex = index;

      // 1. 处理因素ID格式，确保是数组
      let factorIds = row.factorId || [];
      if (!Array.isArray(factorIds)) {
        // 如果是字符串格式，尝试用逗号分割（兼容可能的字符串存储）
        factorIds =
          typeof factorIds === 'string'
            ? factorIds
                .split(',')
                .map(id => id.trim())
                .filter(Boolean)
            : [factorIds];
      }

      // 2. 过滤无效ID（不在因素列表中的ID）
      const validFactorIds = factorIds.filter(id => this.allFactorIds.has(id));

      // 3. 深拷贝表单数据
      this.form = {
        ...row,
        factorId: [...validFactorIds], // 使用处理后的有效ID数组
      };

      // 4. 确保因素类型对应的选项被正确加载
      this.$nextTick(() => {
        // 先加载当前类型的所有选项
        this.filterByType(this.form.factortype);
        // 强制刷新选择器，确保选中项正确显示
        this.$forceUpdate();
      });

      this.dialogVisible = true;
    },

    resetForm() {
      this.form = {
        ifheaFactor: false,
        factortype: '',
        factorId: [],
        checkMonitorPeoples: null,
        checkShouldPeoples: null,
        checkActualPeoples: null,
        unusualNum: null,
      };
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate();
        }
      });
    },

    handleIfheaFactorChange(val) {
      if (!val) {
        this.form.checkMonitorPeoples = null;
        this.form.checkShouldPeoples = null;
        this.form.checkActualPeoples = null;
        this.form.unusualNum = null;
      }
    },

    handleTypeChange(factortype) {
      // 类型改变时保留有效选项
      if (this.form.factorId.length > 0) {
        this.filterByType(factortype);
        // 过滤掉不属于当前类型的选项
        const validFactors = this.filteredFactorOptions.map(item => item._id);
        this.form.factorId = this.form.factorId.filter(id =>
          validFactors.includes(id)
        );
      } else {
        this.form.factorId = [];
        this.filterByType(factortype);
      }
    },

    getCategoryName(typeValue) {
      const typeMap = {
        1: '粉尘',
        2: '化学',
        3: '物理',
      };
      return typeMap[typeValue] || '';
    },

    // 验证方法保持不变
    validatePeopleNum(rule, value, callback) {
      if (value === null || value === '') {
        callback(new Error('请输入体检人数'));
      } else if (value < 0) {
        callback(new Error('体检人数不能小于0'));
      } else if (value > 999999) {
        callback(new Error('体检人数不能超过6位数'));
      } else {
        callback();
      }
    },

    validateShouldNum(rule, value, callback) {
      if (value === null || value === '') {
        callback(new Error('请输入应复查人数'));
      } else if (value < 0) {
        callback(new Error('应复查人数不能小于0'));
      } else if (value > this.form.checkMonitorPeoples) {
        callback(new Error('应复查人数不能大于体检人数'));
      } else if (value > 999999) {
        callback(new Error('应复查人数不能超过6位数'));
      } else {
        callback();
      }
    },

    validateActualNum(rule, value, callback) {
      if (value === null || value === '') {
        callback(new Error('请输入实际复查人数'));
      } else if (value < 0) {
        callback(new Error('实际复查人数不能小于0'));
      } else if (value > this.form.checkShouldPeoples) {
        callback(new Error('实际复查人数不能大于应复查人数'));
      } else if (value > 999999) {
        callback(new Error('实际复查人数不能超过6位数'));
      } else {
        callback();
      }
    },

    validateUnusualNum(rule, value, callback) {
      if (value === null || value === '') {
        callback(new Error('请输入异常人数'));
      } else if (value < 0) {
        callback(new Error('异常人数不能小于0'));
      } else if (value > this.form.checkMonitorPeoples) {
        callback(new Error('异常人数不能大于体检人数'));
      } else if (value > 999999) {
        callback(new Error('异常人数不能超过6位数'));
      } else {
        callback();
      }
    },

    submitForm() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (this.editIndex === -1) {
            this.tableData.push({ ...this.form });
          } else {
            this.tableData.splice(this.editIndex, 1, { ...this.form });
          }
          this.$emit('supervisionItem', [...this.tableData]);
          this.dialogVisible = false;
          this.$message.success(
            this.editIndex === -1 ? '新增成功' : '编辑成功'
          );
        } else {
          this.$message.error('请填写完整信息');
          return false;
        }
      });
    },

    handleDelete(index) {
      this.$confirm('确定要删除这条记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.tableData.splice(index, 1);
          this.$emit('supervisionItem', this.tableData);
          this.$message.success('删除成功');
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },

    getTypeLabel(factortype) {
      const item = this.typeOptions.find(item => item.value === factortype);
      return item ? item.label : '';
    },
  },
};
</script>

<style scoped>
.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}
.container {
  padding: 20px;
}
</style>
