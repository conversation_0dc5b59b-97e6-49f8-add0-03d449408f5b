<template>
  <div>
    <el-dialog
      title="添加化学危害因素"
      :visible.sync="dialogVisible"
      width="800"
      @close="handleDialogClose"
    >
      <div>
        <TitleTag titleName="查询条件"></TitleTag>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item label="分类">
            <el-input
              v-model="searchForm.category"
              placeholder="请输入分类名称"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="危害因素">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入危害因素名称"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
        <TitleTag titleName="添加化学危害因素"></TitleTag>
        <el-table
          ref="multipleTable"
          :data="paginatedData"
          tooltip-effect="light"
          style="min-width: 100%"
          stripe
          border
          header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
          align="center"
          @selection-change="handleSelectionChange"
          row-key="id"
        >
          <el-table-column
            type="selection"
            width="55"
            reserve-selection
            :selectable="() => true"
          ></el-table-column>
          <el-table-column prop="category" label="分类" width="200">
          </el-table-column>
          <el-table-column prop="name" label="危害因素"> </el-table-column>
          <!-- <el-table-column label="别名" width="200">
            <template slot-scope="{row}">
              <span v-if="row.aliases && row.aliases.length > 0">
                {{ row.aliases.join(', ') }}
              </span>
              <span v-else style="color: #999">无</span>
            </template>
          </el-table-column> -->
        </el-table>
        <div class="pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50, 100]"
            :page-size="pageSize"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="filteredData.length"
          >
          </el-pagination>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import { factorList } from '@/api';

export default {
  components: {
    TitleTag,
  },
  props: {
    taskId: {
      type: String,
    },
  },
  data() {
    return {
      dialogVisible: false,
      searchForm: {
        category: '',
        name: '',
      },
      allTableData: [], // 存储所有数据
      currentPage: 1,
      pageSize: 10,
      multipleSelection: [], // 存储选中的数据
    };
  },
  computed: {
    // 过滤后的数据（根据搜索条件）
    filteredData() {
      return this.allTableData.filter((item) => {
        const matchesCategory = this.searchForm.category
          ? item.category.includes(this.searchForm.category)
          : true;
        const matchesName = this.searchForm.name
          ? item.name.includes(this.searchForm.name) ||
            (item.aliases &&
              item.aliases.some((alias) =>
                alias.includes(this.searchForm.name)
              ))
          : true;
        return matchesCategory && matchesName;
      });
    },
    // 分页后的数据
    paginatedData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.filteredData.slice(start, end);
    },
    // 总条数
    total() {
      return this.filteredData.length;
    },
  },
  methods: {
    show() {
      this.dialogVisible = true;
      this.fetchFactorList();
    },
    async fetchFactorList() {
      try {
        let params = {
          taskId: this.taskId,
        };
        const res = await factorList(params);

        // 扁平化处理
        this.allTableData = res.data.flatMap((category) => {
          return category.children.map((child) => ({
            category: category._id,
            id: child._id,
            name: child.chineseName.join('、'),
            aliases: child.chineseName.slice(1),
          }));
        });

        // 重置分页
        this.currentPage = 1;
      } catch (error) {
        console.error('获取危害因素列表失败:', error);
        this.$message.error('获取危害因素列表失败');
      }
    },
    handleSearch() {
      this.currentPage = 1;
    },
    resetSearch() {
      this.searchForm = {
        category: '',
        name: '',
      };
      this.currentPage = 1;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1; // 改变每页条数时回到第一页
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },
    handleConfirm() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请至少选择一项危害因素');
        return;
      }

      this.confirmLoading = true;
      try {
        // 直接返回选中的完整对象数组
        this.$emit('confirm', this.multipleSelection);
        this.dialogVisible = false;
        this.$message.success('添加成功');
      } catch (error) {
        console.error('保存失败:', error);
        this.$message.error('保存失败');
      } finally {
        this.confirmLoading = false;
      }
    },
    handleDialogClose() {
      // 清空选择
      this.$refs.multipleTable.clearSelection();
      this.multipleSelection = [];
    },
  },
};
</script>

<style lang="scss" scoped>
.pagination {
  margin-top: 10px;
  text-align: right;
}
.demo-form-inline {
  margin-bottom: 20px;
}
</style>
