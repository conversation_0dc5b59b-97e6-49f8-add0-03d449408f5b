<template>
  <div>
    <el-dialog
      title="选择单位"
      :visible.sync="dialogVisible"
      width="800"
      @close="handleClose"
    >
      <div>
        <TitleTag titleName="查询条件"></TitleTag>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item label="单位名称">
            <el-input
              v-model="searchForm.searchkey"
              placeholder="请输入单位名称/法人关键字查询"
              clearable
              @keyup.enter.native="handleSearch"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
          </el-form-item>
        </el-form>
        <TitleTag titleName="选择单位"></TitleTag>
        <el-table
          ref="multipleTable"
          :data="resGetList"
          tooltip-effect="light"
          style="min-width: 100%"
          stripe
          border
          header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
          align="center"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column prop="cname" label="单位名称"> </el-table-column>
          <el-table-column prop="corp" label="法人"> </el-table-column>
          <el-table-column prop="contact" label="联系人"> </el-table-column>
          <el-table-column prop="phoneNum" label="联系方式"> </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pageInfo.current"
            :page-sizes="[10, 20, 30, 50, 100]"
            :page-size="pageInfo.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import { getList } from '@/api';

export default {
  components: {
    TitleTag,
  },
  data() {
    return {
      dialogVisible: false,
      searchForm: {
        searchkey: '',
      },
      pageInfo: {
        current: 1, // 修改为current以匹配你的参数
        pageSize: 10,
      },
      total: 0,
      multipleSelection: [],
      resGetList: [],
      selectedRows: [], // 存储最终选中的行数据
      initialSelections: [], // 存储初始传入的选中项
    };
  },
  methods: {
    show(selected = []) {
      this.dialogVisible = true;
      this.initialSelections = [...selected]; // 保存初始选中项
      this.selectedRows = [...selected]; // 初始化已选中的行
      this.getList();
    },

    async getList() {
      try {
        let params = {
          pageInfos: { ...this.pageInfo }, // 使用你修改后的参数结构
          searchkey: this.searchForm.searchkey,
        };

        const res = await getList(params);
        if (res.status == 200) {
          this.resGetList = res.data.docs || [];
          this.total = res.data.docs.length || 0;

          // 在下一次DOM更新后设置选中状态
          this.$nextTick(() => {
            this.resGetList.forEach((row) => {
              if (this.selectedRows.some((item) => item._id === row._id)) {
                this.$refs.multipleTable.toggleRowSelection(row, true);
              }
            });
          });
        }
      } catch (error) {
        console.error('获取列表失败:', error);
      }
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    handleSearch() {
      this.pageInfo.current = 1; // 搜索时重置到第一页
      this.getList();
    },

    handleSizeChange(val) {
      this.pageInfo.pageSize = val;
      this.getList();
    },

    handleCurrentChange(val) {
      this.pageInfo.current = val;
      this.getList();
    },

    handleConfirm() {
      // 合并之前选中的和当前选中的，去重
      const newSelected = [...this.selectedRows];
      this.multipleSelection.forEach((item) => {
        if (!newSelected.some((selected) => selected._id === item._id)) {
          newSelected.push(item);
        }
      });

      // 过滤掉在当前页取消选中的项
      const finalSelected = newSelected.filter((item) => {
        // 如果当前页有这一项且没有被选中，则过滤掉
        const inCurrentPage = this.resGetList.some(
          (row) => row._id === item._id
        );
        if (inCurrentPage) {
          return this.multipleSelection.some(
            (selected) => selected._id === item._id
          );
        }
        return true;
      });

      this.$emit('confirm', finalSelected);
      this.dialogVisible = false;
    },

    handleCancel() {
      // 恢复初始选择状态
      this.selectedRows = [...this.initialSelections];
      this.dialogVisible = false;
    },

    handleClose() {
      // 清空临时选择
      this.multipleSelection = [];
      this.$refs.multipleTable.clearSelection();
    },
  },
};
</script>

<style lang="scss" scoped>
.pagination {
  margin-top: 10px;
  text-align: right;
}
</style>
