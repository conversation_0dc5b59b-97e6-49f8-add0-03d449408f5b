<template>
  <div class="container">
    <!-- 新增按钮 -->
    <el-button type="primary" @click="showDio()">新增监测因素</el-button>

    <!-- 弹窗表单 -->
    <el-dialog
      title="新增监测因素"
      :visible.sync="dialogVisible"
      width="50%"
      @closed="resetForm"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="因素类型" prop="type">
          <el-radio-group v-model="form.type" @change="handleTypeChange">
            <el-radio :label="1">粉尘因素</el-radio>
            <el-radio :label="2">化学因素</el-radio>
            <el-radio :label="3">物理因素</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="监测因素" prop="factorId">
          <el-select
            v-model="form.factorId"
            placeholder="请选择监测因素"
            clearable
            filterable
            :filter-method="filterFactor"
            style="width: 100%"
          >
            <el-option
              v-for="item in filteredFactorOptions"
              :key="item._id"
              :label="item.chineseName.join('、')"
              :value="item._id"
            >
              <template v-if="item.chineseName.join('、').length > 30">
                <el-tooltip
                  :content="item.chineseName.join('、')"
                  placement="top"
                >
                  <span>{{
                    item.chineseName.join('、').substring(0, 30) + '...'
                  }}</span>
                </el-tooltip>
              </template>
              <template v-else>
                <span>{{ item.chineseName.join('、') }}</span>
              </template>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="接触人数" prop="contactNum">
          <el-input-number
            v-model="form.contactNum"
            :min="1"
            :step="1"
            placeholder="请输入接触人数"
          ></el-input-number>
        </el-form-item>

        <el-form-item label="数据类型" prop="dataType">
          <el-radio-group v-model="form.dataType">
            <el-radio :label="1">国家要求</el-radio>
            <el-radio :label="2">自定义添加</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      border
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="type" label="因素类型">
        <template slot-scope="scope">
          {{ getTypeName(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column prop="factorId" label="监测因素" width="180">
        <template slot-scope="scope">
          {{ getFactorName(scope.row.factorId) }}
        </template>
      </el-table-column>
      <el-table-column prop="contactNum" label="接触人数"></el-table-column>
      <el-table-column prop="dataType" label="数据类型">
        <template slot-scope="scope">
          <el-tag :type="scope.row.dataType === 1 ? 'primary' : 'success'">
            {{ scope.row.dataType === 1 ? '国家要求' : '自定义添加' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="danger"
            plain
            @click="handleDelete(scope.$index)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import { getFactorListNation } from '@/api';
export default {
  name: 'firstMessage',
  components: {
    TitleTag,
  },
  props: {
    taskId: {
      type: String,
    },
    tableData: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        type: null,
        factorId: '',
        contactNum: 1,
        dataType: null,
      },
      rules: {
        type: [
          { required: true, message: '请选择因素类型', trigger: 'change' },
        ],
        factorId: [
          { required: true, message: '请选择监测因素编码', trigger: 'change' },
        ],
        contactNum: [
          { required: true, message: '请输入接触人数', trigger: 'blur' },
          {
            type: 'number',
            min: 1,
            message: '接触人数必须大于0',
            trigger: 'blur',
          },
        ],
        dataType: [
          { required: true, message: '请选择数据类型', trigger: 'change' },
        ],
      },
      // 监测因素编码选项
      factorOptions: [
        // { value: 'F001', label: '粉尘-总尘' },
        // { value: 'F002', label: '粉尘-呼尘' },
        // { value: 'C001', label: '化学-苯' },
        // { value: 'C002', label: '化学-甲苯' },
        // { value: 'P001', label: '物理-噪声' },
        // { value: 'P002', label: '物理-高温' },
      ],
      filteredFactorOptions: [], // 过滤后的选项
      searchText: '', // 搜索文本
      // tableData: [],
    };
  },
  async created() {
    console.log(this.tableData, 'tableData-------000');
    this.fetchFactorList();
  },
  watch: {},
  async mounted() {},
  methods: {
    showDio() {
      this.dialogVisible = true;
    },
    async fetchFactorList() {
      try {
        // let params = {
        //   taskId: this.taskId,
        // };
        const res = await getFactorListNation();

        // 按类型分类存储
        this.factorOptions = res.data;

        // 初始化显示所有因素
        this.filteredFactorOptions = this.getAllFactors();
      } catch (error) {
        console.error('获取监测因素列表失败:', error);
        this.$message.error('获取监测因素列表失败');
      }
    },
    // 获取所有因素扁平化数组
    getAllFactors() {
      return this.factorOptions.flatMap((category) =>
        category.children.map((child) => ({
          ...child,
          category: category._id,
        }))
      );
    },
    filterByType(type) {
      const typeMap = {
        1: '粉尘',
        2: '化学',
        3: '物理',
      };
      const typeName = typeMap[type];

      if (!typeName) {
        this.filteredFactorOptions = this.getAllFactors();
        return;
      }

      const category = this.factorOptions.find((item) => item._id === typeName);
      this.filteredFactorOptions = category
        ? category.children.map((child) => ({
            ...child,
            category: category._id,
          }))
        : [];
    },
    // 修改因素类型change事件
    handleTypeChange(type) {
      this.form.factorId = ''; // 清空已选因素
      this.filterByType(type);
    },
    // 过滤方法
    filterFactor(query) {
      if (!query) {
        // 没有搜索词时显示当前类型的所有选项
        this.filterByType(this.form.type);
        return;
      }

      const lowerQuery = query.toLowerCase();
      const allFactors = this.getAllFactors();

      this.filteredFactorOptions = allFactors.filter((item) => {
        // 如果已选择类型，且当前因素不属于该类型，则过滤掉
        if (
          this.form.type &&
          item.category !== this.getCategoryName(this.form.type)
        ) {
          return false;
        }

        // 检查主名称是否匹配
        const mainName = item.chineseName[0] || '';
        if (mainName.toLowerCase().includes(lowerQuery)) {
          return true;
        }

        // 检查别名是否匹配
        return item.chineseName
          .slice(1)
          .some((alias) => alias.toLowerCase().includes(lowerQuery));
      });
    },

    // 根据类型值获取分类名称
    getCategoryName(type) {
      const typeMap = {
        1: '粉尘',
        2: '化学',
        3: '物理',
      };
      return typeMap[type];
    },
    // 提交表单
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // 添加到表格数据
          console.log(this.form.factorId, '-this.form.factorId00');
          this.tableData.push({
            type: this.form.type,
            factorId: this.form.factorId,
            contactNum: this.form.contactNum,
            dataType: this.form.dataType,
          });
          this.$emit('factorCrowdItem', this.tableData);

          this.$message.success('新增成功');
          this.dialogVisible = false;
        }
      });
    },

    // 删除行
    handleDelete(index) {
      this.$confirm('确定要删除该条数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.tableData.splice(index, 1);
          this.$emit('factorCrowdItem', this.tableData);

          this.$message.success('删除成功');
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },

    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields();
      this.form = {
        type: null,
        factorId: '',
        contactNum: 1,
        dataType: null,
      };
    },

    // 获取因素类型名称
    getTypeName(type) {
      const types = {
        1: '粉尘因素',
        2: '化学因素',
        3: '物理因素',
      };
      return types[type] || type;
    },

    // 根据编码获取因素名称 - 增强兼容性
    getFactorName(factorId) {
      if (!factorId) return '未知因素';

      // 处理factorId可能是对象的情况
      const id = typeof factorId === 'object' ? factorId._id : factorId;

      if (!this.factorOptions || this.factorOptions.length === 0) {
        return '加载中...';
      }

      // 在所有分类中查找匹配的因素
      for (const category of this.factorOptions) {
        const found = category.children.find((child) => child._id === id);
        if (found) {
          return found.chineseName.join('、');
        }
      }

      return id; // 没找到则返回原始ID
    },
  },
  computed: {},
};
</script>
<style scoped>
.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}
.container {
  padding: 20px;
}
</style>
