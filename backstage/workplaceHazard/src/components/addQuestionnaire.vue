<template>
  <div class="worker-investigation-container">
    <el-button type="primary" @click="openAddDialog" size="mini"
      >添加工作场所调查</el-button
    >

    <el-table
      :data="processedTableData"
      border
      style="width: 100%; margin-top: 15px"
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
      :span-method="objectSpanMethod"
      row-key="rowKey"
    >
      <!-- 工作场所名称列 -->
      <el-table-column
        prop="workplaceName"
        label="工作场所名称"
        align="center"
        width="180"
      ></el-table-column>

      <!-- 岗位编码 -->
      <el-table-column prop="jobNo" label="岗位编码" align="center" width="120">
        <template #default="{ row }">
          {{ getJobNameByNo(row.jobNo) }}
        </template>
      </el-table-column>

      <!-- 其他岗位名称 -->
      <el-table-column
        prop="jobNameOther"
        label="其他岗位"
        align="center"
        width="150"
      >
        <template #default="{ row }">
          {{ row.jobNameOther || '-' }}
        </template>
      </el-table-column>

      <!-- 岗位类型 -->
      <el-table-column
        prop="jobType"
        label="岗位类型"
        align="center"
        width="120"
      >
        <template #default="{ row }">
          {{ getJobTypeLabel(row.jobType) }}
        </template>
      </el-table-column>

      <!-- 岗位人数 -->
      <el-table-column
        prop="jobPersonNum"
        label="岗位人数"
        align="center"
        width="100"
      ></el-table-column>

      <!-- 危害因素信息 -->
      <el-table-column
        prop="workTime"
        label="工作时间"
        align="center"
        width="200"
      >
        <template #default="{ row }">
          {{ row.startTime }}{{ row.startTimeFlag === 2 ? '(次日)' : '' }} -
          {{ row.endTime }}{{ row.endTimeFlag === 2 ? '(次日)' : '' }}
        </template>
      </el-table-column>

      <el-table-column
        prop="workPlace"
        label="工作地点"
        align="center"
        width="150"
      ></el-table-column>

      <el-table-column
        prop="factorNames"
        label="危害因素"
        align="center"
        width="200"
      >
        <template #default="{ row }">
          {{ getFactorNamesByIds(row.factorId) }}
        </template></el-table-column
      >

      <el-table-column
        prop="contactHour"
        label="接触时间(h)"
        align="center"
        width="120"
      ></el-table-column>

      <el-table-column label="操作" align="center" width="150" fixed="right">
        <template #default="{ row }">
          <el-button
            size="mini"
            @click="editItem(row.workplaceIndex, row.jobIndex)"
            >编辑</el-button
          >
          <el-button
            size="mini"
            type="danger"
            @click="removeItem(row.workplaceIndex)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="95%"
      top="5vh"
      @close="resetDialogForm"
    >
      <el-form
        :model="dialogForm"
        :rules="dialogRules"
        ref="dialogForm"
        size="mini"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="工作场所名称"
              prop="workplaceName"
              label-width="180px"
            >
              <el-input
                v-model="dialogForm.workplaceName"
                placeholder="请输入工作场所名称"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left">岗位信息</el-divider>
        <el-table :data="dialogForm.jobList" border>
          <el-table-column
            prop="jobNo"
            label="岗位编码"
            align="center"
            width="120"
          >
            <template #default="{ row, $index }">
              <el-form-item
                :prop="'jobList.' + $index + '.jobNo'"
                :rules="dialogRules.jobNo"
              >
                <!-- :remote-method="handleJobRemoteSearch" 远程搜索有问题，使用本地搜索 -->
                <el-select
                  v-model="row.jobNo"
                  placeholder="请选择岗位编码"
                  filterable
                  remote
                  :loading="jobLoading"
                  @change="handleJobNoChange(row, $index)"
                >
                  <el-option
                    v-for="item in jobOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column prop="jobNameOther" label="其他岗位名称">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="'jobList.' + $index + '.jobNameOther'"
                :rules="
                  row.jobNo === '20070231' ? dialogRules.jobNameOther : []
                "
              >
                <el-input
                  v-model="row.jobNameOther"
                  placeholder="请输入其他岗位名称"
                  :disabled="row.jobNo !== '20070231'"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column prop="jobType" label="岗位类型" width="110">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="'jobList.' + $index + '.jobType'"
                :rules="dialogRules.jobType"
              >
                <el-radio-group v-model="row.jobType">
                  <el-radio :label="1">固定岗位</el-radio>
                  <el-radio :label="2">流动岗位</el-radio>
                  <el-radio :label="3">巡检岗位</el-radio>
                </el-radio-group>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column prop="jobPersonNum" label="岗位人数">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="'jobList.' + $index + '.jobPersonNum'"
                :rules="dialogRules.jobPersonNum"
              >
                <el-input-number
                  v-model="row.jobPersonNum"
                  :min="1"
                  :max="999999"
                  controls-position="right"
                ></el-input-number>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column prop="operateNum" label="每班人数">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="'jobList.' + $index + '.operateNum'"
                :rules="dialogRules.operateNum"
              >
                <el-input-number
                  v-model="row.operateNum"
                  :min="1"
                  :max="999999"
                  controls-position="right"
                ></el-input-number>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column prop="workType" label="工作班制" width="90">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="'jobList.' + $index + '.workType'"
                :rules="dialogRules.workType"
              >
                <el-radio-group
                  v-model="row.workType"
                  @change="handleWorkTypeChange(row)"
                >
                  <el-radio :label="1">轮班制</el-radio>
                  <el-radio :label="2">单班制</el-radio>
                </el-radio-group>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column prop="workShifts" label="工作班制类型">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="'jobList.' + $index + '.workShifts'"
                :rules="row.workType === 1 ? dialogRules.workShifts : []"
              >
                <el-select
                  v-model="row.workShifts"
                  placeholder="请选择班制类型"
                  :disabled="row.workType !== 1"
                  @change="handleWorkShiftsChange(row)"
                >
                  <el-option
                    v-for="item in workShiftsOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column
            prop="otherWorkShifts"
            label="其他班制数"
            width="150"
          >
            <template #default="{ row, $index }">
              <el-form-item
                :prop="'jobList.' + $index + '.otherWorkShifts'"
                :rules="
                  row.workShifts === 99 ? dialogRules.otherWorkShifts : []
                "
              >
                <el-input
                  v-model="row.otherWorkShifts"
                  placeholder="请输入其他班制数"
                  :disabled="row.workShifts !== 99"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column prop="totalHour" label="班制时长(h/d)">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="'jobList.' + $index + '.totalHour'"
                :rules="dialogRules.totalHour"
              >
                <el-input
                  v-model="row.totalHour"
                  placeholder="请输入班制时长"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column prop="weekDay" label="每周接触天数(d/w)">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="'jobList.' + $index + '.weekDay'"
                :rules="dialogRules.weekDay"
              >
                <el-select
                  v-model="row.weekDay"
                  placeholder="请选择每周接触天数"
                >
                  <el-option
                    v-for="day in 7"
                    :key="day"
                    :label="day"
                    :value="day"
                  ></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column prop="workerName" label="劳动者姓名">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="'jobList.' + $index + '.workerName'"
                :rules="dialogRules.workerName"
              >
                <el-input
                  v-model="row.workerName"
                  placeholder="请输入劳动者姓名"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="80">
            <template #default="{ $index }">
              <el-button
                type="text"
                size="mini"
                @click="removeJob($index)"
                style="color: #f56c6c"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <el-row style="margin-top: 10px">
          <el-col :span="24">
            <el-button type="primary" size="mini" @click="addJob"
              >添加岗位</el-button
            >
          </el-col>
        </el-row>

        <!-- 危害因素信息表格 - 现在放在每个岗位下面 -->
        <div
          v-for="(job, jobIndex) in dialogForm.jobList"
          :key="'job-' + jobIndex"
        >
          <el-divider content-position="left">
            岗位{{ jobIndex + 1 }}的危害因素信息
          </el-divider>

          <el-table
            :data="job.workerInvestigationFactorList"
            border
            style="margin-bottom: 20px"
          >
            <el-table-column prop="startTimeFlag" label="工作开始时间标记">
              <template #default="{ row, $index }">
                <el-form-item
                  :prop="
                    'jobList.' +
                    jobIndex +
                    '.workerInvestigationFactorList.' +
                    $index +
                    '.startTimeFlag'
                  "
                  :rules="dialogRules.startTimeFlag"
                >
                  <el-radio-group
                    v-model="row.startTimeFlag"
                    @change="handleStartTimeFlagChange(row)"
                  >
                    <el-radio :label="1">当日</el-radio>
                    <el-radio :label="2">次日</el-radio>
                  </el-radio-group>
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column prop="startTime" label="工作开始时间">
              <template #default="{ row, $index }">
                <el-form-item
                  :prop="
                    'jobList.' +
                    jobIndex +
                    '.workerInvestigationFactorList.' +
                    $index +
                    '.startTime'
                  "
                  :rules="dialogRules.startTime"
                >
                  <el-time-picker
                    v-model="row.startTime"
                    format="HH:mm"
                    value-format="HH:mm"
                    placeholder="选择时间"
                  ></el-time-picker>
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column prop="endTimeFlag" label="工作结束时间标记">
              <template #default="{ row, $index }">
                <el-form-item
                  :prop="
                    'jobList.' +
                    jobIndex +
                    '.workerInvestigationFactorList.' +
                    $index +
                    '.endTimeFlag'
                  "
                  :rules="dialogRules.endTimeFlag"
                  lable-width="0"
                >
                  <el-radio-group
                    v-model="row.endTimeFlag"
                    :disabled="row.startTimeFlag === 2"
                  >
                    <el-radio :label="1">当日</el-radio>
                    <el-radio :label="2">次日</el-radio>
                  </el-radio-group>
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column prop="endTime" label="工作结束时间">
              <template #default="{ row, $index }">
                <el-form-item
                  :prop="
                    'jobList.' +
                    jobIndex +
                    '.workerInvestigationFactorList.' +
                    $index +
                    '.endTime'
                  "
                  :rules="getEndTimeRules(row)"
                  lable-width="0"
                >
                  <el-time-picker
                    v-model="row.endTime"
                    format="HH:mm"
                    value-format="HH:mm"
                    placeholder="选择时间"
                    :disabled="!row.startTime"
                  ></el-time-picker>
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column prop="workPlace" label="工作地点">
              <template #default="{ row, $index }">
                <el-form-item
                  :prop="
                    'jobList.' +
                    jobIndex +
                    '.workerInvestigationFactorList.' +
                    $index +
                    '.workPlace'
                  "
                  :rules="dialogRules.workPlace"
                  lable-width="0"
                >
                  <el-input
                    v-model="row.workPlace"
                    placeholder="请输入工作地点"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column prop="workContent" label="工作内容">
              <template #default="{ row, $index }">
                <el-form-item
                  :prop="
                    'jobList.' +
                    jobIndex +
                    '.workerInvestigationFactorList.' +
                    $index +
                    '.workContent'
                  "
                  :rules="dialogRules.workContent"
                  lable-width="0"
                >
                  <el-input
                    v-model="row.workContent"
                    placeholder="请输入工作内容"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column prop="factorId" label="危害因素">
              <template #default="{ row, $index }">
                <el-form-item
                  :prop="
                    'jobList.' +
                    jobIndex +
                    '.workerInvestigationFactorList.' +
                    $index +
                    '.factorId'
                  "
                  :rules="dialogRules.factorId"
                  lable-width="0"
                >
                  <!-- <el-select
                    v-model="row.factorId"
                    multiple
                    placeholder="请选择危害因素"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in factorOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select> -->

                  <!-- :filter-method="filterFactor" 远程搜索 暂时不用 -->
                  <el-select
                    v-model="row.factorId"
                    placeholder="请选择"
                    clearable
                    filterable
                    multiple
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in filteredFactorOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                      <template v-if="item.name.length > 30">
                        <el-tooltip :content="item.name" placement="top">
                          <span>{{ item.name.substring(0, 30) + '...' }}</span>
                        </el-tooltip>
                      </template>
                      <template v-else>
                        <span>{{ item.name }}</span>
                      </template>
                    </el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column prop="contactHour" label="接触时间(h)">
              <template #default="{ row, $index }">
                <el-form-item
                  :prop="
                    'jobList.' +
                    jobIndex +
                    '.workerInvestigationFactorList.' +
                    $index +
                    '.contactHour'
                  "
                  :rules="dialogRules.contactHour"
                  lable-width="0"
                >
                  <el-input-number
                    v-model="row.contactHour"
                    :min="0"
                    :precision="2"
                    :step="0.1"
                    controls-position="right"
                  ></el-input-number>
                </el-form-item>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="80">
              <template #default="{ $index }">
                <el-button
                  type="text"
                  size="mini"
                  @click="removeFactor(jobIndex, $index)"
                  style="color: #f56c6c"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <el-row style="margin-top: 10px; margin-bottom: 20px">
            <el-col :span="24">
              <el-button type="primary" size="mini" @click="addFactor(jobIndex)"
                >添加危害因素</el-button
              >
            </el-col>
          </el-row>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="mini">取 消</el-button>
        <el-button type="primary" @click="submitDialogForm" size="mini"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import { cloneDeep } from 'lodash';
import { getJobList, getFactorListNation, previewWorker } from '@/api';
export default {
  name: 'firstMessage',
  components: {
    TitleTag,
  },
  props: {
    tableData: {
      type: Array,
      default: [],
    },
    taskId: {
      type: String,
    },
    EnterpriseID: {
      type: String,
    },
  },
  data() {
    // 自定义验证规则
    const validateWorkplaceName = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入工作场所名称'));
      } else if (this.dialogVisible && this.currentEditIndex === -1) {
        // 添加时检查名称是否重复
        const exists = this.form.workerInvestigationSituationList.some(
          item => item.workplaceName === value
        );
        if (exists) {
          callback(new Error('工作场所名称不能重复'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };

    const validateTotalHour = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入班制时长'));
      } else if (
        isNaN(value) ||
        parseFloat(value) <= 0 ||
        parseFloat(value) > 24
      ) {
        callback(new Error('班制时长必须在1-24小时之间'));
      } else {
        callback();
      }
    };

    const validateContactHourSum = (rule, value, callback, job) => {
      if (
        !job.workerInvestigationFactorList ||
        job.workerInvestigationFactorList.length === 0
      ) {
        callback();
        return;
      }

      const total = parseFloat(value);
      const sum = job.workerInvestigationFactorList.reduce(
        (acc, factor) => acc + parseFloat(factor.contactHour || 0),
        0
      );

      if (job.workType === 1) {
        // 轮班制：班制时长必须等于接触时间之和
        if (Math.abs(total - sum) > 0.01) {
          callback(new Error('轮班制的班制时长必须等于各接触时间之和'));
        } else {
          callback();
        }
      } else {
        // 单班制：班制时长必须大于等于接触时间之和
        if (total < sum) {
          callback(new Error('单班制的班制时长必须大于等于各接触时间之和'));
        } else {
          callback();
        }
      }
    };

    return {
      factorOptions: [],
      form: {
        workerInvestigationSituationList: [],
      },
      dialogVisible: false,
      dialogTitle: '添加工作场所调查',
      currentEditIndex: -1,
      dialogForm: {
        workplaceName: '',
        jobList: [
          {
            jobNo: '',
            jobNameOther: '',
            jobType: undefined,
            jobPersonNum: undefined,
            operateNum: undefined,
            workType: undefined,
            workShifts: undefined,
            otherWorkShifts: undefined,
            totalHour: '',
            weekDay: undefined,
            workerName: '',
            workerInvestigationFactorList: [
              {
                startTimeFlag: 1,
                startTime: '',
                endTimeFlag: 1,
                endTime: '',
                workPlace: '',
                workContent: '',
                factorId: [],
                contactHour: undefined,
              },
            ],
          },
        ],
      },
      dialogRules: {
        workplaceName: [{ validator: validateWorkplaceName, trigger: 'blur' }],
        jobNo: [
          { required: true, message: '请选择岗位编码', trigger: 'change' },
        ],
        jobNameOther: [
          { required: true, message: '请输入其他岗位名称', trigger: 'blur' },
        ],
        jobType: [
          { required: true, message: '请选择岗位类型', trigger: 'change' },
        ],
        jobPersonNum: [
          { required: true, message: '请输入岗位人数', trigger: 'blur' },
          {
            type: 'number',
            min: 1,
            max: 999999,
            message: '岗位人数必须在1-999999之间',
            trigger: 'blur',
          },
        ],
        operateNum: [
          { required: true, message: '请输入每班人数', trigger: 'blur' },
          {
            type: 'number',
            min: 1,
            max: 999999,
            message: '每班人数必须在1-999999之间',
            trigger: 'blur',
          },
        ],
        workType: [
          { required: true, message: '请选择工作班制', trigger: 'change' },
        ],
        workShifts: [
          { required: true, message: '请选择工作班制类型', trigger: 'change' },
        ],
        otherWorkShifts: [
          { required: true, message: '请输入其他班制数', trigger: 'blur' },
        ],
        totalHour: [
          { required: true, message: '请输入班制时长', trigger: 'blur' },
          { validator: validateTotalHour, trigger: 'blur' },
          { validator: validateContactHourSum, trigger: 'blur' },
        ],
        weekDay: [
          { required: true, message: '请选择每周接触天数', trigger: 'change' },
        ],
        workerName: [
          { required: true, message: '请输入劳动者姓名', trigger: 'blur' },
        ],
        startTimeFlag: [
          {
            required: true,
            message: '请选择工作开始时间标记',
            trigger: 'change',
          },
        ],
        startTime: [
          { required: true, message: '请选择工作开始时间', trigger: 'change' },
        ],
        endTimeFlag: [
          {
            required: true,
            message: '请选择工作结束时间标记',
            trigger: 'change',
          },
        ],
        workPlace: [
          { required: true, message: '请输入工作地点', trigger: 'blur' },
        ],
        workContent: [
          { required: true, message: '请输入工作内容', trigger: 'blur' },
        ],
        factorId: [
          { required: true, message: '请选择危害因素', trigger: 'change' },
        ],
        contactHour: [
          { required: true, message: '请输入接触时间', trigger: 'blur' },
          {
            type: 'number',
            min: 0,
            message: '接触时间不能为负数',
            trigger: 'blur',
          },
        ],
      },
      // 下拉选项数据
      jobOptions: [
        // { value: '20070101', label: '生产岗位' },
        // { value: '20070231', label: '其他岗位' },
        // { value: '20070305', label: '管理岗位' },
      ],
      jobLoading: false, // 加载状态
      jobSearchKeyword: '', // 搜索关键词
      workShiftsOptions: [
        { value: 1, label: '两班两运转' },
        { value: 2, label: '三班两运转' },
        { value: 3, label: '三班三运转' },
        { value: 4, label: '四班三运转' },
        { value: 5, label: '五班三运转' },
        { value: 6, label: '五班四运转' },
        { value: 99, label: '其他' },
      ],
      filteredFactorOptions: [
        // { value: 'F001', label: '粉尘' },
        // { value: 'C001', label: '化学物质' },
        // { value: 'N001', label: '噪声' },
        // { value: 'R001', label: '辐射' },
      ],
      spanArr: [], // 用于存储合并信息
    };
  },
  computed: {
    // 处理原始数据为扁平化结构，便于表格展示
    processedTableData() {
      const result = [];
      this.form.workerInvestigationSituationList.forEach(
        (workplace, wpIndex) => {
          // 计算该工作场所下所有危害因素总数
          let totalFactors = 0;
          workplace.jobList.forEach(job => {
            totalFactors += job.workerInvestigationFactorList.length || 1;
          });

          workplace.jobList.forEach((job, jobIndex) => {
            if (job.workerInvestigationFactorList.length === 0) {
              // 如果没有危害因素，添加一条空记录
              result.push({
                ...job,
                workplaceName: workplace.workplaceName,
                workplaceIndex: wpIndex,
                jobIndex: jobIndex,
                rowKey: `${wpIndex}-${jobIndex}-0`,
                factorNames: '无',
                startTime: '',
                endTime: '',
                workPlace: '',
                contactHour: '',
                workplaceRowspan: totalFactors, // 记录工作场所合并行数
                jobRowspan: 1, // 记录岗位合并行数
              });
            } else {
              // 每个危害因素生成一行数据
              job.workerInvestigationFactorList.forEach(
                (factor, factorIndex) => {
                  result.push({
                    ...job,
                    ...factor,
                    workplaceName: workplace.workplaceName,
                    workplaceIndex: wpIndex,
                    jobIndex: jobIndex,
                    rowKey: `${wpIndex}-${jobIndex}-${factorIndex}`,
                    factorNames: factor.factorId.join(', '),
                    workplaceRowspan: totalFactors, // 记录工作场所合并行数
                    jobRowspan: job.workerInvestigationFactorList.length, // 记录岗位合并行数
                  });
                }
              );
            }
          });
        }
      );
      return result;
    },
    // 生成合并配置信息
    spanConfig() {
      const config = {
        workplace: {}, // 工作场所合并配置
        job: {}, // 岗位合并配置
      };

      this.processedTableData.forEach((row, rowIndex) => {
        const wpKey = row.workplaceIndex;
        const jobKey = `${row.workplaceIndex}-${row.jobIndex}`;

        // 工作场所合并配置
        if (!config.workplace[wpKey]) {
          config.workplace[wpKey] = {
            start: rowIndex,
            span: row.workplaceRowspan,
          };
        }

        // 岗位合并配置
        if (!config.job[jobKey]) {
          config.job[jobKey] = {
            start: rowIndex,
            span: row.jobRowspan,
          };
        }
      });

      return config;
    },
  },
  watch: {
    'form.workerInvestigationSituationList': {
      handler(newVal) {
        this.getSpanArr(newVal);
      },
      deep: true,
    },
    tableData: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.form.workerInvestigationSituationList = cloneDeep(newVal);
        } else {
          this.form.workerInvestigationSituationList = [];
        }
      },
    },
  },
  created() {
    // 初始加载岗位列表
    this.fetchJobList();
    // 危害因素
    this.fetchFactorList();
  },
  methods: {
    // 根据岗位编码获取岗位名称
    getJobNameByNo(jobNo) {
      if (!jobNo) return '-';
      // 从岗位选项中查找对应的名称（jobOptions 是岗位编码列表数据）
      const job = this.jobOptions.find(item => item.value === jobNo);
      return job ? job.jobName : jobNo; // 找不到时显示原始编码
    },
    // 根据危害因素ID列表获取名称列表
    getFactorNamesByIds(factorIds) {
      if (!Array.isArray(factorIds) || factorIds.length === 0) return '无';
      // factorOptions 是危害因素列表数据（包含id和name）
      return factorIds
        .map(id => {
          const factor = this.factorOptions.find(item => item.id === id);
          return factor ? factor.name : `未知(${id})`; // 找不到时显示“未知(ID)”
        })
        .join(', '); // 用逗号分隔多个危害因素
    },
    async fetchFactorList() {
      try {
        // const params = { taskId: this.taskId };
        const res = await getFactorListNation();

        this.factorOptions = res.data.flatMap(category => {
          return category.children.map(child => ({
            category: category._id,
            id: child._id,
            name: child.chineseName.join('、'),
            aliases: child.chineseName.slice(1),
          }));
        });

        this.filteredFactorOptions = [...this.factorOptions];
      } catch (error) {
        console.error('获取监测因素列表失败:', error);
        this.$message.error('获取监测因素列表失败');
      }
    },

    filterFactor(query) {
      // if (!query) {
      //   this.filteredFactorOptions = [...this.factorOptions];
      //   return;
      // }
      // const lowerQuery = query.toLowerCase();
      // this.filteredFactorOptions = this.factorOptions.filter((item) => {
      //   if (item.name.toLowerCase().includes(lowerQuery)) {
      //     return true;
      //   }
      //   if (item.aliases) {
      //     return item.aliases.some((alias) =>
      //       alias.toLowerCase().includes(lowerQuery)
      //     );
      //   }
      //   return false;
      // });
    },
    // 处理岗位编码远程搜索
    handleJobRemoteSearch(keyword) {
      // 延迟搜索，避免频繁请求
      clearTimeout(this.jobSearchTimer);
      this.jobSearchTimer = setTimeout(() => {
        this.fetchJobList(keyword);
      }, 300);
    },

    // 修改岗位编码变更处理方法，可获取岗位名称
    handleJobNoChange(row, index) {
      const selectedJob = this.jobOptions.find(
        item => item.value === row.jobNo
      );
      if (row.jobNo !== '20070231') {
        row.jobNameOther = '';
        // 清除jobNameOther字段的验证状态
        this.$refs.dialogForm.clearValidate(`jobList.${index}.jobNameOther`);
      }
      if (selectedJob) {
        row.jobName = selectedJob.jobName;
        this.dialogForm.jobList.forEach((job, jobIndex) => {
          if (job.jobNo === row.jobNo) {
            job.jobName = selectedJob.jobName;
          }
        });
      }
      // 重新验证jobNameOther字段
      this.$refs.dialogForm.validateField(`jobList.${index}.jobNameOther`);
    },
    // 获取岗位编码列表
    async fetchJobList(keyword = '') {
      this.jobLoading = true;
      try {
        const response = await getJobList({ keyword });
        // 转换接口返回格式为el-select需要的{label, value}格式
        this.jobOptions = response.data.list.map(item => ({
          label: `${item.jobName} - ${item.cnCsjcNo}`, // 显示编码和名称
          value: item.cnCsjcNo, // 值为岗位编码
          jobName: item.jobName, // 保存岗位名称用于后续使用
        }));
      } catch (error) {
        this.$message.error('获取岗位编码列表失败');
        console.error(error);
      } finally {
        this.jobLoading = false;
      }
    },
    // 时间字符串转换为分钟数
    parseTime(timeStr) {
      if (!timeStr) return 0;
      const [hours, minutes] = timeStr.split(':').map(Number);
      return hours * 60 + minutes;
    },
    // 验证结束时间
    validateEndTime(rule, value, callback, row) {
      if (!value) {
        callback(new Error('请选择工作结束时间'));
      } else if (row.startTime) {
        const start = this.parseTime(row.startTime);
        const end = this.parseTime(value);

        if (row.startTimeFlag === 1 && row.endTimeFlag === 1) {
          // 都是当日
          if (end <= start) {
            callback(new Error('结束时间必须大于开始时间'));
          } else {
            callback();
          }
        } else if (row.startTimeFlag === 1 && row.endTimeFlag === 2) {
          // 开始当日，结束次日
          callback();
        } else if (row.startTimeFlag === 2 && row.endTimeFlag === 2) {
          // 都是次日
          if (end <= start) {
            callback(new Error('结束时间必须大于开始时间'));
          } else {
            callback();
          }
        }
      } else {
        callback();
      }
    },

    // 获取结束时间验证规则
    getEndTimeRules(row) {
      return [
        { required: true, message: '请选择工作结束时间', trigger: 'change' },
        {
          validator: (rule, value, callback) => {
            this.validateEndTime(rule, value, callback, row);
          },
          trigger: 'change',
        },
      ];
    },

    // 工作班制变化处理
    handleWorkTypeChange(row) {
      if (row.workType === 2) {
        // 单班制时清空班制类型
        row.workShifts = undefined;
        row.otherWorkShifts = undefined;
      }
    },

    // 班制类型变化处理
    handleWorkShiftsChange(row) {
      if (row.workShifts !== 99) {
        row.otherWorkShifts = undefined;
      }
    },

    // 工作开始时间标记变化处理
    handleStartTimeFlagChange(row) {
      if (row.startTimeFlag === 2) {
        // 开始时间标记为次日时，结束时间标记必须为次日
        row.endTimeFlag = 2;
      }
    },

    // 打开添加对话框
    async openAddDialog() {
      this.dialogTitle = '添加工作场所调查';
      this.currentEditIndex = -1;
      this.dialogVisible = true;
      if (this.processedTableData.length == 0) {
        // 预填写
        const res = await previewWorker({
          EnterpriseID: this.EnterpriseID,
        });
        if (res.status == 200) {
          this.previewList = res.data;
          // 过滤，是否存在国家岗位里面
          const jobNameSet = new Map(
            this.jobOptions.map(option => [option.jobName, option.value])
          );

          // 过滤previewList，只保留workTypeName存在于jobNameSet中的项
          this.filteredPreviewList = this.previewList.filter(item =>
            jobNameSet.has(item.workTypeName)
          );
          if (this.filteredPreviewList.length > 0) {
            
            this.dialogForm.workplaceName =
              this.filteredPreviewList[0].workspaceName;
            this.dialogForm.jobList = this.filteredPreviewList.map(item => ({
              jobNo: jobNameSet.get(item.workTypeName),
              jobNameOther: '',
              jobType: undefined,
              jobPersonNum: undefined,
              operateNum: undefined,
              workType: undefined,
              workShifts: undefined,
              otherWorkShifts: undefined,
              totalHour: item.exposureHours,
              weekDay: item.workDays,
              workerName: item.employees.map(e => e.name).join(','),
              workerInvestigationFactorList: item.stations.map(e => ({
                startTimeFlag: 1,
                startTime: '',
                endTimeFlag: 1,
                endTime: '',
                workPlace: '',
                workContent: '',
                factorId: this.filteredFactorOptions
                  .filter(option => e.harmFactors.includes(option.name))
                  .map(options => options.id),
                contactHour: e.weeklyExposureHours,
              })),
            }));
          }
        }
      }
    },

    // 添加岗位
    addJob() {
      this.dialogForm.jobList.push({
        jobNo: '',
        jobNameOther: '',
        jobType: undefined,
        jobPersonNum: undefined,
        operateNum: undefined,
        workType: undefined,
        workShifts: undefined,
        otherWorkShifts: undefined,
        totalHour: '',
        weekDay: undefined,
        workerName: '',
        workerInvestigationFactorList: [
          {
            startTimeFlag: 1,
            startTime: '',
            endTimeFlag: 1,
            endTime: '',
            workPlace: '',
            workContent: '',
            factorId: [],
            contactHour: undefined,
          },
        ],
      });
    },

    // 删除岗位
    removeJob(index) {
      if (this.dialogForm.jobList.length > 1) {
        this.dialogForm.jobList.splice(index, 1);
      } else {
        this.$message.warning('至少需要保留一个岗位');
      }
    },

    // 添加危害因素
    addFactor(jobIndex) {
      this.dialogForm.jobList[jobIndex].workerInvestigationFactorList.push({
        startTimeFlag: 1,
        startTime: '',
        endTimeFlag: 1,
        endTime: '',
        workPlace: '',
        workContent: '',
        factorId: [],
        contactHour: undefined,
      });
    },

    // 删除危害因素
    removeFactor(jobIndex, factorIndex) {
      const factors =
        this.dialogForm.jobList[jobIndex].workerInvestigationFactorList;
      if (factors.length > 1) {
        factors.splice(factorIndex, 1);
      } else {
        this.$message.warning('至少需要保留一个危害因素');
      }
    },

    // 编辑项目
    editItem(index) {
      this.dialogTitle = '编辑工作场所调查';
      this.currentEditIndex = index;
      // 深拷贝数据
      const originalData = cloneDeep(
        this.form.workerInvestigationSituationList[index]
      );
      // 处理历史数据中factorId不是数组的情况
      originalData.jobList.forEach(job => {
        job.workerInvestigationFactorList.forEach(factor => {
          if (!Array.isArray(factor.factorId)) {
            factor.factorId = factor.factorId ? [factor.factorId] : [];
          }
        });
      });
      this.dialogForm = originalData;
      this.dialogVisible = true;
    },

    // 删除项目
    removeItem(index) {
      this.$confirm('确定要删除该工作场所调查吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.form.workerInvestigationSituationList.splice(index, 1);
          this.$emit(
            'processedTableData',
            this.form.workerInvestigationSituationList
          );
          this.$message.success('删除成功');
        })
        .catch(() => {});
    },

    // 提交对话框表单
    submitDialogForm() {
      this.$refs.dialogForm.validate(valid => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.dialogForm));

          // 处理数据格式
          formData.jobList.forEach(job => {
            job.jobPersonNum = Number(job.jobPersonNum);
            job.operateNum = Number(job.operateNum);
            job.totalHour = String(job.totalHour);
            if (job.otherWorkShifts) {
              job.otherWorkShifts = Number(job.otherWorkShifts);
            }

            job.workerInvestigationFactorList.forEach(factor => {
              factor.contactHour = Number(factor.contactHour);
            });
          });

          if (this.currentEditIndex >= 0) {
            // 编辑现有项
            this.form.workerInvestigationSituationList.splice(
              this.currentEditIndex,
              1,
              formData
            );
          } else {
            // 添加新项
            this.form.workerInvestigationSituationList.push(formData);
          }
          this.$emit(
            'processedTableData',
            this.form.workerInvestigationSituationList
          );
          this.dialogVisible = false;
          this.$message.success('操作成功');
        } else {
          return false;
        }
      });
    },

    // 重置对话框表单
    resetDialogForm() {
      this.dialogForm = {
        workplaceName: '',
        jobList: [
          {
            jobNo: '',
            jobNameOther: '',
            jobType: undefined,
            jobPersonNum: undefined,
            operateNum: undefined,
            workType: undefined,
            workShifts: undefined,
            otherWorkShifts: undefined,
            totalHour: '',
            weekDay: undefined,
            workerName: '',
            workerInvestigationFactorList: [
              {
                startTimeFlag: 1,
                startTime: '',
                endTimeFlag: 1,
                endTime: '',
                workPlace: '',
                workContent: '',
                factorId: [],
                contactHour: undefined,
              },
            ],
          },
        ],
      };
      this.$refs.dialogForm && this.$refs.dialogForm.clearValidate();
    },

    // 获取岗位类型标签
    getJobTypeLabel(type) {
      const map = { 1: '固定岗位', 2: '流动岗位', 3: '巡检岗位' };
      return map[type] || '';
    },
    // 计算合并行的方法
    getSpanArr(data) {
      this.spanArr = [];
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (this.shouldMerge(data[i], data[i - 1])) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    },

    // 判断是否需要合并
    shouldMerge(current, previous) {
      // 这里根据你的业务逻辑判断是否需要合并
      // 例如：如果工作场所名称相同则合并
      return current.workplaceName === previous.workplaceName;
    },

    // 合并单元格方法
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 工作场所名称列(第0列)和操作列(最后一列)合并
      if (columnIndex === 0 || columnIndex === 9) {
        const wpConfig = this.spanConfig.workplace[row.workplaceIndex];
        if (rowIndex === wpConfig.start) {
          return {
            rowspan: wpConfig.span,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }

      // 岗位信息列(1-4列)合并
      if (columnIndex >= 1 && columnIndex <= 4) {
        const jobKey = `${row.workplaceIndex}-${row.jobIndex}`;
        const jobConfig = this.spanConfig.job[jobKey];
        if (rowIndex === jobConfig.start) {
          return {
            rowspan: jobConfig.span,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }

      // 危害因素信息列(5-8列)不合并
      return {
        rowspan: 1,
        colspan: 1,
      };
    },

    // 判断是否是某个岗位的第一个危害因素行
    isFirstFactorRow(rowKey) {
      const [wpIndex, jobIndex, factorIndex] = rowKey.split('-').map(Number);
      return factorIndex === 0;
    },

    // 获取某个岗位的危害因素数量
    getFactorCount(workplaceIndex, jobIndex) {
      const workplace =
        this.form.workerInvestigationSituationList[workplaceIndex];
      if (!workplace || !workplace.jobList[jobIndex]) return 1;
      const factors = workplace.jobList[jobIndex].workerInvestigationFactorList;
      return factors.length > 0 ? factors.length : 1;
    },
  },
};
</script>
<style scoped>
.el-divider {
  margin: 20px 0;
}
.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}
.worker-investigation-container {
  padding: 20px;
}

.job-item {
  margin-bottom: 8px;
  padding: 5px;
  border-bottom: 1px dashed #eee;
}

.factor-section {
  margin-top: 8px;
  padding-left: 10px;
  border-left: 2px solid #eee;
}

.factor-title {
  font-weight: bold;
  margin-bottom: 5px;
}

/* .factor-item {
  margin-bottom: 8px;
  padding: 5px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.job-item:last-child,
.factor-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
} */

.el-divider {
  margin: 20px 0;
}

.sub-title {
  font-size: 16px;
  font-weight: bold;
  color: #606266;
  margin-bottom: 15px;
}

.dialog-footer {
  text-align: right;
}
</style>
<style scoped>
/* 调整样式以适应合并后的显示 */
.worker-investigation-container {
  padding: 20px;
}

.factor-item {
  padding: 8px 0;
  border-bottom: 1px dashed #eee;
}

.factor-item:last-child {
  border-bottom: none;
}

.el-table .cell {
  white-space: pre-line;
  padding: 8px 0;
}

/* 合并行的样式调整 */
.merged-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>
