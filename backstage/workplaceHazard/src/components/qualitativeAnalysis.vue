<template>
  <div>
    <TitleTag titleName="定性分析情况列表"></TitleTag>
    <el-form
      :model="formData"
      :rules="rules"
      ref="formData"
      label-width="200px"
      size="mini"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item
            prop="ifExistAnalysis"
            label="是否存在混合性有机溶剂"
            label-width="250px"
          >
            <el-radio-group
              v-model="formData.ifExistAnalysis"
              @change="handleExistChange"
            >
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item
            prop="ifAnalysis"
            label="是否开展有机溶剂定性分析"
            label-width="250px"
            :required="formData.ifExistAnalysis"
          >
            <el-radio-group
              v-model="formData.ifAnalysis"
              @change="handleAnalysisChange"
            >
              <el-radio :label="1">是</el-radio>
              <el-radio :label="2">否</el-radio>
              <el-radio :label="3">未监测有机溶剂</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item
            prop="sdsFileName"
            label="SDS资料附件名称"
            label-width="250px"
            :required="formData.ifAnalysis === 3"
          >
            <el-input
              v-model="formData.sdsFileName"
              placeholder="请输入SDS资料附件名称"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-divider></el-divider>
          <div class="table-title">定性分析物质列表</div>
          <el-button type="primary" size="mini" @click="openAddDialog"
            >添加物质</el-button
          >
          <el-table
            :data="formData.analysisSituationList"
            border
            style="width: 100%; margin-top: 10px"
            header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
          >
            <el-table-column
              prop="substanceName"
              label="物质名称"
            ></el-table-column>
            <el-table-column prop="jobNos" label="涉及岗位编码" width="200">
              <template #default="{ row }">
                {{ getJobNames(row.jobNos).join('，') }}
              </template>
            </el-table-column>
            <el-table-column
              prop="jobNameOther"
              label="其他岗位名称"
            ></el-table-column>
            <el-table-column label="定性分析危害因素" min-width="300">
              <template #default="{ row }">
                <div
                  v-for="(factor, index) in row.analysisFactorList"
                  :key="index"
                  class="factor-item"
                >
                  <span
                    >{{ getFactorName(factor.factorId) }} -
                    {{ factor.ifDetection ? '检出' : '未检出' }}</span
                  >
                  <span v-if="factor.ifDetection"
                    >(含量: {{ factor.analysisValue }}%)</span
                  >
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="{ row, $index }">
                <el-button
                  size="mini"
                  type="text"
                  @click="removeItem($index)"
                  style="color: #f56c6c"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>

      <el-dialog
        :title="dialogTitle"
        :visible.sync="dialogVisible"
        width="800px"
        @close="resetDialogForm"
      >
        <el-form
          :model="dialogForm"
          :rules="dialogRules"
          ref="dialogForm"
          label-width="auto"
          size="mini"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item prop="substanceName" label="物质名称">
                <el-input
                  v-model="dialogForm.substanceName"
                  placeholder="请输入物质名称"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item prop="jobNos" label="涉及岗位编码">
                <el-select
                  v-model="dialogForm.jobNos"
                  multiple
                  filterable
                  reserve-keyword
                  placeholder="请选择岗位编码"
                  :filter-method="filterJobList"
                  style="width: 100%"
                  value-key="cnCsjcNo"
                >
                  <el-option
                    v-for="item in filteredJobList"
                    :key="item.cnCsjcNo"
                    :label="item.jobName"
                    :value="item"
                  >
                    <span>{{ item.jobName }}</span>
                    <span
                      style="float: right; color: #8492a6; font-size: 13px"
                      >{{ item.cnCsjcNo }}</span
                    >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item
                prop="jobNameOther"
                label="其他岗位名称"
                :required="requireJobNameOther"
              >
                <el-input
                  v-model="dialogForm.jobNameOther"
                  placeholder="请输入其他岗位名称"
                  :disabled="!requireJobNameOther"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider></el-divider>
          <div class="sub-title">定性分析危害因素</div>

          <el-row>
            <el-col :span="24">
              <el-table :data="dialogForm.analysisFactorList" border>
                <el-table-column prop="factorId" label="监测因素NO">
                  <template #default="{ row, $index }">
                    <el-form-item
                      label-width="0"
                      :prop="'analysisFactorList.' + $index + '.factorId'"
                      :rules="dialogRules.factorId"
                    >
                      <!-- <el-select
                        v-model="row.factorId"
                        placeholder="请选择"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="item in factorOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select> -->
                      <el-select
                        v-model="row.factorId"
                        placeholder="请选择"
                        clearable
                        filterable
                        :filter-method="filterFactor"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="item in filteredFactorOptions"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                        >
                          <template v-if="item.name.length > 30">
                            <el-tooltip :content="item.name" placement="top">
                              <span>{{
                                item.name.substring(0, 30) + '...'
                              }}</span>
                            </el-tooltip>
                          </template>
                          <template v-else>
                            <span>{{ item.name }}</span>
                          </template>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>

                <el-table-column prop="ifDetection" label="是否检出">
                  <template #default="{ row, $index }">
                    <el-form-item
                      label-width="0"
                      :prop="'analysisFactorList.' + $index + '.ifDetection'"
                      :rules="dialogRules.ifDetection"
                    >
                      <el-radio-group v-model="row.ifDetection">
                        <el-radio :label="1">检出</el-radio>
                        <el-radio :label="0">未检出</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </template>
                </el-table-column>

                <el-table-column prop="analysisValue" label="含量(%)">
                  <template #default="{ row, $index }">
                    <el-form-item
                      label-width="0"
                      :prop="'analysisFactorList.' + $index + '.analysisValue'"
                      :rules="row.ifDetection ? dialogRules.analysisValue : []"
                    >
                      <el-input-number
                        v-model="row.analysisValue"
                        :min="0"
                        :max="100"
                        :precision="2"
                        :step="0.1"
                        :disabled="row.ifDetection !== 1"
                        style="width: 100%"
                      ></el-input-number>
                    </el-form-item>
                  </template>
                </el-table-column>

                <el-table-column label="操作">
                  <template #default="{ $index }">
                    <el-button
                      type="text"
                      size="mini"
                      @click="removeFactor($index)"
                      style="color: #f56c6c"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>

          <el-row style="margin-top: 10px">
            <el-col :span="24">
              <el-button type="primary" size="mini" @click="addFactor"
                >添加危害因素</el-button
              >
              <span class="form-tip" style="margin-left: 10px"
                >至少需要添加一个危害因素，且至少有一个检出</span
              >
            </el-col>
          </el-row>
        </el-form>

        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false" size="mini"
            >取 消</el-button
          >
          <el-button type="primary" @click="submitDialogForm" size="mini"
            >确 定</el-button
          >
        </div>
      </el-dialog>
      <el-row style="margin-top: 20px">
        <el-col :span="24">
          <el-form-item label-width="20px">
            <el-button type="primary" size="medium" @click="nextStep"
              >下一步</el-button
            >
            <el-button size="medium" @click="saveShortTime">暂存</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import {
  getJobList,
  getFactorListNation,
  monitoringAnalyse,
  getMonitoringAnalyseDetail,
} from '@/api';

export default {
  components: {
    TitleTag,
  },
  props: {
    taskId: {
      type: String,
    },
  },
  data() {
    const validateIfAnalysis = (rule, value, callback) => {
      if (this.formData.ifExistAnalysis && value === undefined) {
        callback(new Error('当存在混合性有机溶剂时，此项为必填'));
      } else {
        callback();
      }
    };

    const validateSdsFileName = (rule, value, callback) => {
      if (this.formData.ifAnalysis === 3 && !value) {
        callback(new Error('当选择"未监测有机溶剂"时，SDS资料附件名称为必填'));
      } else {
        callback();
      }
    };

    const validateJobNameOther = (rule, value, callback) => {
      if (this.requireJobNameOther && !value) {
        callback(new Error('当涉及岗位编码包含20070231时，其他岗位名称为必填'));
      } else {
        callback();
      }
    };

    const validateFactorDetection = (rule, value, callback) => {
      const hasDetection = this.dialogForm.analysisFactorList.some(
        (item) => item.ifDetection === 1
      );
      if (!hasDetection) {
        callback(new Error('至少需要一个危害因素检出'));
      } else {
        callback();
      }
    };

    return {
      formData: {
        ifExistAnalysis: null,
        ifAnalysis: null,
        sdsFileName: '',
        analysisSituationList: [],
      },
      rules: {
        ifExistAnalysis: [
          {
            required: true,
            message: '请选择是否存在混合性有机溶剂',
            trigger: 'change',
          },
        ],
        ifAnalysis: [{ validator: validateIfAnalysis, trigger: 'change' }],
        sdsFileName: [{ validator: validateSdsFileName, trigger: 'blur' }],
        analysisSituationList: [
          {
            validator: (rule, value, callback) => {
              if (this.showAnalysisList && (!value || value.length === 0)) {
                callback(new Error('请至少添加一个物质'));
              } else {
                callback();
              }
            },
            trigger: 'change',
          },
        ],
      },

      dialogVisible: false,
      dialogTitle: '添加物质',
      currentEditIndex: -1,
      dialogForm: {
        substanceName: '',
        jobNos: [],
        jobNameOther: '',
        analysisFactorList: [
          { factorId: '', ifDetection: 0, analysisValue: null },
        ],
      },
      dialogRules: {
        substanceName: [
          { required: true, message: '请输入物质名称', trigger: 'blur' },
          { max: 100, message: '长度不能超过100个字符', trigger: 'blur' },
        ],
        jobNos: [
          { required: true, message: '请选择涉及岗位编码', trigger: 'change' },
          {
            type: 'array',
            min: 1,
            message: '至少选择一个岗位编码',
            trigger: 'change',
          },
        ],
        jobNameOther: [
          { validator: validateJobNameOther, trigger: 'blur' },
          { max: 500, message: '长度不能超过500个字符', trigger: 'blur' },
        ],
        factorId: [
          { required: true, message: '请选择监测因素NO', trigger: 'change' },
          // { max: 20, message: '长度不能超过20个字符', trigger: 'blur' },
        ],
        ifDetection: [
          { required: true, message: '请选择是否检出', trigger: 'change' },
        ],
        analysisValue: [
          { required: true, message: '请输入含量(%)', trigger: 'blur' },
          {
            type: 'number',
            min: 0,
            max: 100,
            message: '含量必须在0-100之间',
            trigger: 'blur',
          },
          {
            validator: (rule, value, callback) => {
              if (value && value.toString().length > 15) {
                callback(new Error('长度不能超过15个字符'));
              } else {
                callback();
              }
            },
            trigger: 'blur',
          },
        ],
        analysisFactorList: [
          { validator: validateFactorDetection, trigger: 'change' },
        ],
      },

      factorOptions: [
        { value: 'NO001', label: '苯' },
        { value: 'NO002', label: '甲苯' },
        { value: 'NO003', label: '二甲苯' },
        { value: 'NO004', label: '正己烷' },
        { value: 'NO005', label: '三氯乙烯' },
        { value: 'NO006', label: '四氯乙烯' },
      ],
      loading: false,
      allJobList: [],
      filteredJobList: [],
      filteredFactorOptions: [], // 过滤后的选项
      searchText: '', // 搜索文本
    };
  },

  computed: {
    showAnalysisList() {
      return (
        this.formData.ifExistAnalysis &&
        (this.formData.ifAnalysis === 1 || this.formData.ifAnalysis === 3)
      );
    },

    requireJobNameOther() {
      return this.dialogForm.jobNos.some(
        (item) => item && item.cnCsjcNo === '20070231'
      );
    },
  },

  watch: {
    'dialogForm.jobNos'(newVal) {
      this.$refs.dialogForm &&
        this.$refs.dialogForm.validateField('jobNameOther');
    },
    'formData.ifExistAnalysis'(newVal) {
      if (!newVal) this.formData.analysisSituationList = [];
    },
    'formData.ifAnalysis'(newVal) {
      if (newVal !== 1 && newVal !== 3) this.formData.analysisSituationList = [];
    },
  },
  async created() {
    const {
      id,
      taskId,
      jcOrgId,
      serviceOrgId,
      EnterpriseID,
      provincialWorkspaceMonitoringId,
    } = this.$route.query;
    this.formData.workspaceMonitoringRecordId = id; //监测记录id
    this.formData.jcOrgId = jcOrgId; // 委托单位
    this.formData.serviceOrgId = serviceOrgId; // 检测机构
    this.formData.EnterpriseID = EnterpriseID; // 获取企业/用人单位
    this.provincialWorkspaceMonitoringId = provincialWorkspaceMonitoringId; //省级任务id
    // 获取机构信息
    // this.getorgList(taskId);
    // 获取详情
    this.getMonitoringAnalyseDetail(id);
  },
  mounted() {
    this.loadAllJobs();
    this.fetchFactorList();
  },

  methods: {
    async getMonitoringAnalyseDetail(id) {
      const res = await getMonitoringAnalyseDetail({
        workspaceMonitoringRecordId: id,
      });
      if (res.status == 200) {
        this.formData.workspaceMonitoringRecordId =
          res.data.workspaceMonitoringRecordId;
        this.formData.EnterpriseID = res.data.EnterpriseID;
        this.formData.ifExistAnalysis = res.data.ifExistAnalysis;
        this.formData.ifAnalysis = res.data.ifAnalysis;
        this.formData.sdsFileName = res.data.sdsFileName;
        this.formData.analysisSituationList = res.data.analysisSituationList;
       
      }
    },
    // 根据岗位编码数组获取名称数组
    getJobNames(jobNos) {
      if (!jobNos || !jobNos.length) return [];
      // 遍历编码数组，匹配对应的名称
      return jobNos.map((code) => {
        const job = this.allJobList.find((item) => item.cnCsjcNo === code);
        return job ? job.jobName : `未知岗位(${code})`; // 未匹配到时显示编码
      });
    },

    // 根据factorId获取监测因素名称
    getFactorName(factorId) {
      if (!factorId) return '';
      const factor = this.factorOptions.find((item) => item.id === factorId);
      return factor ? factor.name : '未知因素';
    },
    async fetchFactorList() {
      try {
        // let params = {
        //   taskId: this.provincialWorkspaceMonitoringId,
        // };
        const res = await getFactorListNation();

        // 扁平化处理
        this.factorOptions = res.data.flatMap((category) => {
          return category.children.map((child) => ({
            category: category._id,
            id: child._id,
            name: child.chineseName.join('、'),
            aliases: child.chineseName.slice(1),
          }));
        });

        // 初始化时显示所有选项
        this.filteredFactorOptions = [...this.factorOptions];
        // console.log(this.factorOptions, 'this.factorOptions----');
      } catch (error) {
        console.error('获取监测因素列表失败:', error);
        this.$message.error('获取监测因素列表失败');
      }
    },
    // 过滤方法
    filterFactor(query) {
      if (!query) {
        // 没有搜索词时显示所有选项
        this.filteredFactorOptions = [...this.factorOptions];
        return;
      }

      const lowerQuery = query.toLowerCase();
      this.filteredFactorOptions = this.factorOptions.filter((item) => {
        // 检查主名称是否匹配
        if (item.name.toLowerCase().includes(lowerQuery)) {
          return true;
        }

        // 检查别名是否匹配
        if (item.aliases) {
          return item.aliases.some((alias) =>
            alias.toLowerCase().includes(lowerQuery)
          );
        }

        return false;
      });
    },
    async loadAllJobs() {
      try {
        this.loading = true;
        const params = { pageSize: 1000 };
        const res = await getJobList(params);
        if (res.status === 200) {
          this.allJobList = res.data.list || [];
          this.filteredJobList = [...this.allJobList];
        }
      } catch (error) {
        console.error('获取岗位列表失败:', error);
        this.$message.error('获取岗位数据失败，请稍后重试');
      } finally {
        this.loading = false;
      }
    },

    filterJobList(query) {
      if (!query) {
        this.filteredJobList = [...this.allJobList];
        return;
      }
      const lowerQuery = query.toLowerCase();
      this.filteredJobList = this.allJobList.filter(
        (item) =>
          item.jobName.toLowerCase().includes(lowerQuery) ||
          item.cnCsjcNo.toLowerCase().includes(lowerQuery)
      );
    },

    handleExistChange(val) {
      if (!val) this.formData.ifAnalysis = null;
    },

    handleAnalysisChange(val) {
      if (val !== 1 && val !== 3) this.formData.analysisSituationList = [];
    },

    openAddDialog() {
      this.dialogTitle = '添加物质';
      this.currentEditIndex = -1;
      this.resetDialogForm();
      this.dialogVisible = true;
    },

    addFactor() {
      this.dialogForm.analysisFactorList.push({
        factorId: '',
        ifDetection: 0,
        analysisValue: null,
      });
    },

    removeFactor(index) {
      if (this.dialogForm.analysisFactorList.length > 1) {
        this.dialogForm.analysisFactorList.splice(index, 1);
      } else {
        this.$message.warning('至少需要保留一个危害因素');
      }
    },

    removeItem(index) {
      this.$confirm('确定要删除该物质吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.formData.analysisSituationList.splice(index, 1);
          this.$message.success('删除成功');
        })
        .catch(() => {});
    },

    submitDialogForm() {
      this.$refs.dialogForm.validate((valid) => {
        if (!valid) return false;

        const hasDetection = this.dialogForm.analysisFactorList.some(
          (item) => item.ifDetection === 1
        );
        if (!hasDetection) {
          this.$message.error('至少需要一个危害因素检出');
          return false;
        }

        const formData = JSON.parse(JSON.stringify(this.dialogForm));
        formData.jobNos = formData.jobNos.map((item) => item.cnCsjcNo);

        if (this.currentEditIndex >= 0) {
          this.formData.analysisSituationList.splice(this.currentEditIndex, 1, formData);
        } else {
          this.formData.analysisSituationList.push(formData);
        }

        this.dialogVisible = false;
        this.$message.success('操作成功');
      });
    },

    resetDialogForm() {
      this.dialogForm = {
        substanceName: '',
        jobNos: [],
        jobNameOther: '',
        analysisFactorList: [
          { factorId: '', ifDetection: 0, analysisValue: null },
        ],
      };
      this.$refs.dialogForm && this.$refs.dialogForm.clearValidate();
    },

    nextStep() {
      this.submitForm();
      this.$emit('nextStepInfo', 5);

    },
     saveShortTime() {
      this.submitForm();
      this.$message.success('定性情况分析暂存成功！');
    },

    validateForm() {
      return new Promise((resolve, reject) => {
        this.$refs.formData.validate((valid) =>
          valid ? resolve() : reject(new Error('表单验证失败'))
        );
      });
    },

    async submitForm() {
      try {
        await this.validateForm();
        // const postData = { ...this.formData };
        // console.log(postData, '提交的数据');
        if(this.formData.analysisSituationList.length==0){
          this.$message.error('定性分析物质至少有一项')
          return
        }
        let params = {
          workspaceMonitoringRecordId:
            this.formData.workspaceMonitoringRecordId,
          EnterpriseID: this.formData.EnterpriseID,
          ...this.formData,
        };
        const res = await monitoringAnalyse(params);
        if (res.status == 200) {
          this.$message.success('定性情况分析保存成功');
        }
      } catch (err) {
        this.$message.error(err.message);
      }
    },
  },
};
</script>

<style scoped>
.table-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
}

.sub-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #606266;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.factor-item {
  margin-bottom: 5px;
}

::v-deep .el-select__tags {
  flex-wrap: wrap;
  padding: 4px;
}

::v-deep .el-select__tag {
  max-width: 200px;
  margin: 2px 4px;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

::v-deep .el-select__tag-content {
  color: #606266;
}
</style>
