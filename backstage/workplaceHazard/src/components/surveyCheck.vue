<template>
  <div>
    <TitleTag titleName="上一年度职业健康检查开展情况"></TitleTag>
    <el-row>
      <el-col :span="24">
        <table
          style="width: 100%; border: 1px solid #ddd"
          class="table-container"
        >
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>上一年度职业健康检查开展情况
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifhea ? '开展' : '未开展' }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>体检总人数
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.checkTotalPeoples }}</span>
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>有无粉尘_健康检查
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifheaDust ? '有' : '无' }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>有无化学物质_健康检查
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifheaChemistry ? '有' : '无' }}</span>
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>有无物理因素_健康检查
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifheaPhysics ? '有' : '无' }}</span>
            </th>
          </tr>
        </table>
      </el-col>
    </el-row>

    <div class="titleNext">职业健康检查报告明细</div>
    <el-table
      :data="healthcustodyReportList"
      border
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column
        prop="uuid"
        label="唯一标识"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="unitName"
        label="检查机构名称"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="creditCode"
        label="社会信用代码"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="ifExistCustodyReport"
        label="是否存在体检"
        width="120"
      >
        <template #default="scope">
          {{ scope.row.ifExistCustodyReport ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="reportNo" label="报告编号"></el-table-column>
      <el-table-column prop="fileName" label="附件名称"></el-table-column>
      
    </el-table>
    <div class="titleNext">职业健康检查情况-危害因素明细</div>
    <el-table
      :data="supervisionItemList"
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
      border
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="ifheaFactor" label="是否检查" width="100">
        <template #default="scope">
          {{ scope.row.ifheaFactor ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="factortype" label="因素类型" width="120">
        <template #default="scope">
          {{ getTypeLabel(scope.row.factortype) }}
        </template>
      </el-table-column>
      <el-table-column prop="factorId" label="监测因素" width="120">
        <template slot-scope="scope">
          {{ getFactorName(scope.row.factorId) }}
        </template></el-table-column
      >
      <el-table-column
        prop="checkMonitorPeoples"
        label="体检人数"
      ></el-table-column>
      <el-table-column
        prop="checkShouldPeoples"
        label="应复查人数"
      ></el-table-column>
      <el-table-column
        prop="checkActualPeoples"
        label="实际复查人数"
      ></el-table-column>
      <el-table-column prop="unusualNum" label="异常人数"></el-table-column>
      
    </el-table>
  </div>
</template>

<script>
import TitleTag from './TitleTag.vue';
import {
  getCheckSituationDetail,
  getIndustryCategory,
} from '@/api';
export default {
  components: { TitleTag },
  props:{
    factorOptions:[]
  },
  data() {
    return {
      healthcustodyReportList: [],
      supervisionItemList: [],
      baseInfo: {
        ifhea: false,
        checkTotalPeoples: 0,
        ifheaDust: undefined,
        ifheaChemistry: undefined,
        ifheaPhysics: undefined,
      },
      filteredFactorOptions: [], // 过滤后的选项
      typeOptions: [
        { value: 1, label: '粉尘因素' },
        { value: 2, label: '化学因素' },
        { value: 3, label: '物理因素' },
      ],
    };
  },

  async created() {
    const {
      id,
      taskId,
      jcOrgId,
      serviceOrgId,
      EnterpriseID,
      provincialWorkspaceMonitoringId,
    } = this.$route.query;
    this.taskId = taskId; // 保存任务ID
    this.baseInfo.workspaceMonitoringRecordId = id; //监测记录id
    this.baseInfo.jcOrgId = jcOrgId; // 委托单位
    this.baseInfo.serviceOrgId = serviceOrgId; // 检测机构
    this.baseInfo.EnterpriseID = EnterpriseID; // 获取企业/用人单位

    this.provincialWorkspaceMonitoringId = provincialWorkspaceMonitoringId; //省级任务id
    this.getCheckSituationDetail(id);

    // 加载行业和经济类型字典（用于转换名称）
    await this.loadDictionaries();
    // 获取监测危害因素
    await this.fetchFactorList();
  },
  methods: {
    async getCheckSituationDetail(id) {
      const res = await getCheckSituationDetail({
        workspaceMonitoringRecordId: id,
      });
      if (res.status == 200) {
        console.log(res.data, '0000');
        if (res.data.ifhea) {
          this.baseInfo.workspaceMonitoringRecordId =
            res.data.workspaceMonitoringRecordId;
          this.baseInfo.EnterpriseID = res.data.EnterpriseID;

          this.baseInfo.ifhea = res.data.ifhea;
          this.baseInfo.checkTotalPeoples = res.data.checkTotalPeoples;
          this.baseInfo.ifheaDust = res.data.ifheaDust;
          this.baseInfo.ifheaChemistry = res.data.ifheaChemistry;
          this.baseInfo.ifheaPhysics = res.data.ifheaPhysics;
          this.healthcustodyReportList = res.data.healthcustodyReportList;
          this.supervisionItemList = res.data.supervisionItemList;
        } else {
          // 当选择"未开展"时，清空并禁用下面的三个选项
          this.baseInfo.checkTotalPeoples = 0;
          this.baseInfo.ifheaDust = undefined;
          this.baseInfo.ifheaChemistry = undefined;
          this.baseInfo.ifheaPhysics = undefined;
          // 清空下面的表格列表
          this.healthcustodyReportList = [];
          this.supervisionItemList = [];
          // 清除验证错误
          this.$nextTick(() => {
            this.$refs.baseInfo.clearValidate([
              'ifheaDust',
              'ifheaChemistry',
              'ifheaPhysics',
            ]);
          });
        }
      }
    },

    async loadDictionaries() {
      // 加载行业分类
      const industryRes = await getIndustryCategory();
      if (industryRes.status === 200) this.industryClass = industryRes.data;
    },


   
    async fetchFactorList() {
      try {
        // let params = {
        //   taskId: this.taskId,
        // };
        // const res = await getFactorListNation();

        // 扁平化处理
        this.factorOptions = this.factorOptions.flatMap((category) => {
          return category.children.map((child) => ({
            category: category._id,
            id: child._id,
            name: child.chineseName.join('、'),
            aliases: child.chineseName.slice(1),
          }));
        });

        // 初始化时显示所有选项
        this.filteredFactorOptions = [...this.factorOptions];
      } catch (error) {
        console.error('获取监测因素列表失败:', error);
      }
    },

    // 获取因素名称
    getFactorName(factorId) {
      const item = this.factorOptions.find((item) => item.id === factorId);
      return item ? item.name : factorId; // 返回name属性而不是label
    },
    
    getTypeLabel(factortype) {
      const item = this.typeOptions.find((item) => item.value === factortype);
      return item ? item.label : '';
    },
  },
};
</script>

<style lang="scss" scoped>
.table-container {
  table-layout: fixed;
  background: #fff;
  width: 100%;
  margin: 0 auto;
  border-collapse: collapse;

  td {
    padding: 5px 0;
  }

  th {
    font-weight: normal;
    border: 1px solid #e5e5e5;
    padding: 3px 0;
  }

  tr {
    border: 1px solid #e5e5e5;
    width: 100%;
    font-size: 14px;
  }

  .left-tittle {
    background: #f5f7fa;
  }

  .table-label {
    text-align: right;
    padding-right: 5px;
    color: #000;
    font-size: 14px;
    height: 42px;
  }

  .th-input {
    text-align: center;
    padding: 2px 4px;

    span {
      color: #333;
    }
  }
}

.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}
</style>
