<template>
  <div class="container">
    <!-- 新增按钮 -->
    <el-button type="primary" @click="dialogVisible = true" :disabled="!ifat"
      >新增监测数据</el-button
    >

    <!-- 弹窗表单 -->
    <el-dialog
      title="新增监测数据"
      :visible.sync="dialogVisible"
      width="800px"
      @closed="resetForm"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="formRef"
        label-width="200px"
      >
        <el-form-item label="是否检测" prop="ifatFactor">
          <el-radio-group
            v-model="formData.ifatFactor"
            @change="handleFactorChange"
          >
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="危害因素" prop="factorId">
          <el-select
            v-model="formData.factorId"
            placeholder="请选择"
            clearable
            filterable
            :filter-method="filterFactor"
            style="width: 100%"
          >
            <el-option
              v-for="item in filteredFactorOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
              <template v-if="item.name.length > 30">
                <el-tooltip :content="item.name" placement="top">
                  <span>{{ item.name.substring(0, 30) + '...' }}</span>
                </el-tooltip>
              </template>
              <template v-else>
                <span>{{ item.name }}</span>
              </template>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          label="场所检测点数"
          prop="checkNum"
          :rules="formData.ifatFactor ? rules.checkNum : []"
        >
          <el-input-number
            v-model="formData.checkNum"
            :min="0"
            :step="1"
            :disabled="!formData.ifatFactor"
            placeholder="请输入场所检测点数"
          ></el-input-number>
        </el-form-item>

        <el-form-item
          label="场所超标点数"
          prop="excessNum"
          :rules="formData.ifatFactor ? rules.excessNum : []"
        >
          <el-input-number
            v-model="formData.excessNum"
            :min="0"
            :step="1"
            :max="formData.checkNum || 0"
            :disabled="!formData.ifatFactor"
            placeholder="请输入场所超标点数"
          ></el-input-number>
        </el-form-item>

        <el-form-item
          label="检测岗位/工种数"
          prop="workNum"
          :rules="formData.ifatFactor ? rules.workNum : []"
        >
          <el-input-number
            v-model="formData.workNum"
            :min="0"
            :step="1"
            :disabled="!formData.ifatFactor"
            placeholder="请输入检测岗位/工种数"
          ></el-input-number>
        </el-form-item>

        <el-form-item
          label="检测岗位/工种超标数"
          prop="workExcessNum"
          :rules="formData.ifatFactor ? rules.workExcessNum : []"
        >
          <el-input-number
            v-model="formData.workExcessNum"
            :min="0"
            :step="1"
            :max="formData.workNum || 0"
            :disabled="!formData.ifatFactor"
            placeholder="请输入检测岗位/工种超标数"
          ></el-input-number>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      border
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="ifatFactor" label="是否检测" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.ifatFactor ? 'success' : 'info'">
            {{ scope.row.ifatFactor ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="factorId" label="危害因素" width="150">
        <template slot-scope="scope">
          {{ getFactorName(scope.row.factorId) }}
        </template>
      </el-table-column>
      <el-table-column prop="checkNum" label="场所检测点数"></el-table-column>
      <el-table-column prop="excessNum" label="场所超标点数"></el-table-column>
      <el-table-column prop="workNum" label="检测岗位/工种数"></el-table-column>
      <el-table-column
        prop="workExcessNum"
        label="检测岗位/工种超标数"
      ></el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.$index)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import { getFactorListNation } from '@/api';

export default {
  name: 'firstMessage',
  components: {
    TitleTag,
  },
  props: {
    taskId: {
      type: String,
    },
    tableData: {
      type: Array,
      default: [],
    },
    ifat: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const validateExcessNum = (rule, value, callback) => {
      if (value > this.formData.checkNum) {
        callback(new Error('超标点数不能大于检测点数'));
      } else if (value > 999999) {
        callback(new Error('数值不能超过6位数'));
      } else {
        callback();
      }
    };

    const validateWorkExcessNum = (rule, value, callback) => {
      if (value > this.formData.workNum) {
        callback(new Error('岗位超标数不能大于岗位总数'));
      } else if (value > 999999) {
        callback(new Error('数值不能超过6位数'));
      } else {
        callback();
      }
    };

    const validateFactorId = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请选择监测因素'));
      }
      if (value.length > 20) {
        // callback(new Error('危害因素ID长度不能超过20'));
      } else {
        callback();
      }
    };

    const validateNumberField = (rule, value, callback) => {
      if (value > 999999) {
        callback(new Error('数值不能超过6位数'));
      } else {
        callback();
      }
    };

    return {
      dialogVisible: false,
      formData: {
        ifatFactor: false,
        factorId: '',
        checkNum: 0,
        excessNum: 0,
        workNum: 0,
        workExcessNum: 0,
      },
      rules: {
        ifatFactor: [
          { required: true, message: '请选择是否检测', trigger: 'change' },
        ],
        factorId: [
          { required: true, message: '请选择监测因素', trigger: 'change' },
          { validator: validateFactorId, trigger: 'blur' },
        ],
        checkNum: [
          { required: true, message: '请输入场所检测点数', trigger: 'blur' },
          {
            type: 'number',
            min: 0,
            message: '检测点数不能小于0',
            trigger: 'blur',
          },
          { validator: validateNumberField, trigger: 'blur' },
        ],
        excessNum: [
          { required: true, message: '请输入场所超标点数', trigger: 'blur' },
          {
            type: 'number',
            min: 0,
            message: '超标点数不能小于0',
            trigger: 'blur',
          },
          { validator: validateExcessNum, trigger: 'blur' },
        ],
        workNum: [
          { required: true, message: '请输入检测岗位/工种数', trigger: 'blur' },
          {
            type: 'number',
            min: 0,
            message: '岗位数不能小于0',
            trigger: 'blur',
          },
          { validator: validateNumberField, trigger: 'blur' },
        ],
        workExcessNum: [
          {
            required: true,
            message: '请输入检测岗位/工种超标数',
            trigger: 'blur',
          },
          {
            type: 'number',
            min: 0,
            message: '岗位超标数不能小于0',
            trigger: 'blur',
          },
          { validator: validateWorkExcessNum, trigger: 'blur' },
        ],
      },
      factorOptions: [
        { value: 'F001', label: '粉尘-总尘' },
        { value: 'F002', label: '粉尘-呼尘' },
        { value: 'C001', label: '化学-苯' },
        { value: 'C002', label: '化学-甲苯' },
        { value: 'P001', label: '物理-噪声' },
        { value: 'P002', label: '物理-高温' },
      ],
      // tableData: [],
      filteredFactorOptions: [], // 过滤后的选项
      searchText: '', // 搜索文本
    };
  },
  async created() {
    this.fetchFactorList();
  },
  watch: {},
  async mounted() {},
  methods: {
    async fetchFactorList() {
      try {
        // let params = {
        //   taskId: this.taskId,
        // };
        const res = await getFactorListNation();

        // 扁平化处理
        this.factorOptions = res.data.flatMap((category) => {
          return category.children.map((child) => ({
            category: category._id,
            id: child._id,
            name: child.chineseName.join('、'),
            aliases: child.chineseName.slice(1),
          }));
        });

        // 初始化时显示所有选项
        this.filteredFactorOptions = [...this.factorOptions];
        // console.log(this.factorOptions, 'this.factorOptions----');
      } catch (error) {
        console.error('获取监测因素列表失败:', error);
        this.$message.error('获取监测因素列表失败');
      }
    },
    // 过滤方法
    filterFactor(query) {
      if (!query) {
        // 没有搜索词时显示所有选项
        this.filteredFactorOptions = [...this.factorOptions];
        return;
      }

      const lowerQuery = query.toLowerCase();
      this.filteredFactorOptions = this.factorOptions.filter((item) => {
        // 检查主名称是否匹配
        if (item.name.toLowerCase().includes(lowerQuery)) {
          return true;
        }

        // 检查别名是否匹配
        if (item.aliases) {
          return item.aliases.some((alias) =>
            alias.toLowerCase().includes(lowerQuery)
          );
        }

        return false;
      });
    },
    // 提交表单
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // 添加到表格数据
          const data = {
            ifatFactor: this.formData.ifatFactor,
            factorId: this.formData.factorId,
            checkNum: this.formData.ifatFactor ? this.formData.checkNum : 0,
            excessNum: this.formData.ifatFactor ? this.formData.excessNum : 0,
            workNum: this.formData.ifatFactor ? this.formData.workNum : 0,
            workExcessNum: this.formData.ifatFactor
              ? this.formData.workExcessNum
              : 0,
          };
          this.tableData.push(data);
          this.$emit('factorCheckItem', this.tableData);

          this.$message.success('新增成功');
          this.dialogVisible = false;
        }
      });
    },

    // 删除行
    handleDelete(index) {
      this.$confirm('确定要删除该条数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.tableData.splice(index, 1);
          this.$emit('factorCheckItem', this.tableData);

          this.$message.success('删除成功');
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },

    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields();
      this.formData = {
        ifatFactor: false,
        factorId: '',
        checkNum: 0,
        excessNum: 0,
        workNum: 0,
        workExcessNum: 0,
      };
    },

    // 处理是否检测变化
    handleFactorChange(val) {
      if (!val) {
        this.formData.checkNum = 0;
        this.formData.excessNum = 0;
        this.formData.workNum = 0;
        this.formData.workExcessNum = 0;
      }
    },

    // 获取因素名称
    getFactorName(factorId) {
      const item = this.factorOptions.find((item) => item.id === factorId);
      return item ? item.name : factorId; // 返回name属性而不是label
    },
  },
  computed: {},
};
</script>
<style scoped>
.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}
.container {
  padding: 20px;
}
</style>
