<template>
  <div>
    <TitleTag titleName="存在的重点岗位/环节情况"></TitleTag>
    <div class="titleNext">监测岗位劳动者工作日调查表</div>
    <addQuestionnaire
      @processedTableData="processedTableData"
      :tableData="workerInvestigationSituationList"
      :taskId="provincialWorkspaceMonitoringId"
      :EnterpriseID="formData.EnterpriseID"
    ></addQuestionnaire>
    <el-row>
      <el-col :span="24">
        <el-button
          type="primary"
          size="medium"
          @click="nextStep"
          style="margin-left: 20px"
          >下一步</el-button
        >
        <el-button size="medium" @click="saveShortTime()">暂存</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import addQuestionnaire from '@/components/addQuestionnaire.vue';
import { workerForm, getWorkerFormDetail, previewWorker } from '@/api';
export default {
  name: 'firstMessage',
  components: {
    TitleTag,
    addQuestionnaire,
  },
  props: {},
  data() {
    return {
      workerInvestigationSituationList: [],
      formData: {},
    };
  },
  async created() {
    const {
      id,
      taskId,
      jcOrgId,
      serviceOrgId,
      EnterpriseID,
      provincialWorkspaceMonitoringId,
    } = this.$route.query;
    this.formData.workspaceMonitoringRecordId = id; //监测记录id
    this.formData.jcOrgId = jcOrgId; // 委托单位
    this.formData.serviceOrgId = serviceOrgId; // 检测机构
    this.formData.EnterpriseID = EnterpriseID; // 获取企业/用人单位
    this.provincialWorkspaceMonitoringId = provincialWorkspaceMonitoringId; //省级任务id
    // 获取详情
    this.getWorkerFormDetail(id);
  },
  watch: {},
  async mounted() {},
  methods: {
    async getWorkerFormDetail(id) {
      const res = await getWorkerFormDetail({
        workspaceMonitoringRecordId: id,
      });
      if (res.status == 200) {
        console.log(res.data, '00002222');
        this.formData.workspaceMonitoringRecordId =
          res.data.workspaceMonitoringRecordId;
        this.formData.EnterpriseID = res.data.EnterpriseID;
        this.workerInvestigationSituationList =
          res.data.workerInvestigationSituationList;
      } 
    },
    async submitForm() {
      if (this.workerInvestigationSituationList.length == 0) {
        this.$message.error('监测岗位劳动者工作日调查表至少有一条');
        return;
      }
      let params = {
        workspaceMonitoringRecordId: this.formData.workspaceMonitoringRecordId,
        EnterpriseID: this.formData.EnterpriseID,
        workerInvestigationSituationList: this.workerInvestigationSituationList,
      };

      const res = await workerForm(params);
      if (res.status == 200) {
        this.$message.success('监测岗位劳动者工作日调查表保存成功');
      }
    },
    // 下一步
    nextStep() {
      this.submitForm();
      // 获取数据，提交数据
      this.$emit('nextStepInfo', 6);
    },
    saveShortTime() {
      this.submitForm();
      this.$message.success('劳动者调查表暂存成功！');
    },
    processedTableData(val) {
      this.workerInvestigationSituationList = val;
    },
  },
  computed: {},
};
</script>
<style scoped>
.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}
</style>
