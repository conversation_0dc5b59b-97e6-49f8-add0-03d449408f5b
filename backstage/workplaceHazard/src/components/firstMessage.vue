<template>
  <div>
    <el-form
      :model="formData"
      :rules="rules"
      ref="formData"
      label-width="200px"
      size="mini"
    >
      <el-row>
        <TitleTag titleName="机构信息"></TitleTag>

        <el-col :span="12">
          <el-form-item prop="name" label="检测机构">
            <el-select
              disabled
              v-model="formData.serviceOrgId"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="item in serviceOrgList"
                :key="item._id"
                :label="item.name"
                :value="item._id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="name" label="委托单位">
            <el-select
              v-model="formData.jcOrgId"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="item in superUserList"
                :key="item._id"
                :label="item.cname"
                :value="item._id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="name" label="用人单位">
            <el-select
              disabled
              v-model="formData.EnterpriseID"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="item in EnterpriseList"
                :key="item._id"
                :label="item.cname"
                :value="item._id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <TitleTag titleName="基本信息"></TitleTag>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="unitName" label="单位名称">
            <el-input v-model="formData.unitName"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="creditCode" label="社会信用代码">
            <el-input v-model="formData.creditCode"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="workAddr" label="工作场所地址">
            <el-input v-model="formData.workAddr"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="regAddr" label="单位注册地址">
            <el-input v-model="formData.regAddr"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="industryCode" label="所属行业">
            <el-cascader
              v-model="formData.industryCode"
              :options="industryClass"
              :props="{
                ...cascaderProps, // 合并原有配置
                // multiple: true, // 启用多选
                emitPath: true, // 必须设置为true，返回完整路径
              }"
              clearable
              placeholder="请选择行业分类"
              style="width: 100%"
            ></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            prop="selectedCode"
            label="经济类型"
            class="economicsClass"
          >
            <div class="economicsSpan">*</div>
            <!-- <el-select
              v-model="formData.economicNo"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="item in rawData"
                :key="item.code"
                :label="item.content"
                :value="item.code"
              />
            </el-select> -->
            <el-select
              v-model="selectedCode"
              placeholder="请选择经济类型"
              clearable
              style="width: 100%"
            >
              <el-option-group
                v-for="group in categoryGroups"
                :key="group.parentId"
                :label="group.parentContent"
              >
                <el-option
                  v-for="item in group.children"
                  :key="item.code"
                  :label="item.content"
                  :value="item.code"
                  :data-parent="item.parentId"
                ></el-option>
              </el-option-group>
            </el-select>
            <!-- <div  style="margin-top: 20px">
              当前选择: {{ currentSelection.content }}
              <br />
              父级ID: {{ currentSelection.parentId }}
              <br />
              当前编码: {{ selectedCode }}
            </div> -->
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="legalPerson" label="法人姓名">
            <el-input v-model="formData.legalPerson"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="linkManager" label="职业卫生管理联系人">
            <el-input v-model="formData.linkManager"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="linkPhone" label="联系电话">
            <el-input v-model="formData.linkPhone"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="empNum" label="本单位在册职工总数">
            <el-input-number
              v-model="formData.empNum"
              :min="0"
              :max="999999"
              :precision="0"
              controls-position="right"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="externalNum" label="外委人员总数">
            <el-input-number
              v-model="formData.externalNum"
              :min="0"
              :precision="0"
              controls-position="right"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="enterpriseScale" label="用人单位规模">
            <el-radio-group v-model="formData.enterpriseScale">
              <el-radio :label="1">大</el-radio>
              <el-radio :label="2">中</el-radio>
              <el-radio :label="3">小</el-radio>
              <el-radio :label="4">微</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left">职业病危害项目申报情况</el-divider>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="ifDeclare" label="是否进行了申报">
            <el-radio-group v-model="formData.ifDeclare">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            prop="ifAnnualUpdate"
            label="是否进行了年度更新"
            :required="formData.ifDeclare == true"
          >
            <el-radio-group v-model="formData.ifAnnualUpdate">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left">职业卫生培训情况</el-divider>

      <el-row>
        <el-col :span="6">
          <el-form-item prop="ifLeadersTrain" label="主要负责人培训">
            <el-radio-group v-model="formData.ifLeadersTrain">
              <el-radio :label="true">有</el-radio>
              <el-radio :label="false">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="ifManagersTrain" label="职业卫生管理人员培训情况">
            <el-radio-group v-model="formData.ifManagersTrain">
              <el-radio :label="true">有</el-radio>
              <el-radio :label="false">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            prop="trainSum"
            label="接触职业病危害因素年度培训总人数"
            label-width="320px"
          >
            <el-input-number
              v-model="formData.trainSum"
              :min="0"
              :max="999999"
              :precision="0"
              controls-position="right"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">防护设施“三同时” 情况</el-divider>

      <el-row>
        <el-col :span="6">
          <el-form-item prop="ifImport" label="引进项目情况">
            <el-radio-group v-model="formData.ifImport">
              <el-radio :label="true">有</el-radio>
              <el-radio :label="false">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            prop="workNode"
            label="当前工作阶段"
            :required="formData.ifImport"
          >
            <el-checkbox-group v-model="formData.workNode">
              <el-checkbox :label="1">可研阶段</el-checkbox>
              <el-checkbox :label="2">初步设计阶段</el-checkbox>
              <el-checkbox :label="3">建设阶段</el-checkbox>
              <el-checkbox :label="4">竣工阶段</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            prop="ifPreLaunch"
            label="预评价开展情况"
            label-width="auto"
            :required="
              formData.workNode &&
              (formData.workNode.includes(1) || formData.workNode.includes(3))
            "
          >
            <!-- :disabled="
                !formData.workNode ||
                (!formData.workNode.includes(1) &&
                  !formData.workNode.includes(3))
              " -->
            <el-radio-group v-model="formData.ifPreLaunch">
              <el-radio :label="1">全部</el-radio>
              <el-radio :label="2">部分</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item
            prop="ifDesign"
            label="职业病防护设施设计专篇"
            :required="
              formData.workNode &&
              (formData.workNode.includes(2) || formData.workNode.includes(3))
            "
          >
            <!-- :disabled="
                !formData.workNode ||
                (!formData.workNode.includes(2) &&
                  !formData.workNode.includes(3))
              " -->
            <el-radio-group v-model="formData.ifDesign">
              <el-radio :label="1">全部</el-radio>
              <el-radio :label="2">部分</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            prop="ifLaunch"
            label="控制效果评价开展情况"
            :required="formData.workNode && formData.workNode.includes(4)"
          >
            <!-- :disabled="!formData.workNode || !formData.workNode.includes(4)" -->
            <el-radio-group v-model="formData.ifLaunch">
              <el-radio :label="1">全部</el-radio>
              <el-radio :label="2">部分</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="ifAfterDeclare" label="调查后是否申报">
            <el-radio-group v-model="formData.ifAfterDeclare">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="dcUnitName" label="调查机构名称">
            <el-input v-model="formData.dcUnitName"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="dcCreditCode" label="调查机构社会信用代码">
            <el-input v-model="formData.dcCreditCode"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="jcUnitName" label="监测机构名称">
            <el-input v-model="formData.jcUnitName"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="jcCreditCode" label="监测机构统一社会信用代码">
            <el-input v-model="formData.jcCreditCode"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            prop="ifNoiseDeafness"
            label="上一年度是否存在职业性噪声聋"
            label-width="auto"
          >
            <el-radio-group v-model="formData.ifNoiseDeafness">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left">重点岗位调查情况集合</el-divider>
      <jobList
        @keyJobInvestigation="keyJobInvestigation"
        :tableData="keyJobInvestigationSituationList"
      ></jobList>
      <el-divider content-position="left"
        >职业病危害因素种类及接触人数</el-divider
      >

      <el-row>
        <el-col :span="6">
          <el-form-item
            prop="contactTotalPeoples"
            label="接触职业病危害因素总人数"
          >
            <el-input-number
              v-model="formData.contactTotalPeoples"
              :min="1"
              :precision="0"
              controls-position="right"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="ifhfDust" label="危害因素_粉尘">
            <el-radio-group v-model="formData.ifhfDust">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="粉尘_接触总人数" prop="hfDustPeoples">
            <el-input-number
              v-model="formData.hfDustPeoples"
              :min="0"
              :precision="0"
              controls-position="right"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="ifhfChemistry" label="危害因素_化学物质">
            <el-radio-group v-model="formData.ifhfChemistry">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="化学物质_接触总人数" prop="hfChemistryPeoples">
            <el-input-number
              v-model="formData.hfChemistryPeoples"
              :min="0"
              :precision="0"
              controls-position="right"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="ifhfPhysics" label="危害因素_物理因素">
            <el-radio-group v-model="formData.ifhfPhysics">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="物理因素_接触总人数" prop="hfPhysicsPeoples">
            <el-input-number
              v-model="formData.hfPhysicsPeoples"
              :min="0"
              :precision="0"
              controls-position="right"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 接触因素及人数明细 -->
      <tourchPerson
        @factorCrowdItem="factorCrowdItem"
        :taskId="provincialWorkspaceMonitoringId"
        :tableData="factorCrowdItemList"
      ></tourchPerson>
      <!-- 工程防护适宜技术应用情况  暂留，表里没字段  -->
      <!-- <div class="titleNext">工程防护适宜技术应用情况</div>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="name" label="是否采用职业病危害防护先进适宜技术">
            <el-radio-group v-model="formData.resource">
              <el-radio label="是"></el-radio>
              <el-radio label="否"></el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="name" label="编号">
            <el-input
              v-model="formData.name"
              type="number"
              placeholder="编号范围1~63"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row>
        <el-col :span="24">
          <el-form-item label-width="20px">
            <!-- <el-button type="primary" @click="onSubmit">立即创建</el-button>
        <el-button>取消</el-button> -->

            <el-button type="primary" size="medium" @click="nextStep"
              >下一步</el-button
            >
            <el-button size="medium" @click="saveShortTime()">暂存</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import jobList from '@/components/jobList.vue';
import tourchPerson from '@/components/tourchPerson.vue';
import {
  orgList,
  getOneAdminorg,
  getEconomicCategory,
  getIndustryCategory,
  baseInfo,
  getBaseInfo,
} from '@/api';
export default {
  name: 'firstMessage',
  components: {
    TitleTag,
    jobList,
    tourchPerson,
  },
  props: {},
  data() {
    // 自定义校验方法
    const validateWorkNode = (rule, value, callback) => {
      if (this.formData.ifImport && (!value || value.length === 0)) {
        callback(new Error('引进项目时必须选择工作阶段'));
      } else {
        callback();
      }
    };
    const validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入联系电话'));
      } else if (!/^1[3-9]\d{9}$/.test(value)) {
        callback(new Error('请输入正确的手机号码'));
      } else {
        callback();
      }
    };
    const validateCreditCode = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入社会信用代码'));
      } else if (!/^[A-Z0-9]{18}$/.test(value)) {
        callback(new Error('社会信用代码格式不正确'));
      } else {
        callback();
      }
    };

    return {
      categoryGroups: [],
      selectedCode: '',
      currentSelection: {},
      industryClass: [],
      rawData: [],
      superUserList: [],
      serviceOrgList: [],
      EnterpriseList: [],
      cascaderProps: {
        value: 'value', // 指定选项的 value 字段
        label: 'label', // 指定选项的 label 字段
        children: 'children', // 指定选项的子级字段
        checkStrictly: true, // 可选：是否允许选择任意一级（默认false，只能选最后一级）
        emitPath: false, // 可选：是否返回完整路径（默认true）
      },
      formData: {
        unitName: '', //单位名称
        creditCode: '', //社会信用代码
        workAddr: '', //工作场所地址
        regAddr: '', //单位注册地址
        industryCode: [], //所属行业
        // selectedCode: '', //经济类型
        legalPerson: '', //法人姓名
        linkManager: '', //职业卫生管理联系人
        linkPhone: '', //联系电话
        empNum: 0, //本单位在册职工总数
        externalNum: 0, //外委人员总数
        enterpriseScale: 3, //用人单位规模

        ifDeclare: false, // 是否申报
        ifAnnualUpdate: false, //是否进行了年度更新

        ifLeadersTrain: false, //主要负责人培训
        ifManagersTrain: false, //职业卫生管理人员培训情况
        trainSum: 0, //接触职业病危害因素年度培训总人数

        ifImport: false, // 引进项目情况 防护设施“三同时” 情况
        workNode: [], //当前工作阶段 多选
        ifPreLaunch: 3,
        ifDesign: 3,
        ifLaunch: 3,

        ifAfterDeclare: false, //调查后是否申报
        dcUnitName: '',
        dcCreditCode: '',
        jcUnitName: '',
        jcCreditCode: '',
        ifNoiseDeafness: false,

        contactTotalPeoples: 0, //接触职业病危害因素总人数
        ifhfDust: false,
        hfDustPeoples: 0,
        ifhfChemistry: false,
        hfChemistryPeoples: 0,
        ifhfPhysics: false,
        hfPhysicsPeoples: 0,
      },
      rules: {
        unitName: [
          { required: true, message: '请输入单位名称', trigger: 'blur' },
          { max: 300, message: '长度不能超过300个字符', trigger: 'blur' },
        ],
        creditCode: [
          { required: true, validator: validateCreditCode, trigger: 'blur' },
        ],
        workAddr: [
          { required: true, message: '请输入工作场所地址', trigger: 'blur' },
        ],
        regAddr: [
          { required: true, message: '请输入注册地址', trigger: 'blur' },
          { max: 400, message: '长度不能超过400个字符', trigger: 'blur' },
        ],
        industryCode: [
          { required: true, message: '请选择所属行业', trigger: 'change' },
        ],
        // selectedCode: [
        //   { required: true, message: '请选择经济类型', trigger: 'change' },
        // ],
        legalPerson: [
          { required: true, message: '请输入法人姓名', trigger: 'blur' },
          { max: 20, message: '长度不能超过20个字符', trigger: 'blur' },
        ],
        linkManager: [
          {
            required: true,
            message: '请输入职业卫生管理联系人',
            trigger: 'blur',
          },
          { max: 20, message: '长度不能超过20个字符', trigger: 'blur' },
        ],
        linkPhone: [
          { required: true, validator: validatePhone, trigger: 'blur' },
        ],
        empNum: [
          { required: true, message: '请输入在册职工总数', trigger: 'blur' },
          { type: 'number', min: 0, message: '人数不能为负数' },
          {
            type: 'number',
            min: 0,
            max: 999999,
            message: '请输入0-999999之间的数字',
            trigger: 'blur',
          },
        ],
        externalNum: [
          { type: 'number', min: 0, message: '人数不能为负数' },
          {
            type: 'number',
            min: 0,
            max: 999999,
            message: '请输入0-999999之间的数字',
            trigger: 'blur',
          },
        ],
        enterpriseScale: [
          { required: true, message: '请选择用人单位规模', trigger: 'change' },
        ],

        ifDeclare: [
          {
            required: true,
            message: '请选择是否进行了申报',
            trigger: 'change',
          },
        ],
        trainSum: [
          { type: 'number', min: 0, message: '人数不能为负数' },
          {
            type: 'number',
            min: 0,
            max: 999999,
            message: '请输入0-999999之间的数字',
            trigger: 'blur',
          },
        ],
        ifImport: [
          { required: true, message: '请选择引进项目情况', trigger: 'change' },
        ],
        workNode: [{ validator: validateWorkNode, trigger: 'change' }],
        ifAfterDeclare: [
          {
            required: true,
            message: '请选择调查后是否申报',
            trigger: 'change',
          },
        ],
        dcUnitName: [
          {
            required: true,
            message: '请输入调查机构名称',
            trigger: 'blur',
          },
          { max: 300, message: '长度不能超过300个字符', trigger: 'blur' },
        ],
        dcCreditCode: [
          { required: true, validator: validateCreditCode, trigger: 'blur' },
        ],
        jcUnitName: [
          {
            required: true,
            message: '请输入监测机构名称',
            trigger: 'blur',
          },
        ],
        jcCreditCode: [
          { required: true, validator: validateCreditCode, trigger: 'blur' },
        ],
      },
      keyJobInvestigationSituationList: [],
      factorCrowdItemList: [],
    };
  },
  async created() {
    const {
      id,
      taskId,
      jcOrgId,
      serviceOrgId,
      EnterpriseID,
      provincialWorkspaceMonitoringId,
    } = this.$route.query;
    this.formData.workspaceMonitoringRecordId = id; //监测记录id
    this.formData.jcOrgId = jcOrgId; // 委托单位
    this.formData.serviceOrgId = serviceOrgId; // 检测机构
    this.formData.EnterpriseID = EnterpriseID; // 获取企业/用人单位
    console.log(this.formData.EnterpriseID, EnterpriseID, '----0099');
    this.provincialWorkspaceMonitoringId = provincialWorkspaceMonitoringId; //省级任务id
    // 获取机构信息
    this.getorgList(taskId);
    // 获取行业分类
    this.getIndustryCategory();
    // 获取经济类型
    this.getEconomicCategory();

    // 获取基本信息详情
    this.getBaseInfo(id, jcOrgId, serviceOrgId, EnterpriseID);
  },
  watch: {
    selectedCode(newVal) {
      if (newVal) {
        this.currentSelection = this.rawData.find(
          (item) => item.code === newVal
        );
        console.log(this.currentSelection, '000this.currentSelection0--');
      } else {
        this.currentSelection = {};
      }
    },
    'formData.ifImport'(val) {
      if (!val) {
        this.formData.workNode = [];
        this.formData.ifPreLaunch = 3;
        this.formData.ifDesign = 3;
        this.formData.ifLaunch = 3;
      }
    },
    'formData.workNode'(val) {
      const workNode = val || [];
      // 清除不需要的字段
      if (!workNode.includes(1) && !workNode.includes(3)) {
        this.formData.ifPreLaunch = 3;
      }
      if (!workNode.includes(2) && !workNode.includes(3)) {
        this.formData.ifDesign = 3;
      }
      if (!workNode.includes(4)) {
        this.formData.ifLaunch = 3;
      }
    },
  },
  async mounted() {},
  methods: {
    async getBaseInfo(id, jcOrgId, serviceOrgId, EnterpriseID) {
      const res = await getBaseInfo({ workspaceMonitoringRecordId: id });
      if (res.status == 200) {
        console.log(res, 'res---');
        if (res.data.unitName) {
          const {
            crowd,
            keyJobInvestigationSituationList,
            factorCrowdItemList,
            ...restFormData
          } = res.data;
          this.formData = { ...restFormData };
          this.formData.EnterpriseID = EnterpriseID;
          this.formData.jcOrgId = jcOrgId; // 委托单位
          this.formData.serviceOrgId = serviceOrgId; // 检测机构
          // 经济类型回显
          this.selectedCode = this.formData.economicNo[1];
          this.currentSelection.parentId = this.formData.economicNo[0];
          // 职业病危害因素种类及接触人数
          this.formData.contactTotalPeoples = crowd.contactTotalPeoples;
          this.formData.ifhfDust = crowd.ifhfDust;
          this.formData.hfDustPeoples = crowd.hfDustPeoples;
          this.formData.ifhfChemistry = crowd.ifhfChemistry;
          this.formData.hfChemistryPeoples = crowd.hfChemistryPeoples;
          this.formData.ifhfPhysics = crowd.ifhfPhysics;
          this.formData.hfPhysicsPeoples = crowd.hfPhysicsPeoples;
          // 重点岗位调查情况集合
          this.keyJobInvestigationSituationList =
            keyJobInvestigationSituationList;
          // 监测因素
          this.factorCrowdItemList = factorCrowdItemList;

          console.log(this.formData, '---this.formData999');
        }
      } else {
        // 根据企业id 获取企业详情有数据的进行预填写
        this.getOneAdminorg(EnterpriseID);
      }
    },
    async getorgList(taskId) {
      const res = await orgList({ task_id: taskId });
      if (res.status == 200) {
        this.EnterpriseList = res.data.EnterpriseList;
        console.log(this.EnterpriseList, 'this.EnterpriseList-----');
        this.serviceOrgList = res.data.serviceOrgList;
        this.superUserList = res.data.superUserList;
      } else {
        this.$message.error(res.message);
      }
    },
    async getIndustryCategory() {
      const res = await getIndustryCategory();
      if (res.status == 200) {
        this.industryClass = res.data;
      }
    },
    async getEconomicCategory() {
      const res = await getEconomicCategory();
      if (res.status == 200) {
        this.rawData = res.data.list;
        this.processData();
      }
    },
    processData() {
      // 按父级分组
      const groups = {};
      const parentMap = {};

      // 先找出所有一级分类
      this.rawData.forEach((item) => {
        if (item.parentId === '0') {
          parentMap[item.code] = item.content;
        }
      });

      // 构建分组
      this.rawData.forEach((item) => {
        if (item.parentId !== '0') {
          if (!groups[item.parentId]) {
            groups[item.parentId] = {
              parentId: item.parentId,
              parentContent: parentMap[item.parentId] || '未知分类',
              children: [],
            };
          }
          groups[item.parentId].children.push(item);
        }
      });

      this.categoryGroups = Object.values(groups);
    },

    // 获取选中值的完整信息
    // getSelectedValue() {
    //   if (!this.selectedCode) return null;

    //   const selectedItem = this.rawData.find(item => item.code === this.selectedCode);
    //   return {
    //     parentId: selectedItem.parentId,
    //     code: selectedItem.code,
    //     content: selectedItem.content
    //   };
    // },
    async getOneAdminorg(EnterpriseID) {
      const result = await getOneAdminorg({ EnterpriseID: EnterpriseID });
      if (result.status == 200) {
        this.formData.unitName = result.data.targetItem.cname;
        this.formData.creditCode = result.data.targetItem.code;
        this.formData.regAddr = result.data.targetItem.regAdd;
        // this.formData.workAddr = result.data.targetItem.workAddress[0].;
        const scaleMap = { 大: 1, 中: 2, 小: 3, 微: 4 };
        this.formData.enterpriseScale =
          scaleMap[result.data.targetItem.companyScale] || 0;
        this.formData.industryCode = result.data.targetItem.industryCategory[0];
        this.formData.legalPerson = result.data.targetItem.corp;
        this.formData.linkManager = result.data.targetItem.contract;
        this.formData.linkPhone = result.data.targetItem.phoneNum;
      }
    },
    keyJobInvestigation(val) {
      this.keyJobInvestigationSituationList = val;
    },
    factorCrowdItem(val) {
      this.factorCrowdItemList = val;
    },
    // 下一步
    nextStep() {
      this.submitForm();
      this.$emit('nextStepInfo', 1);
    },
    saveShortTime() {
      this.submitForm();
      this.$message.success('基本信息暂存成功！');
    },

    // 提交
    // 提交前数据校验
    validateForm() {
      return new Promise((resolve, reject) => {
        this.$refs.formData.validate((valid) => {
          if (valid) {
            resolve();
          } else {
            reject(new Error('表单验证失败'));
          }
        });
      });
    },

    // 提交数据格式化
    formatSubmitData() {
      const data = { ...this.formData };

      return data;
    },

    async submitForm() {
      try {
        await this.validateForm();
        const postData = this.formatSubmitData();
        // 调用API提交postData
        // 经济类型父子级
        this.formData.economicNo = [
          this.currentSelection.parentId,
          this.selectedCode,
        ];
        console.log('提交数据表单数据:', postData);
        // 岗位环节数据
        if (this.keyJobInvestigationSituationList.length == 0) {
          this.$message.error('请新增岗位环节');
          return;
        }
        console.log(
          this.keyJobInvestigationSituationList,
          'this.keyJobInvestigationSituationList岗位环节数据'
        );
        // 监测因素数据
        if (this.factorCrowdItemList.length == 0) {
          this.$message.error('请新增监测因素数据');
          return;
        }
        console.log(
          this.factorCrowdItemList,
          'factorCrowdItemList监测因素数据'
        );
        const {
          contactTotalPeoples,
          ifhfDust,
          hfDustPeoples,
          ifhfChemistry,
          hfChemistryPeoples,
          ifhfPhysics,
          hfPhysicsPeoples,
          ...restFormData // 剩余的 formData 属性
        } = this.formData;

        // 创建 crowd 对象
        const crowd = {
          contactTotalPeoples,
          ifhfDust,
          hfDustPeoples,
          ifhfChemistry,
          hfChemistryPeoples,
          ifhfPhysics,
          hfPhysicsPeoples,
        };
        let params = {
          ...restFormData,
          keyJobInvestigationSituationList:
            this.keyJobInvestigationSituationList,
          factorCrowdItemList: this.factorCrowdItemList,
          crowd,
        };
        console.log(params, 'params0000000');
        const res = await baseInfo(params);
        // console.log(res, '000000res');
        if (res.status == 200) {
          this.$message.success('基本信息保存成功');
        }
      } catch (err) {
        console.log(err, 'err.message');
        console.log(err.message, 'err.message');
        this.$message.error(err.message);
      }
    },
  },
  computed: {},
};
</script>
<style lang="scss" scoped>
.economicsClass {
  position: relative;
  .economicsSpan {
    position: absolute;
    left: -77px;
    top: 0;
    color: #f56c6c;
  }
}
</style>
