<template>
  <div>
    <TitleTag titleName="防护设施&用品"></TitleTag>
    <el-form
      :model="form"
      :rules="rules"
      ref="formRef"
      label-width="220px"
      size="mini"
    >
      <!-- 防尘设施部分 -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="防尘设施-设置情况" prop="fcssSituation">
            <el-radio-group
              v-model="form.fcssSituation"
              @change="handleFcssChange"
            >
              <el-radio :label="1">有</el-radio>
              <el-radio :label="2">部分有</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="防尘设施-防护效果"
            prop="fcssEffect"
            :required="form.fcssSituation !== 3"
          >
            <el-radio-group
              v-model="form.fcssEffect"
              :disabled="form.fcssSituation === 3"
            >
              <el-radio :label="1">有效</el-radio>
              <el-radio :label="2">部分有效</el-radio>
              <el-radio :label="3">无效</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 防毒设施部分 -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="防毒设施-设置情况" prop="fdssSituation">
            <el-radio-group
              v-model="form.fdssSituation"
              @change="handleFdssChange"
            >
              <el-radio :label="1">有</el-radio>
              <el-radio :label="2">部分有</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="防毒设施-防护效果"
            prop="fdssEffect"
            :required="form.fdssSituation !== 3"
          >
            <el-radio-group
              v-model="form.fdssEffect"
              :disabled="form.fdssSituation === 3"
            >
              <el-radio :label="1">有效</el-radio>
              <el-radio :label="2">部分有效</el-radio>
              <el-radio :label="3">无效</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 防噪声设施部分 -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="防噪声设施-设置情况" prop="fzsssSituation">
            <el-radio-group
              v-model="form.fzsssSituation"
              @change="handleFzsssChange"
            >
              <el-radio :label="1">有</el-radio>
              <el-radio :label="2">部分有</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="防噪声设施-防护效果"
            prop="fzsssEffect"
            :required="form.fzsssSituation !== 3"
          >
            <el-radio-group
              v-model="form.fzsssEffect"
              :disabled="form.fzsssSituation === 3"
            >
              <el-radio :label="1">有效</el-radio>
              <el-radio :label="2">部分有效</el-radio>
              <el-radio :label="3">无效</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 防尘口罩部分 -->
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="防尘口罩-发放情况"
            prop="fckzDistributionSituation"
          >
            <el-radio-group
              v-model="form.fckzDistributionSituation"
              @change="handleFckzChange"
            >
              <el-radio :label="1">有</el-radio>
              <el-radio :label="2">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="防尘口罩-佩戴情况"
            prop="fckzWearSituation"
            :required="form.fckzDistributionSituation === 1"
          >
            <el-radio-group
              v-model="form.fckzWearSituation"
              :disabled="form.fckzDistributionSituation !== 1"
            >
              <el-radio :label="1">有</el-radio>
              <el-radio :label="2">部分</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 防毒口罩部分 -->
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="防毒口罩或面罩-发放情况"
            prop="fdkzDistributionSituation"
          >
            <el-radio-group
              v-model="form.fdkzDistributionSituation"
              @change="handleFdkzChange"
            >
              <el-radio :label="1">有</el-radio>
              <el-radio :label="2">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="防毒口罩或面罩-佩戴情况"
            prop="fdkzWearSituation"
            :required="form.fdkzDistributionSituation === 1"
          >
            <el-radio-group
              v-model="form.fdkzWearSituation"
              :disabled="form.fdkzDistributionSituation !== 1"
            >
              <el-radio :label="1">有</el-radio>
              <el-radio :label="2">部分</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 防噪声耳塞部分 -->
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="防噪声耳塞或耳罩-发放情况"
            prop="fzyesDistributionSituation"
          >
            <el-radio-group
              v-model="form.fzyesDistributionSituation"
              @change="handleFzyesChange"
            >
              <el-radio :label="1">有</el-radio>
              <el-radio :label="2">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="防噪声耳塞或耳罩-佩戴情况"
            prop="fzyesWearSituation"
            :required="form.fzyesDistributionSituation === 1"
          >
            <el-radio-group
              v-model="form.fzyesWearSituation"
              :disabled="form.fzyesDistributionSituation !== 1"
            >
              <el-radio :label="1">有</el-radio>
              <el-radio :label="2">部分</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 警示标识部分 -->
      <el-row>
        <el-col :span="8">
          <el-form-item
            label="粉尘职业病危害警示标识及警示说明"
            label-width="auto"
            prop="signDust"
          >
            <el-radio-group v-model="form.signDust">
              <el-radio :label="1">有</el-radio>
              <el-radio :label="2">部分有</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="化学职业病危害警示标识及警示说明"
            prop="signChemistry"
            label-width="auto"
          >
            <el-radio-group v-model="form.signChemistry">
              <el-radio :label="1">有</el-radio>
              <el-radio :label="2">部分有</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="物理职业病危害警示标识及警示说明"
            prop="signPhysics"
            label-width="auto"
          >
            <el-radio-group v-model="form.signPhysics">
              <el-radio :label="1">有</el-radio>
              <el-radio :label="2">部分有</el-radio>
              <el-radio :label="3">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item> -->
      <el-row>
        <el-col :span="24">
          <el-form-item label-width="20px">
            <el-button type="primary" size="medium" @click="nextStep"
              >下一步</el-button
            >
            <el-button size="medium" @click="saveShortTime()">暂存</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import { safeguard, getSafeguardDetail } from '@/api';
export default {
  components: {
    TitleTag,
  },
  data() {
    // 自定义验证规则
    const validateFcssEffect = (rule, value, callback) => {
      if (this.form.fcssSituation !== 3 && value === undefined) {
        callback(new Error('请选择防尘设施防护效果'));
      } else {
        callback();
      }
    };

    const validateFdssEffect = (rule, value, callback) => {
      if (this.form.fdssSituation !== 3 && value === undefined) {
        callback(new Error('请选择防毒设施防护效果'));
      } else {
        callback();
      }
    };

    const validateFzsssEffect = (rule, value, callback) => {
      if (this.form.fzsssSituation !== 3 && value === undefined) {
        callback(new Error('请选择防噪声设施防护效果'));
      } else {
        callback();
      }
    };

    const validateFckzWear = (rule, value, callback) => {
      if (this.form.fckzDistributionSituation === 1 && value === undefined) {
        callback(new Error('请选择防尘口罩佩戴情况'));
      } else {
        callback();
      }
    };

    const validateFdkzWear = (rule, value, callback) => {
      if (this.form.fdkzDistributionSituation === 1 && value === undefined) {
        callback(new Error('请选择防毒口罩佩戴情况'));
      } else {
        callback();
      }
    };

    const validateFzyesWear = (rule, value, callback) => {
      if (this.form.fzyesDistributionSituation === 1 && value === undefined) {
        callback(new Error('请选择防噪声耳塞佩戴情况'));
      } else {
        callback();
      }
    };

    return {
      form: {
        fcssSituation: 3,
        fcssEffect: undefined,
        fdssSituation: 3,
        fdssEffect: undefined,
        fzsssSituation: 3,
        fzsssEffect: undefined,
        fckzDistributionSituation: 2,
        fckzWearSituation: undefined,
        fdkzDistributionSituation: 2,
        fdkzWearSituation: undefined,
        fzyesDistributionSituation: 2,
        fzyesWearSituation: undefined,
        signDust: 3,
        signChemistry: 3,
        signPhysics: 3,
      },
      rules: {
        fcssSituation: [
          {
            required: true,
            message: '请选择防尘设施设置情况',
            trigger: 'change',
          },
        ],
        fcssEffect: [{ validator: validateFcssEffect, trigger: 'change' }],
        fdssSituation: [
          {
            required: true,
            message: '请选择防毒设施设置情况',
            trigger: 'change',
          },
        ],
        fdssEffect: [{ validator: validateFdssEffect, trigger: 'change' }],
        fzsssSituation: [
          {
            required: true,
            message: '请选择防噪声设施设置情况',
            trigger: 'change',
          },
        ],
        fzsssEffect: [{ validator: validateFzsssEffect, trigger: 'change' }],
        fckzDistributionSituation: [
          {
            required: true,
            message: '请选择防尘口罩发放情况',
            trigger: 'change',
          },
        ],
        fckzWearSituation: [{ validator: validateFckzWear, trigger: 'change' }],
        fdkzDistributionSituation: [
          {
            required: true,
            message: '请选择防毒口罩发放情况',
            trigger: 'change',
          },
        ],
        fdkzWearSituation: [{ validator: validateFdkzWear, trigger: 'change' }],
        fzyesDistributionSituation: [
          {
            required: true,
            message: '请选择防噪声耳塞发放情况',
            trigger: 'change',
          },
        ],
        fzyesWearSituation: [
          { validator: validateFzyesWear, trigger: 'change' },
        ],
        signDust: [
          {
            required: true,
            message: '请选择粉尘警示标识情况',
            trigger: 'change',
          },
        ],
        signChemistry: [
          {
            required: true,
            message: '请选择化学警示标识情况',
            trigger: 'change',
          },
        ],
        signPhysics: [
          {
            required: true,
            message: '请选择物理警示标识情况',
            trigger: 'change',
          },
        ],
      },
    };
  },
  async created() {
    const {
      id,
      taskId,
      jcOrgId,
      serviceOrgId,
      EnterpriseID,
      provincialWorkspaceMonitoringId,
    } = this.$route.query;
    this.form.workspaceMonitoringRecordId = id; //监测记录id
    this.form.jcOrgId = jcOrgId; // 委托单位
    this.form.serviceOrgId = serviceOrgId; // 检测机构
    this.form.EnterpriseID = EnterpriseID; // 获取企业/用人单位
    this.provincialWorkspaceMonitoringId = provincialWorkspaceMonitoringId; //省级任务id
    // 获取详情
    this.getSafeguardDetail(id);
  },
  methods: {
    async getSafeguardDetail(id) {
      console.log(id, '-----id');
      const res = await getSafeguardDetail({
        workspaceMonitoringRecordId: id,
      });
      if (res.status == 200) {
        // 防尘设施
        this.form.fcssSituation = res.data.fcssSituation;
        this.form.fcssEffect =
          res.data.fcssSituation === 3
            ? undefined
            : res.data.fcssEffect || undefined;

        // 防毒设施
        this.form.fdssSituation = res.data.fdssSituation;
        this.form.fdssEffect =
          res.data.fdssSituation === 3
            ? undefined
            : res.data.fdssEffect || undefined;

        // 防噪声设施
        this.form.fzsssSituation = res.data.fzsssSituation;
        this.form.fzsssEffect =
          res.data.fzsssSituation === 3
            ? undefined
            : res.data.fzsssEffect || undefined;

        // 防尘口罩
        this.form.fckzDistributionSituation =
          res.data.fckzDistributionSituation;
        this.form.fckzWearSituation =
          res.data.fckzDistributionSituation !== 1
            ? undefined
            : res.data.fckzWearSituation || undefined;

        // 防毒口罩
        this.form.fdkzDistributionSituation =
          res.data.fdkzDistributionSituation;
        this.form.fdkzWearSituation =
          res.data.fdkzDistributionSituation !== 1
            ? undefined
            : res.data.fdkzWearSituation || undefined;

        // 防噪声耳塞
        this.form.fzyesDistributionSituation =
          res.data.fzyesDistributionSituation;
        this.form.fzyesWearSituation =
          res.data.fzyesDistributionSituation !== 1
            ? undefined
            : res.data.fzyesWearSituation || undefined;

        // 警示标识
        this.form.signDust = res.data.signDust;
        this.form.signChemistry = res.data.signChemistry;
        this.form.signPhysics = res.data.signPhysics;
      }
    },
    handleFcssChange(val) {
      if (val === 3) {
        this.form.fcssEffect = undefined;
      }
    },
    handleFdssChange(val) {
      if (val === 3) {
        this.form.fdssEffect = undefined;
      }
    },
    handleFzsssChange(val) {
      if (val === 3) {
        this.form.fzsssEffect = undefined;
      }
    },
    handleFckzChange(val) {
      if (val !== 1) {
        this.form.fckzWearSituation = undefined;
      }
    },
    handleFdkzChange(val) {
      if (val !== 1) {
        this.form.fdkzWearSituation = undefined;
      }
    },
    handleFzyesChange(val) {
      if (val !== 1) {
        this.form.fzyesWearSituation = undefined;
      }
    },
    // 去除对象中值为undefined的属性
    removeUndefinedFields(obj) {
      const newObj = {};
      Object.keys(obj).forEach((key) => {
        if (obj[key] !== undefined) {
          newObj[key] = obj[key];
        }
      });
      return newObj;
    },
    submitForm() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          // 提交表单逻辑
          console.log('表单数据:', this.form);
          // 去掉数据为undefined的数据
          // 创建新对象并去除undefined值
          // const formData = this.removeUndefinedFields(this.form);
          // console.log(formData, '去除undefined的数据');
          // 这里可以调用API提交formData
          let params = {
            // workspaceMonitoringRecordId: this.form.workspaceMonitoringRecordId,
            // EnterpriseID: this.form.EnterpriseID,
            ...this.form,
          };
          console.log(params, 'params00000');
          const res = await safeguard(params);
          if (res.status == 200) {
            this.$message.success('提交成功');
          }
          // this.$api.submitForm(formData).then(...)
        } else {
          this.$message.error('请检查表单填写是否正确');
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.formRef.resetFields();
    },
    // 下一步
    nextStep() {
      this.submitForm();
      this.$emit('nextStepInfo', 4);
    },
    saveShortTime() {
      this.submitForm();
      this.$message.success('防护设施&用品暂存成功！');
    },
  },
};
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
}
.el-col {
  padding-right: 20px;
}
</style>
