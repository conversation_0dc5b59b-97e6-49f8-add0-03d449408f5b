<template>
  <div class="detection-data-page">
    <!-- 基本信息卡片 -->

    <TitleTag titleName="检测数据展示"></TitleTag>
    <div class="info-container">
      <el-row :gutter="20" class="info-row">
        <el-col :span="12" class="info-item">
          <span class="info-label">单位名称：</span>
          <span class="info-value">{{ unitInfo.unitName || '-' }}</span>
        </el-col>
        <el-col :span="12" class="info-item">
          <span class="info-label">统一社会信用代码：</span>
          <span class="info-value">{{ unitInfo.creditCode || '-' }}</span>
        </el-col>
        <el-col :span="12" class="info-item">
          <span class="info-label">采样开始日期：</span>
          <span class="info-value">{{
            unitInfo.samplingStartDate || '-'
          }}</span>
        </el-col>
        <el-col :span="12" class="info-item">
          <span class="info-label">采样结束日期：</span>
          <span class="info-value">{{ unitInfo.samplingEndDate || '-' }}</span>
        </el-col>
      </el-row>
    </div>

    <!-- 第一个表格：检测结果重点行业对应检测因素 -->

    <el-divider content-position="left"
      >检测结果重点行业对应检测因素</el-divider
    >
    <el-table
      :data="tdZxjcResultIndustryFactorList"
      border
      stripe
      :header-cell-style="{ background: '#f5f7fa' }"
    >
      <el-table-column type="index" label="序号" width="60"></el-table-column>
      <el-table-column label="监测因素编码" show-overflow-tooltip>
        <template #default="scope">
          {{ getFactorName(scope.row.factorNo) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="checkDate"
        label="危害因素检测日期"
      ></el-table-column>
    </el-table>

    <!-- 第二个表格：岗位环节对应检测地点 - 实现二级表头 -->

    <el-divider content-position="left">岗位环节对应检测地点</el-divider>
    <el-table
      :data="resultJobPlaceList"
      border
      stripe
      :header-cell-style="{ background: '#f5f7fa' }"
      style="width: 100%"
      row-key="resultFactorJobUuid"
    >
      <!-- 主表列 - 跨越两级表头 -->
      <el-table-column
        type="index"
        label="序号"
        width="60"
        :rowspan="2"
      ></el-table-column>
      <el-table-column
        prop="resultFactorJobUuid"
        label="岗位唯一标识"
        :rowspan="2"
      ></el-table-column>
      <el-table-column label="监测因素编码" :rowspan="2" show-overflow-tooltip>
        <template #default="scope">
          {{ getFactorName(scope.row.factorNo) }}
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="checkPlace"
        label="检测地点"
        :rowspan="2"
      ></el-table-column>
      <el-table-column
        prop="contactHour"
        label="接触时间（h）"
        :rowspan="2"
      ></el-table-column>
      <el-table-column
        prop="contactNum"
        label="接触次数"
        :rowspan="2"
      ></el-table-column>
      <el-table-column
        prop="timeSpace"
        label="间隔时间"
        :rowspan="2"
      ></el-table-column>

      <!-- 多级表头：检测地点下的检测项目 -->
      <el-table-column label="检测地点下的检测项目">
        <el-table-column
          v-for="(col, index) in resultJobPlaceProColumns"
          :key="index"
          :label="col.label"
          :width="col.width"
        >
          <!-- 直接循环原始数组 resultJobPlaceProList -->
          <template #default="scope">
            <div
              v-for="(item, idx) in scope.row.resultJobPlaceProList"
              :key="idx"
              class="multi-item"
            >
              {{ item[col.prop] }}
            </div>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>

    <!-- 第三个表格：岗位/环节对应监测项目的检测值 -->

    <el-divider content-position="left"
      >岗位/环节对应监测项目的检测值</el-divider
    >
    <el-table
      :data="resultProValueList"
      border
      stripe
      :header-cell-style="{ background: '#f5f7fa' }"
    >
      <el-table-column type="index" label="序号" width="60"></el-table-column>
      <el-table-column
        prop="resultFactorJobUuid"
        label="岗位唯一标识"
      ></el-table-column>
      <el-table-column label="监测因素编码" show-overflow-tooltip>
        <template #default="scope">
          {{ getFactorName(scope.row.factorNo) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="factorProNo"
        label="检测项目编码"
      ></el-table-column>
      <el-table-column prop="fixType" label="个体/定点"></el-table-column>
      <el-table-column prop="checkValue" label="检测值"></el-table-column>
      <el-table-column prop="sampleTag" label="检测值标记"></el-table-column>
      <el-table-column
        prop="silicaContent"
        label="游离SiO₂含量"
      ></el-table-column>
    </el-table>
    <el-button type="primary" v-if="!fromDetail" style="margin-top: 20px" @click="publishBtn()"
      >提交审核</el-button
    >
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import { auditRecord, monitoringResult, getFactorListNation } from '@/api';
export default {
  name: 'DetectionDataDisplay',
  components: {
    TitleTag,
  },
  props: {
    fromDetail:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      resultJobPlaceProColumns: [
        { prop: 'factorProNo', label: '检测项目NO', width: 100 },
        { prop: 'sample1', label: '样本值1', width: 80 },
        { prop: 'sample1Tag', label: '样本1标记', width: 80 },
        { prop: 'sample2', label: '样本值2', width: 80 },
        { prop: 'sample2Tag', label: '样本2标记', width: 80 },
        { prop: 'sample3', label: '样本值3', width: 80 },
        { prop: 'sample3Tag', label: '样本3标记', width: 80 },
        { prop: 'sample4', label: '样本值4', width: 80 },
        { prop: 'sample4Tag', label: '样本4标记', width: 80 },
        { prop: 'sample5', label: '样本值5', width: 80 },
        { prop: 'sample5Tag', label: '样本5标记', width: 80 },
        { prop: 'sample6', label: '样本值6', width: 80 },
        { prop: 'sample6Tag', label: '样本6标记', width: 80 },
        { prop: 'fillCste', label: 'CSTE（短时间浓度）', width: 140 },
        { prop: 'avgValue', label: '平均值', width: 80 },
        { prop: 'silicaContent', label: '游离SiO₂含量', width: 120 },
      ],
      // 基本信息
      unitInfo: {
        unitName: '',
        creditCode: '',
        samplingStartDate: '',
        samplingEndDate: '',
      },
      // 第一个表格数据
      tdZxjcResultIndustryFactorList: [],
      // 第二个表格原始数据
      resultJobPlaceList: [],
      // 格式化后的第二个表格数据（用于展示）
      formattedResultJobPlaceList: [],
      // 第三个表格数据
      resultProValueList: [],
      formData: {},
      provincialWorkspaceMonitoringId: '',
      factorOptions: [],
    };
  },
  async created() {
    const {
      id,
      taskId,
      jcOrgId,
      serviceOrgId,
      EnterpriseID,
      provincialWorkspaceMonitoringId,
    } = this.$route.query;
    this.formData.workspaceMonitoringRecordId = id; //监测记录id
    this.formData.jcOrgId = jcOrgId; // 委托单位
    this.formData.serviceOrgId = serviceOrgId; // 检测机构
    this.formData.EnterpriseID = EnterpriseID; // 获取企业/用人单位
    this.provincialWorkspaceMonitoringId = provincialWorkspaceMonitoringId; //省级任务id

    // 获取详情
    await this.fetchFactorList(); // 等待因素数据加载完成
    this.monitoringResult(taskId);
  },
  mounted() {
    // 模拟从接口获取数据
    // this.fetchData();
  },
  methods: {
    async monitoringResult(taskId) {
      let params = { workspaceMonitoringRecordId: taskId };
      const res = await monitoringResult(params);
      if (res.status == 200) {
        console.log(res.data, '0000--================');
        this.unitInfo = res.data;
        this.resultJobPlaceList = res.data.resultJobPlaceList;
        this.resultProValueList = res.data.resultProValueList;
        this.tdZxjcResultIndustryFactorList =
          res.data.tdZxjcResultIndustryFactorList;
      }
    },
    async fetchFactorList() {
      const res = await getFactorListNation();
      if (res.status == 200) {
        this.factorOptions = res.data;
      }
    },
    getFactorName(factorNo) {
      if (!factorNo) return '-'; // 空值处理

      // 遍历所有分类（化学、生物、物理等）
      for (const category of this.factorOptions) {
        // 每个分类的具体因素在children数组中
        const factors = category.children;

        // 确保children是数组
        if (Array.isArray(factors)) {
          // 查找cnCsjcCode与factorNo匹配的因素（注意类型转换）
          const matchedFactor = factors.find(item => {
            // cnCsjcCode是数字，factorNo是字符串，转为同类型对比
            return item.cnCsjcCode.toString() === factorNo;
          });

          if (matchedFactor) {
            // 拼接chineseName数组为字符串
            return matchedFactor.chineseName.join(',');
          }
        }
      }

      // 未匹配到时返回原始编码
      return factorNo;
    },
    // 模拟接口请求
    fetchData() {
      setTimeout(() => {
        // 基本信息
        this.unitInfo = {
          unitName: 'XX制造有限公司',
          creditCode: '91110101ABCDEFGHIJ',
          samplingStartDate: '2023-05-10',
          samplingEndDate: '2023-05-15',
        };

        // 第一个表格数据
        this.tdZxjcResultIndustryFactorList = [
          { factorNo: 'FACT-001', checkDate: '2023-05-10' },
          { factorNo: 'FACT-002', checkDate: '2023-05-11' },
          { factorNo: 'FACT-003', checkDate: '2023-05-12' },
          { factorNo: 'FACT-004', checkDate: '2023-05-13' },
        ];

        // 第二个表格原始数据 - resultJobPlaceProList为数组
        this.resultJobPlaceList = [
          {
            resultFactorJobUuid: 'JOB-001',
            factorNo: 'FACT-001',
            checkPlace: '车间A区',
            contactHour: 8,
            contactNum: 3,
            timeSpace: 2,
            resultJobPlaceProList: [
              {
                factorProNo: 'PRO-001',
                sample1: 0.05,
                sample1Tag: '合格',
                sample2: 0.06,
                sample2Tag: '合格',
                sample3: 0.04,
                sample3Tag: '合格',
                sample4: 0.05,
                sample4Tag: '合格',
                sample5: 0.07,
                sample5Tag: '合格',
                sample6: 0.05,
                sample6Tag: '合格',
                fillCste: 0.06,
                avgValue: 0.05,
                silicaContent: '2.3%',
              },
              {
                factorProNo: 'PRO-002',
                sample1: 0.03,
                sample1Tag: '合格',
                sample2: 0.04,
                sample2Tag: '合格',
                sample3: 0.05,
                sample3Tag: '合格',
                sample4: 0.04,
                sample4Tag: '合格',
                sample5: 0.03,
                sample5Tag: '合格',
                sample6: 0.04,
                sample6Tag: '合格',
                fillCste: 0.04,
                avgValue: 0.04,
                silicaContent: '1.8%',
              },
            ],
          },
          {
            resultFactorJobUuid: 'JOB-002',
            factorNo: 'FACT-002',
            checkPlace: '车间B区',
            contactHour: 6,
            contactNum: 2,
            timeSpace: 4,
            resultJobPlaceProList: [
              {
                factorProNo: 'PRO-003',
                sample1: 0.08,
                sample1Tag: '合格',
                sample2: 0.09,
                sample2Tag: '合格',
                sample3: 0.07,
                sample3Tag: '合格',
                sample4: 0.08,
                sample4Tag: '合格',
                sample5: 0.09,
                sample5Tag: '合格',
                sample6: 0.08,
                sample6Tag: '合格',
                fillCste: 0.08,
                avgValue: 0.08,
                silicaContent: '3.1%',
              },
            ],
          },
        ];

        // 第三个表格数据
        this.resultProValueList = [
          {
            resultFactorJobUuid: 'JOB-001',
            factorNo: 'FACT-001',
            factorProNo: 'PRO-001',
            fixType: '定点',
            checkValue: 0.06,
            sampleTag: '合格',
            silicaContent: '2.3%',
          },
          {
            resultFactorJobUuid: 'JOB-001',
            factorNo: 'FACT-001',
            factorProNo: 'PRO-002',
            fixType: '个体',
            checkValue: 0.05,
            sampleTag: '合格',
            silicaContent: '2.1%',
          },
          {
            resultFactorJobUuid: 'JOB-002',
            factorNo: 'FACT-002',
            factorProNo: 'PRO-003',
            fixType: '定点',
            checkValue: 0.08,
            sampleTag: '合格',
            silicaContent: '3.1%',
          },
        ];
      }, 500);
    },

    // 格式化表格数据，将嵌套的数组展开为平级结构
    formatTableData() {
      this.formattedResultJobPlaceList = [];

      this.resultJobPlaceList.forEach(mainItem => {
        // 如果有多个检测项目，除了第一个外，其他的主表信息为空
        mainItem.resultJobPlaceProList.forEach((subItem, index) => {
          const formattedItem =
            index === 0
              ? { ...mainItem, ...subItem } // 第一个项目保留主表信息
              : {
                  ...subItem,
                  resultFactorJobUuid: '',
                  factorNo: '',
                  checkPlace: '',
                  contactHour: '',
                  contactNum: '',
                  timeSpace: '',
                }; // 后续项目清空主表信息

          // 删除原始的数组属性
          delete formattedItem.resultJobPlaceProList;

          this.formattedResultJobPlaceList.push(formattedItem);
        });
      });
    },
    publishBtn() {
      this.$confirm('确定要提交审核吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          let params = {
            recordId: this.formData.workspaceMonitoringRecordId,
          };
          const res = await auditRecord(params);
          if (res.status == 200) {
            this.$message({
              type: 'success',
              message: '提交审核成功!',
            });
            this.$router.go(-1);
          } else {
            this.$message({
              type: 'error',
              message: res.message,
            });
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
.detection-data-page {
  padding: 20px;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.info-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
  overflow: auto;
}

.info-container {
  padding: 15px 0;
  font-size: 14px;
}

.info-row {
  margin: 0 -10px;
}

.info-item {
  padding: 10px;
  box-sizing: border-box;
}

.info-label {
  display: inline-block;
  width: 150px;
  font-weight: bold;
  color: #606266;
}

.info-value {
  color: #303133;
  word-break: break-all;
}

/* 调整表格列的样式 */
::v-deep .el-table th {
  font-weight: bold;
}

/* 处理表格内容过长的情况 */
::v-deep .el-table .cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

::v-deep .el-card__header {
  font-weight: bold;
  font-size: 16px;
}
/* 新增数组项样式，优化展示 */
.multi-item {
  padding: 4px 0;
  &:not(:last-child) {
    border-bottom: 1px dashed #eee;
  }
}
</style>
