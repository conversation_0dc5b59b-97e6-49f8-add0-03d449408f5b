<template>
  <div>
    <TitleTag titleName="机构信息"></TitleTag>
    <el-row>
      <el-col :span="24">
        <table
          style="width: 100%; border: 1px solid #ddd"
          class="table-container"
        >
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>检测机构
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.serviceOrgName || '暂无数据' }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>委托单位
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.jcOrgName || '暂无数据' }}</span>
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>用人单位
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.enterpriseName || '暂无数据' }}</span>
            </th>
          </tr>
        </table>
      </el-col>
    </el-row>
    <TitleTag titleName="基本信息"></TitleTag>
    <el-row>
      <el-col :span="24">
        <table
          style="width: 100%; border: 1px solid #ddd"
          class="table-container"
        >
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>用人单位名称
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.unitName }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>社会信用代码
            </th>
            <th colspan="8" class="th-input">
              {{ baseInfo.creditCode }}
            </th>
          </tr>

          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>工作场所地址
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.workAddr }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>单位注册地址
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.regAddr }}</span>
            </th>
          </tr>

          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>所属行业
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.industryName }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>法人姓名
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.legalPerson }}</span>
            </th>
          </tr>

          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>职业卫生管理联系人
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.linkManager }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>联系电话
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.linkPhone }}</span>
            </th>
          </tr>

          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>本单位在册职工总数
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.empNum }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>外委人员总数
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.externalNum }}</span>
            </th>
          </tr>

          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>经济类型
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.economicType }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>用人单位规模
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.enterpriseScaleName }}</span>
            </th>
          </tr>
        </table>
      </el-col>
    </el-row>

    <div class="titleNext">职业病危害项目申报情况</div>
    <el-row>
      <el-col :span="24">
        <table
          style="width: 100%; border: 1px solid #ddd"
          class="table-container"
        >
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>是否进行了申报
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifDeclare ? '是' : '否' }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>是否进行了年度更新
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifAnnualUpdate ? '是' : '否' }}</span>
            </th>
          </tr>
        </table>
      </el-col>
    </el-row>

    <div class="titleNext">职业卫生培训情况</div>
    <el-row>
      <el-col :span="24">
        <table
          style="width: 100%; border: 1px solid #ddd"
          class="table-container"
        >
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>主要负责人培训
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifLeadersTrain ? '有' : '无' }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>职业卫生管理人员培训情况
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifManagersTrain ? '有' : '无' }}</span>
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>接触职业病危害因素年度培训总人数
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.trainSum }}</span>
            </th>
          </tr>
        </table>
      </el-col>
    </el-row>

    <div class="titleNext">防护设施“三同时” 情况</div>
    <el-row>
      <el-col :span="24">
        <table
          style="width: 100%; border: 1px solid #ddd"
          class="table-container"
        >
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>引进项目情况
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifImport ? '有' : '无' }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>当前工作阶段
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.workNodeNames.join('、') }}</span>
            </th>
          </tr>

          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>预评价开展情况
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifPreLaunchName }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>职业病防护设施设计专篇
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifDesignName }}</span>
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>控制效果评价开展情况
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifLaunchName }}</span>
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>调查后是否申报
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifAfterDeclare ? '是' : '否' }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>调查机构名称
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.dcUnitName }}</span>
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>调查机构社会信用代码
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.dcCreditCode }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>监测机构名称
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.jcUnitName }}</span>
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>监测机构统一社会信用代码
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.jcCreditCode }}</span>
            </th>

            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>上一年度是否存在职业性噪声聋
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifNoiseDeafness ? '是' : '否' }}</span>
            </th>
          </tr>
        </table>
      </el-col>
    </el-row>
    <div class="titleNext">重点岗位调查情况集合</div>
    <el-table
      :data="baseInfo.keyJobInvestigationSituationList"
      border
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="jobNo" label="岗位环节名称">
        <template slot-scope="scope">
          {{ getJobName(scope.row.jobNo) }}
          <!-- ({{ scope.row.jobNo }}) -->
        </template>
      </el-table-column>
      <el-table-column prop="ifExistJob" label="是否重点岗位">
        <template slot-scope="scope">
          <el-tag :type="scope.row.ifExistJob ? 'danger' : 'success'">
            {{ scope.row.ifExistJob ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
    <div class="titleNext">职业病危害因素种类及接触人数</div>
    <el-row>
      <el-col :span="24">
        <table
          style="width: 100%; border: 1px solid #ddd"
          class="table-container"
        >
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>接触职业病危害因素总人数
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.contactTotalPeoples || 0 }}</span>
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4">危害因素_粉尘</th>
            <th colspan="2" class="th-input">
              {{ baseInfo.ifhfDust ? '是' : '否' }}
            </th>
            <th class="left-tittle table-label" colspan="2">接触总人数</th>
            <th colspan="6" class="th-input">
              {{ baseInfo.hfDustPeoples || 0 }}
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4">
              危害因素_化学物质
            </th>
            <th colspan="2" class="th-input">
              {{ baseInfo.ifhfChemistry ? '是' : '否' }}
            </th>
            <th class="left-tittle table-label" colspan="2">接触总人数</th>
            <th colspan="6" class="th-input">
              {{ baseInfo.hfChemistryPeoples || 0 }}
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4">
              危害因素_物理因素
            </th>
            <th colspan="2" class="th-input">
              {{ baseInfo.ifhfPhysics ? '是' : '否' }}
            </th>
            <th class="left-tittle table-label" colspan="2">接触总人数</th>
            <th colspan="6" class="th-input">
              {{ baseInfo.hfPhysicsPeoples || 0 }}
            </th>
          </tr>
        </table>
      </el-col>
    </el-row>
    <el-table
      :data="baseInfo.factorCrowdItemList"
      border
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="type" label="因素类型">
        <template slot-scope="scope">
          {{ getTypeName(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column prop="factorId" label="监测因素" width="180">
        <template slot-scope="scope">
          {{ getFactorName(scope.row.factorId) }}
        </template>
      </el-table-column>
      <el-table-column prop="contactNum" label="接触人数"></el-table-column>
      <el-table-column prop="dataType" label="数据类型">
        <template slot-scope="scope">
          <el-tag :type="scope.row.dataType === 1 ? 'primary' : 'success'">
            {{ scope.row.dataType === 1 ? '国家要求' : '自定义添加' }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import TitleTag from './TitleTag.vue';
import {
  getIndustryCategory,
  getEconomicCategory,
  getBaseInfo,
  orgList,
  getJobList,
} from '@/api';
export default {
  components: { TitleTag },
  props:{
    factorOptions:[],
    jobList:[]
  },
  data() {
    return {
      baseInfo: {
        // 基本信息
        unitName: '',
        creditCode: '',
        workAddr: '',
        regAddr: '',
        industryName: '',
        legalPerson: '',
        linkManager: '',
        linkPhone: '',
        empNum: 0,
        externalNum: 0,
        economicType: '',
        enterpriseScaleName: '',

        // 职业卫生培训
        ifLeadersTrain: false,
        ifManagersTrain: false,
        trainSum: 0,

        // 职业病申报
        ifDeclare: false,
        ifAnnualUpdate: false,

        // 三同时情况
        ifImport: false,
        workNodeNames: [],
        ifPreLaunchName: '',
        ifDesignName: '',
        ifLaunchName: '',

        ifAfterDeclare: false, //调查后是否申报
        dcUnitName: '',
        dcCreditCode: '',
        jcUnitName: '',
        jcCreditCode: '',
        ifNoiseDeafness: false,

        // 危害因素
        contactTotalPeoples: 0,
        ifhfDust: false,
        hfDustPeoples: 0,
        ifhfChemistry: false,
        hfChemistryPeoples: 0,
        ifhfPhysics: false,
        hfPhysicsPeoples: 0,
        // 机构名称字段
        serviceOrgName: '', // 检测机构名称
        jcOrgName: '', // 委托单位名称
        enterpriseName: '', // 用人单位名称

        // 新增：重点岗位调查情况集合
        keyJobInvestigationSituationList: [],

        // 新增：职业病危害因素相关数据
        contactTotalPeoples: 0,
        ifhfDust: false,
        hfDustPeoples: 0,
        ifhfChemistry: false,
        hfChemistryPeoples: 0,
        ifhfPhysics: false,
        hfPhysicsPeoples: 0,

        // 新增：职业病危害因素及接触人数明细
        factorCrowdItemList: [],
      },
      industryClass: [], // 行业分类数据
      economicData: [], // 经济类型数据
      serviceOrgList: [], // 检测机构列表
      superUserList: [], // 委托单位列表
      EnterpriseList: [], // 用人单位列表
      taskId: '', // 任务ID（用于获取机构列表）
      provincialWorkspaceMonitoringId: '',
    };
  },
  async created() {
    const {
      id,
      taskId,
      jcOrgId,
      serviceOrgId,
      EnterpriseID,
      provincialWorkspaceMonitoringId,
    } = this.$route.query;
    this.taskId = taskId; // 保存任务ID
    this.baseInfo.workspaceMonitoringRecordId = id; //监测记录id
    this.baseInfo.jcOrgId = jcOrgId; // 委托单位
    this.baseInfo.serviceOrgId = serviceOrgId; // 检测机构
    console.log(EnterpriseID, 'EnterpriseID---');
    this.baseInfo.EnterpriseID = EnterpriseID; // 获取企业/用人单位
    console.log(
      this.baseInfo.EnterpriseID,
      'this.baseInfo.EnterpriseID00009999'
    );
    this.provincialWorkspaceMonitoringId = provincialWorkspaceMonitoringId; //省级任务id
    // 获取表单原始数据
    await this.loadBaseData(id);
    // 加载行业和经济类型字典（用于转换名称）
    await this.loadDictionaries();
    // 获取机构列表数据
    await this.getOrgList();
    // 转换数据格式（原有+新增转换逻辑）
    this.transformBaseInfo();
    // 获取岗位列表
    // await this.fetchJobList();
    // 获取监测危害因素
    await this.fetchFactorList();

  },
  methods: {
    async loadBaseData(id) {
      const res = await getBaseInfo({ workspaceMonitoringRecordId: id });
      if (res.status === 200 && res.data) {
        const { crowd, ...formData } = res.data;
        this.baseInfo = { ...this.baseInfo, ...formData, ...crowd };
      }
    },
    async loadDictionaries() {
      // 加载行业分类
      const industryRes = await getIndustryCategory();
      if (industryRes.status === 200) this.industryClass = industryRes.data;

      // 加载经济类型
      const economicRes = await getEconomicCategory();
      if (economicRes.status === 200) this.economicData = economicRes.data.list;
    },
    // 获取机构列表数据
    async getOrgList() {
      if (!this.taskId) return;
      const res = await orgList({ task_id: this.taskId });
      if (res.status === 200) {
        this.serviceOrgList = res.data.serviceOrgList || [];
        this.superUserList = res.data.superUserList || [];
        this.EnterpriseList = res.data.EnterpriseList || [];
      }
    },
    transformBaseInfo() {
      const { baseInfo, industryClass, economicData } = this;

      // 转换行业名称（级联选择拼接）
      if (baseInfo.industryCode && baseInfo.industryCode.length) {
        let industryLabels = [];
        let currentLevel = industryClass;
        baseInfo.industryCode.forEach((code) => {
          const item = currentLevel.find((i) => i.value === code);
          if (item) {
            industryLabels.push(item.label);
            currentLevel = item.children || [];
          }
        });
        baseInfo.industryName = industryLabels.join('/');
      }

      // 转换经济类型名称 - 修复可选链语法
      if (baseInfo.economicNo && baseInfo.economicNo[1]) {
        const economicItem = economicData.find(
          (item) => item.code === baseInfo.economicNo[1]
        );
        // 将可选链操作符 ?. 替换为传统判断方式
        baseInfo.economicType =
          economicItem && economicItem.content ? economicItem.content : '';
      }

      // 转换企业规模
      const scaleMap = { 1: '大', 2: '中', 3: '小', 4: '微' };
      baseInfo.enterpriseScaleName = scaleMap[baseInfo.enterpriseScale] || '';

      // 转换工作阶段
      const workNodeMap = {
        1: '可研阶段',
        2: '初步设计阶段',
        3: '建设阶段',
        4: '竣工阶段',
      };
      baseInfo.workNodeNames = (baseInfo.workNode || []).map(
        (node) => workNodeMap[node] || ''
      );

      // 转换评价情况
      const evalMap = { 1: '全部', 2: '部分', 3: '无' };
      baseInfo.ifPreLaunchName = evalMap[baseInfo.ifPreLaunch] || '';
      baseInfo.ifDesignName = evalMap[baseInfo.ifDesign] || '';
      baseInfo.ifLaunchName = evalMap[baseInfo.ifLaunch] || '';

      // 1. 检测机构名称
      if (baseInfo.serviceOrgId) {
        const serviceOrg = this.serviceOrgList.find(
          (item) => item._id == baseInfo.serviceOrgId
        );
        baseInfo.serviceOrgName = serviceOrg ? serviceOrg.name : '未知机构';
      }

      // 2. 委托单位名称
      if (baseInfo.jcOrgId) {
        const jcOrg = this.superUserList.find(
          (item) => item._id == baseInfo.jcOrgId
        );
        baseInfo.jcOrgName = jcOrg ? jcOrg.cname : '未知单位';
      }

      // 3. 用人单位名称
      if (baseInfo.EnterpriseID) {
        console.log(baseInfo.EnterpriseID, 'baseInfo.EnterpriseID0000');
        console.log(this.EnterpriseList, 'this.EnterpriseList');
        const enterprise = this.EnterpriseList.find(
          (item) => item._id == baseInfo.EnterpriseID._id
        );
        baseInfo.enterpriseName = enterprise ? enterprise.cname : '未知单位';
      }
    },
    // 获取岗位列表
    // async fetchJobList(keyword) {
    //   try {
    //     const params = { keyword };
    //     const res = await getJobList(params);
    //     if (res.status === 200) {
    //       this.jobList = res.data.list || [];
    //     }
    //   } catch (error) {
    //     console.error('获取岗位列表失败:', error);
    //     this.jobList = [];
    //   }
    // },

    // 根据编码获取岗位名称
    getJobName(jobNo) {
      const item = this.jobList.find((item) => item._id == jobNo);
      return item ? item.jobName : '未知岗位';
    },
    // 获取所有因素扁平化数组
    getAllFactors() {
      return this.factorOptions.flatMap((category) =>
        category.children.map((child) => ({
          ...child,
          category: category._id,
        }))
      );
    },
    // 获取因素类型名称
    getTypeName(type) {
      const types = {
        1: '粉尘因素',
        2: '化学因素',
        3: '物理因素',
      };
      return types[type] || type;
    },
    async fetchFactorList() {
      try {

        // 初始化显示所有因素
        this.filteredFactorOptions = this.getAllFactors();
      } catch (error) {
        console.error('获取监测因素列表失败:', error);
      }
    },
    // 根据编码获取因素名称 - 增强兼容性
    getFactorName(factorId) {
      if (!factorId) return '未知因素';

      // 处理factorId可能是对象的情况
      const id = typeof factorId === 'object' ? factorId._id : factorId;

      if (!this.factorOptions || this.factorOptions.length === 0) {
        return '加载中...';
      }

      // 在所有分类中查找匹配的因素
      for (const category of this.factorOptions) {
        const found = category.children.find((child) => child._id === id);
        if (found) {
          return found.chineseName.join('、');
        }
      }

      return id; // 没找到则返回原始ID
    },
  },
};
</script>

<style lang="scss" scoped>
.table-container {
  table-layout: fixed;
  background: #fff;
  width: 100%;
  margin: 0 auto;
  border-collapse: collapse;

  td {
    padding: 5px 0;
  }

  th {
    font-weight: normal;
    border: 1px solid #e5e5e5;
    padding: 3px 0;
  }

  tr {
    border: 1px solid #e5e5e5;
    width: 100%;
    font-size: 14px;
  }

  .left-tittle {
    background: #f5f7fa;
  }

  .table-label {
    text-align: right;
    padding-right: 5px;
    color: #000;
    font-size: 14px;
    height: 42px;
  }

  .th-input {
    text-align: center;
    padding: 2px 4px;

    span {
      color: #333;
    }
  }
}

.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}
</style>
