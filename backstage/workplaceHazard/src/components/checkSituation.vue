<template>
  <div>
    <TitleTag titleName="上一年度职业健康检查开展情况"></TitleTag>
    <el-form
      :model="formData"
      :rules="rules"
      ref="formData"
      label-width="200px"
      size="mini"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item
            prop="ifhea"
            label="上一年度职业健康检查开展情况"
            label-width="250px"
          >
            <el-radio-group
              v-model="formData.ifhea"
              @change="handleIfheaChange"
            >
              <el-radio :label="true">开展</el-radio>
              <el-radio :label="false">未开展</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item prop="checkTotalPeoples" label="体检总人数">
            <el-input-number
              v-model="formData.checkTotalPeoples"
              :min="0"
              :max="999999"
              :precision="0"
              :disabled="!formData.ifhea"
              controls-position="right"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="ifheaDust" label="有无粉尘_健康检查">
            <el-radio-group
              v-model="formData.ifheaDust"
              :disabled="!formData.ifhea"
            >
              <el-radio :label="true">有</el-radio>
              <el-radio :label="false">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="ifheaChemistry" label="有无化学物质_健康检查">
            <el-radio-group
              v-model="formData.ifheaChemistry"
              :disabled="!formData.ifhea"
            >
              <el-radio :label="true">有</el-radio>
              <el-radio :label="false">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="ifheaPhysics" label="有无物理因素_健康检查">
            <el-radio-group
              v-model="formData.ifheaPhysics"
              :disabled="!formData.ifhea"
            >
              <el-radio :label="true">有</el-radio>
              <el-radio :label="false">无</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left">职业健康检查报告明细</el-divider>
      <addHealthcustodyReportList
        @healthcustodyReport="healthcustodyReport"
        :tableData="healthcustodyReportList"
        :ifhea="formData.ifhea"
      ></addHealthcustodyReportList>
      <el-divider content-position="left"
        >职业健康检查情况-危害因素明细</el-divider
      >
      <addSupervisionItemList
        @supervisionItem="supervisionItem"
        :tableData="supervisionItemList"
        :ifhea="formData.ifhea"
        :taskId="provincialWorkspaceMonitoringId"
        :fatherFactorOptions="factorOptions"
      ></addSupervisionItemList>
      <el-row>
        <el-col :span="24">
          <el-form-item label-width="20px">
            <el-button type="primary" size="medium" @click="nextStep"
              >下一步</el-button
            >
            <el-button size="medium">暂存</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import addHealthcustodyReportList from '@/components/addHealthcustodyReportList.vue';
import addSupervisionItemList from '@/components/addSupervisionItemList.vue';
import {
  checkSituation,
  getCheckSituationDetail,
  previewPhysicalHazard,
  getFactorListNation,
  previewCheckSum,
} from '@/api';
export default {
  name: 'firstMessage',
  components: {
    TitleTag,
    addHealthcustodyReportList,
    addSupervisionItemList,
  },
  props: {},
  data() {
    // 自定义验证规则
    const validateIfatFields = (rule, value, callback) => {
      if (this.formData.ifhea && value === undefined) {
        callback(new Error('此项为必填项'));
      } else {
        callback();
      }
    };
    // const validateNumberField = (rule, value, callback) => {
    //   if (value > 999999) {
    //     callback(new Error('数值不能超过6位数'));
    //   } else {
    //     callback();
    //   }
    // };
    return {
      healthcustodyReportList: [],
      supervisionItemList: [],
      formData: {
        ifhea: false,
        checkTotalPeoples: 0,
        ifheaDust: undefined,
        ifheaChemistry: undefined,
        ifheaPhysics: undefined,
      },
      rules: {
        // checkTotalPeoples: [
        //   { required: true, message: '请输入体检总人数', trigger: 'blur' },
        //   {
        //     type: 'number',
        //     min: 0,
        //     message: '体检总人数不能小于0',
        //     trigger: 'blur',
        //   },
        //   { validator: validateNumberField, trigger: 'blur' },
        // ],
        ifheaDust: [{ validator: validateIfatFields, trigger: 'change' }],
        ifheaChemistry: [{ validator: validateIfatFields, trigger: 'change' }],
        ifheaPhysics: [{ validator: validateIfatFields, trigger: 'change' }],
      },
      serviceOrgList: [],
      provincialWorkspaceMonitoringId: '',
      factorOptions: [],
    };
  },
  async created() {
    const {
      id,
      taskId,
      jcOrgId,
      serviceOrgId,
      EnterpriseID,
      provincialWorkspaceMonitoringId,
    } = this.$route.query;
    this.formData.workspaceMonitoringRecordId = id; //监测记录id
    this.formData.jcOrgId = jcOrgId; // 委托单位
    this.formData.serviceOrgId = serviceOrgId; // 检测机构
    this.formData.EnterpriseID = EnterpriseID; // 获取企业/用人单位
    this.provincialWorkspaceMonitoringId = provincialWorkspaceMonitoringId; //省级任务id
    // 获取详情
    this.getCheckSituationDetail(id);
  },
  watch: {},
  async mounted() {},
  methods: {
    async getCheckSituationDetail(id) {
      const res = await getCheckSituationDetail({
        workspaceMonitoringRecordId: id,
      });
      if (res.status == 200) {
        if (res.data.ifhea) {
          this.formData.workspaceMonitoringRecordId =
            res.data.workspaceMonitoringRecordId;
          this.formData.EnterpriseID = res.data.EnterpriseID;

          this.formData.ifhea = res.data.ifhea;
          this.formData.checkTotalPeoples = res.data.checkTotalPeoples;
          this.formData.ifheaDust = res.data.ifheaDust;
          this.formData.ifheaChemistry = res.data.ifheaChemistry;
          this.formData.ifheaPhysics = res.data.ifheaPhysics;
          this.healthcustodyReportList = res.data.healthcustodyReportList;
          this.supervisionItemList = res.data.supervisionItemList;
        } else {
          // 当选择"未开展"时，清空并禁用下面的三个选项
          this.formData.checkTotalPeoples = 0;
          this.formData.ifheaDust = undefined;
          this.formData.ifheaChemistry = undefined;
          this.formData.ifheaPhysics = undefined;
          // 清空下面的表格列表
          this.healthcustodyReportList = [];
          this.supervisionItemList = [];
          // 清除验证错误
          this.$nextTick(() => {
            this.$refs.formData.clearValidate([
              'ifheaDust',
              'ifheaChemistry',
              'ifheaPhysics',
            ]);
          });
        }
      } else {
        // 如果预填写有数据 一定是 已经进行了 “开展”，
        this.previewPhysicalHazard();
        this.previewCheckSum();
      }
    },
    async previewPhysicalHazard() {
      let params = { EnterpriseID: this.formData.EnterpriseID };
      const res = await previewPhysicalHazard(params);
      if (res.status == 200) {
        if (res.data.length > 0) {
          this.formData.ifhea = true;
          this.formData.checkTotalPeoples = 0;
          this.formData.ifheaDust = undefined;
          this.formData.ifheaChemistry = undefined;
          this.formData.ifheaPhysics = undefined;
          this.previewHazardList = res.data;
          // 先对危害因素进行匹配,如果this.factorOptions中包含this.previewHazardList中的某一项，包含就留下这条数据
          await this.fetchFactorList(); // 确保factorOptions已加载
          const validFactorNames = new Set();
          const factorTypeMap = new Map(); // 存储因素名称到类型的映射

          // 遍历factorOptions收集所有chineseName及对应的类型
          this.factorOptions.forEach(option => {
            if (option.children && Array.isArray(option.children)) {
              let factorType = 0;
              // 根据分类名称确定类型值
              if (option._id === '粉尘') factorType = 1;
              else if (option._id === '化学') factorType = 2;
              else if (option._id === '物理') factorType = 3;

              option.children.forEach(child => {
                if (child.chineseName && Array.isArray(child.chineseName)) {
                  child.chineseName.forEach(name => {
                    const trimmedName = name.trim();
                    if (trimmedName) {
                      validFactorNames.add(trimmedName);
                      factorTypeMap.set(trimmedName, factorType);
                    }
                  });
                }
              });
            }
          });
          // 2. 筛选previewHazardList：保留harmFactors存在于有效因素中的数据
          const validHazardList = this.previewHazardList.filter(item => {
            const harmFactors =
              item && item.harmFactors ? item.harmFactors.trim() : '';
            if (!harmFactors) return false;

            // 分割多因素（中文分号分隔）并去空格
            const factors = harmFactors.split('；').map(f => f.trim());
            return factors.some(factor => validFactorNames.has(factor));
          });

          // 处理supervisionItemList，确定factortype
          this.supervisionItemList = validHazardList.map((item, index) => {
            const { harmFactors, healthcheck } = item;
            const factors = harmFactors
              .trim()
              .split('；')
              .map(f => f.trim());

            // 确定因素类型（取第一个匹配的因素类型）
            let factortype = 0;
            for (const factor of factors) {
              if (factorTypeMap.has(factor)) {
                factortype = factorTypeMap.get(factor);
                break;
              }
            }

            return {
              ifheaFactor: true,
              factortype: factortype,
              factorId: factors
                .filter(f => validFactorNames.has(f))
                .map(f => this.getFactorIdByName(f)), // 获取因素ID
              checkMonitorPeoples: healthcheck.actuallNum || 0,
              checkShouldPeoples: healthcheck.re_examination || 0,
              checkActualPeoples: healthcheck.re_examination || 0,
              unusualNum: healthcheck.abnormalNum || 0,
            };
          });
        }
      }
    },
    async previewCheckSum() {
      let params = { EnterpriseID: this.formData.EnterpriseID };
      const res = await previewCheckSum(params);
      if (res.status == 200) {
        if (res.data.length > 0) {
          this.formData.ifhea = true;
          this.formData.checkTotalPeoples = 0;
          this.formData.ifheaDust = undefined;
          this.formData.ifheaChemistry = undefined;
          this.formData.ifheaPhysics = undefined;
          this.previewSum = res.data;
          // 给this.healthcustodyReportList赋值

          this.healthcustodyReportList = this.previewSum.map((item, index) => {
            // 判断projectNumber或medicalExaminationReportFileName是否有值
            // 注意：需要排除空字符串的情况
            const hasProjectNumber =
              item.projectNumber && item.projectNumber.trim() !== '';
            const hasFileName =
              item.medicalExaminationReportFileName &&
              item.medicalExaminationReportFileName.trim() !== '';

            // 只要有一个有值就为true，都没有值则为false
            const ifExistCustodyReport = hasProjectNumber || hasFileName;
            return {
              unitName: item.organization,
              creditCode: item.physicalExamOrgCode,
              ifExistCustodyReport: ifExistCustodyReport,
              reportNo: item.projectNumber,
              fileName: item.medicalExaminationReportFileName,
            };
          });
        }
      }
    },
    // 添加根据因素名称获取ID的方法
    getFactorIdByName(factorName) {
      for (const option of this.factorOptions) {
        if (option.children && Array.isArray(option.children)) {
          for (const child of option.children) {
            if (child.chineseName && child.chineseName.includes(factorName)) {
              return child._id;
            }
          }
        }
      }
      return '';
    },
    async fetchFactorList() {
      const res = await getFactorListNation();
      if (res.status == 200) {
        this.factorOptions = res.data;
      }
    },
    healthcustodyReport(val) {
      this.healthcustodyReportList = val;
    },
    supervisionItem(val) {
      this.supervisionItemList = val;
    },
    // 下一步
    nextStep() {
      this.submitForm()
        .then(() => {
          // 验证通过后，submitForm内部会触发下一步
          // this.$emit('nextStepInfo', 3);
        })
        .catch(() => {
          // 验证失败，不执行跳转
        });
    },
    // 提交
    // 提交前数据校验
    validateForm() {
      return new Promise((resolve, reject) => {
        this.$refs.formData.validate(valid => {
          if (valid) {
            resolve();
          } else {
            reject(new Error('请把信息填写完整'));
          }
        });
      });
    },

    // 提交数据格式化
    formatSubmitData() {
      const data = { ...this.formData };

      return data;
    },

    async submitForm() {
      try {
        await this.validateForm();

        if (this.formData.ifhea) {
          // 将表格验证逻辑封装为Promise
          const validateTables = () => {
            return new Promise((resolve, reject) => {
              if (this.healthcustodyReportList.length === 0) {
                reject(new Error('体检总结报告至少有一条'));
              } else if (this.supervisionItemList.length === 0) {
                reject(new Error('体检因素统计至少有一条'));
              } else {
                resolve();
              }
            });
          };

          await validateTables(); // 等待表格验证完成

          let params = {
            workspaceMonitoringRecordId:
              this.formData.workspaceMonitoringRecordId,
            EnterpriseID: this.formData.EnterpriseID,
            ifhea: this.formData.ifhea,
            checkTotalPeoples: this.formData.checkTotalPeoples,
            ifheaDust: this.formData.ifheaDust,
            ifheaChemistry: this.formData.ifheaChemistry,
            ifheaPhysics: this.formData.ifheaPhysics,
            healthcustodyReportList: this.healthcustodyReportList,
            supervisionItemList: this.supervisionItemList,
          };

          const res = await checkSituation(params);
          if (res.status == 200) {
            this.$message.success('检查情况保存成功');
            this.$emit('nextStepInfo', 3); // 验证通过后才触发下一步
          }
        } else {
          let params = {
            workspaceMonitoringRecordId:
              this.formData.workspaceMonitoringRecordId,
            EnterpriseID: this.formData.EnterpriseID,
            ifhea: false,
          };
          const res = await checkSituation(params);
          if (res.status == 200) {
            this.$message.success('检查情况保存成功');
            this.$emit('nextStepInfo', 3); // 验证通过后才触发下一步
          }
        }
      } catch (err) {
        this.$message.error(err.message);
        throw err; // 重新抛出错误以阻止后续操作
      }
    },
    handleIfheaChange(value) {
      if (!value) {
        this.$confirm('选择未开展将清空以下内容?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            // 当选择"未开展"时，清空并禁用下面的三个选项
            this.formData.checkTotalPeoples = 0;
            this.formData.ifheaDust = undefined;
            this.formData.ifheaChemistry = undefined;
            this.formData.ifheaPhysics = undefined;
            // 清空下面的表格列表
            this.healthcustodyReportList = [];
            this.supervisionItemList = [];
            // 清除验证错误
            this.$nextTick(() => {
              this.$refs.formData.clearValidate([
                'checkTotalPeoples',
                'ifheaDust',
                'ifheaChemistry',
                'ifheaPhysics',
              ]);
            });
          })
          .catch(() => {
            this.formData.ifhea = true;
          });
      }
    },
  },
  computed: {},
};
</script>
<style scoped></style>
