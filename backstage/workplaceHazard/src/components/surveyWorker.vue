<template>
  <div>
    <TitleTag titleName="存在的重点岗位/环节情况"></TitleTag>

    <div class="titleNext">监测岗位劳动者工作日调查表</div>

    <el-table
      :data="processedTableData"
      border
      style="width: 100%; margin-top: 10px"
      header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
      :span-method="objectSpanMethod"
      row-key="rowKey"
    >
      <!-- 工作场所名称列 -->
      <el-table-column prop="workplaceName" label="工作场所名称" align="center" width="180"></el-table-column>

      <!-- 岗位编码 -->
      <el-table-column prop="jobNo" label="岗位编码" align="center" width="120" show-overflow-tooltip>
        <template #default="{ row }">
          {{ getJobNameByNo(row.jobNo) }}
        </template>
      </el-table-column>

      <!-- 其他岗位名称 -->
      <el-table-column prop="jobNameOther" label="其他岗位" align="center" width="150">
        <template #default="{ row }">
          {{ row.jobNameOther || "-" }}
        </template>
      </el-table-column>

      <!-- 岗位类型 -->
      <el-table-column prop="jobType" label="岗位类型" align="center" width="120">
        <template #default="{ row }">
          {{ getJobTypeLabel(row.jobType) }}
        </template>
      </el-table-column>

      <!-- 岗位人数 -->
      <el-table-column prop="jobPersonNum" label="岗位人数" align="center" width="100">
        <template #default="{ row }">
          {{ row.jobPersonNum || "-" }}
        </template>
      </el-table-column>

      <!-- 工作时间 -->
      <el-table-column prop="workTime" label="工作时间" align="center" width="200">
        <template #default="{ row }">
          {{ row.startTime }}{{ row.startTimeFlag === 2 ? "(次日)" : "" }} - {{ row.endTime }}{{ row.endTimeFlag === 2 ? "(次日)" : "" }}
        </template>
      </el-table-column>

      <el-table-column prop="workPlace" label="工作地点" align="center" width="150" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.workPlace || "-" }}
        </template>
      </el-table-column>

      <el-table-column prop="factorNames" label="危害因素" align="center" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          {{ getFactorNamesByIds(row.factorId) }}
        </template>
      </el-table-column>

      <el-table-column prop="contactHour" label="接触时间(h)" align="center" width="120">
        <template #default="{ row }">
          {{ formatContactHour(row.contactHour) }}
        </template>
      </el-table-column>

      <!-- 额外补充的列 -->
      <el-table-column prop="operateNum" label="每班人数" align="center" width="100">
        <template #default="{ row }">
          {{ row.operateNum || "-" }}
        </template>
      </el-table-column>

      <el-table-column prop="workType" label="工作班制" align="center" width="120">
        <template #default="{ row }">
          {{ row.workType === 1 ? "轮班制" : row.workType === 2 ? "单班制" : "-" }}
        </template>
      </el-table-column>

      <el-table-column prop="workShifts" label="班制类型" align="center" width="120">
        <template #default="{ row }">
          {{ getWorkShiftsLabel(row.workShifts, row.otherWorkShifts) }}
        </template>
      </el-table-column>

      <el-table-column prop="totalHour" label="班制时长(h/d)" align="center" width="130">
        <template #default="{ row }">
          {{ row.totalHour || "-" }}
        </template>
      </el-table-column>

      <el-table-column prop="weekDay" label="每周接触天数(d/w)" align="center" width="150">
        <template #default="{ row }">
          {{ row.weekDay || "-" }}
        </template>
      </el-table-column>

      <el-table-column prop="workerName" label="劳动者姓名" align="center" width="150">
        <template #default="{ row }">
          {{ row.workerName || "-" }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import TitleTag from "./TitleTag.vue";
import { getWorkerFormDetail, getJobList } from "@/api";

export default {
  components: { TitleTag },
  props: {
    factorOptionsFather: {
      type: Array, // 明确类型为数组
      default: () => [] // 默认值为空数组（引用类型用函数返回）
    },
    jobList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 核心数据
      workerInvestigationSituationList: [],
      // 辅助数据
      // jobList: [], // 岗位列表
      // 班制选项
      workShiftsOptions: [
        { value: 1, label: "两班两运转" },
        { value: 2, label: "三班两运转" },
        { value: 3, label: "三班三运转" },
        { value: 4, label: "四班三运转" },
        { value: 5, label: "五班三运转" },
        { value: 6, label: "五班四运转" },
        { value: 99, label: "其他" }
      ],
      // 合并单元格配置
      spanConfig: {}
    };
  },

  computed: {
    // 处理原始数据为表格展示格式
    processedTableData() {
      const result = [];
      this.workerInvestigationSituationList.forEach((workplace, wpIndex) => {
        workplace.jobList.forEach((job, jobIndex) => {
          // 替换可选链操作符
          const hasFactors = job.workerInvestigationFactorList && job.workerInvestigationFactorList.length > 0;

          if (!hasFactors) {
            // 无危害因素时添加一行
            result.push({
              ...job,
              workplaceName: workplace.workplaceName,
              workplaceIndex: wpIndex,
              jobIndex: jobIndex,
              rowKey: `${wpIndex}-${jobIndex}-0`,
              factorId: [],
              startTime: "",
              endTime: "",
              startTimeFlag: 1,
              endTimeFlag: 1,
              workPlace: "",
              contactHour: "",
              // 合并行计算
              workplaceRowspan: this.getTotalFactors(workplace),
              jobRowspan: 1
            });
          } else {
            // 有危害因素时按因素拆分多行
            job.workerInvestigationFactorList.forEach((factor, factorIndex) => {
              result.push({
                ...job,
                ...factor,
                workplaceName: workplace.workplaceName,
                workplaceIndex: wpIndex,
                jobIndex: jobIndex,
                rowKey: `${wpIndex}-${jobIndex}-${factorIndex}`,
                // 合并行计算
                workplaceRowspan: this.getTotalFactors(workplace),
                jobRowspan: job.workerInvestigationFactorList.length
              });
            });
          }
        });
      });
      return result;
    }
  },

  watch: {
    processedTableData: {
      handler() {
        this.calculateSpanConfig();
      },
      deep: true,
      immediate: true
    },
    factorOptionsFather: {
      deep: true, // 深度监听数组内部变化
      immediate: true, // 初始化时立即执行
      handler(newVal) {
        console.log("更新后的 factorOptionsFather:", newVal);
        this.fetchFactorList(); // 重新处理因子数据
      }
    }
  },

  created() {
    const { id } = this.$route.query;
    // 获取详情数据
    this.getWorkerFormDetail(id);
  },

  methods: {
    // 添加格式化接触时间的方法
    formatContactHour(value) {
      // 检查值是否存在且有效
      if (value === undefined || value === null || value === "") {
        return "-";
      }

      // 尝试将值转换为数字
      const num = Number(value);

      // 检查是否为有效数字
      if (isNaN(num)) {
        return "-";
      }

      // 保留一位小数
      return num.toFixed(1);
    },
    // 获取劳动者调查详情
    async getWorkerFormDetail(id) {
      const res = await getWorkerFormDetail({ workspaceMonitoringRecordId: id });
      if (res.status === 200) {
        this.workerInvestigationSituationList = res.data.workerInvestigationSituationList || [];
      }
    },

    // 获取岗位列表
    // async fetchJobList() {
    //   try {
    //     const res = await getJobList({ pageSize: 1000 });
    //     this.jobList = res.data.list || [];
    //   } catch (error) {
    //     console.error('获取岗位列表失败:', error);
    //   }
    // },

    // 获取危害因素列表
    async fetchFactorList() {
      try {
        // const res = await getFactorListNation();
        console.log(this.factorOptionsFather, "this.factorOptionsFather0999");
        this.factorOptions = this.factorOptionsFather.flatMap((category) =>
          category.children.map((child) => ({
            id: child._id,
            name: child.chineseName.join("、")
          }))
        );
      } catch (error) {
        console.error("获取危害因素列表失败:", error);
      }
    },

    // 计算工作场所下总危害因素数量（用于合并行）
    getTotalFactors(workplace) {
      return workplace.jobList.reduce(function (total, job) {
        // 替换可选链操作符
        var length = 1;
        if (job.workerInvestigationFactorList && job.workerInvestigationFactorList.length) {
          length = job.workerInvestigationFactorList.length;
        }
        return total + length;
      }, 0);
    },

    // 计算合并单元格配置
    calculateSpanConfig() {
      this.spanConfig = { workplace: {}, job: {} };
      this.processedTableData.forEach((row, rowIndex) => {
        const wpKey = row.workplaceIndex;
        const jobKey = `${row.workplaceIndex}-${row.jobIndex}`;

        // 工作场所合并配置
        if (!this.spanConfig.workplace[wpKey]) {
          this.spanConfig.workplace[wpKey] = {
            start: rowIndex,
            span: row.workplaceRowspan
          };
        }

        // 岗位信息合并配置
        if (!this.spanConfig.job[jobKey]) {
          this.spanConfig.job[jobKey] = {
            start: rowIndex,
            span: row.jobRowspan
          };
        }
      });
    },

    // 合并单元格方法
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 工作场所名称列（第0列）合并
      if (columnIndex === 0) {
        const config = this.spanConfig.workplace[row.workplaceIndex];
        if (rowIndex === config.start) {
          return { rowspan: config.span, colspan: 1 };
        } else {
          return { rowspan: 0, colspan: 0 };
        }
      }

      // 岗位基础信息列（1-5列）合并
      if (columnIndex >= 1 && columnIndex <= 5) {
        const jobKey = `${row.workplaceIndex}-${row.jobIndex}`;
        const config = this.spanConfig.job[jobKey];
        if (rowIndex === config.start) {
          return { rowspan: config.span, colspan: 1 };
        } else {
          return { rowspan: 0, colspan: 0 };
        }
      }

      // 其他列不合并
      return { rowspan: 1, colspan: 1 };
    },

    // 岗位类型转换
    getJobTypeLabel(type) {
      const map = { 1: "固定岗位", 2: "流动岗位", 3: "巡检岗位" };
      return map[type] || "-";
    },

    // 班制类型转换
    getWorkShiftsLabel(shift, other) {
      if (!shift) return "-";
      const shiftMap = this.workShiftsOptions.reduce((map, item) => {
        map[item.value] = item.label;
        return map;
      }, {});
      return shift === 99 ? `其他(${other || "-"})` : shiftMap[shift] || "-";
    },

    // 岗位编码转名称
    getJobNameByNo(jobNo) {
      if (!jobNo) return "-";
      const job = this.jobList.find((item) => item.cnCsjcNo === jobNo);
      return job ? `${job.jobName}(${jobNo})` : `未知(${jobNo})`;
    },

    // 危害因素ID转名称
    getFactorNamesByIds(factorIds) {
      if (!factorIds || !factorIds.length) return "-";
      return factorIds
        .map((id) => {
          const factor = this.factorOptions.find((item) => item.id === id);
          return factor ? factor.name : `未知(${id})`;
        })
        .join("、");
    }
  }
};
</script>

<style lang="scss" scoped>
.titleNext {
  font-size: 14px;
  font-weight: bold;
  color: #1a1a1a;
  margin: 20px 0;
  padding-left: 5px;
}

// 表格样式优化
::v-deep .el-table {
  margin-top: 15px;

  .cell {
    white-space: normal;
    line-height: 1.5;
    padding: 8px 10px;
  }

  th {
    font-weight: 500;
  }
}

// 合并单元格样式
::v-deep .el-table__row td.el-table__cell {
  vertical-align: middle;
}
</style>
