<template>
  <div>
    <TitleTag titleName="上一年度检测情况"></TitleTag>
    <el-form
      :model="formData"
      :rules="rules"
      ref="formData"
      label-width="200px"
      size="mini"
    >
      <el-row>
        <el-col :span="6">
          <el-form-item
            prop="ifat"
            label="上一年度检测情况"
            label-width="150px"
          >
            <el-radio-group v-model="formData.ifat" @change="handleIfatChange">
              <el-radio :label="false">未检测</el-radio>
              <el-radio :label="true">检测</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item
            prop="ifatDust"
            label="是否粉尘_检测"
            label-width="150px"
          >
            <el-radio-group
              v-model="formData.ifatDust"
              :disabled="!formData.ifat"
            >
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            prop="ifatChemistry"
            label="是否化学物质_检测"
            label-width="150px"
          >
            <el-radio-group
              v-model="formData.ifatChemistry"
              :disabled="!formData.ifat"
            >
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            prop="ifatPhysics"
            label="是否物理因素_检测"
            label-width="150px"
          >
            <el-radio-group
              v-model="formData.ifatPhysics"
              :disabled="!formData.ifat"
            >
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left"
        >职业病危害因素检测报告明细</el-divider
      >
      <addChemical
        @factorcheckReport="factorcheckReport"
        :tableData="factorcheckReportList"
        :ifat="formData.ifat"
      ></addChemical>
      <el-divider content-position="left"
        >职业病危害因素监测情况-危害因素明细</el-divider
      >
      <addhazardFactors
        @factorCheckItem="factorCheckItem"
        :tableData="factorCheckItemList"
        :ifat="formData.ifat"
        :taskId="provincialWorkspaceMonitoringId"
      ></addhazardFactors>
      <el-divider content-position="left"
        >上年度检测结果调查情况明细</el-divider
      >
      <addPreviousYear
        @detectionResult="detectionResult"
        :tableData="detectionResultList"
        :ifat="formData.ifat"
        :taskId="provincialWorkspaceMonitoringId"
      ></addPreviousYear>
      <el-row>
        <el-col :span="24">
          <el-form-item label-width="20px">
            <el-button type="primary" size="medium" @click="nextStep"
              >下一步</el-button
            >
            <el-button size="medium" @click="saveShortTime()">暂存</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';
import addChemical from '@/components/addChemical.vue';
import addhazardFactors from '@/components/addhazardFactors.vue';
import addPreviousYear from '@/components/addPreviousYear.vue';
import { monitoringAnalyse, getMonitoringAnalyseDetail } from '@/api';
export default {
  name: 'firstMessage',
  components: {
    TitleTag,
    addChemical,
    addhazardFactors,
    addPreviousYear,
  },
  data() {
    // 自定义验证规则
    const validateIfatFields = (rule, value, callback) => {
      if (this.formData.ifat && value === undefined) {
        callback(new Error('此项为必填项'));
      } else {
        callback();
      }
    };

    return {
      formData: {
        ifat: false,
        ifatDust: undefined,
        ifatChemistry: undefined,
        ifatPhysics: undefined,
      },
      rules: {
        ifatDust: [{ validator: validateIfatFields, trigger: 'change' }],
        ifatChemistry: [{ validator: validateIfatFields, trigger: 'change' }],
        ifatPhysics: [{ validator: validateIfatFields, trigger: 'change' }],
      },
      factorcheckReportList: [],
      factorCheckItemList: [],
      detectionResultList: [],
      // serviceOrgList: [],
    };
  },
  async created() {
    const {
      id,
      taskId,
      jcOrgId,
      serviceOrgId,
      EnterpriseID,
      provincialWorkspaceMonitoringId,
    } = this.$route.query;
    this.formData.workspaceMonitoringRecordId = id; //监测记录id
    this.formData.jcOrgId = jcOrgId; // 委托单位
    this.formData.serviceOrgId = serviceOrgId; // 检测机构
    this.formData.EnterpriseID = EnterpriseID; // 获取企业/用人单位
    this.provincialWorkspaceMonitoringId = provincialWorkspaceMonitoringId; //省级任务id
    // 获取机构信息
    // this.getorgList(taskId);
    // 获取详情
    this.getMonitoringAnalyseDetail(id);
  },
  methods: {
    async getMonitoringAnalyseDetail(id) {
      const res = await getMonitoringAnalyseDetail({
        workspaceMonitoringRecordId: id,
      });
      if (res.status == 200) {
        if (res.data.ifat) {
          this.formData.workspaceMonitoringRecordId =
            res.data.workspaceMonitoringRecordId;
          this.formData.EnterpriseID = res.data.EnterpriseID;

          this.formData.ifat = res.data.ifat;
          this.formData.ifatDust = res.data.ifatDust;
          this.formData.ifatChemistry = res.data.ifatChemistry;
          this.formData.ifatPhysics = res.data.ifatPhysics;
          this.factorcheckReportList = res.data.factorcheckReportList;
          this.factorCheckItemList = res.data.factorCheckItemList;
          this.detectionResultList =
            res.data.detectionResultInvestigationSituationList;
        } else {
          // 当选择"未检测"时，清空并禁用下面的三个选项
          this.formData.ifatDust = undefined;
          this.formData.ifatChemistry = undefined;
          this.formData.ifatPhysics = undefined;
          // 清空下面的表格列表
          this.factorcheckReportList = [];
          this.factorCheckItemList = [];
          this.detectionResultList = [];
          // 清除验证错误
          this.$nextTick(() => {
            this.$refs.formData.clearValidate([
              'ifatDust',
              'ifatChemistry',
              'ifatPhysics',
            ]);
          });
        }
      }
    },
    // async getorgList(taskId) {
    //   const res = await orgList({ task_id: taskId });
    //   if (res.status == 200) {
    //     this.serviceOrgList = res.data.serviceOrgList;
    //   } else {
    //     this.$message.error(res.message);
    //   }
    // },
    factorcheckReport(val) {
      this.factorcheckReportList = val;
    },
    factorCheckItem(val) {
      this.factorCheckItemList = val;
    },
    detectionResult(val) {
      this.detectionResultList = val;
    },
    // 下一步
    nextStep() {
      this.submitForm()
        .then(() => {
          this.$emit('nextStepInfo', 2);
        })
        .catch(() => {
          // 验证失败，不执行跳转
        });
    },
    saveShortTime() {
      this.submitForm();
      this.$message.success('监测情况暂存成功！');
    },

    // 提交
    // 提交前数据校验
    validateForm() {
      return new Promise((resolve, reject) => {
        this.$refs.formData.validate((valid) => {
          if (valid) {
            resolve();
          } else {
            reject(new Error('请把信息填写完整'));
          }
        });
      });
    },

    // 提交数据格式化
    // formatSubmitData() {
    //   const data = { ...this.formData };

    //   return data;
    // },

    async submitForm() {
      try {
        await this.validateForm();

        if (this.formData.ifat) {
          // 将验证逻辑放在Promise中统一处理
          const validateTables = () => {
            return new Promise((resolve, reject) => {
              if (this.factorcheckReportList.length === 0) {
                reject(new Error('检测报告数据至少有一条'));
              } else if (this.factorCheckItemList.length === 0) {
                reject(new Error('监测数据至少有一条'));
              } else if (this.detectionResultList.length === 0) {
                reject(new Error('工作场所检测数据至少有一条'));
              } else {
                resolve();
              }
            });
          };

          await validateTables(); // 等待表格验证完成

          let params = {
            workspaceMonitoringRecordId:
              this.formData.workspaceMonitoringRecordId,
            EnterpriseID: this.formData.EnterpriseID,
            ifat: this.formData.ifat,
            ifatDust: this.formData.ifatDust,
            ifatChemistry: this.formData.ifatChemistry,
            ifatPhysics: this.formData.ifatPhysics,
            factorcheckReportList: this.factorcheckReportList,
            factorCheckItemList: this.factorCheckItemList,
            detectionResultInvestigationSituationList: this.detectionResultList,
          };

          const res = await monitoringAnalyse(params);
          if (res.status == 200) {
            this.$message.success('监测情况保存成功');
          }
        } else {
          let params = {
            workspaceMonitoringRecordId:
              this.formData.workspaceMonitoringRecordId,
            EnterpriseID: this.formData.EnterpriseID,
            ifat: false,
          };
          const res = await monitoringAnalyse(params);
          if (res.status == 200) {
            this.$message.success('监测情况保存成功');
          }
        }
      } catch (err) {
        this.$message.error(err.message);
        throw err; // 重新抛出错误以阻止后续操作
      }
    },
    handleIfatChange(value) {
      if (!value) {
        this.$confirm('选择未检测将清空以下内容?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            // 当选择"未检测"时，清空并禁用下面的三个选项
            this.formData.ifatDust = undefined;
            this.formData.ifatChemistry = undefined;
            this.formData.ifatPhysics = undefined;
            // 清空下面的表格列表
            this.factorcheckReportList = [];
            this.factorCheckItemList = [];
            this.detectionResultList = [];
            // 清除验证错误
            this.$nextTick(() => {
              this.$refs.formData.clearValidate([
                'ifatDust',
                'ifatChemistry',
                'ifatPhysics',
              ]);
            });
          })
          .catch(() => {
            this.formData.ifat = true;
          });
      }
    },
  },
};
</script>
<style scoped>
.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}
</style>
