<!-- 各个tab列表标题，抽离复用 -->
<template>
    <el-row style="margin: 5px 0px; 
        overflow: hidden; 
        height: 55px;
        line-height: 55px;
        display: flex;">
        <span style="font-size: 14px;
                width: 100%;
                font-weight: bold;
                color: #1a1a1a;" class="bacisTitle">{{ titleName }}</span>
    </el-row>
</template>
<script>
export default {
    name: 'TitleTag',
    data() {
        return {

        }
    },
    props: {
        titleName: {
            type: String,
            default: ""
        }
    }
}
</script>
<style scoped>
.bacisTitle {
    flex: 1;
    display: inline-block;
    height: 55px;
    white-space: nowrap;
    overflow: hidden;
}

.bacisTitle:before {
    content: '';
    width: 5px;
    height: 20px;
    background: #0063e0;
    display: inline-block;
    margin-right: 10px;
    vertical-align: sub;
}

.bacisTitle:after {
    content: '';
    background: #D8D8D8;
    color: #D8D8D8;
    width: 100%;
    flex: 1;
    display: inline-block;
    height: .5px;
    margin: 10px 0px 8px 40px;
    vertical-align: sub;
}
</style>