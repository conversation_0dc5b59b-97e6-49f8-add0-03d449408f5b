<template>
  <div class="container">
    <!-- 新增按钮 -->
    <el-button type="primary" @click="dialogVisible = true" :disabled="!ifat"
      >新增检测报告</el-button
    >

    <!-- 弹窗表单 -->
    <el-dialog
      title="新增检测报告"
      :visible.sync="dialogVisible"
      width="50%"
      @closed="resetForm"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="formRef"
        label-width="180px"
      >
        <el-form-item label="检测报告唯一标识" prop="uuid">
          <el-input v-model="formData.uuid" placeholder="请输入UUID"></el-input>
        </el-form-item>

        <el-form-item label="检测机构名称" prop="unitName">
          <el-input
            v-model="formData.unitName"
            placeholder="请输入检测机构名称"
          ></el-input>
          <!-- <el-select
            v-model="formData.unitName"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in serviceOrgList"
              :key="item._id"
              :label="item.name"
              :value="item._id"
            ></el-option>
          </el-select> -->
        </el-form-item>

        <el-form-item label="检测机构社会信用代码" prop="creditCode">
          <el-input
            v-model="formData.creditCode"
            placeholder="请输入社会信用代码"
          ></el-input>
        </el-form-item>

        <el-form-item label="是否存在检测报告" prop="ifExistCheckReport">
          <el-radio-group
            v-model="formData.ifExistCheckReport"
            @change="handleReportExistChange"
          >
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          label="检测报告编号"
          prop="reportNo"
          :rules="formData.ifExistCheckReport ? rules.reportNo : []"
        >
          <el-input
            v-model="formData.reportNo"
            placeholder="请输入检测报告编号"
            :disabled="!formData.ifExistCheckReport"
          ></el-input>
        </el-form-item>

        <el-form-item
          label="检测报告附件名称"
          prop="fileName"
          :rules="formData.ifExistCheckReport ? rules.fileName : []"
        >
          <el-input
            v-model="formData.fileName"
            placeholder="请输入附件名称"
            :disabled="!formData.ifExistCheckReport"
          ></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
      border
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="uuid" label="唯一标识"></el-table-column>
      <el-table-column prop="unitName" label="检测机构名称">
        <template slot-scope="scope">
          <!-- {{ getOrgName(scope.row.unitName) }} -->
          {{ scope.row.unitName }}
        </template>
      </el-table-column>
      <el-table-column prop="creditCode" label="社会信用代码"></el-table-column>
      <el-table-column prop="ifExistCheckReport" label="是否有报告" width="120">
        <template slot-scope="scope">
          <el-tag :type="scope.row.ifExistCheckReport ? 'success' : 'info'">
            {{ scope.row.ifExistCheckReport ? '有' : '无' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="reportNo"
        label="报告编号"
        width="180"
      ></el-table-column>
      <el-table-column prop="fileName" label="附件名称"></el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="danger"
            plain
            @click="handleDelete(scope.$index)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import TitleTag from '@/components/TitleTag.vue';

export default {
  name: 'firstMessage',
  components: {
    TitleTag,
  },
  props: {
    tableData: {
      type: Array,
      default: [],
    },
    ifat: {
      type: Boolean,
      default: false,
    },
    // serviceOrgList: {
    //   type: Array,
    //   default: [],
    // },
  },
  data() {
    // 社会信用代码校验函数
    const validateCreditCode = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入社会信用代码'));
      }
      // 社会信用代码正则表达式：18位，包含数字和大写字母
      const reg = /^[0-9A-Z]{18}$/;
      if (!reg.test(value)) {
        callback(new Error('请输入正确的18位社会信用代码（数字和大写字母）'));
      } else {
        callback();
      }
    };
    return {
      dialogVisible: false,
      formData: {
        uuid: '',
        unitName: '',
        creditCode: '',
        ifExistCheckReport: false,
        reportNo: '',
        fileName: '',
      },
      rules: {
        uuid: [
          {
            required: true,
            message: '请输入检测报告唯一标识',
            trigger: 'blur',
          },
          {
            max: 32,
            message: '长度不能超过32个字符',
            trigger: 'blur',
          },
        ],
        unitName: [
          { required: true, message: '请输入检测机构名称', trigger: 'blur' },
          {
            max: 50,
            message: '长度不能超过50个字符',
            trigger: 'blur',
          },
        ],
        creditCode: [
          { required: true, message: '请输入社会信用代码', trigger: 'blur' },
          { validator: validateCreditCode, trigger: 'blur' },
        ],
        ifExistCheckReport: [
          {
            required: true,
            message: '请选择是否存在检测报告',
            trigger: 'change',
          },
        ],
        reportNo: [
          { required: true, message: '请输入检测报告编号', trigger: 'blur' },
          {
            max: 50,
            message: '长度不能超过50个字符',
            trigger: 'blur',
          },
        ],
        fileName: [
          { required: true, message: '请输入附件名称', trigger: 'blur' },
          {
            max: 50,
            message: '长度不能超过50个字符',
            trigger: 'blur',
          },
        ],
      },
      // tableData: [],
    };
  },
  async created() {},
  watch: {},
  async mounted() {},
  methods: {
    // getOrgName(unitId) {
    //   const org = this.serviceOrgList.find((item) => item._id === unitId);
    //   return org ? org.name : unitId;
    // },
    // 提交表单
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // 添加到表格数据
          this.tableData.push({
            uuid: this.formData.uuid,
            unitName: this.formData.unitName,
            creditCode: this.formData.creditCode,
            ifExistCheckReport: this.formData.ifExistCheckReport,
            reportNo: this.formData.ifExistCheckReport
              ? this.formData.reportNo
              : '',
            fileName: this.formData.ifExistCheckReport
              ? this.formData.fileName
              : '',
          });

          this.$emit('factorcheckReport', this.tableData);
          this.$message.success('新增成功');
          this.dialogVisible = false;
        }
      });
    },

    // 删除行
    handleDelete(index) {
      this.$confirm('确定要删除该条数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.tableData.splice(index, 1);
          this.$emit('factorcheckReport', this.tableData);

          this.$message.success('删除成功');
        })
        .catch(() => {
          this.$message.info('已取消删除');
        });
    },

    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields();
      this.formData = {
        uuid: '',
        unitName: '',
        creditCode: '',
        ifExistCheckReport: false,
        reportNo: '',
        fileName: '',
      };
    },

    // 处理检测报告存在状态变化
    handleReportExistChange(val) {
      if (!val) {
        this.formData.reportNo = '';
        this.formData.fileName = '';
      }
    },
  },
  computed: {},
};
</script>
<style scoped>
.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}
.container {
  padding: 20px;
}
</style>
