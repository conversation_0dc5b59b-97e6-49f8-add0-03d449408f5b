<template>
  <div>
    <TitleTag titleName="基本信息"></TitleTag>
    <el-row>
      <el-col :span="24">
        <table
          style="width: 100%; border: 1px solid #ddd"
          class="table-container"
        >
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>任务名称
            </th>
            <th colspan="8" class="th-input">
              <span>{{ areaFlag == 1 ? baseInfo.title : title }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>年份
            </th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.year }}</span>
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>开始时间
            </th>
            <th colspan="8" class="th-input">
              <span>{{ handleTime(baseInfo.startTime) }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              <span style="color: red">*</span>结束时间
            </th>
            <th colspan="8" class="th-input">
              <span>{{ handleTime(baseInfo.endTime) }}</span>
            </th>
          </tr>
        </table>
      </el-col>
    </el-row>
    <div class="header-container">
      <TitleTag titleName="监测行业&危害因素"></TitleTag>
    </div>
    <el-row>
      <el-col :span="24">
        <el-table
          :data="industryCheckFactorList"
          tooltip-effect="light"
          style="width: 100%"
          stripe
          border
          header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
          align="center"
        >
          <el-table-column
            prop="industry"
            label="行业"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ scope.row.industry.join(' / ') }}
            </template>
          </el-table-column>
          <el-table-column
            prop="unit"
            label="必检危害因素"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <div
                v-for="item in scope.row.checkFactorList"
                :key="item.factor._id"
              >
                <span v-if="item.selectType === '必检'">
                  {{ item.factor.chineseName.join('、') }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="personType"
            label="选检危害因素"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <div
                v-for="item in scope.row.checkFactorList"
                :key="item.factor._id"
              >
                <span v-if="item.selectType === '选检'">
                  {{ item.factor.chineseName.join('、') }}
                </span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import TitleTag from './TitleTag.vue';
import moment from 'moment';

export default {
  components: {
    TitleTag,
  },
  props: {
    baseInfo: {
      type: Object,
      default: () => ({}),
    },
    industryCheckFactorList: {
      type: Array,
      default: () => [],
    },
    areaFlag: {
      type: Number,
    },
    title: {
      type: String,
    },
  },
  data() {
    return {};
  },
  methods: {
    handleTime(data) {
      return data ? moment(data).format('YYYY-MM-DD') : '';
    },
  },
};
</script>
<style lang="scss" scoped>
.table-container {
  table-layout: fixed;
  background: #fff;
  width: 100%;
  margin: 0 auto;
  border-collapse: collapse;

  td {
    padding: 5px 0;
  }

  th {
    font-weight: normal;
    border: 1px solid #e5e5e5;
    padding: 3px 0;
  }

  tr {
    border: 1px solid #e5e5e5;
    width: 100%;
    font-size: 14px;
  }

  .left-tittle {
    background: #f5f7fa;
  }

  .center-tittle {
    background: #ecf5ff;
  }

  .table-label {
    text-align: right;
    padding-right: 5px;
    color: #000;
    font-size: 14px;
    height: 42px;
  }

  .th-input {
    text-align: center;
    padding: 2px 4px;

    span {
      color: #333;
    }
  }

  .th-radio {
    text-align: center;
    padding-left: 6px;
    padding-right: 6px;
  }

  .input-width {
    width: 200px;
    background-color: bisque;
  }

  .health-check-th {
    padding: 11px 28px;
    text-align: center;
  }
}
</style>
