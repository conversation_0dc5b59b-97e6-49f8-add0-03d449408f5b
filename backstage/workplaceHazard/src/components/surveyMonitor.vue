<template>
  <div>
    <TitleTag titleName="上一年度检测情况"></TitleTag>
    <el-row>
      <el-col :span="24">
        <table style="width: 100%; border: 1px solid #ddd" class="table-container">
          <tr>
            <th class="left-tittle table-label" colspan="4"><span style="color: red">*</span>上一年度检测情况</th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifat ? "检测" : "未检测" }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4"><span style="color: red">*</span>是否粉尘_检测</th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifatDust ? "是" : "否" }}</span>
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4"><span style="color: red">*</span>是否化学物质_检测</th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifatChemistry ? "是" : "否" }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4"><span style="color: red">*</span>是否物理因素_检测</th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifatPhysics ? "是" : "否" }}</span>
            </th>
          </tr>
        </table>
      </el-col>
    </el-row>

    <div class="titleNext">职业病危害因素检测报告明细</div>
    <el-table
      :data="factorcheckReportList"
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
      border
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="uuid" label="唯一标识"></el-table-column>
      <el-table-column prop="unitName" label="检测机构名称">
        <template slot-scope="scope">
          {{ scope.row.unitName }}
        </template>
      </el-table-column>
      <el-table-column prop="creditCode" label="社会信用代码"></el-table-column>
      <el-table-column prop="ifExistCheckReport" label="是否有报告" width="120">
        <template slot-scope="scope">
          <el-tag :type="scope.row.ifExistCheckReport ? 'success' : 'info'">
            {{ scope.row.ifExistCheckReport ? "有" : "无" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="reportNo" label="报告编号" width="180"></el-table-column>
      <el-table-column prop="fileName" label="附件名称"></el-table-column>
    </el-table>
    <div class="titleNext">职业病危害因素监测情况-危害因素明细</div>
    <el-table
      :data="factorCheckItemList"
      border
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="ifatFactor" label="是否检测" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.ifatFactor ? 'success' : 'info'">
            {{ scope.row.ifatFactor ? "是" : "否" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="factorId" label="危害因素" width="150">
        <template slot-scope="scope">
          {{ getFactorName(scope.row.factorId) }}
        </template>
      </el-table-column>
      <el-table-column prop="checkNum" label="场所检测点数"></el-table-column>
      <el-table-column prop="excessNum" label="场所超标点数"></el-table-column>
      <el-table-column prop="workNum" label="检测岗位/工种数"></el-table-column>
      <el-table-column prop="workExcessNum" label="检测岗位/工种超标数"></el-table-column>
    </el-table>
    <div class="titleNext">上年度检测结果调查情况明细</div>
    <el-table
      :data="formattedTableData"
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
      border
      style="width: 100%"
      row-key="id"
    >
      <el-table-column prop="workplaceName" label="工作场所名称"></el-table-column>

      <el-table-column label="上年度检测岗位明细">
        <el-table-column v-for="(job, index) in jobColumns" :key="index" :prop="job.prop" :label="job.label">
          <template #default="scope">
            <div v-for="(item, idx) in scope.row.jobList" :key="idx">
              {{ item[job.prop] }}
            </div>
          </template>
        </el-table-column>
      </el-table-column>

      <el-table-column label="检测危害因素明细">
        <el-table-column v-for="(factor, index) in factorColumns" :key="index" :prop="factor.prop" :label="factor.label">
          <template #default="scope">
            <template v-if="factor.prop === 'factorName'">
              <div v-for="(item, idx) in scope.row.detectionFactorList" :key="idx">
                {{ item.factorName }}
              </div>
            </template>
            <template v-else-if="factor.prop === 'jobIfQualifiedText'">
              <div v-for="(item, idx) in scope.row.detectionFactorList" :key="idx">
                {{ item.jobIfQualifiedText }}
              </div>
            </template>
            <template v-else-if="factor.prop === 'workplaceIfQualifiedText'">
              <div v-for="(item, idx) in scope.row.detectionFactorList" :key="idx">
                {{ item.workplaceIfQualifiedText }}
              </div>
            </template>
            <template v-else>
              <div v-for="(item, idx) in scope.row.detectionFactorList" :key="idx">
                {{ item[factor.prop] }}
              </div>
            </template>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import TitleTag from "./TitleTag.vue";
import { getMonitoringAnalyseDetail, orgList, getJobList } from "@/api";
export default {
  components: { TitleTag },
  props: {
    factorOptions: [],
    jobList: []
  },
  data() {
    return {
      baseInfo: {
        ifat: false,
        ifatDust: undefined,
        ifatChemistry: undefined,
        ifatPhysics: undefined
      },
      factorcheckReportList: [],
      factorCheckItemList: [],
      detectionResultList: [],
      filteredFactorOptions: [], // 过滤后的选项
      jobColumns: [
        { prop: "originalReportJobName", label: "原报告中岗位名称" },
        { prop: "jobNo", label: "标化后岗位环节编码" },
        { prop: "jobNameOther", label: "标化后其他岗位名称" }
      ],
      factorColumns: [
        { prop: "factorName", label: "危害因素名称" },
        { prop: "ctwaCheckValue", label: "Ctwa检测值" },
        { prop: "cstelCheckValue", label: "Cstel检测值" },
        { prop: "noiseCheckValue", label: "等效声级检测值" },
        { prop: "macCheckValue", label: "Mac检测值" },
        { prop: "jobIfQualifiedText", label: "岗位合格标记" },
        { prop: "workplaceIfQualifiedText", label: "工作场所合格标记" },
        { prop: "remark", label: "备注说明" }
      ]
    };
  },
  computed: {
    formattedTableData() {
      return this.detectionResultList.map((item) => {
        return {
          ...item,
          jobIfQualifiedText: this.getQualifiedText(item.jobIfQualified),
          workplaceIfQualifiedText: this.getQualifiedText(item.workplaceIfQualified),
          detectionFactorList: item.detectionFactorList.map((factor) => {
            const factorObj = this.factorOptions.find((f) => f.id === factor.factorId) || {};
            return {
              ...factor,
              factorName: factorObj.name || "未知危害因素",
              jobIfQualifiedText: this.getQualifiedText(factor.jobIfQualified),
              workplaceIfQualifiedText: this.getQualifiedText(factor.workplaceIfQualified)
            };
          })
        };
      });
    }
  },
  async created() {
    const { id, taskId, jcOrgId, serviceOrgId, EnterpriseID, provincialWorkspaceMonitoringId } = this.$route.query;
    this.taskId = taskId; // 保存任务ID
    this.baseInfo.workspaceMonitoringRecordId = id; //监测记录id
    this.baseInfo.jcOrgId = jcOrgId; // 委托单位
    this.baseInfo.serviceOrgId = serviceOrgId; // 检测机构
    this.baseInfo.EnterpriseID = EnterpriseID; // 获取企业/用人单位

    this.provincialWorkspaceMonitoringId = provincialWorkspaceMonitoringId; //省级任务id
    this.getMonitoringAnalyseDetail(id);

    // 获取机构列表数据
    await this.getOrgList();
    // 获取岗位列表
    // await this.fetchJobList();
    // 获取监测危害因素
    await this.fetchFactorList();
  },
  methods: {
    async getMonitoringAnalyseDetail(id) {
      const res = await getMonitoringAnalyseDetail({
        workspaceMonitoringRecordId: id
      });
      if (res.status == 200) {
        if (res.data.ifat) {
          this.baseInfo.workspaceMonitoringRecordId = res.data.workspaceMonitoringRecordId;
          this.baseInfo.EnterpriseID = res.data.EnterpriseID;

          this.baseInfo.ifat = res.data.ifat;
          this.baseInfo.ifatDust = res.data.ifatDust;
          this.baseInfo.ifatChemistry = res.data.ifatChemistry;
          this.baseInfo.ifatPhysics = res.data.ifatPhysics;
          this.factorcheckReportList = res.data.factorcheckReportList;
          this.factorCheckItemList = res.data.factorCheckItemList;
          this.detectionResultList = res.data.detectionResultInvestigationSituationList;
        } else {
          // 当选择"未检测"时，清空并禁用下面的三个选项
          this.formData.ifatDust = undefined;
          this.formData.ifatChemistry = undefined;
          this.formData.ifatPhysics = undefined;
          // 清空下面的表格列表
          this.factorcheckReportList = [];
          this.factorCheckItemList = [];
          this.detectionResultList = [];
          // 清除验证错误
          this.$nextTick(() => {
            this.$refs.formData.clearValidate(["ifatDust", "ifatChemistry", "ifatPhysics"]);
          });
        }
      }
    },

    // 获取机构列表数据
    async getOrgList() {
      if (!this.taskId) return;
      const res = await orgList({ task_id: this.taskId });
      if (res.status === 200) {
        this.serviceOrgList = res.data.serviceOrgList || [];
        this.superUserList = res.data.superUserList || [];
        this.EnterpriseList = res.data.EnterpriseList || [];
      }
    },
    // 获取岗位列表
    // async fetchJobList(keyword) {
    //   try {
    //     const params = { keyword };
    //     const res = await getJobList(params);
    //     if (res.status === 200) {
    //       this.jobList = res.data.list || [];
    //     }
    //   } catch (error) {
    //     console.error('获取岗位列表失败:', error);
    //     this.jobList = [];
    //   }
    // },

    // 根据编码获取岗位名称
    getJobName(jobNo) {
      const item = this.jobList.find((item) => item.cnCsjcNo === jobNo);
      return item ? item.jobName : "未知岗位";
    },
    async fetchFactorList() {
      try {
        // let params = {
        //   taskId: this.taskId,
        // };
        // const res = await getFactorListNation();

        // 扁平化处理
        this.factorOptions = this.factorOptions.flatMap((category) => {
          return category.children.map((child) => ({
            category: category._id,
            id: child._id,
            name: child.chineseName.join("、"),
            aliases: child.chineseName.slice(1)
          }));
        });

        // 初始化时显示所有选项
        this.filteredFactorOptions = [...this.factorOptions];
        // console.log(this.factorOptions, 'this.factorOptions----');
      } catch (error) {
        console.error("获取监测因素列表失败:", error);
      }
    },

    // 获取因素名称
    getFactorName(factorId) {
      const item = this.factorOptions.find((item) => item.id === factorId);
      return item ? item.name : factorId; // 返回name属性而不是label
    },
    getQualifiedText(value) {
      const map = {
        0: "超标",
        1: "未超标",
        2: "未评判"
      };
      return map[value] || "未知";
    }
  }
};
</script>

<style lang="scss" scoped>
.table-container {
  table-layout: fixed;
  background: #fff;
  width: 100%;
  margin: 0 auto;
  border-collapse: collapse;

  td {
    padding: 5px 0;
  }

  th {
    font-weight: normal;
    border: 1px solid #e5e5e5;
    padding: 3px 0;
  }

  tr {
    border: 1px solid #e5e5e5;
    width: 100%;
    font-size: 14px;
  }

  .left-tittle {
    background: #f5f7fa;
  }

  .table-label {
    text-align: right;
    padding-right: 5px;
    color: #000;
    font-size: 14px;
    height: 42px;
  }

  .th-input {
    text-align: center;
    padding: 2px 4px;

    span {
      color: #333;
    }
  }
}

.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}
</style>
