<template>
  <div>
    <TitleTag titleName="定性分析情况列表"></TitleTag>
    <el-row>
      <el-col :span="24">
        <table style="width: 100%; border: 1px solid #ddd" class="table-container">
          <tr>
            <th class="left-tittle table-label" colspan="4"><span style="color: red">*</span>是否存在混合性有机溶剂</th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifExistAnalysis ? "是" : "否" }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4"><span style="color: red">*</span>是否开展有机溶剂定性分析</th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.ifAnalysis == 1 ? "是" : baseInfo.ifAnalysis == 2 ? "否" : "未监测有机溶剂" }}</span>
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4"><span v-if="baseInfo.ifAnalysis === 3" style="color: red">*</span>SDS资料附件名称</th>
            <th colspan="8" class="th-input">
              <span>{{ baseInfo.sdsFileName || "-" }}</span>
            </th>
          </tr>
        </table>
      </el-col>
    </el-row>

    <div class="titleNext">定性分析物质列表</div>

    <el-table
      :data="baseInfo.analysisSituationList"
      border
      style="width: 100%; margin-top: 10px"
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
    >
      <el-table-column prop="substanceName" label="物质名称" width="180"></el-table-column>
      <el-table-column prop="jobNos" label="涉及岗位编码" width="220">
        <template #default="{ row }">
          {{ getJobNames(row.jobNos).join("，") }}
        </template>
      </el-table-column>
      <el-table-column prop="jobNameOther" label="其他岗位名称" width="200">
        <template #default="{ row }">
          {{ row.jobNameOther || "-" }}
        </template>
      </el-table-column>
      <el-table-column label="定性分析危害因素" min-width="400">
        <template #default="{ row }">
          <div v-for="(factor, index) in row.analysisFactorList" :key="index" class="factor-item">
            <span>{{ getFactorName(factor.factorId) }} - {{ factor.ifDetection === 1 ? "检出" : "未检出" }}</span>
            <span v-if="factor.ifDetection === 1">(含量: {{ factor.analysisValue ? factor.analysisValue.toFixed(2) : "0.00" }}%)</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import TitleTag from "./TitleTag.vue";
import { getMonitoringAnalyseDetail, orgList, getJobList } from "@/api";
export default {
  components: { TitleTag },
  props: {
    factorOptionsFather: {
      type: Array,
      default: () => [] // 数组默认值用函数返回（引用类型规范）
    },
    jobList: {
      type: Array, // 明确类型为数组
      default: () => [] // 默认值为空数组（避免undefined）
    }
  },
  data() {
    return {
      healthcustodyReportList: [],
      supervisionItemList: [],
      baseInfo: {
        ifExistAnalysis: null,
        ifAnalysis: null,
        sdsFileName: "",
        analysisSituationList: [],
        workspaceMonitoringRecordId: "",
        EnterpriseID: "",
        jcOrgId: "",
        serviceOrgId: ""
      },
      filteredFactorOptions: [], // 过滤后的选项
      // jobList: [], // 岗位列表数据
      taskId: "",
      provincialWorkspaceMonitoringId: ""
    };
  },
watch: {
    // 监听 factorOptionsFather 变化，重新处理因子数据
    factorOptionsFather: {
      deep: true, // 深度监听（数组内部元素变化也能触发）
      immediate: true, // 初始化时立即执行一次
      handler(newVal) {
        // console.log('更新后的 factorOptionsFather:', newVal);
        this.fetchFactorList(); // 重新处理因子数据
      }
    },
    // 监听 jobList 变化（确保模板使用最新数据）
    jobList: {
      deep: true,
      immediate: true,
      handler(newVal) {
        // console.log('更新后的 jobList:', newVal);
        // 无需额外处理，模板会自动重新渲染并调用 getJobNames
      }
    }
  },
  created() {
    const { id, taskId, jcOrgId, serviceOrgId, EnterpriseID, provincialWorkspaceMonitoringId } = this.$route.query;
    this.taskId = taskId; // 保存任务ID
    this.baseInfo.workspaceMonitoringRecordId = id; //监测记录id
    this.baseInfo.jcOrgId = jcOrgId; // 委托单位
    this.baseInfo.serviceOrgId = serviceOrgId; // 检测机构
    this.baseInfo.EnterpriseID = EnterpriseID; // 获取企业/用人单位
    this.provincialWorkspaceMonitoringId = provincialWorkspaceMonitoringId; //省级任务id
    this.getOrgList();
    this.getMonitoringAnalyseDetail(id);
  },
  methods: {
    async getMonitoringAnalyseDetail(id) {
      const res = await getMonitoringAnalyseDetail({
        workspaceMonitoringRecordId: id
      });
      if (res.status == 200) {
        this.baseInfo = {
          ...this.baseInfo,
          workspaceMonitoringRecordId: res.data.workspaceMonitoringRecordId,
          EnterpriseID: res.data.EnterpriseID,
          ifExistAnalysis: res.data.ifExistAnalysis,
          ifAnalysis: res.data.ifAnalysis,
          sdsFileName: res.data.sdsFileName,
          analysisSituationList: res.data.analysisSituationList || []
        };
      }
    },

    // 获取机构列表数据
    async getOrgList() {
      if (!this.taskId) return;
      const res = await orgList({ task_id: this.taskId });
      if (res.status === 200) {
        this.serviceOrgList = res.data.serviceOrgList || [];
        this.superUserList = res.data.superUserList || [];
        this.EnterpriseList = res.data.EnterpriseList || [];
      }
    },

    // 获取岗位列表
    // async fetchJobList(keyword) {
    //   try {
    //     const params = { keyword, pageSize: 1000 };
    //     const res = await getJobList(params);
    //     if (res.status === 200) {
    //       this.jobList = res.data.list || [];
    //     }
    //   } catch (error) {
    //     console.error('获取岗位列表失败:', error);
    //     this.jobList = [];
    //   }
    // },

    // 根据岗位编码数组获取名称数组
    getJobNames(jobNos) {
      if (!jobNos || !jobNos.length) return [];
      return jobNos.map((code) => {
        const job = this.jobList.find((item) => item.cnCsjcNo === code);
        return job ? job.jobName : `未知岗位(${code})`;
      });
    },

    async fetchFactorList() {
      try {
        // 扁平化处理
        this.factorOptions = this.factorOptionsFather.flatMap((category) => {
          return category.children.map((child) => ({
            category: category._id,
            id: child._id,
            name: child.chineseName.join("、"),
            aliases: child.chineseName.slice(1)
          }));
        });
        this.filteredFactorOptions = [...this.factorOptions];
      } catch (error) {
        console.error("获取监测因素列表失败:", error);
        this.factorOptions = [];
      }
    },

    // 根据factorId获取监测因素名称
    getFactorName(factorId) {
      if (!factorId) return "";
      const factor = this.factorOptions.find((item) => item.id === factorId);
      return factor ? factor.name : "未知因素";
    }
  }
};
</script>

<style lang="scss" scoped>
.table-container {
  table-layout: fixed;
  background: #fff;
  width: 100%;
  margin: 0 auto;
  border-collapse: collapse;

  td {
    padding: 5px 0;
  }

  th {
    font-weight: normal;
    border: 1px solid #e5e5e5;
    padding: 3px 0;
  }

  tr {
    border: 1px solid #e5e5e5;
    width: 100%;
    font-size: 14px;
  }

  .left-tittle {
    background: #f5f7fa;
  }

  .table-label {
    text-align: right;
    padding-right: 5px;
    color: #000;
    font-size: 14px;
    height: 42px;
  }

  .th-input {
    text-align: left;
    padding: 2px 10px;

    span {
      color: #333;
    }
  }
}

.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}

.factor-item {
  margin-bottom: 5px;
  line-height: 1.5;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
