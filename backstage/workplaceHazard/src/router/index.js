import Vue from 'vue';
import Router from 'vue-router';
import settings from '@root/publicMethods/settings';
import Index from '@/views/index';
import WorkplaceDetail from '@/views/workplaceDetail.vue';
import Review from '@/views/review';
import ReviewDetail from '@/views/reviewDetail.vue';
import ProgressQuery from '@/views/progressQuery';
import RecordQuery from '@/views/recordQuery';
// import RecordQueryDetail from '@/views/recordQueryDetail';
// import SurveyFillingOut from '@/views/surveyFillingOut';
import SurveyFillingOutAdd from '@/views/surveyFillingOutAdd';

Vue.use(Router);

const createRouter = () =>
  new Router({
    mode: 'history',
    base: process.env.BASE_URL,
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: [
      {
        path: settings.admin_base_path + '/workplaceHazard', // 监测任务管理
        name: 'index',
        component: Index,
      },
      {
        path: settings.admin_base_path + '/workplaceHazard/workplaceDetail', // 监测任务管理详情
        name: 'workplaceDetail',
        component: WorkplaceDetail,
      },
      {
        path: settings.admin_base_path + '/workplaceHazard/review', // 疾控审核- 监测记录列表
        name: 'review',
        component: Review,
      },
      {
        path: settings.admin_base_path + '/workplaceHazard/reviewDetail', // 监测记录列表-审核
        name: 'reviewDetail',
        component: ReviewDetail,
      },
      {
        path: settings.admin_base_path + '/workplaceHazard/progressQuery', // 监测统计分析- 监测进度查询
        name: 'progressQuery',
        component: ProgressQuery,
      },
      {
        path: settings.admin_base_path + '/workplaceHazard/recordQuery', // 监测统计分析- 监测记录查询
        name: 'recordQuery',
        component: RecordQuery,
      },
      // {
      //   path: settings.admin_base_path + '/workplaceHazard/recordQueryDetail', // 监测统计分析- 监测记录查询-详情
      //   name: 'recordQueryDetail',
      //   component: RecordQueryDetail,
      // },
      // {
      //   path: settings.admin_base_path + '/workplaceHazard/surveyFillingOut', // 调查填报
      //   name: 'surveyFillingOut',
      //   component: SurveyFillingOut,
      // },
      {
        path: settings.admin_base_path + '/workplaceHazard/surveyFillingOutAdd', // 调查填报-添加
        name: 'surveyFillingOutAdd',
        component: SurveyFillingOutAdd,
      },
    ],
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
