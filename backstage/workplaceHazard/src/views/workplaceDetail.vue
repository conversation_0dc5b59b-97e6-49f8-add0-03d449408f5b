<template>
  <div style="padding: 15px 20px">
    <div :class="classObj">
      <div class="main-container">
        <div>
          <div
            style="
              width: 100%;
              text-align: right;
              margin: 10px;
              display: flex;
              justify-content: space-between;
            "
          >
            <el-button size="small" @click="goBack" icon="el-icon-back"
              >返回</el-button
            >
          </div>
          <!-- 基本信息，只做显示 -->
          <taskBaseMess
            :base-info="baseInfo"
            :industryCheckFactorList="industryCheckFactorList"
            :areaFlag="areaFlag"
            :title="title"
          />
          <!-- 省级 新增 -->
          <TitleTag
            titleName="添加监测化学因素"
            v-if="type == 'edit' && areaFlag == 1"
          ></TitleTag>
          <!-- <TitleTag titleName="自定义危害因素" v-if="type == 'view'"></TitleTag> -->
          <TitleTag
            titleName="自定义危害因素"
            v-if="type == 'view' || areaFlag == 2 || areaFlag == 3"
          ></TitleTag>
          <el-row v-if="type == 'edit' && areaFlag == 1">
            <el-col :span="24">
              <el-button
                type="primary"
                @click="addMonitoringFactors"
                icon="el-icon-plus"
                plain
                style="margin-bottom: 10px"
                >添加监测因素</el-button
              >
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-table
                :data="customFactorList"
                tooltip-effect="light"
                style="width: 100%"
                stripe
                border
                header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
                align="center"
              >
                <el-table-column
                  prop="catetory"
                  label="分类"
                  align="center"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  prop="chineseName"
                  label="监测因素"
                  align="center"
                  show-overflow-tooltip
                >
                </el-table-column>
                <!-- <el-table-column
                  prop="unit"
                  label="CAS码"
                  align="center"
                  show-overflow-tooltip
                ></el-table-column> -->
                <el-table-column
                  prop=""
                  label="操作"
                  align="center"
                  v-if="type == 'edit' && areaFlag == 1"
                >
                  <template slot-scope="scope">
                    <el-button
                      type="danger"
                      @click="delCustom(scope.row._id)"
                      plain
                      size="small"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
          <TitleTag titleName="监测任务数"></TitleTag>
          <!-- 省级 -->
          <el-row v-if="areaFlag == 1">
            <el-col :span="24">
              <div style="margin-bottom: 10px">
                任务总数：{{ baseInfo.taskNum }} (当前分配总数：{{
                  currentTotal
                }})
                <span v-if="isTotalInsufficient" style="color: red">
                  （分配总数不足任务总数！）
                </span>
              </div>
              <div class="form-container">
                <el-form
                  :model="subTaskList"
                  :rules="rules"
                  ref="formRef"
                  label-width="auto"
                  label-position="right"
                  :inline="true"
                >
                  <el-form-item
                    v-for="(item, index) in subTaskList"
                    :key="index"
                    :label="item.areaName + '：'"
                    :prop="`${index}.taskNum`"
                    :rules="rules.taskNum"
                  >
                    <el-input
                      v-model.number="item.taskNum"
                      type="number"
                      placeholder="请输入数值"
                      clearable
                      :disabled="type == 'view'"
                      @change="validateTotal"
                    >
                    </el-input>
                  </el-form-item>

                  <el-form-item v-if="type == 'edit'" style="display: none">
                    <el-button type="primary" @click="submitForm"
                      >提交</el-button
                    >
                  </el-form-item>
                </el-form>
              </div>
            </el-col>
          </el-row>
          <!-- 市级  -->
          <el-row v-if="areaFlag == 2 || areaFlag == 3">
            <el-col :span="24">
              <div
                v-if="type == 'edit'"
                style="
                  display: flex;
                  align-items: baseline;
                  font-size: 14px;
                  color: #606266;
                "
              >
                <el-button
                  type="primary"
                  @click="addComputer"
                  icon="el-icon-plus"
                  plain
                  style="margin-bottom: 10px"
                  >选择企业</el-button
                >
                <span style="margin-left: 20px"
                  >当前任务数为{{ taskNum }}，已选择{{
                    EnterpriseList.length
                  }}家企业</span
                >
              </div>
              <el-table :data="EnterpriseList" style="width: 100%">
                <el-table-column
                  prop="cname"
                  label="单位名称"
                ></el-table-column>
                <el-table-column label="操作" v-if="type == 'edit'">
                  <template slot-scope="scope">
                    <el-button
                      type="danger"
                      @click="delBtn(scope.row._id)"
                      plain
                      size="small"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
          <el-row style="margin-top: 20px" v-if="type == 'edit'">
            <el-col :span="24">
              <el-button
                type="primary"
                icon="el-icon-check"
                @click="publishBtn()"
                >发布</el-button
              >
              <el-button @click="shengTimeSave()" v-if="areaFlag == 1"
                >暂存</el-button
              >
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <addChemicalHazard
      ref="ChemicalHazard"
      :taskId="id"
      @confirm="handleFactorConfirm"
    ></addChemicalHazard>
    <addComputer
      ref="addComputerRef"
      @confirm="handleConfirmSelection"
    ></addComputer>
  </div>
</template>

<script>
import { initEvent } from '@root/publicMethods/events';
import moment from 'moment';
import TitleTag from '@/components/TitleTag.vue';
import addChemicalHazard from '@/components/addChemicalHazard.vue';
import addComputer from '@/components/addComputer.vue';
import taskBaseMess from '@/components/taskBaseMess.vue';
import {
  taskDetail,
  taskDetailPublish,
  taskDetailCityPublish,
  jurisdiction,
} from '@/api';
import axios from 'axios';
export default {
  components: {
    TitleTag,
    addChemicalHazard,
    addComputer,
    taskBaseMess,
  },
  data() {
    // 自定义验证规则 - 检查单个数值是否>=0
    const validateTaskNum = (rule, value, callback) => {
      if (value === '' || value === null) {
        return callback(new Error('请输入数值'));
      }
      if (!Number.isInteger(value)) {
        return callback(new Error('请输入整数'));
      }
      if (value < 0) {
        return callback(new Error('不能小于0'));
      }
      callback();
    };
    return {
      id: null,
      type: '',
      projectCategoryOptions: [],
      sidebarOpened: true,
      baseInfo: {},
      // recordApplyTableFile:[]   // 建设项目职业病防护设施竣工验收(备案)申请书
      subTaskList: [],
      rules: {
        taskNum: [
          { required: true, validator: validateTaskNum, trigger: 'blur' },
        ],
      },
      customFactorList: [],
      areaFlag: 0,
      EnterpriseList: [],
      title: '',
      taskNum: 0,
    };
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: 'false',
        mobile: this.device === 'mobile',
      };
    },
    // 计算当前分配总数
    currentTotal() {
      return this.subTaskList.reduce(
        (sum, item) => sum + (Number(item.taskNum) || 0),
        0
      );
    },
    // 检查是否满足任务总数要求
    isTotalInsufficient() {
      return this.currentTotal < this.baseInfo.taskNum;
    },
  },
  async created() {
    this.id = this.$route.query.id;
    this.type = this.$route.query.type;
    this.title = this.$route.query.title;

    this.taskDetail();
  },
  mounted() {
    initEvent(this);
  },
  methods: {
    // 获取辖下区域
    jurisdiction() {
      jurisdiction().then(res => {
        if (res.status == 200) {
          this.subTaskList = res.data.map(item => ({
            areaName: item.name, // 映射名称字段
            taskNum: 0, // 初始化任务数为0，可根据需求调整默认值
            // _id: item._id,
            // area_code: item.area_code
          }));
        }
      });
    },
    // 发布
    async publishBtn() {
      if (this.areaFlag == 1) {
        // 省级
        const statusFlag = 1;
        this.submitForm(statusFlag);
      } else if (this.areaFlag == 2 || this.areaFlag == 3) {
        // 判断当前任务数是否大于当前企业数
        if (this.EnterpriseList.length < this.taskNum) {
          this.$message.error('选择的企业数一定要大于或者等于分配的任务数');
          return;
        }

        // 市区
        const idList =
          this.EnterpriseList.length > 0
            ? this.EnterpriseList.map(item => item._id)
            : [];
        let params = {
          _id: this.id,
          EnterpriseList: idList,
        };
        const res = await taskDetailCityPublish(params);
        if (res.status == 200) {
          this.$message.success('发布成功！');
          this.goBack();
        }
      } else {
        this.$message.danger('暂无权限');
      }
    },
    // 省级暂存
    shengTimeSave() {
      const statusFlag = 0;
      this.submitForm(statusFlag);
    },
    // 验证总数
    validateTotal() {
      this.$refs.formRef.validate(() => {});
    },
    // 提交表单
    async submitForm(statusFlag) {
      const valid = await this.$refs.formRef.validate();
      if (!valid) {
        this.$message.error('请检查输入内容');
        return false;
      }

      // 检查总数是否足够
      if (this.isTotalInsufficient) {
        this.$message.error(
          `分配总数不能小于任务总数（${this.baseInfo.taskNum}）`
        );
        return false;
      }
      const idList =
        this.customFactorList.length > 0
          ? this.customFactorList.map(item => item._id)
          : [];
      let params = {
        _id: this.id,
        taskList: [...JSON.parse(JSON.stringify(this.subTaskList))],
        customFactorList: idList,
        taskStatus: statusFlag == 1 ? '1' : '0',
      };
      const res = await taskDetailPublish(params);
      if (res.status == 200) {
        this.$message.success(statusFlag == 1 ? '发布成功！' : '暂存成功！');
        this.goBack();
      }

      // console.log('有效数据：', JSON.parse(JSON.stringify(this.subTaskList)));
    },
    handleConfirmSelection(selected) {
      // this.EnterpriseList.push(...selected);
      // 遍历新选中的企业，只添加不在现有列表中的项
      selected.forEach(newItem => {
        // 检查现有列表中是否已存在相同_id的企业
        const isDuplicate = this.EnterpriseList.some(
          existingItem => existingItem._id === newItem._id
        );
        // 若不存在则添加
        if (!isDuplicate) {
          this.EnterpriseList.push(newItem);
        }
      });
    },

    delBtn(id) {
      this.EnterpriseList = this.EnterpriseList.filter(item => item._id !== id);
    },
    addMonitoringFactors() {
      // this.dialogVisible=true
      this.$refs.ChemicalHazard.show();
    },
    handleFactorConfirm(selectedItems) {
      console.log('选中的危害因素:', selectedItems);
      // selectedItems 现在是完整的对象数组，例如：
      // [
      //   {
      //     category: "放射性因素",
      //     id: "6132e6c7e9ddcf6c4994e051",
      //     name: "电离辐射(...)",
      //     aliases: ["电离辐射"]
      //   },
      //   {
      //     category: "物理",
      //     id: "2mF7Q1o92W7a",
      //     name: "手传振动",
      //     aliases: []
      //   }
      // ]
      selectedItems.forEach(e => {
        let item = { catetory: e.category, chineseName: e.name, _id: e.id };
        this.customFactorList.push(item);
      });
    },
    delCustom(id) {
      // 确认对话框
      this.$confirm('确定要删除此项吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 调用删除方法
          this.deleteItem(id);
        })
        .catch(() => {
          // 取消操作
        });
    },
    deleteItem(id) {
      // 1. 找到要删除项的索引
      const index = this.customFactorList.findIndex(item => item._id == id);
      if (index !== -1) {
        // 2. 从数组中删除该项
        this.customFactorList.splice(index, 1);

        // 3. 显示成功消息
        this.$message({
          type: 'success',
          message: '删除成功!',
        });
      }
    },
    addComputer() {
      this.$refs.addComputerRef.show();
    },
    handleHarmFactor(harmFactors) {
      // 初始化结果数组
      const result = [];
      // 遍历 harmFactors 数组
      harmFactors &&
        Array.isArray(harmFactors) &&
        harmFactors.forEach((group, groupIndex) => {
          // 生成序号
          const serialNumber = groupIndex + 1;
          // 生成格式化的字符串
          const formattedString = `${serialNumber}. ${group[0]}-${group[1]}`;
          // 添加到结果数组
          result.push(formattedString);
        });

      // 将结果数组转换为字符串
      return result.join('\n');
    },

    handleProjectCategory(val) {
      let formatString = '';
      this.projectCategoryOptions.forEach(item => {
        if (item.value === val) {
          formatString = item.name;
        }
      });
      return formatString;
    },
    async taskDetail() {
      const res = await taskDetail({ id: this.id });
      if (res.status === 200) {
        this.baseInfo = res.data;
        console.log(this.baseInfo.areaName.length, '判断省市');
        this.areaFlag = this.baseInfo.areaName.length;
        // 监测行业&危害因素
        this.industryCheckFactorList = res.data.industryCheckFactorList;
        // 监测化学因素
        this.customFactorList = res.data.customFactorList;
        this.customFactorList.forEach(e => {
          e.chineseName = e.chineseName.join('、');
        });
        this.taskNum = res.data.taskNum;
        // 监测任务数
        if (this.baseInfo.areaName.length == 1) {
          if (this.type == 'edit') {
            // 省级编辑
            this.jurisdiction();
          } else {
            this.subTaskList = res.data.subTaskList;
          }
        }
        // 获取县级任务分配情况
        if (
          this.baseInfo.areaName.length == 2 ||
          this.baseInfo.areaName.length == 3
        ) {
          this.EnterpriseList = res.data.EnterpriseList;
          console.log('我是县区级');
        }
      }
    },

    goBack() {
      this.$router.go(-1);
    },
    // 处理时间
    handleTime(data) {
      return data ? moment(data).format('YYYY-MM-DD') : '';
    },
  },
};
</script>

<style lang="scss" scoped>
.entry-container {
  padding: 10px 15px;
}

.header-container {
  width: 100%;
  position: relative;
}

.header-container-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background-color: #fff;
}

.table-container {
  table-layout: fixed;
  background: #fff;
  width: 100%;
  margin: 0 auto;
  border-collapse: collapse;

  td {
    padding: 5px 0;
  }

  th {
    font-weight: normal;
    border: 1px solid #e5e5e5;
    padding: 3px 0;
  }

  tr {
    border: 1px solid #e5e5e5;
    width: 100%;
    font-size: 14px;
  }

  .left-tittle {
    background: #f5f7fa;
  }

  .center-tittle {
    background: #ecf5ff;
  }

  .table-label {
    text-align: right;
    padding-right: 5px;
    color: #000;
    font-size: 14px;
    height: 42px;
  }

  .th-input {
    text-align: center;
    padding: 2px 4px;

    span {
      color: #333;
    }
  }

  .th-radio {
    text-align: center;
    padding-left: 6px;
    padding-right: 6px;
  }

  .input-width {
    width: 200px;
    background-color: bisque;
  }

  .health-check-th {
    padding: 11px 28px;
    text-align: center;
  }
}

.upload-container {
  display: flex;
  padding: 15px 0px;
}

.doc-container {
  padding: 5px 1px;
  cursor: pointer;
  color: #409eff;
}

.form-container {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.form-title {
  margin-bottom: 20px;
  padding-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid #eee;
}
.el-form-item {
  margin-bottom: 22px;
}
/* 紧凑模式 */
.compact-form .el-form-item {
  margin-bottom: 16px;
}
.compact-form .el-form-item__label {
  padding-right: 8px;
}
</style>
