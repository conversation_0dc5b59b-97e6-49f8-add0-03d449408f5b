<template>
    <div style="padding:15px 20px;">
        <div :class="classObj">
            <div class="main-container">
                <div>
                    <div
                        style="width: 100%;text-align: right;margin: 10px;display: flex;justify-content: space-between;">
                        <el-button size="small" @click="goBack" icon="el-icon-back">返回</el-button>
                        <!-- <span>
                        <el-button type="warning" size="small" @click="tempSave">暂存</el-button>
                        <el-button type="success" size="small" @click="downloadApplyTable" icon="el-icon-download">下载申请表</el-button>
                        <el-button type="primary" size="small" @click="submitRecord">提交备案</el-button>
                    </span> -->
                    </div>
                    <TitleTag titleName="基本信息"></TitleTag>
                    <el-row>
                        <el-col :span="24">
                            <table style="width:100%;border:1px solid #ddd;" class="table-container">
                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>项目名称
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.projectName }}</span>
                                    </th>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>申请单位
                                    </th>
                                    <th colspan="8" class="th-input">
                                        {{ baseInfo.cname }}
                                    </th>
                                </tr>

                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>统一社会信用代码
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.code }}</span>
                                    </th>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>项目性质
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ handleProjectType(baseInfo.projectType) }}</span>
                                    </th>
                                </tr>

                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>所在地区
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.companyAddress ? baseInfo.companyAddress.join('/') : ''
                                        }}</span>
                                    </th>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>单位详细地址
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.companyDetailAddress }}</span>
                                    </th>
                                </tr>

                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>项目所在地区
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.projectAddress ? baseInfo.projectAddress.join('/') : ''
                                        }}</span>
                                    </th>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>项目详细地址
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.projectDetailAddress }}</span>
                                    </th>
                                </tr>

                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>建设单位法人
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.cuLegalPerson }}</span>
                                    </th>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>总投资情况
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.investDetail }}</span>
                                    </th>
                                </tr>

                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>项目负责人
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.projectDutyPerson }}</span>
                                    </th>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>联系电话
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.phone }}</span>
                                    </th>
                                </tr>

                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>项目类型
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ handleProjectCategory (baseInfo.projectCategory) }}</span>
                                    </th>

                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>职业病危害因素
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ handleHarmFactor (baseInfo.harmFactor) }}</span>
                                    </th>
                                </tr>
                            </table>
                        </el-col>
                    </el-row>

                    <TitleTag titleName="职业病危害预评价执行情况"></TitleTag>
                    <el-row>
                        <el-col :span="24">
                            <table style="width:100%;border:1px solid #ddd;" class="table-container">
                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>报告编制单位
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.yOrganizationUnit }}</span>
                                        <!-- <el-input v-model="baseInfo.yOrganizationUnit"></el-input> -->
                                    </th>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>评审时间
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ handleTime(baseInfo.yReviewTime) }}</span>
                                        <!-- <el-date-picker style="width:100%;" v-model="baseInfo.yReviewTime" type="date" value="yyyy-MM-dd"
                value-format="yyyy-MM-dd" placeholder="选择日期">
              </el-date-picker> -->
                                    </th>
                                </tr>
                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>联系人
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.yContact }}</span>
                                        <!-- <el-input v-model="baseInfo.yContact"></el-input> -->
                                    </th>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>联系电话
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.yNumber }}</span>
                                        <!-- <el-input v-model="baseInfo.yNumber"></el-input> -->
                                    </th>
                                </tr>
                            </table>
                        </el-col>
                    </el-row>

                    <TitleTag titleName="职业病防护设施设计执行情况"></TitleTag>
                    <el-row>
                        <el-col :span="24">
                            <table style="width:100%;border:1px solid #ddd;" class="table-container">
                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>报告编制单位
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.reportWriteUnit }}</span>
                                    </th>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>评审时间
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ handleTime(baseInfo.reviewTime) }}</span>
                                    </th>
                                </tr>
                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>联系人
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.reviewUnitContact }}</span>
                                    </th>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>联系电话
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.reviewUnitPhone }}</span>
                                    </th>
                                </tr>
                            </table>
                        </el-col>
                    </el-row>

                    <TitleTag titleName="职业病危害控制效果评价报告情况"></TitleTag>
                    <el-row>
                        <el-col :span="24">
                            <table style="width:100%;border:1px solid #ddd;" class="table-container">
                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>职业病危害控制效果评价报告编制单位
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.controlResultWriteUnit }}</span>
                                    </th>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>建设项目职业病危害风险分类
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span v-show="baseInfo.cpHazardLevel == 1">一般</span>
                                        <span v-show="baseInfo.cpHazardLevel == 2">较重</span>
                                        <span v-show="baseInfo.cpHazardLevel == 3">严重</span>
                                    </th>
                                </tr>

                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>联系人
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.controlResultWriteUnitContact }}</span>
                                    </th>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>联系电话
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.controlResultWriteUnitPhone }}</span>
                                    </th>
                                </tr>
                            </table>
                        </el-col>
                    </el-row>


                    <TitleTag titleName="职业病防护设施验收安排"></TitleTag>
                    <el-row>
                        <el-col :span="24">
                            <table style="width:100%;border:1px solid #ddd;" class="table-container">
                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                    <span style="color: red">*</span>验收单位
                                    </th>
                                    <th colspan="8" class="th-input">
                                    <!-- <el-input v-model="baseInfo.inspectionUnit"></el-input> -->
                                    <span>{{ baseInfo.inspectionUnit }}</span>
                                    </th>
                                    <th class="left-tittle table-label" colspan="4">
                                    <span style="color: red">*</span>验收单位地址
                                    </th>
                                    <th colspan="8" class="th-input">
                                    <span>{{ baseInfo.inspectionUnitAddress ? baseInfo.inspectionUnitAddress.join('/') : '' }}</span>
                                    <!-- <el-cascader style="width: 100%;" :props="workAdd" v-model="baseInfo.inspectionUnitAddress" clearable
                                        filterable></el-cascader> -->
                                    </th>
                                </tr>
                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>验收具体时间
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ handleTime(baseInfo.checkAcceptTime) }}</span>
                                    </th>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>验收地点
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.checkAcceptPosition }}</span>
                                    </th>
                                </tr>
                            </table>
                        </el-col>
                    </el-row>

                    <TitleTag titleName="建设项目概况"></TitleTag>
                    <el-row>
                        <el-col :span="24">
                            <table style="width:100%;border:1px solid #ddd;" class="table-container">
                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>建设内容
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.constructionContent }}</span>
                                        <!-- <el-input type="textarea" autosize placeholder="请输入建设内容" v-model="baseInfo.constructionContent"></el-input> -->
                                    </th>

                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>主要原辅材料
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.materials }}</span>
                                        <!-- <el-input v-model="baseInfo.materials"></el-input> -->
                                    </th>
                                </tr>

                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>主要工艺
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.process }}</span>
                                        <!-- <el-input v-model="baseInfo.process"></el-input> -->
                                    </th>

                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>工作制度与劳动定员
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.workSystem }}</span>
                                        <!-- <el-input v-model="baseInfo.workSystem"></el-input> -->
                                    </th>
                                </tr>

                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>试运行情况简介
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.testRun }}</span>
                                        <!-- <el-input v-model="baseInfo.testRun"></el-input> -->
                                    </th>

                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>产生的职业病危害因素种类以及接触人数
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.occupationalHazards }}</span>
                                        <!-- <el-input v-model="baseInfo.occupationalHazards"></el-input> -->
                                    </th>
                                </tr>

                                <tr>
                                    <th class="left-tittle table-label" colspan="4">
                                        <span style="color: red">*</span>职业病危害因素检测超标情况
                                    </th>
                                    <th colspan="8" class="th-input">
                                        <span>{{ baseInfo.exceedStandard }}</span>
                                        <!-- <el-input v-model="baseInfo.exceedStandard"></el-input> -->
                                    </th>
                                </tr>
                            </table>
                        </el-col>
                    </el-row>

                    <div class="header-container">
                        <TitleTag titleName="拟定参加职业病防护设施验收人员与职责分工"></TitleTag>
                    </div>
                    <el-row>
                        <el-col :span="24">
                            <el-table :data="peopleData" tooltip-effect="light" style="width: 100%" stripe border
                                header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
                                align="center">
                                <el-table-column type="index" align="center" show-overflow-tooltip label="序号"
                                    width="60"></el-table-column>
                                <el-table-column prop="name" label="姓名" align="center"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="unit" label="单位" align="center"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="title" label="职务/职称" align="center"></el-table-column>
                                <el-table-column prop="personType" label="人员类别" align="center"></el-table-column>
                            </el-table>
                        </el-col>
                    </el-row>

                    <TitleTag titleName="附件"></TitleTag>
                    <div v-if="baseInfo.recordApplyTableFile">
                        <div>建设项目职业病防护设施竣工验收(备案)申请书：</div>
                        <el-button v-for="item in baseInfo.recordApplyTableFile" @click="openUrl(item.url)"
                            type="text">{{ item.name }}</el-button>
                    </div>

                    <div v-if="baseInfo.designApproveTableFile">
                        <div>建设项目设计专项批复文件：</div>
                        <el-button v-for="item in baseInfo.designApproveTableFile" @click="openUrl(item.url)"
                            type="text">{{ item.name }}</el-button>
                    </div>

                    <div v-if="baseInfo.checkSelfReportFile">
                        <div>建设项目职业病防护设施竣工自行验收情况报告：</div>
                        <el-button v-for="item in baseInfo.checkSelfReportFile" @click="openUrl(item.url)"
                            type="text">{{ item.name }}</el-button>
                    </div>

                    <div v-if="baseInfo.projectInitiationApprovalFile">
                        <div>建设项目立项审批文件：</div>
                        <el-button v-for="item in baseInfo.projectInitiationApprovalFile" @click="openUrl(item.url)"
                            type="text">{{ item.name }}</el-button>
                    </div>

                    <div v-if="baseInfo.finishRecordCommonFile">
                        <div>申请建设项目职业病防护设施竣工验收（备案）的公函：</div>
                        <el-button v-for="item in baseInfo.finishRecordCommonFile" @click="openUrl(item.url)"
                            type="text">{{ item.name }}</el-button>
                    </div>

                    <div v-if="baseInfo.unitCertificateFile">
                        <div>单位资质证明：</div>
                        <el-button v-for="item in baseInfo.unitCertificateFile" @click="openUrl(item.url)"
                            type="text">{{ item.name }}</el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { initEvent } from "@root/publicMethods/events";
import moment from 'moment'
import TitleTag from "@/components/TitleTag.vue";
import { getContructproEquRecordDetail, getProjectCategoryOptions,getDictionaryByKey } from '@/api'
import axios from "axios";
export default {
    components: {
        TitleTag
    },
    data() {
        return {
            id: null,
            projectCategoryOptions:[],
            sidebarOpened: true,
            peopleData: [],
            baseInfo: {},
            workAdd: {
                lazy: true,
                lazyLoad(node, resolve) {
                    if (node.level > 0) {
                        node = node.data;
                    }
                    const url = "/api/address/list";
                    axios({
                        method: "get",
                        url: url,
                        params: node,
                    }).then((response) => {
                        try {
                            const districts = response.data.data;
                            const a = districts[0];
                            let nodes = districts.map((item) => ({
                                value: item.name,
                                label: item.name,
                                id: item.id,
                                area_code: item.area_code,
                                parent_code: item.parent_code,
                                lat: item.lat,
                                lon: item.lng,
                                merger_name: item.merger_name,
                                short_name: item.short_name,
                                leaf: item.level >= 3,
                            }));

                            resolve(nodes);
                        } catch (e) {
                            console.log(e);
                        }
                    });
                },
            },
            projectTypeOptions: [
                { value: 1, label: '新建' },
                { value: 2, label: '改建' },
                { value: 3, label: '扩建' },
                { value: 4, label: '技术改造' },
                { value: 5, label: '技术引进' }
            ],
            zlTypeOption: [
                { value: 1, label: '放射治疗' },
                { value: 2, label: '核医学' },
                { value: 3, label: '介入放射学' },
                { value: 4, label: 'X射线影像诊断' }
            ],
            // recordApplyTableFile:[]   // 建设项目职业病防护设施竣工验收(备案)申请书
        }
    },
    computed: {
        classObj() {
            return {
                hideSidebar: !this.sidebarOpened,
                openSidebar: this.sidebarOpened,
                withoutAnimation: "false",
                mobile: this.device === "mobile",
            };
        },
    },
    async created() {
        this.id = this.$route.query.id
        await this.getProjectCategoryOptions()
        this.getContructproEquRecordDetail()
    },
    mounted() {
        initEvent(this);
    },
    methods: {
        handleHarmFactor(harmFactors){
            // 初始化结果数组
            const result = [];
            // 遍历 harmFactors 数组
            harmFactors && Array.isArray(harmFactors) && harmFactors.forEach((group, groupIndex) => {
                // 生成序号
                const serialNumber = groupIndex + 1;
                // 生成格式化的字符串
                const formattedString = `${serialNumber}. ${group[0]}-${group[1]}`;
                // 添加到结果数组
                result.push(formattedString);
            });

            // 将结果数组转换为字符串
            return result.join('\n');
        },

        async getProjectCategoryOptions(){
            let params = {
                key:'construct_project_type'
            }
            const res = await getDictionaryByKey(params)
            if(res.status == 200){
                this.projectCategoryOptions = res.data
            }
        },

        handleProjectCategory(val){
            let formatString = ''
            this.projectCategoryOptions.forEach(item => {
                if(item.value === val){
                formatString = item.name
                }
            })
            return formatString
        },
        async getContructproEquRecordDetail() {
            const res = await getContructproEquRecordDetail({ id: this.id })
            if (res.status === 200) {
                this.baseInfo = res.data
                this.peopleData = res.data.peopleData
            }
        },
        async getCompanyInsititutionInfo() {
            const res = await getCompanyInsititutionInfo()
            if (res.status === 200) {
                this.baseInfo.cname = res.data.cname
                this.baseInfo.code = res.data.code
                this.baseInfo.legalPerson = res.data.corp
                this.baseInfo.companyAddress = res.data.districtRegAdd
                this.baseInfo.companyDetailAddress = res.data.companyDetailAddress
            }
        },
        goBack() {
            this.$router.go(-1)
        },
        // 处理时间
        handleTime(data) {
            return data ? moment(data).format('YYYY-MM-DD') : ''
        },
        // 处理项目类型
        handleProjectType(data) {
            let stringName = ''
            this.projectTypeOptions.forEach(item => {
                if (data == item.value) {
                    stringName = item.label
                }
            })
            return stringName
        },
        openUrl(url) {
            console.log('进来了=======')
            window.open(url, '_blank')
        }
    }
}
</script>

<style lang="scss" scoped>
.entry-container {
    padding: 10px 15px;
}

.header-container {
    width: 100%;
    position: relative;
}

.header-container-btn {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    background-color: #fff;
}

.table-container {
    table-layout: fixed;
    background: #fff;
    width: 100%;
    margin: 0 auto;
    border-collapse: collapse;

    td {
        padding: 5px 0;
    }

    th {
        font-weight: normal;
        border: 1px solid #e5e5e5;
        padding: 3px 0;
    }

    tr {
        border: 1px solid #e5e5e5;
        width: 100%;
        font-size: 14px;
    }

    .left-tittle {
        background: #ECF5FF;
    }

    .center-tittle {
        background: #ECF5FF;
    }

    .table-label {
        text-align: right;
        padding-right: 5px;
        color: #000;
        font-size: 14px;
        height: 42px;
    }

    .th-input {
        text-align: center;
        padding: 2px 4px;

        span {
            color: #333;
        }
    }

    .th-radio {
        text-align: center;
        padding-left: 6px;
        padding-right: 6px;
    }

    .input-width {
        width: 200px;
        background-color: bisque;
    }

    .health-check-th {
        padding: 11px 28px;
        text-align: center;
    }
}

.upload-container {
    display: flex;
    padding: 15px 0px;
}

.doc-container {
    padding: 5px 1px;
    cursor: pointer;
    color: #409EFF;
}
</style>