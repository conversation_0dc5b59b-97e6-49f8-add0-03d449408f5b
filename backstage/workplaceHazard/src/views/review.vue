<template>
  <div class="expert-container">
    <div :class="classObj">
      <div class="main-container">
        <TitleTag titleName="查询条件"></TitleTag>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item label="监测任务">
            <el-select v-model="searchForm.taskId" placeholder="请选择">
              <el-option
                :label="item.title"
                :value="item._id"
                :key="item._id"
                v-for="(item, index) in taskList"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="单位名称">
            <el-input
              v-model="searchForm.projectName"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="行业分类">
            <el-select v-model="searchForm.projectType" placeholder="请选择">
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="(item, index) in classificationIndustry"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="填报日期">
            <el-date-picker
              v-model="searchForm.projectType"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="审核日期">
            <el-date-picker
              v-model="searchForm.projectType"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item> -->
          <el-form-item label="审核状态">
            <el-checkbox-group v-model="searchForm.taskStatus">
              <el-checkbox
                :label="item.id"
                v-for="(item, index) in filteredStatusList"
                :key="index"
                >{{ item.title }}</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-form>

        <div class="header-container">
          <TitleTag titleName="监测记录列表"></TitleTag>
          <div class="header-container-btn">
            <el-button
              v-if="regAdd.length > 1"
              @click="addSurveyFilling"
              icon="el-icon-plus"
              type="primary"
              plain
              >创建监测记录</el-button
            >
          </div>
        </div>
        <el-table
          :data="tableData"
          tooltip-effect="light"
          style="min-width: 100%"
          stripe
          border
          header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
          align="center"
        >
          <el-table-column
            type="index"
            align="center"
            show-overflow-tooltip
            width="80"
            label="序号"
          >
            <template slot-scope="scope">
              {{
                (pageInfo.curPage - 1) * pageInfo.pageSize + scope.$index + 1
              }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="regAdd.length == 1"
            prop="EnterpriseID"
            label="市级名称"
            align="center"
            show-overflow-tooltip
            min-width="160"
          >
            <template slot-scope="scope">
              {{
                scope.row.taskId.areaName[scope.row.taskId.areaName.length - 1]
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="EnterpriseID"
            label="单位名称"
            align="center"
            show-overflow-tooltip
            min-width="160"
          >
            <template slot-scope="scope">
              {{ scope.row.EnterpriseID.cname }}
            </template>
          </el-table-column>
          <el-table-column
            prop="jcOrgId"
            label="委托单位"
            align="center"
            show-overflow-tooltip
            min-width="160"
          >
            <template slot-scope="scope">
              {{ scope.row.jcOrgId.cname }}
            </template>
          </el-table-column>

          <el-table-column
            prop="jcOrgId"
            label="检测机构"
            align="center"
            show-overflow-tooltip
            min-width="160"
          >
            <template slot-scope="scope">
              {{ scope.row.serviceOrgId.name }}
            </template>
          </el-table-column>
          <el-table-column
            prop="agreeStatus"
            label="状态"
            align="center"
            show-overflow-tooltip
            min-width="110"
          >
            <template slot-scope="scope">
              <el-tag
                v-for="(item, index) in statusList"
                :key="index"
                :type="item.color"
                plain
                size="small"
                v-show="item.id == scope.row.taskStatus"
                >{{ item.title }}</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column label="操作" align="left" width="320" fixed="right">
            <template slot-scope="scope">
              <!-- <el-button
                v-if="regAdd.length == 1 || regAdd.length == 2"
                type="primary"
                @click="toCheck(scope.row._id)"
                plain
                size="small"
                >审核</el-button
              > -->
              <!-- <el-button
                v-if="regAdd.length > 1"
                type="primary"
                @click="toEdit(scope.row)"
                plain
                size="small"
                >编辑</el-button
              > -->
              <el-button
                v-if="regAdd.length > 1"
                type="primary"
                @click="toSurvey(scope.row)"
                plain
                size="small"
                >调查填报</el-button
              >
              <el-button
                type="primary"
                @click="toDetail(scope.row)"
                plain
                size="small"
                >查看详情</el-button
              >
              <el-button
                v-if="regAdd.length > 1"
                type="danger"
                @click="toDelete(scope.row._id)"
                plain
                size="small"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            @size-change="monitoringRecord"
            @current-change="monitoringRecord"
            :current-page.sync="pageInfo.curPage"
            :page-size.sync="pageInfo.pageSize"
            :page-sizes="[10, 20, 30, 50, 100]"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <el-dialog
      :title="recordTitle"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <el-form
        :model="formRecord"
        :rules="rules"
        ref="formRef"
        label-width="120px"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="监测任务" prop="taskId">
              <el-select
                v-model="formRecord.taskId"
                placeholder="请选择监测任务"
                filterable
                style="width: 100%"
                @change="taskChange"
              >
                <el-option
                  v-for="item in taskList"
                  :key="item._id"
                  :label="item.title"
                  :value="item._id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="委托单位">
              <el-select
                v-model="formRecord.jcOrgId"
                placeholder="请选择委托单位"
                clearable
                filterable
                style="width: 100%"
                :disabled="!formRecord.taskId"
              >
                <el-option
                  v-for="item in superUserList"
                  :key="item._id"
                  :label="item.cname"
                  :value="item._id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="用人单位" prop="EnterpriseID">
              <el-select
                v-model="formRecord.EnterpriseID"
                placeholder="请选择用人单位"
                filterable
                style="width: 100%"
                :disabled="!formRecord.taskId"
              >
                <el-option
                  v-for="item in EnterpriseList"
                  :key="item._id"
                  :label="item.cname"
                  :value="item._id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测机构" prop="serviceOrgId">
              <el-select
                v-model="formRecord.serviceOrgId"
                placeholder="请选择检测机构"
                filterable
                style="width: 100%"
                :disabled="!formRecord.taskId"
              >
                <el-option
                  v-for="item in serviceOrgList"
                  :key="item._id"
                  :label="item.name"
                  :value="item._id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { initEvent } from '@root/publicMethods/events';
import TitleTag from '@/components/TitleTag.vue';
import {
  getUserSession,
  monitoringRecord,
  getTaskManage,
  orgList,
  createMonitoringRecord,
  updateMonitoringRecord,
  deleteMonitoringRecord,
  economicType,
} from '@/api';
import moment from 'moment';
export default {
  components: {
    TitleTag,
  },
  data() {
    return {
      sidebarOpened: true,
      tableData: [],
      pageInfo: {
        curPage: 1,
        pageSize: 10,
      },
      total: null,
      searchForm: {
        taskId: '',
        taskStatus: [],
      },
      classificationIndustry: [],
      dialogVisible: false,
      formRecord: {
        taskId: '',
        jcOrgId: '',
        EnterpriseID: '',
        serviceOrgId: '',
      },
      rules: {
        taskId: [
          { required: true, message: '请选择监测任务', trigger: 'change' },
        ],
        EnterpriseID: [
          { required: true, message: '请选择用人单位', trigger: 'change' },
        ],
        serviceOrgId: [
          { required: true, message: '请选择检测机构', trigger: 'change' },
        ],
      },
      taskList: [],
      superUserList: [],
      serviceOrgList: [],
      EnterpriseList: [],
      statusList: [
        { id: 0, title: '暂存中', color: 'warning' },
        { id: 1, title: '待审核', color: 'primary' }, //市级
        { id: 2, title: '待审核', color: 'primary' }, //省级
        { id: 3, title: '审核通过', color: 'primary' },
        { id: 4, title: '审核不通过/驳回', color: 'danger' },
        { id: 5, title: '上报国家中', color: 'primary' },
        { id: 6, title: '已完成', color: 'success' },
      ],
      recordTitle: '创建监测记录',
      recordStatus: 0,
      regAdd: [],
    };
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: 'false',
        mobile: this.device === 'mobile',
      };
    },
    filteredStatusList() {
      // 如果是市级（regAdd.length > 1），过滤掉 id=2 的项（省级待审核）
      if (this.regAdd.length > 1) {
        return this.statusList.filter(item => item.id !== 2);
      }
      // 如果是省级（regAdd.length === 1），过滤掉 id=1 的项（市级待审核）
      else if (this.regAdd.length === 1) {
        return this.statusList.filter(item => item.id !== 1);
      }
      // 默认情况返回全部
      return this.statusList;
    },
  },
  mounted() {
    initEvent(this);
  },
  created() {
    // 获取省市
    this.getUserSession();
    this.monitoringRecord();
    // 获取监测任务
    this.gettaskList();
  },
  methods: {
    async getUserSession() {
      const res = await getUserSession();
      console.log(res.data.userInfo.regAdd, '---res-');
      if (res.status == 200) {
        this.regAdd = res.data.userInfo.regAdd;
      }
    },
    addSurveyFilling() {
      this.dialogVisible = true;
      this.recordTitle = '创建监测记录';
      this.recordStatus = 0;
    },
    taskChange() {
      // 清空已选的其他选项
      this.formRecord.jcOrgId = '';
      this.formRecord.EnterpriseID = '';
      this.formRecord.serviceOrgId = '';
      // 获取可选委托单位/检测机构/用人单位
      this.getorgList();
    },
    async gettaskList() {
      const res = await getTaskManage({ curPage: 1, pageSize: 10000 });
      this.taskList = res.data.list;
    },
    async getorgList() {
      const res = await orgList({ task_id: this.formRecord.taskId });
      if (res.status == 200) {
        this.EnterpriseList = res.data.EnterpriseList;
        this.serviceOrgList = res.data.serviceOrgList;
        this.superUserList = res.data.superUserList;
      } else {
        this.$message.error(res.message);
      }
    },
    submitForm() {
      this.$refs.formRef.validate(async valid => {
        if (valid) {
          if (this.recordStatus == 0) {
            let params = {
              taskId: this.formRecord.taskId,
              jcOrgId: this.formRecord.jcOrgId,
              EnterpriseID: this.formRecord.EnterpriseID,
              serviceOrgId: this.formRecord.serviceOrgId,
            };
            const res = await createMonitoringRecord(params);
            if (res.status == 200) {
              this.$message.success('创建成功');
              this.dialogVisible = false;
              this.monitoringRecord();
            } else {
              this.$message.error(res.message);
            }
          } else {
            let params = {
              _id: this.recordId,
              taskId: this.formRecord.taskId,
              jcOrgId: this.formRecord.jcOrgId,
              EnterpriseID: this.formRecord.EnterpriseID,
              serviceOrgId: this.formRecord.serviceOrgId,
            };
            const res = await updateMonitoringRecord(params);
            if (res.status == 200) {
              this.$message.success('编辑成功');
              this.dialogVisible = false;
              this.monitoringRecord();
            } else {
              this.$message.error(res.message);
            }
          }
        } else {
          this.$message.error('请填写完整表单');
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.formRef.resetFields();
    },
    handleClose() {
      this.dialogVisible = false;
      this.resetForm(); // 关闭时清空表单
    },
    resetForm() {
      this.$refs.formRef.resetFields(); // 重置表单验证
      this.formRecord = {
        taskId: '',
        jcOrgId: '',
        EnterpriseID: '',
        serviceOrgId: '',
      };
      // 清空关联的选项列表（可选）
      this.superUserList = [];
      this.EnterpriseList = [];
      this.serviceOrgList = [];
    },
    onSearch() {
      this.pageInfo.curPage = 1;
      this.monitoringRecord();
    },
    reset() {
      this.searchForm = this.$options.data()['searchForm'];
      this.pageInfo = this.$options.data()['pageInfo'];
      this.monitoringRecord();
    },
    toAddqyProtectEquAcceptanceRecord() {
      this.$router.push({
        name: 'addqyProtectEquAcceptanceRecord',
      });
    },
    async monitoringRecord() {
      let params = {
        ...this.pageInfo,
      };
      // 处理taskStatus
      if (this.searchForm.taskStatus && this.searchForm.taskStatus.length > 0) {
        params.taskStatus = this.searchForm.taskStatus.join(',');
      }

      // 处理taskId
      if (this.searchForm.taskId) {
        params.taskId = this.searchForm.taskId;
      }

      const res = await monitoringRecord(params);
      if (res.status === 200) {
        this.tableData = res.data.list;
        this.total = res.data.total;

        //         const batchData= JSON.stringify([
        //   {
        //     "code": "10150001",
        //     "content": "内资企业",
        //     "parentId": "0",
        //     "levelCode": "10150001"
        //   },
        //   {
        //     "code": "10150002",
        //     "content": "国有企业",
        //     "parentId": "10150001",
        //     "levelCode": "10150001.10150002"
        //   },
        //   {
        //     "code": "10150003",
        //     "content": "集体企业",
        //     "parentId": "10150001",
        //     "levelCode": "10150001.10150003"
        //   },
        //   {
        //     "code": "10150004",
        //     "content": "股份合作企业",
        //     "parentId": "10150001",
        //     "levelCode": "10150001.10150004"
        //   },
        //   {
        //     "code": "10150005",
        //     "content": "联营企业",
        //     "parentId": "10150001",
        //     "levelCode": "10150001.10150005"
        //   },
        //   {
        //     "code": "10150010",
        //     "content": "有限责任公司",
        //     "parentId": "10150001",
        //     "levelCode": "10150001.10150010"
        //   },
        //   {
        //     "code": "10150013",
        //     "content": "股份有限公司",
        //     "parentId": "10150001",
        //     "levelCode": "10150001.10150013"
        //   },
        //   {
        //     "code": "10150014",
        //     "content": "私营企业",
        //     "parentId": "10150001",
        //     "levelCode": "10150001.10150014"
        //   },
        //   {
        //     "code": "10150019",
        //     "content": "其他企业",
        //     "parentId": "10150001",
        //     "levelCode": "10150001.10150019"
        //   },
        //   {
        //     "code": "10150020",
        //     "content": "港、澳、台商投资企业",
        //     "parentId": "0",
        //     "levelCode": "10150020"
        //   },
        //   {
        //     "code": "10150021",
        //     "content": "合资经营企业（港或澳、台资）",
        //     "parentId": "10150020",
        //     "levelCode": "10150020.10150021"
        //   },
        //   {
        //     "code": "10150022",
        //     "content": "合作经营企业（港或澳、台资）",
        //     "parentId": "10150020",
        //     "levelCode": "10150020.10150022"
        //   },
        //   {
        //     "code": "10150023",
        //     "content": "港、澳、台商独资经营企业",
        //     "parentId": "10150020",
        //     "levelCode": "10150020.10150023"
        //   },
        //   {
        //     "code": "10150024",
        //     "content": "港、澳、台商投资股份有限公司",
        //     "parentId": "10150020",
        //     "levelCode": "10150020.10150024"
        //   },
        //   {
        //     "code": "10150025",
        //     "content": "其他港、澳、台商投资企业",
        //     "parentId": "10150020",
        //     "levelCode": "10150020.10150025"
        //   },
        //   {
        //     "code": "10150026",
        //     "content": "外商投资企业",
        //     "parentId": "0",
        //     "levelCode": "10150026"
        //   },
        //   {
        //     "code": "10150027",
        //     "content": "中外合资经营企业",
        //     "parentId": "10150026",
        //     "levelCode": "10150026.10150027"
        //   },
        //   {
        //     "code": "10150028",
        //     "content": "中外合作经营企业",
        //     "parentId": "10150026",
        //     "levelCode": "10150026.10150028"
        //   },
        //   {
        //     "code": "10150029",
        //     "content": "外资企业",
        //     "parentId": "10150026",
        //     "levelCode": "10150026.10150029"
        //   },
        //   {
        //     "code": "10150030",
        //     "content": "外商投资股份有限公司",
        //     "parentId": "10150026",
        //     "levelCode": "10150026.10150030"
        //   },
        //   {
        //     "code": "10150031",
        //     "content": "其他外商投资企业",
        //     "parentId": "10150026",
        //     "levelCode": "10150026.10150031"
        //   }
        // ], null, 2)
        //         const res1 = await economicType(JSON.parse(batchData));
        //         console.log(res1,'res100000')
      } else {
        this.$message.error(res.message);
      }
    },
    toDetail(row) {
      let type = 'fromReview';
      this.$router.push({
        name: 'reviewDetail',
        query: {
          id: row._id,
          type,
          taskId: row.taskId._id,
          provincialWorkspaceMonitoringId:
            row.taskId.provincialWorkspaceMonitoringId,
          jcOrgId: row.jcOrgId._id,
          serviceOrgId: row.serviceOrgId._id,
          EnterpriseID: row.EnterpriseID._id,
        },
      });
    },
    toEdit(row) {
      this.dialogVisible = true;
      this.recordTitle = '编辑监测记录';
      this.recordStatus = 1;
      console.log(row, 'row---');
      this.recordId = row._id;
      this.formRecord.taskId = row.taskId._id;
      this.formRecord.jcOrgId = row.jcOrgId._id;
      this.formRecord.EnterpriseID = row.EnterpriseID._id;
      this.formRecord.serviceOrgId = row.serviceOrgId._id;
      this.getorgList();
    },
    toSurvey(row) {
      this.$router.push({
        name: 'surveyFillingOutAdd',
        query: {
          id: row._id,
          taskId: row.taskId._id,
          provincialWorkspaceMonitoringId:
            row.taskId.provincialWorkspaceMonitoringId,
          jcOrgId: row.jcOrgId._id,
          serviceOrgId: row.serviceOrgId._id,
          EnterpriseID: row.EnterpriseID._id,
        },
      });
    },
    toCheck(id) {},
    toDelete(id) {
      this.$confirm('确认删除此项吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const res = await deleteMonitoringRecord({ _id: id });
          if (res.status == 200) {
            this.$message({
              type: 'success',
              message: '删除成功!',
            });
            this.monitoringRecord();
          } else {
            this.$message({
              type: 'error',
              message: res.message,
            });
          }
        })
        .catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // });
        });
    },
    // 处理诊疗类别
    handleZLType(data) {
      if (data && Array.isArray(data)) {
        let tempArry = [];
        data.forEach(item => {
          this.zlTypeOption.forEach(ele => {
            if (ele.value == item) {
              tempArry.push(ele.label);
            }
          });
        });
        return tempArry.join('/');
      }
    },
    // 处理时间
    handleTime(data) {
      return moment(data).format('YYYY-MM-DD');
    },
    // 处理项目类型
    handleProjectType(data) {
      let stringName = '';
      this.projectTypeOptions.forEach(item => {
        if (data == item.value) {
          stringName = item.label;
        }
      });
      return stringName;
    },
  },
  filters: {
    handletoPhone(val) {
      let reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
      let phone = val.replace(reg, '$1****$2');
      return phone;
    },
    handletoIdNumber(val) {
      const reg = /^(\d{6})\d{8}(\d{4})$/;
      return val.replace(reg, '$1********$2');
    },
  },
};
</script>

<style lang="scss" scoped>
.expert-container {
  padding: 20px 15px;
}
.data-container {
  padding: 10px 10px;
}
.header-container {
  min-width: 100%;
  position: relative;
}
.header-container-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background-color: #fff;
}
::v-deep .el-tag {
  border-radius: 28px;
}
.pagination {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
</style>
