<template>
  <div style="padding: 15px 20px">
    <div :class="classObj">
      <div class="main-container">
        <div>
          <div
            style="
              width: 100%;
              text-align: right;
              margin: 10px;
              display: flex;
              justify-content: space-between;
            "
          >
            <el-button size="small" @click="goBack" icon="el-icon-back"
              >返回</el-button
            >
          </div>
          <!-- 监测记录查询  v-if="typeName == 'recordQuery'" -->
          <div class="header-container" v-if="auditStatusFlag != 0">
            <TitleTag titleName="审核记录"></TitleTag>
            <el-row>
              <el-col :span="24">
                <el-table
                  :data="auditData"
                  tooltip-effect="light"
                  style="width: 100%"
                  stripe
                  border
                  header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
                  align="center"
                >
                  <el-table-column
                    prop="auditStatus"
                    label="审核结果"
                    align="center"
                    show-overflow-tooltip
                  >
                    <template slot-scope="scope">
                      <el-tag v-if="scope.row.auditStatus === 0" type="info">
                        待审核
                      </el-tag>

                      <el-tag
                        v-else-if="scope.row.auditStatus === 1"
                        type="success"
                      >
                        审核通过
                      </el-tag>

                      <el-tag
                        v-else-if="scope.row.auditStatus === 2"
                        type="danger"
                      >
                        审核不通过
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="updatedAt"
                    label="审核日期"
                    align="center"
                    show-overflow-tooltip
                  >
                    <template slot-scope="scope">
                      {{ handleTime(scope.row.updatedAt) }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="auditOrgId"
                    label="审核机构"
                    align="center"
                    show-overflow-tooltip
                  >
                    <template slot-scope="scope">
                      {{
                        scope.row.auditOrgId ? scope.row.auditOrgId.cname : ''
                      }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="auditor"
                    label="审核人"
                    align="center"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="auditOpinion"
                    label="审核意见"
                    align="center"
                    show-overflow-tooltip
                  ></el-table-column>
                </el-table>
              </el-col>
            </el-row>
          </div>
          <!-- 基本信息 -->
          <surveyBaseMess :factorOptions="factorOptions" :jobList="jobList"></surveyBaseMess>
          <!-- 监测情况 -->
          <surveyMonitor :factorOptions="factorOptions" :jobList="jobList"></surveyMonitor>
          <!-- 检查情况 -->
          <surveyCheck :factorOptions="factorOptions"></surveyCheck>
          <!-- 防护设施&用品 -->
          <surveySafe></surveySafe>
          <!-- 定性情况分析 -->
          <surveyQualitative :factorOptionsFather="factorOptions" :jobList="jobList"></surveyQualitative>
          <!-- 劳动者调查表 -->
          <surveyWorker :factorOptionsFather="factorOptions"
          :jobList="jobList"></surveyWorker>
          <!-- 检测结果  与调查填报使用同一个组件 -->
           <testingResult :fromDetail="true"></testingResult>
          <div
            class="header-container"
            v-if="auditStatusFlag != 0 && typeName != 'recordQuery'"
          >
            <TitleTag titleName="审核意见"></TitleTag>
            <el-input
              type="textarea"
              :rows="5"
              placeholder="若审核不通过，请填写"
              v-model="auditOpinion"
            >
            </el-input>
            <div style="margin-top: 20px">
              <el-button type="primary" plain @click="passAudit(1)"
                >审核通过</el-button
              >
              <el-button type="danger" plain @click="passAudit(2)"
                >审核不通过</el-button
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { initEvent } from '@root/publicMethods/events';
import moment from 'moment';
import TitleTag from '@/components/TitleTag.vue';
import surveyBaseMess from '@/components/surveyBaseMess.vue';
import surveyMonitor from '@/components/surveyMonitor.vue';
import surveyCheck from '@/components/surveyCheck.vue';
import surveySafe from '@/components/surveySafe.vue';
import surveyQualitative from '@/components/surveyQualitative.vue';
import surveyWorker from '@/components/surveyWorker.vue';
import testingResult from '@/components/testingResult.vue';

import { getFactorListNation, auditRecordList, auditRecordPut,getJobList } from '@/api';
import axios from 'axios';
export default {
  components: {
    TitleTag,
    surveyBaseMess,
    surveyMonitor,
    surveyCheck,
    surveySafe,
    surveyQualitative,
    surveyWorker,
    testingResult
  },
  data() {
    return {
      id: null,
      typeName: '',

      projectCategoryOptions: [],
      sidebarOpened: true,
      peopleData: [],
      baseInfo: {},
      workAdd: {
        lazy: true,
        lazyLoad(node, resolve) {
          if (node.level > 0) {
            node = node.data;
          }
          const url = '/api/address/list';
          axios({
            method: 'get',
            url: url,
            params: node,
          }).then((response) => {
            try {
              const districts = response.data.data;
              const a = districts[0];
              let nodes = districts.map((item) => ({
                value: item.name,
                label: item.name,
                id: item.id,
                area_code: item.area_code,
                parent_code: item.parent_code,
                lat: item.lat,
                lon: item.lng,
                merger_name: item.merger_name,
                short_name: item.short_name,
                leaf: item.level >= 3,
              }));

              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          });
        },
      },
      
      auditOpinion: '',
      // recordApplyTableFile:[]   // 建设项目职业病防护设施竣工验收(备案)申请书
      auditStatusFlag: 0,
      auditData: [],
      factorOptions:[],
      jobList:[]

    };
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: 'false',
        mobile: this.device === 'mobile',
      };
    },
  },
  async created() {
    this.id = this.$route.query.id;

    this.typeName = this.$route.query.type
      ? this.$route.query.type
      : '';
    // 获取审核列表
    this.auditList();
    // 获取危害因素
    this.fetchFactorList()
    // 岗位
    this.fetchJobList()
  },
  mounted() {
    initEvent(this);
  },
  methods: {
    handleTime(val) {
      return moment(val).format('YYYY-MM-DD hh:mm:ss');
    },
    async auditList() {
      // 先获取审核列表的id
      let pams = {
        pageNum: 1,
        pageSize: 10,
        recordId: this.id,
      };
      const res = await auditRecordList(pams);
      if (res.status == 200) {
        // 检查列表是否有数据
        if (res.data.list && res.data.list.length > 0) {
          this.auditData = res.data.list;
          // 获取最后一条数据（使用length - 1获取最后一个元素的索引）
          // const lastItem = res.data.list[res.data.list.length - 1];
          const lastItem = res.data.list[0];
          this.auditStatusFlag = 1;
          this.auditListId = lastItem._id;
        } else {
          // 没有数据 没有审核权限
          this.auditStatusFlag = 0;
        }
      }
    },
    // 审核
    async passAudit(auditStatus) {
      if (auditStatus == 2) {
        if (this.auditOpinion == '') {
          this.$message.error('审核不通过请填写原因！');
          return;
        }
      }

      let params = {
        _id: this.auditListId,
        auditStatus: auditStatus,
        auditOpinion: this.auditOpinion,
      };
      const resData = await auditRecordPut(params);
      if (resData.status == 200) {
        this.$message.success('提交成功！');
        this.goBack();
      } else {
        this.$message.error(resData.message);
      }
    },

    goBack() {
      this.$router.go(-1);
    },
    // 处理时间
    handleTime(data) {
      return data ? moment(data).format('YYYY-MM-DD') : '';
    },
    // 获取危害因素列表
    async fetchFactorList() {
      const res = await getFactorListNation();
      if (res.status === 200) {
        this.factorOptions = res.data;
      }

    },
    // 获取岗位列表
    async fetchJobList() {
      try {
        const res = await getJobList();
        if (res.status === 200) {
          this.jobList = res.data.list || [];
        }
      } catch (error) {
        console.error('获取岗位列表失败:', error);
        this.jobList = [];
      }
    },

  },
};
</script>

<style lang="scss" scoped>
.entry-container {
  padding: 10px 15px;
}

.header-container {
  width: 100%;
  position: relative;
}

.header-container-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background-color: #fff;
}

.table-container {
  table-layout: fixed;
  background: #fff;
  width: 100%;
  margin: 0 auto;
  border-collapse: collapse;

  td {
    padding: 5px 0;
  }

  th {
    font-weight: normal;
    border: 1px solid #e5e5e5;
    padding: 3px 0;
  }

  tr {
    border: 1px solid #e5e5e5;
    width: 100%;
    font-size: 14px;
  }

  .left-tittle {
    background: #f5f7fa;
  }

  .center-tittle {
    background: #ecf5ff;
  }

  .table-label {
    text-align: right;
    padding-right: 5px;
    color: #000;
    font-size: 14px;
    height: 42px;
  }

  .th-input {
    text-align: center;
    padding: 2px 4px;

    span {
      color: #333;
    }
  }

  .th-radio {
    text-align: center;
    padding-left: 6px;
    padding-right: 6px;
  }

  .input-width {
    width: 200px;
    background-color: bisque;
  }

  .health-check-th {
    padding: 11px 28px;
    text-align: center;
  }
}

.upload-container {
  display: flex;
  padding: 15px 0px;
}

.doc-container {
  padding: 5px 1px;
  cursor: pointer;
  color: #409eff;
}
.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}
</style>
