<template>
  <div class="expert-container">
    <div :class="classObj">
      <div class="main-container">
        <TitleTag titleName="查询条件"></TitleTag>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item label="单位名称">
            <el-input
              v-model="searchForm.projectName"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="行业分类">
            <el-select v-model="searchForm.projectType" placeholder="请选择">
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="(item, index) in projectNature"
              ></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="填报日期">
            <el-date-picker
              v-model="searchForm.projectType"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        
          <el-form-item label="审核状态">
            <el-checkbox-group v-model="searchForm.checkList">
              <el-checkbox label="0">暂存中</el-checkbox>
              <el-checkbox label="0">待审核</el-checkbox>
              <el-checkbox label="1">已审核</el-checkbox>
              <el-checkbox label="2">已驳回</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
          <el-form-item>
            <el-button @click="addSurveyFilling" icon="el-icon-plus">添加</el-button>
          </el-form-item>
        </el-form>

        <div class="header-container">
          <TitleTag
            titleName="建设项目职业病防护设施验收方案备案列表"
          ></TitleTag>
          <!-- <div class="header-container-btn">
              <el-button icon="el-icon-plus" type="primary" plain size="small" @click="toAddqyProtectEquAcceptanceRecord">新增项目备案</el-button>
            </div> -->
        </div>
        <el-table
          :data="tableData"
          tooltip-effect="light"
          style="min-width: 100%"
          stripe
          border
          header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
          align="center"
        >
          <el-table-column
            type="index"
            align="center"
            show-overflow-tooltip
            width="80"
            label="序号"
          ></el-table-column>
          <el-table-column
            prop="projectName"
            label="项目名称"
            align="center"
            show-overflow-tooltip
            min-width="160"
          ></el-table-column>
          <el-table-column
            prop="projectName"
            label="建设项目地址"
            align="center"
            show-overflow-tooltip
            min-width="160"
          >
            <template slot-scope="scope">
              {{
                scope.row.projectAddress.join('') +
                scope.row.projectDetailAddress
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="cuLegalPerson"
            label="建设单位法人"
            align="center"
            show-overflow-tooltip
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="createdAt"
            label="申请日期"
            align="center"
            min-width="120"
          >
            <template slot-scope="scope">
              {{ handleTime(scope.row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="projectType"
            label="项目性质"
            align="center"
            min-width="110"
          >
            <template slot-scope="scope">
              {{ handleProjectType(scope.row.projectType) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="recordNumber"
            label="备案编号"
            align="center"
            min-width="200"
          ></el-table-column>
          <el-table-column
            prop="agreeStatus"
            label="状态"
            align="center"
            show-overflow-tooltip
            min-width="110"
          >
            <template slot-scope="scope">
              <el-tag
                type="primary"
                plain
                size="small"
                v-show="scope.row.status == 1"
                >已备案</el-tag
              >
              <el-tag
                type="success"
                plain
                size="small"
                v-show="scope.row.status == 2"
                >已暂存</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column label="操作" align="left" width="200" fixed="right">
            <template slot-scope="scope">
              <el-button
                type="primary"
                @click="toDetail(scope.row._id)"
                plain
                size="small"
                >暂存</el-button
              >
              <el-button
                type="primary"
                @click="toDetail(scope.row._id)"
                plain
                size="small"
                >提交</el-button
              >
              <el-button
                type="danger"
                @click="toDetail(scope.row._id)"
                plain
                size="small"
                >删除</el-button
              >
              <!-- <el-button type="success" @click="deleteApply(scope.row._id)" plain size="small" v-show="scope.row.status == 1">下载告知书</el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            @size-change="getContructproEquRecordList"
            @current-change="getContructproEquRecordList"
            :current-page.sync="pageInfo.pageNum"
            :page-size.sync="pageInfo.pageSize"
            :page-sizes="[10, 20, 30, 50, 100]"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { initEvent } from '@root/publicMethods/events';
import TitleTag from '@/components/TitleTag.vue';
import { getContructproEquRecordList } from '@/api';
import moment from 'moment';
export default {
  components: {
    TitleTag,
  },
  data() {
    return {
      sidebarOpened: true,
      tableData: [],
      pageInfo: {
        pageNum: 1,
        pageSize: 10,
      },
      total: null,
      projectTypeOptions: [
        { value: 1, label: '新建' },
        { value: 2, label: '改建' },
        { value: 3, label: '扩建' },
        { value: 4, label: '技术改造' },
        { value: 5, label: '技术引进' },
      ],
      zlTypeOption: [
        { value: 1, label: '放射治疗' },
        { value: 2, label: '核医学' },
        { value: 3, label: '介入放射学' },
        { value: 4, label: 'X射线影像诊断' },
      ],
      searchForm: {
        projectName: '',
        projectType: null,
      },
      applyDate: null,
    };
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: 'false',
        mobile: this.device === 'mobile',
      };
    },
  },
  mounted() {
    initEvent(this);
  },
  created() {
    this.getContructproEquRecordList();
  },
  methods: {

    onSearch() {
      this.pageInfo = this.$options.data()['pageInfo'];
      this.getContructproEquRecordList();
    },
    reset() {
      this.applyDate = this.$options.data()['applyDate'];
      this.searchForm = this.$options.data()['pageInfo'];
      this.pageInfo = this.$options.data()['pageInfo'];
      this.getContructproEquRecordList();
    },
    toAddqyProtectEquAcceptanceRecord() {
      this.$router.push({
        name: 'addqyProtectEquAcceptanceRecord',
      });
    },
    async getContructproEquRecordList() {
      let params = {
        ...this.searchForm,
        ...this.pageInfo,
      };
      if (this.applyDate) {
        params.startDate = this.applyDate[0];
        params.endDate = this.applyDate[1];
      }
      const res = await getContructproEquRecordList(params);
      if (res.status === 200) {
        this.tableData = res.data.data;
        this.total = res.data.total;
      }
    },
    addSurveyFilling(){
      this.$router.push({
        name: 'surveyFillingOutAdd',
        // query: {
        //   id,
        // },
      });
    },
    // toDetail(id) {
    //   this.$router.push({
    //     name: 'workplaceDetail',
    //     query: {
    //       id,
    //     },
    //   });
    // },
    // 处理诊疗类别
    handleZLType(data) {
      if (data && Array.isArray(data)) {
        let tempArry = [];
        data.forEach((item) => {
          this.zlTypeOption.forEach((ele) => {
            if (ele.value == item) {
              tempArry.push(ele.label);
            }
          });
        });
        return tempArry.join('/');
      }
    },
    // 处理时间
    handleTime(data) {
      return moment(data).format('YYYY-MM-DD');
    },
    // 处理项目类型
    handleProjectType(data) {
      let stringName = '';
      this.projectTypeOptions.forEach((item) => {
        if (data == item.value) {
          stringName = item.label;
        }
      });
      return stringName;
    },
  },
  filters: {
    handletoPhone(val) {
      let reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
      let phone = val.replace(reg, '$1****$2');
      return phone;
    },
    handletoIdNumber(val) {
      const reg = /^(\d{6})\d{8}(\d{4})$/;
      return val.replace(reg, '$1********$2');
    },
  },
};
</script>

<style lang="scss" scoped>
.expert-container {
  padding: 20px 15px;
}
.data-container {
  padding: 10px 10px;
}
.header-container {
  min-width: 100%;
  position: relative;
}
.header-container-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background-color: #fff;
}
::v-deep .el-tag {
  border-radius: 28px;
}
.pagination {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
</style>
