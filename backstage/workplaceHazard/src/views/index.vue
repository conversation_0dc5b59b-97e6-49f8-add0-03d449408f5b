<template>
  <div class="expert-container">
    <div :class="classObj">
      <div class="main-container">
        <TitleTag titleName="查询条件"></TitleTag>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item label="任务名称">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入任务名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="年份">
            <el-date-picker
              v-model="searchForm.year"
              type="year"
              value-format="yyyy"
              format="yyyy"
              placeholder="选择年"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="状态">
            <el-radio-group v-model="searchForm.taskStatus">
              <el-radio label="">全部</el-radio>
              <el-radio label="0">待发布</el-radio>
              <el-radio label="1">已发布</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-form>

        <div class="header-container">
          <TitleTag titleName="监测任务管理"></TitleTag>
          <!-- <div class="header-container-btn">
              <el-button icon="el-icon-plus" type="primary" plain size="small" @click="toAddqyProtectEquAcceptanceRecord">新增项目备案</el-button>
            </div> -->
        </div>
        <el-table
          :data="tableData"
          tooltip-effect="light"
          style="min-width: 100%"
          stripe
          border
          header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
          align="center"
        >
          <el-table-column
            type="index"
            align="center"
            show-overflow-tooltip
            width="80"
            label="序号"
          >
            <template slot-scope="scope">
              {{
                (pageInfo.curPage - 1) * pageInfo.pageSize + scope.$index + 1
              }}
            </template></el-table-column
          >
          <el-table-column
            prop="title"
            label="任务名称"
            align="center"
            show-overflow-tooltip
            min-width="160"
          ></el-table-column>

          <el-table-column
            prop="year"
            label="年份"
            align="center"
            show-overflow-tooltip
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="taskNum"
            label="任务数"
            align="center"
            show-overflow-tooltip
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="startTime"
            label="开始时间"
            align="center"
            min-width="120"
          >
            <template slot-scope="scope">
              {{ handleTime(scope.row.startTime) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="endTime"
            label="结束时间"
            align="center"
            min-width="120"
          >
            <template slot-scope="scope">
              {{ handleTime(scope.row.endTime) }}
            </template>
          </el-table-column>

          <el-table-column
            prop="agreeStatus"
            label="状态"
            align="center"
            show-overflow-tooltip
            min-width="110"
          >
            <template slot-scope="scope">
              <el-tag
                type="primary"
                plain
                size="small"
                v-show="scope.row.taskStatus == 0"
                >待发布</el-tag
              >
              <el-tag
                type="success"
                plain
                size="small"
                v-show="scope.row.taskStatus == 1"
                >已发布</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column label="操作" align="left" width="160" fixed="right">
            <template slot-scope="scope">
              <el-button
                type="primary"
                @click="toDetail(scope.row._id, 'edit', scope.row.title)"
                plain
                size="small"
                v-show="scope.row.taskStatus == 0"
                >编辑</el-button
              >
              <el-button
                type="primary"
                @click="toDetail(scope.row._id, 'view', scope.row.title)"
                plain
                size="small"
                v-show="scope.row.taskStatus == 1"
                >查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="pageInfo.curPage"
            :page-sizes="[10, 20, 30, 50, 100]"
            :page-size.sync="pageInfo.pageSize"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { initEvent } from '@root/publicMethods/events';
import TitleTag from '@/components/TitleTag.vue';
import { getTaskManage } from '@/api';
import moment from 'moment';
export default {
  components: {
    TitleTag,
  },
  data() {
    return {
      sidebarOpened: true,
      tableData: [],
      pageInfo: {
        curPage: 1,
        pageSize: 10,
      },
      total: null,
      searchForm: {
        keyword: '',
        year: null,
        taskStatus: '',
      },
    };
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: 'false',
        mobile: this.device === 'mobile',
      };
    },
  },
  mounted() {
    initEvent(this);
  },
  created() {
    this.getTaskManage();
  },
  methods: {
    // 分页大小变化
    handleSizeChange(val) {
      this.pageInfo.pageSize = val;
      this.pageInfo.curPage = 1;
      this.getTaskManage();
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.pageInfo.curPage = val;
      this.getTaskManage();
    },
    onSearch() {
      this.pageInfo = this.$options.data()['pageInfo'];
      this.getTaskManage();
    },
    reset() {
      this.searchForm = {
        keyword: '',
        year: null,
        taskStatus: '',
      };

      this.getTaskManage();
    },
    toAddqyProtectEquAcceptanceRecord() {
      this.$router.push({
        name: 'addqyProtectEquAcceptanceRecord',
      });
    },
    async getTaskManage() {
      let params = {
        ...this.searchForm,
        curPage: this.pageInfo.curPage,
        pageSize: this.pageInfo.pageSize,
      };

      const res = await getTaskManage(params);
      if (res.status === 200) {
        this.tableData = res.data.list;
        this.total = res.data.pageInfo.total;
        // 将返回的pageNum映射到本地的curPage
        this.pageInfo.curPage = res.data.pageInfo.pageNum;
        this.pageInfo.pageSize = res.data.pageInfo.pageSize;
      }
    },
    toDetail(id, type, title) {
      this.$router.push({
        name: 'workplaceDetail',
        query: {
          id,
          type,
          title
        },
      });
    },
    // 处理时间
    handleTime(data) {
      return moment(data).format('YYYY-MM-DD');
    },
  },
  filters: {
    handletoPhone(val) {
      let reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
      let phone = val.replace(reg, '$1****$2');
      return phone;
    },
    handletoIdNumber(val) {
      const reg = /^(\d{6})\d{8}(\d{4})$/;
      return val.replace(reg, '$1********$2');
    },
  },
};
</script>

<style lang="scss" scoped>
.expert-container {
  padding: 20px 15px;
}
.data-container {
  padding: 10px 10px;
}
.header-container {
  min-width: 100%;
  position: relative;
}
.header-container-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background-color: #fff;
}
::v-deep .el-tag {
  border-radius: 28px;
}
.pagination {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
</style>
