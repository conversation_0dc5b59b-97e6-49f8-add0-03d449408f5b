<template>
  <div style="padding: 15px 20px">
    <div :class="classObj">
      <div class="main-container">
        <div>
          <div
            style="
              width: 100%;
              text-align: right;
              margin: 10px;
              display: flex;
              justify-content: space-between;
            "
          >
            <el-button size="small" @click="goBack" icon="el-icon-back"
              >返回</el-button
            >
            <!-- <span>
                        <el-button type="warning" size="small" @click="tempSave">暂存</el-button>
                        <el-button type="success" size="small" @click="downloadApplyTable" icon="el-icon-download">下载申请表</el-button>
                        <el-button type="primary" size="small" @click="submitRecord">提交备案</el-button>
                    </span> -->
          </div>
          <div class="surveyoutAdd">
            <el-steps :active="activeAdd" align-center>
              <el-step
                v-for="(step, index) in steps"
                :key="index"
                :title="step.title"
                @click.native="handleStepClick(index)"
              >
                >
              </el-step>
            </el-steps>
          </div>
          <div v-if="activeAdd == 0">
            <!-- 基本信息 -->
            <firstMessage @nextStepInfo="nextStepMain"></firstMessage>
          </div>
          <div v-if="activeAdd == 1">
            <!-- 监测情况 -->
            <monitorMessage @nextStepInfo="nextStepMain"></monitorMessage>
          </div>
          <div v-if="activeAdd == 2">
            <!-- 检查情况/体检情况 -->
            <checkSituation @nextStepInfo="nextStepMain"></checkSituation>
          </div>
          <div v-if="activeAdd == 3">
            <!-- 防护设施&用品 -->
            <safeguardMessage @nextStepInfo="nextStepMain"></safeguardMessage>
          </div>
          <div v-if="activeAdd == 4">
            <!-- 定性情况分析 -->
            <qualitativeAnalysis @nextStepInfo="nextStepMain"></qualitativeAnalysis>
          </div>
          <div v-if="activeAdd == 5">
            <!-- 劳动者调查表 -->
            <workerMessage @nextStepInfo="nextStepMain"
            ></workerMessage>
          </div>
          <div v-if="activeAdd == 6">
            <!-- 检测结果 -->
            <testingResult></testingResult>
          </div>
          <!-- <el-button type="primary" @click="nextStep" v-show="activeAdd < 6"
            >下一步</el-button
          >
          <el-button>暂存</el-button> -->

          <el-button type="primary" v-show="activeAdd == 7">提交</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { initEvent } from '@root/publicMethods/events';
import moment from 'moment';
import TitleTag from '@/components/TitleTag.vue';
import firstMessage from '@/components/firstMessage.vue';
import workerMessage from '@/components/workerMessage.vue';
import monitorMessage from '@/components/monitorMessage.vue';
import safeguardMessage from '@/components/safeguardMessage.vue';
import checkSituation from '@/components/checkSituation.vue';
import qualitativeAnalysis from '@/components/qualitativeAnalysis.vue';
import testingResult from '@/components/testingResult.vue';


import {} from '@/api';
import axios from 'axios';
export default {
  components: {
    TitleTag,
    firstMessage,
    workerMessage,
    monitorMessage,
    safeguardMessage,
    checkSituation,
    qualitativeAnalysis,
    testingResult
  },
  data() {
    return {
      id: null,
      projectCategoryOptions: [],
      sidebarOpened: true,
      peopleData: [],
      baseInfo: {},
      activeAdd: 0, // 当前激活的步骤
      // steps: [
      //   { title: '基本信息' },
      //   { title: '劳动者调查' },
      //   { title: '上一年度调查信息' },
      //   { title: '防护设施&用品&警示标识' },
      //   { title: '步骤5' },
      // ],
      steps: [
        { title: '基本信息' },
        { title: '监测情况' },
        { title: '检查情况' },
        { title: '防护设施&用品' },
        { title: '定性情况分析' },
        { title: '劳动者调查表' },
        { title: '检测结果' },
      ],
      // formData: { checkList: [] },
      // rules: {
      //   name: [{ required: true, message: '请输入', trigger: 'blur' }],
      // },
      formData2: {},
      rules2: {
        name: [{ required: true, message: '请输入', trigger: 'blur' }],
      },

      projectNature: [],
    };
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: 'false',
        mobile: this.device === 'mobile',
      };
    },
  },
  async created() {
    // this.id = this.$route.query.id;
  },
  mounted() {
    initEvent(this);
  },
  methods: {
    nextStepMain(val) {
      this.activeAdd = val;
    },
    nextStep() {
      this.activeAdd = this.activeAdd + 1;
    },
    handleStepClick(index) {
      console.log('点击了步骤:', index + 1, this.steps[index].title);
      this.activeAdd = index; // 切换到点击的步骤
      // 这里可以添加其他逻辑，比如跳转或加载对应步骤的内容
    },
    goBack() {
      this.$router.go(-1);
    },
    // 处理时间
    handleTime(data) {
      return data ? moment(data).format('YYYY-MM-DD') : '';
    },
  },
};
</script>

<style lang="scss" scoped>
.entry-container {
  padding: 10px 15px;
}

.header-container {
  width: 100%;
  position: relative;
}

.header-container-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background-color: #fff;
}

.table-container {
  table-layout: fixed;
  background: #fff;
  width: 100%;
  margin: 0 auto;
  border-collapse: collapse;

  td {
    padding: 5px 0;
  }

  th {
    font-weight: normal;
    border: 1px solid #e5e5e5;
    padding: 3px 0;
  }

  tr {
    border: 1px solid #e5e5e5;
    width: 100%;
    font-size: 14px;
  }

  .left-tittle {
    background: #ecf5ff;
  }

  .center-tittle {
    background: #ecf5ff;
  }

  .table-label {
    text-align: right;
    padding-right: 5px;
    color: #000;
    font-size: 14px;
    height: 42px;
  }

  .th-input {
    text-align: center;
    padding: 2px 4px;

    span {
      color: #333;
    }
  }

  .th-radio {
    text-align: center;
    padding-left: 6px;
    padding-right: 6px;
  }

  .input-width {
    width: 200px;
    background-color: bisque;
  }

  .health-check-th {
    padding: 11px 28px;
    text-align: center;
  }
}

.upload-container {
  display: flex;
  padding: 15px 0px;
}

.doc-container {
  padding: 5px 1px;
  cursor: pointer;
  color: #409eff;
}
.titleNext {
  font-size: 14px;
  width: 100%;
  font-weight: bold;
  color: rgb(26, 26, 26);
  margin: 20px 0;
  margin-left: 20px;
}
</style>
