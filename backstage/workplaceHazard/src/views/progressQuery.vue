<template>
  <div class="expert-container">
    <div :class="classObj">
      <div class="main-container">
        <TitleTag titleName="查询条件"></TitleTag>
        <div style="display: flex; justify-content: space-between">
          <div>
            <el-form
              :inline="true"
              :model="searchForm"
              class="demo-form-inline"
            >
              <el-form-item label="所属地区" v-if="regAdd.length == 1">
                <el-select v-model="searchForm.areaName" placeholder="请选择">
                  <el-option
                    :label="item.name"
                    :value="item.name"
                    v-for="(item, index) in areaList"
                    :key="index"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="年份">
                <el-date-picker
                  v-model="searchForm.year"
                  type="year"
                  value-format="yyyy"
                  format="yyyy"
                  placeholder="选择年"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="onSearch">查询</el-button>
              </el-form-item>
              <el-form-item>
                <el-button @click="reset">重置</el-button>
              </el-form-item>
              <el-form-item>
                <el-button @click="handleExport" :loading="exportLoading">
                  导出
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <div class="header-container">
          <TitleTag titleName="监测进度查询"></TitleTag>
        </div>
        <el-table
          :data="tableData"
          tooltip-effect="light"
          style="min-width: 100%"
          stripe
          border
          header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
          align="center"
        >
          <el-table-column
            type="index"
            align="center"
            show-overflow-tooltip
            width="80"
            label="序号"
          >
            <template slot-scope="scope">
              {{
                (pageInfo.pageNum - 1) * pageInfo.pageSize + scope.$index + 1
              }}
            </template></el-table-column
          >
          <el-table-column
            prop="areaName"
            label="地区"
            align="center"
            show-overflow-tooltip
            width="120"
          ></el-table-column>
          <el-table-column
            prop="taskNum"
            label="任务数"
            align="center"
            show-overflow-tooltip
            width="80"
          >
          </el-table-column>
          <el-table-column
            prop="submitCount"
            label="提交数"
            align="center"
            show-overflow-tooltip
            width="80"
          ></el-table-column>
          <el-table-column
            prop="draftCount"
            label="暂存数"
            align="center"
            width="80"
          >
          </el-table-column>
          <el-table-column
            v-if="regAdd.length == 1"
            prop="cityReviewPending"
            label="待审核数量"
            align="center"
          >
          </el-table-column>
          <el-table-column
            v-if="regAdd.length > 1"
            prop="reviewPending"
            label="待审核数量"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="cityReviewCount"
            label="市级审核数"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="cityCompletionRate"
            label="市级完成率（%）"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column
            v-if="regAdd.length == 1"
            prop="provincialReviewCount"
            label="省级审核数"
            align="center"
            width="100"
          ></el-table-column>
          <el-table-column
            v-if="regAdd.length == 1"
            prop="provincialCompletionRate"
            label="省级完成率（%）"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column
            v-if="regAdd.length == 1"
            prop="keyIndustryCount"
            label="完成重点行业企业数"
            align="center"
            min-width="120"

          ></el-table-column>
          <el-table-column
            v-if="regAdd.length == 1"
            prop="keyIndustryRate"
            label="重点行业企业占比（%）"
            align="center"
            min-width="120"

          ></el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            @size-change="handlePageChange"
            @current-change="handlePageChange"
            :current-page.sync="pageInfo.pageNum"
            :page-size.sync="pageInfo.pageSize"
            :page-sizes="[10, 20, 30, 50, 100]"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { initEvent } from '@root/publicMethods/events';
import TitleTag from '@/components/TitleTag.vue';
import { monitorProgress, getUserSession, jurisdiction } from '@/api';
import moment from 'moment';
import XLSX from 'xlsx'; // 仅保留xlsx库

export default {
  components: {
    TitleTag,
  },
  data() {
    return {
      sidebarOpened: true,
      tableData: [],
      pageInfo: {
        pageNum: 1,
        pageSize: 10,
      },
      total: null,
      searchForm: {
        year: new Date().getFullYear().toString(),
        areaName: null,
      },
      applyDate: null,
      regAdd: [],
      areaList: [],
      exportLoading: false, // 导出加载状态
    };
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: 'false',
        mobile: this.device === 'mobile',
      };
    },
  },
  mounted() {
    initEvent(this);
  },
  created() {
    this.getUserSession();
    this.monitorProgress();
    this.jurisdiction();
  },
  methods: {
    async jurisdiction() {
      const res = await jurisdiction();
      if (res.status == 200) {
        this.areaList = res.data;
      }
    },
    async getUserSession() {
      const res = await getUserSession();
      if (res.status == 200) {
        this.regAdd = res.data.userInfo.regAdd;
      }
    },
    onSearch() {
      this.pageInfo.pageNum = 1;
      this.monitorProgress();
    },
    reset() {
      this.applyDate = null;
      this.searchForm = {
        year: new Date().getFullYear().toString(),
        areaName: null,
      };
      this.pageInfo = {
        pageNum: 1,
        pageSize: 10,
      };
      this.monitorProgress();
    },
    handlePageChange() {
      this.monitorProgress();
    },
    async monitorProgress() {
      let params = {
        ...this.searchForm,
        ...this.pageInfo,
      };
      if (this.applyDate) {
        params.startDate = this.applyDate[0];
        params.endDate = this.applyDate[1];
      }
      const res = await monitorProgress(params);
      if (res.status === 200) {
        this.tableData = res.data.list;
        this.total = res.data.total;
      }
    },
    // 导出Excel功能 - 不依赖FileSaver库
    async handleExport() {
      this.exportLoading = true;
      try {
        // 1. 准备查询参数，获取最多2000条数据
        const exportParams = {
          ...this.searchForm,
          pageSize: 2000,
          pageNum: 1,
        };

        // 2. 调用接口获取数据
        const res = await monitorProgress(exportParams);
        if (res.status !== 200) {
          this.$message.error('获取导出数据失败');
          return;
        }

        const exportData = res.data.list;
        if (exportData.length === 0) {
          this.$message.warning('没有可导出的数据');
          return;
        }

        // 3. 处理导出数据格式
        const formattedData = exportData.map((item, index) => {
          const baseData = {
            序号: index + 1,
            地区: item.areaName || '',
            任务数: item.taskNum || 0,
            提交数: item.submitCount || 0,
            暂存数: item.draftCount || 0,
            市级审核数: item.cityReviewCount || 0,
            '市级完成率（%）': item.cityCompletionRate || '0.00%',
          };

          // 省级用户特有字段
          if (this.regAdd.length === 1) {
            return {
              ...baseData,
              待审核数量: item.cityReviewPending || 0,
              省级审核数: item.provincialReviewCount || 0,
              '省级完成率（%）': item.provincialCompletionRate || '0.00%',
              完成重点行业企业数: item.keyIndustryCount || 0,
              '重点行业企业占比（%）': item.keyIndustryRate || '0.00%',
            };
          }
          // 市/区级用户特有字段
          else {
            return {
              ...baseData,
              待审核数量: item.reviewPending || 0,
            };
          }
        });

        // 4. 创建工作簿和工作表
        const worksheet = XLSX.utils.json_to_sheet(formattedData);

        // 5. 调整列宽
        const wscols = Object.keys(formattedData[0]).map((key) => ({
          wch: key.length * 2 + 4,
        }));
        worksheet['!cols'] = wscols;

        // 6. 创建工作簿并添加工作表
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, '监测进度数据');

        // 7. 生成Excel文件并下载（不使用FileSaver）
        const excelBuffer = XLSX.write(workbook, {
          bookType: 'xlsx',
          type: 'array',
        });
        const blob = new Blob([excelBuffer], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });

        // 使用浏览器原生API创建下载链接
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;

        // 文件名
        const fileName = `监测进度_${
          this.searchForm.year || new Date().getFullYear()
        }_${this.searchForm.areaName || '全部'}_${Date.now()}.xlsx`;
        a.download = fileName;

        // 触发下载
        document.body.appendChild(a);
        a.click();

        // 清理
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.$message.success('导出成功');
      } catch (error) {
        console.error('导出失败:', error);
        this.$message.error('导出失败，请重试');
      } finally {
        this.exportLoading = false;
      }
    },
    toDetail(id) {
      this.$router.push({
        name: 'workplaceDetail',
        query: { id },
      });
    },
    handleZLType(data) {
      if (data && Array.isArray(data)) {
        let tempArry = [];
        data.forEach((item) => {
          this.zlTypeOption.forEach((ele) => {
            if (ele.value == item) {
              tempArry.push(ele.label);
            }
          });
        });
        return tempArry.join('/');
      }
    },
    handleTime(data) {
      return moment(data).format('YYYY-MM-DD');
    },
    handleProjectType(data) {
      let stringName = '';
      this.projectTypeOptions.forEach((item) => {
        if (data == item.value) {
          stringName = item.label;
        }
      });
      return stringName;
    },
  },
  filters: {
    handletoPhone(val) {
      let reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
      let phone = val.replace(reg, '$1****$2');
      return phone;
    },
    handletoIdNumber(val) {
      const reg = /^(\d{6})\d{8}(\d{4})$/;
      return val.replace(reg, '$1********$2');
    },
  },
};
</script>

<style lang="scss" scoped>
.expert-container {
  padding: 20px 15px;
}
.data-container {
  padding: 10px 10px;
}
.header-container {
  min-width: 100%;
  position: relative;
  margin-bottom: 15px;
}
.header-container-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background-color: #fff;
}
::v-deep .el-tag {
  border-radius: 28px;
}
.pagination {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
.export-btn {
  margin-left: 10px;
}
</style>
