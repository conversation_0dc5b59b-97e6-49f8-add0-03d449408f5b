<template>
  <div class="expert-container">
    <div :class="classObj">
      <div class="main-container">
        <TitleTag titleName="查询条件"></TitleTag>
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item label="调查年份">
            <el-date-picker
              v-model="searchForm.year"
              type="year"
              value-format="yyyy"
              format="yyyy"
              placeholder="选择年"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="单位名称">
            <el-input
              v-model="searchForm.projectName"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="地区">
            <el-select v-model="searchForm.projectType" placeholder="请选择">
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="(item, index) in projectNature"
              ></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="行业选择">
            <el-cascader
              v-model="searchForm.industryCode"
              :options="industryClass"
              :props="{
                ...cascaderProps, // 合并原有配置
                // multiple: true, // 启用多选
                emitPath: true, // 必须设置为true，返回完整路径
              }"
              clearable
              placeholder="请选择行业分类"
              style="width: 100%"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="岗位/环节">
            <el-autocomplete
              v-model="searchForm.jobNameDisplay"
              :fetch-suggestions="querySearchAsync"
              placeholder="请输入搜索岗位名称"
              clearable
              style="width: 100%"
              @select="handleSelect"
              value-key="jobName"
            >
              <template slot-scope="{ item }">
                <div>
                  {{ item.jobName }}
                  <!-- ({{ item._id }}) -->
                  <!-- ({{ item.jobNo }}) -->
                </div>
              </template>
            </el-autocomplete>
          </el-form-item>
          <el-form-item label="企业规模">
            <el-select
              v-model="searchForm.enterpriseScale"
              placeholder="请选择"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="(item, index) in scaleList"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="调查时间">
            <el-date-picker
              v-model="searchForm.projectType"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item> -->
          <!-- <el-form-item label="接触危害因素">
            <el-select
              v-model="searchForm.factorId"
              placeholder="请选择"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="item in filteredFactorOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
                <template v-if="item.name.length > 30">
                  <el-tooltip :content="item.name" placement="top">
                    <span>{{ item.name.substring(0, 30) + '...' }}</span>
                  </el-tooltip>
                </template>
                <template v-else>
                  <span>{{ item.name }}</span>
                </template>
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="经济类型">
            <el-select
              v-model="searchForm.economicNo"
              placeholder="请选择经济类型"
              clearable
              style="width: 100%"
            >
              <el-option-group
                v-for="group in categoryGroups"
                :key="group.parentId"
                :label="group.parentContent"
              >
                <el-option
                  v-for="item in group.children"
                  :key="item.code"
                  :label="item.content"
                  :value="item.code"
                ></el-option>
              </el-option-group>
            </el-select>
            <!-- 特殊存值 -->
            <!-- <el-select
              v-model="selectedCode"
              placeholder="请选择经济类型"
              clearable
              style="width: 100%"
            >
              <el-option-group
                v-for="group in categoryGroups"
                :key="group.parentId"
                :label="group.parentContent"
              >
                <el-option
                  v-for="item in group.children"
                  :key="item.code"
                  :label="item.content"
                  :value="item.code"
                  :data-parent="item.parentId"
                ></el-option>
              </el-option-group>
            </el-select> -->
          </el-form-item>
          <el-form-item label="审核时间">
            <el-date-picker
              v-model="searchForm.auditTime"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="changeTme"
            ></el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="检测危害因素">
            <el-select v-model="searchForm.projectType" placeholder="请选择">
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="(item, index) in projectNature"
              ></el-option>
            </el-select>
          </el-form-item> -->
          <!-- <el-form-item label="检测项目">
            <el-select v-model="searchForm.projectType" placeholder="请选择">
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="(item, index) in projectNature"
              ></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="申报情况">
            <el-select
              v-model="searchForm.ifDeclare"
              placeholder="请选择"
              clearable
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="(item, index) in ifDeclareList"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="年度更新情况">
            <el-select v-model="searchForm.ifAnnualUpdate" placeholder="请选择">
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="(item, index) in ifAnnualUpdateList"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="报告类型">
            <el-checkbox-group v-model="searchForm.checkList">
              <el-checkbox label="0">常规报告</el-checkbox>
              <el-checkbox label="1">典型报告</el-checkbox>
            </el-checkbox-group>
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleExport" :loading="exportLoading"
              >导出</el-button
            >
          </el-form-item>
        </el-form>

        <div class="header-container">
          <TitleTag titleName="监测记录查询"></TitleTag>
        </div>
        <el-table
          :data="tableData"
          tooltip-effect="light"
          style="min-width: 100%"
          stripe
          border
          header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
          align="center"
        >
          <el-table-column
            type="index"
            align="center"
            show-overflow-tooltip
            width="80"
            label="序号"
          >
            <template slot-scope="scope">
              {{
                (pageInfo.pageNum - 1) * pageInfo.pageSize + scope.$index + 1
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="unitName"
            label="单位名称"
            align="center"
            show-overflow-tooltip
            min-width="120"
          >
            <template slot-scope="scope">
              {{ scope.row.EnterpriseID.cname }}
            </template>
          </el-table-column>
          <el-table-column
            prop="taskAreaName"
            label="所属地区"
            align="center"
            show-overflow-tooltip
            min-width="120"
          >
          </el-table-column>
          <el-table-column
            prop="workAddr"
            label="场所地址"
            align="center"
            show-overflow-tooltip
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="industryCode"
            label="行业"
            align="center"
            min-width="120"
            show-overflow-tooltip
            :formatter="formatIndustryCode"
          >
          </el-table-column>
          <el-table-column
            prop="projectType"
            label="经济类型"
            align="center"
            min-width="110"
            show-overflow-tooltip
            :formatter="formatEconomicNo"
          >
          </el-table-column>
          <el-table-column
            prop="enterpriseScale"
            label="规模"
            align="center"
            :formatter="formatEnterpriseScale"
          >
          </el-table-column>
          <el-table-column
            prop="ifDeclare"
            label="申报情况"
            align="center"
            :formatter="formatIfDeclare"
          ></el-table-column>
          <el-table-column
            prop="ifAnnualUpdate"
            label="年度更新情况"
            align="center"
            :formatter="formatIfAnnualUpdate"
          ></el-table-column>
          <!-- <el-table-column
            prop="recordNumber"
            label="调查时间"
            align="center"
            min-width="200"
          >
            <template slot-scope="scope">
              {{ handleTime(scope.row.createdAt) }}
            </template>
          </el-table-column> -->
          <el-table-column
            prop="auditTime"
            label="审核时间"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <!-- <el-table-column
            prop="recordNumber"
            label="填报及时性"
            align="center"
            min-width="200"
          ></el-table-column> -->

          <el-table-column label="操作" align="left" width="120" fixed="right">
            <template slot-scope="scope">
              <el-button
                type="primary"
                @click="toDetail(scope.row)"
                plain
                size="small"
                :disabled="scope.row.ifDeclare == null"
                >查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            @size-change="getContructproEquRecordList"
            @current-change="getContructproEquRecordList"
            :current-page.sync="pageInfo.pageNum"
            :page-size.sync="pageInfo.pageSize"
            :page-sizes="[10, 20, 30, 50, 100]"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { initEvent } from '@root/publicMethods/events';
import TitleTag from '@/components/TitleTag.vue';
import {
  monitorRecordStatistics,
  getContructproEquRecordList,
  getIndustryCategory,
  getJobList,
  getFactorListNation,
  getEconomicCategory,
} from '@/api';
import XLSX from 'xlsx';
import moment from 'moment';
import { debounce } from 'lodash';

export default {
  components: {
    TitleTag,
  },
  data() {
    return {
      sidebarOpened: true,
      tableData: [],
      pageInfo: {
        pageNum: 1,
        pageSize: 10,
      },
      total: null,

      searchForm: {
        year: null, // 调查年份
        projectName: '', // 单位名称（对应后端unitName）
        industryCode: null, // 行业选择（级联选择值）
        jobNameDisplay: '', // 岗位名称（显示用）
        jobNo: '', // 岗位编码（实际查询用）
        enterpriseScale: null, // 企业规模
        economicNo: '', // 经济类型代码
        ifDeclare: null, // 申报情况
        ifAnnualUpdate: null, // 年度更新情况
        auditTimeStart: null, // 审核时间开始
        auditTimeEnd: null, // 审核时间结束
        taskStatus: '', // 任务状态（可选）
      },
      applyDate: null,
      projectNature: [
        { label: '贵州省', value: '002' },
        { label: '浙江省', value: '003' },
      ],
      industryClass: [],
      industryData: [],
      cascaderProps: {
        value: 'value', // 指定选项的 value 字段
        label: 'label', // 指定选项的 label 字段
        children: 'children', // 指定选项的子级字段
        checkStrictly: true, // 可选：是否允许选择任意一级（默认false，只能选最后一级）
        emitPath: false, // 可选：是否返回完整路径（默认true）
      },
      jobList: [], // 存储从后端获取的岗位列表
      scaleList: [
        { label: '大', value: 1 },
        { label: '中', value: 2 },
        { label: '小', value: 3 },
        { label: '微', value: 4 },
      ],
      filteredFactorOptions: [], // 过滤后的选项
      categoryGroups: [],
      selectedCode: '',
      ifDeclareList: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      ifAnnualUpdateList: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      economicMap: {}, // 存储 { 代码: 名称 } 的映射关系
    };
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: 'false',
        mobile: this.device === 'mobile',
      };
    },
  },
  mounted() {
    initEvent(this);
  },
  created() {
    this.getContructproEquRecordList();
    // 获取行业分类
    this.getIndustryCategory();
    // 危害因素
    this.fetchFactorList();
    // 获取经济类型
    this.getEconomicCategory();
  },
  methods: {
    // 导出方法
    // handleExport: function () {

    // },
    // 前端导出主方法
    async handleExport() {
      this.exportLoading = true;
      try {
        // 1. 一次性获取大量数据（2000条）
        const largeData = await this.fetchLargeData();
        if (!largeData || largeData.length === 0) {
          this.$message.warning('暂无符合条件的数据可导出');
          this.exportLoading = false;
          return;
        }

        // 2. 处理数据格式，转换为Excel所需的结构
        const excelData = this.processExportData(largeData);

        // 3. 创建工作簿并添加数据
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.json_to_sheet(excelData);

        // 4. 调整列宽
        const wscols = [
          { wch: 25 }, // 单位名称
          { wch: 20 }, // 所属地区
          { wch: 25 }, // 场所地址
          { wch: 20 }, // 行业
          { wch: 15 }, // 经济类型
          { wch: 10 }, // 企业规模
          { wch: 10 }, // 申报情况
          { wch: 12 }, // 年度更新情况
          { wch: 15 }, // 任务状态
          { wch: 20 }, // 审核时间
          { wch: 20 }, // 委托单位
          { wch: 20 }, // 检测机构
        ];
        worksheet['!cols'] = wscols;

        // 5. 添加工作表到工作簿
        XLSX.utils.book_append_sheet(workbook, worksheet, '监测记录统计');

        // 6. 生成并下载文件
        const fileName = `监测记录统计_${moment().format('YYYYMMDD')}.xlsx`;
        XLSX.writeFile(workbook, fileName);

        // 7. 提示成功
        // this.$message.success('文件导出成功');
      } catch (error) {
        console.error('前端导出失败:', error);
        this.$message.error('文件导出失败，请重试');
      } finally {
        this.exportLoading = false;
      }
    },

    // 一次性获取大量数据
    async fetchLargeData() {
      // 构建查询参数（使用当前的查询条件）
      const params = {
        // 不分页，一次性获取2000条
        curPage: 1,
        pageSize: 2000,

        // 复用当前的查询条件
        year: this.searchForm.year,
        unitName: this.searchForm.projectName,
        industryCode: this.searchForm.industryCode
          ? Array.isArray(this.searchForm.industryCode)
            ? this.searchForm.industryCode[
                this.searchForm.industryCode.length - 1
              ]
            : this.searchForm.industryCode
          : null,
        keyJob: this.searchForm.jobNo || null,
        enterpriseScale: this.searchForm.enterpriseScale,
        economicNo: this.searchForm.economicNo,
        ifDeclare: this.searchForm.ifDeclare,
        ifAnnualUpdate: this.searchForm.ifAnnualUpdate,
        auditTimeStart: this.searchForm.auditTimeStart,
        auditTimeEnd: this.searchForm.auditTimeEnd,
        taskStatus: this.searchForm.taskStatus,
      };

      try {
        const res = await monitorRecordStatistics(params);
        if (res.status === 200 && res.data && res.data.list) {
          console.log(`成功获取 ${res.data.list.length} 条数据用于导出`);
          return res.data.list;
        }
        return [];
      } catch (error) {
        console.error('获取导出数据失败:', error);
        throw new Error('获取数据失败，请检查网络');
      }
    },

    // 处理数据格式，转换为Excel需要的结构
    processExportData(rawData) {
      // 状态映射表
      const taskStatusMap = {
        0: '暂存中',
        1: '待审核（市级）',
        2: '待审核（省级）',
        3: '审核通过',
        4: '审核不通过',
        5: '上报国家',
        6: '已完成',
      };

      const scaleMap = { 1: '大', 2: '中', 3: '小', 4: '微' };

      return rawData.map(
        function (item) {
          // 处理单位名称（替换可选链）
          const unitName =
            (item.EnterpriseID && item.EnterpriseID.cname) || '-';

          // 处理所属地区（替换可选链）
          let taskArea = item.taskAreaName || '-';
          if (Array.isArray(taskArea)) {
            taskArea = taskArea.join('/');
          }

          // 处理行业
          const industryText = this.formatIndustryCode(item) || '-';

          // 处理经济类型
          const economicText = this.formatEconomicNo(item) || '-';

          // 处理企业规模
          const scaleText =
            item.enterpriseScale !== undefined
              ? scaleMap[item.enterpriseScale] || '-'
              : '-';

          // 处理申报情况
          const declareText =
            item.ifDeclare === true
              ? '是'
              : item.ifDeclare === false
              ? '否'
              : '-';

          // 处理年度更新情况
          const updateText =
            item.ifAnnualUpdate === true
              ? '是'
              : item.ifAnnualUpdate === false
              ? '否'
              : '-';

          // 处理任务状态
          const statusText = taskStatusMap[item.taskStatus] || '-';

          // 处理审核时间
          const auditTimeText = item.auditTime
            ? moment(item.auditTime).format('YYYY-MM-DD HH:mm')
            : '-';

          // 处理委托单位（替换可选链）
          const jcOrgCname = (item.jcOrgId && item.jcOrgId.cname) || '-';

          // 处理检测机构（替换可选链）
          const serviceOrgName =
            (item.serviceOrgId && item.serviceOrgId.name) || '-';

          return {
            单位名称: unitName,
            所属地区: taskArea,
            场所地址: item.workAddr || '-',
            行业: industryText,
            经济类型: economicText,
            企业规模: scaleText,
            申报情况: declareText,
            年度更新情况: updateText,
            任务状态: statusText,
            审核时间: auditTimeText,
            委托单位: jcOrgCname,
            检测机构: serviceOrgName,
          };
        }.bind(this)
      ); // 注意绑定this上下文
    },
    changeTme(val) {
      this.searchForm.auditTimeStart = val[0];
      this.searchForm.auditTimeEnd = val[1];
    },
    // 格式化申报情况（true/false 转 是/否）
    formatIfDeclare(row) {
      const { ifDeclare } = row;
      // 处理 null/undefined 情况
      if (ifDeclare === null || ifDeclare === undefined) {
        return '-';
      }
      // 从 ifDeclareList 中匹配对应的标签
      const item = this.ifDeclareList.find(item => item.value === ifDeclare);
      return item ? item.label : '-';
    },

    // 格式化年度更新情况（true/false 转 是/否）
    formatIfAnnualUpdate(row) {
      const { ifAnnualUpdate } = row;
      // 处理 null/undefined 情况
      if (ifAnnualUpdate === null || ifAnnualUpdate === undefined) {
        return '-';
      }
      // 从 ifAnnualUpdateList 中匹配对应的标签
      const item = this.ifAnnualUpdateList.find(
        item => item.value === ifAnnualUpdate
      );
      return item ? item.label : '-';
    },
    // 格式化企业规模（数值转汉字）
    formatEnterpriseScale(row) {
      const { enterpriseScale } = row;

      // 处理 null/undefined 或无效值
      if (enterpriseScale === null || enterpriseScale === undefined) {
        return '-';
      }

      // 在 scaleList 中查找匹配的 label
      const scaleItem = this.scaleList.find(
        item => item.value === enterpriseScale
      );

      // 找到则返回 label，否则返回原始值或 '-'
      return scaleItem ? scaleItem.label : '-';
    },
    // 优化递归查找逻辑，确保深层子节点被正确遍历
    findIndustryLabel(code, data) {
      // 1. 基础校验：数据必须是有效数组
      if (!Array.isArray(data) || data.length === 0) {
        return code;
      }

      // 2. 定义递归函数（使用闭包确保深层遍历）
      const search = nodes => {
        for (const node of nodes) {
          // 跳过无效节点
          if (!node || typeof node !== 'object' || !node.value) continue;

          // 3. 匹配当前节点
          if (node.value === code) {
            return node.label;
          }

          // 4. 递归查找子节点（如果存在）
          if (Array.isArray(node.children) && node.children.length > 0) {
            const childResult = search(node.children);
            // 子节点找到结果后立即返回，不再继续当前层级循环
            if (childResult !== code) {
              return childResult;
            }
          }
        }
        // 本层级及子层级均未找到，返回原始代码
        return code;
      };

      // 启动递归查找
      return search(data);
    },

    // 格式化行业代码为标签
    formatIndustryCode(row) {
      const { industryCode } = row;
      // 处理industryCode为null的情况
      if (industryCode === null) {
        return '-';
      }
      // 处理非数组的情况（如字符串或undefined）
      if (!Array.isArray(industryCode) || industryCode.length === 0) {
        return '-';
      }

      // 遍历每个代码，转换为标签
      const labels = industryCode.map(code => {
        // 确保code是字符串类型（避免数字与字符串不匹配）
        const strCode = String(code);
        // 从完整行业数据中查找
        return this.findIndustryLabel(strCode, this.industryData);
      });

      return labels.join(' / ');
    },
    async getIndustryCategory() {
      const res = await getIndustryCategory();
      if (res.status == 200) {
        this.industryClass = res.data;
        this.industryData = Array.isArray(res.data) ? res.data : [];
        // 打印数据，检查是否包含"C"、"13"等代码
        console.log('行业数据:', this.industryData);
        // 检查是否有value为"C"的根节点
        const cItem = this.industryData.find(item => item.value === 'C');
        console.log('是否有C对应的根节点:', cItem); // 如果为undefined，说明缺少数据
      }
    },
    // 远程搜索方法
    querySearchAsync: debounce(function (queryString, cb) {
      if (!queryString) {
        cb([]);
        return;
      }

      this.fetchJobList(queryString).then(() => {
        cb(this.jobList);
      });
    }, 500),
    // 选择选项时的处理
    handleSelect(item) {
      this.searchForm.jobNo = item._id; // 存储编码
      this.searchForm.jobNameDisplay = item.jobName; // 显示名称
      // console.log(this.searchForm.jobNo, 'this.searchForm.jobNo0000');
    },
    // 获取岗位列表
    async fetchJobList(keyword) {
      try {
        const params = { keyword };
        const res = await getJobList(params);
        if (res.status === 200) {
          this.jobList = res.data.list || [];
        }
      } catch (error) {
        console.error('获取岗位列表失败:', error);
        this.jobList = [];
      }
    },
    async fetchFactorList() {
      try {
        const res = await getFactorListNation();

        // 扁平化处理
        this.factorOptions = res.data.flatMap(category => {
          return category.children.map(child => ({
            category: category._id,
            id: child._id,
            name: child.chineseName.join('、'),
            aliases: child.chineseName.slice(1),
          }));
        });

        // 初始化时显示所有选项
        this.filteredFactorOptions = [...this.factorOptions];
      } catch (error) {
        console.error('获取监测因素列表失败:', error);
        this.$message.error('获取监测因素列表失败');
      }
    },
    // 经济类型
    async getEconomicCategory() {
      const res = await getEconomicCategory();
      if (res.status == 200) {
        this.rawData = res.data.list;
        this.processData();

        // 构建经济类型代码-名称映射（关键步骤）
        this.economicMap = this.rawData.reduce((map, item) => {
          // 存储 code -> content 的映射
          map[item.code] = item.content;
          return map;
        }, {});

        console.log('经济类型映射:', this.economicMap); // 打印映射关系，便于调试
      }
    },
    processData() {
      // 按父级分组
      const groups = {};
      const parentMap = {};

      // 先找出所有一级分类
      this.rawData.forEach(item => {
        if (item.parentId === '0') {
          parentMap[item.code] = item.content;
        }
      });

      // 构建分组
      this.rawData.forEach(item => {
        if (item.parentId !== '0') {
          if (!groups[item.parentId]) {
            groups[item.parentId] = {
              parentId: item.parentId,
              parentContent: parentMap[item.parentId] || '未知分类',
              children: [],
            };
          }
          groups[item.parentId].children.push(item);
        }
      });

      this.categoryGroups = Object.values(groups);
    },
    // 格式化经济类型（支持单代码或数组）
    formatEconomicNo(row) {
      const { economicNo } = row;

      // 处理 null/undefined 情况
      if (!economicNo) {
        return '-';
      }

      // 处理数组形式（如 ["10150001", "10150002"]）
      if (Array.isArray(economicNo)) {
        // 过滤无效代码，映射为名称
        const labels = economicNo
          .filter(code => this.economicMap[code] !== undefined) // 排除未匹配的代码
          .map(code => this.economicMap[code]);

        return labels.length > 0 ? labels.join(' / ') : '-';
      }

      // 处理单个代码（如 "10150001"）
      return this.economicMap[economicNo] || '-';
    },
    onSearch() {
      this.pageInfo.pageNum = 1; // 重置为第一页
      this.getContructproEquRecordList();
    },
    reset() {
      this.searchForm = {
        year: null,
        projectName: '',
        industryCode: null,
        jobNameDisplay: '',
        jobNo: '',
        enterpriseScale: null,
        economicNo: '',
        ifDeclare: null,
        ifAnnualUpdate: null,
        auditTimeStart: null,
        auditTimeEnd: null,
        taskStatus: '',
      };
      this.pageInfo = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getContructproEquRecordList();
    },
    toAddqyProtectEquAcceptanceRecord() {
      this.$router.push({
        name: 'addqyProtectEquAcceptanceRecord',
      });
    },
    async getContructproEquRecordList() {
      let params = {
        // 分页参数
        curPage: this.pageInfo.pageNum,
        pageSize: this.pageInfo.pageSize,

        // 年份
        year: this.searchForm.year,

        // 单位名称（对应后端unitName）
        unitName: this.searchForm.projectName,

        // 行业选择（处理级联选择的路径，取最后一级code）
        industryCode: this.searchForm.industryCode
          ? Array.isArray(this.searchForm.industryCode)
            ? this.searchForm.industryCode[
                this.searchForm.industryCode.length - 1
              ]
            : this.searchForm.industryCode
          : null,

        // 岗位/环节（对应后端keyJob，这里用jobNo关联）
        keyJob: this.searchForm.jobNo || null, // 传递岗位编码

        // 企业规模
        enterpriseScale: this.searchForm.enterpriseScale,

        // 经济类型（对应后端economicNo）
        economicNo: this.searchForm.economicNo,

        // 申报情况
        ifDeclare: this.searchForm.ifDeclare,

        // 年度更新情况
        ifAnnualUpdate: this.searchForm.ifAnnualUpdate,

        // 审核时间范围
        auditTimeStart: this.searchForm.auditTimeStart,
        auditTimeEnd: this.searchForm.auditTimeEnd,

        // 任务状态（可选）
        taskStatus: this.searchForm.taskStatus,
      };

      const res = await monitorRecordStatistics(params);
      if (res.status === 200) {
        this.tableData = res.data.list;
        this.total = res.data.total;
      }
    },
    toDetail(row) {
      let type = 'recordQuery';
      this.$router.push({
        name: 'reviewDetail',
        query: {
          id: row._id,
          type,
          taskId: row.taskId._id,
          provincialWorkspaceMonitoringId:
            row.taskId.provincialWorkspaceMonitoringId,
          jcOrgId: row.jcOrgId._id,
          serviceOrgId: row.serviceOrgId._id,
          EnterpriseID: row.EnterpriseID._id,
        },
      });
    },
    // 处理诊疗类别
    handleZLType(data) {
      if (data && Array.isArray(data)) {
        let tempArry = [];
        data.forEach(item => {
          this.zlTypeOption.forEach(ele => {
            if (ele.value == item) {
              tempArry.push(ele.label);
            }
          });
        });
        return tempArry.join('/');
      }
    },
    // 处理时间
    handleTime(data) {
      return moment(data).format('YYYY-MM-DD');
    },
    // 处理项目类型
    handleProjectType(data) {
      let stringName = '';
      this.projectTypeOptions.forEach(item => {
        if (data == item.value) {
          stringName = item.label;
        }
      });
      return stringName;
    },
  },
  filters: {
    handletoPhone(val) {
      let reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
      let phone = val.replace(reg, '$1****$2');
      return phone;
    },
    handletoIdNumber(val) {
      const reg = /^(\d{6})\d{8}(\d{4})$/;
      return val.replace(reg, '$1********$2');
    },
  },
};
</script>

<style lang="scss" scoped>
.expert-container {
  padding: 20px 15px;
}
.data-container {
  padding: 10px 10px;
}
.header-container {
  min-width: 100%;
  position: relative;
}
.header-container-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background-color: #fff;
}
::v-deep .el-tag {
  border-radius: 28px;
}
.pagination {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}
</style>
