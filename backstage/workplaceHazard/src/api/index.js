import request from '@root/publicMethods/request';

export function getContructproEquRecordList(params) {
  return request({
    url: '/manage/jgContructProjectManage/getContructproEquRecordList',
    params,
    method: 'get',
  });
}

export function getContructproEquRecordDetail(params) {
  return request({
    url: '/manage/jgContructProjectManage/getContructproEquRecordDetail',
    params,
    method: 'get',
  });
}

export function getDictionaryByKey(params) {
  return request({
    url: '/api/adminorgGov/getDictionaryByKey',
    params,
    method: 'get',
  });
}

// 获取监测任务管理
export function getTaskManage(params) {
  return request({
    url: '/manage/workspace/taskManage',
    params,
    method: 'get',
  });
}

// 获取任务详情
export function taskDetail(params) {
  return request({
    url: '/manage/workspace/taskDetail',
    params,
    method: 'get',
  });
}

// 获取可选的自定义危害因素列表
export function factorList(params) {
  return request({
    url: '/manage/workspace/factorList',
    params,
    method: 'get',
  });
}
// 国家 自定义危害因素列表（添加cnCsjcCode  国家-场所监测危害因素编码）
export function getFactorListNation(params) {
  return request({
    url: '/manage/workspace/getFactorListNation',
    params,
    method: 'get',
  });
}
// 省级单位发布任务
export const taskDetailPublish = data => {
  return request({
    url: '/manage/workspace/taskDetail',
    method: 'post',
    data,
  });
};

// 获取各个单位企业
export function getList(params) {
  return request({
    url: '/manage/adminorgGov/getList',
    params,
    method: 'get',
  });
}

//  市级单位发布任务（选择企业名单）
export const taskDetailCityPublish = data => {
  return request({
    url: '/manage/workspace/taskDetail',
    data,
    method: 'put',
  });
};
// 监测记录列表
export function monitoringRecord(params) {
  return request({
    url: '/manage/workspace/monitoringRecord',
    params,
    method: 'get',
  });
}
// 创建监测记录
export const createMonitoringRecord = data => {
  return request({
    url: '/manage/workspace/monitoringRecord',
    method: 'post',
    data,
  });
};
//  更新监测记录
export const updateMonitoringRecord = data => {
  return request({
    url: '/manage/workspace/monitoringRecord',
    data,
    method: 'put',
  });
};
// 删除监测记录
export function deleteMonitoringRecord(data) {
  return request({
    url: '/manage/workspace/monitoringRecord',
    data,
    method: 'delete',
  });
}

// 获取可选委托单位/检测机构/用人单位
export function orgList(params) {
  return request({
    url: '/manage/workspace/orgList',
    params,
    method: 'get',
  });
}

// 获取登录状态（判断当前是省是市）
export function getUserSession() {
  return request({
    url: '/manage/getUserSession',
    method: 'get',
  });
}

// 获取企业详情
export function getOneAdminorg(params) {
  return request({
    url: '/manage/adminorgGov/getCompany',
    params,
    method: 'get',
  });
}

// 所属行业
export function getIndustryCategory() {
  return request({
    method: 'get',
    url: '/api/adminorgGov/getIndustryCategory',
  });
}

// 获取经济类型字典
export function getEconomicTypes(params) {
  return request({
    url: '/api/adminorgGov/hbEconomicTypes',
    method: 'get',
    params,
  });
}
// 创建经济类型
export const economicType = data => {
  return request({
    url: '/manage/workspace/economicType',
    method: 'post',
    data,
  });
};
// 获取经济类型列表
export function getEconomicCategory(params) {
  return request({
    url: '/manage/workspace/getEconomicCategory',
    method: 'get',
    params,
  });
}
// 获取岗位编码列表
export function getJobList(params) {
  return request({
    url: '/manage/workspace/getJobList',
    method: 'get',
    params,
  });
}
// 创建基本信息
export const baseInfo = data => {
  return request({
    url: '/manage/workspace/baseInfo',
    method: 'post',
    data,
  });
};
// 获取基本信息详情
export function getBaseInfo(params) {
  return request({
    url: '/manage/workspace/getBaseInfo',
    method: 'get',
    params,
  });
}
// 新增/编辑监测情况+定性分析
export const monitoringAnalyse = data => {
  return request({
    url: '/manage/workspace/monitoringAnalyse',
    method: 'post',
    data,
  });
};
// 获取监测情况+定性分析详情
export function getMonitoringAnalyseDetail(params) {
  return request({
    url: '/manage/workspace/getMonitoringAnalyseDetail',
    method: 'get',
    params,
  });
}
// 新增/编辑检查情况
export const checkSituation = data => {
  return request({
    url: '/manage/workspace/checkSituation',
    method: 'post',
    data,
  });
};
// 获取新增/编辑检查情况详情
export function getCheckSituationDetail(params) {
  return request({
    url: '/manage/workspace/getCheckSituationDetail',
    method: 'get',
    params,
  });
}
// 新增/编辑防护设施&用品
export const safeguard = data => {
  return request({
    url: '/manage/workspace/safeguard',
    method: 'post',
    data,
  });
};
// 获取防护设施&用品详情
export function getSafeguardDetail(params) {
  return request({
    url: '/manage/workspace/getSafeguardDetail',
    method: 'get',
    params,
  });
}
// 新增/编辑劳动者调查表
export const workerForm = data => {
  return request({
    url: '/manage/workspace/workerForm',
    method: 'post',
    data,
  });
};
// 获取劳动者调查表详情
export function getWorkerFormDetail(params) {
  return request({
    url: '/manage/workspace/getWorkerFormDetail',
    method: 'get',
    params,
  });
}

// 提交审核
export const auditRecord = data => {
  return request({
    url: '/manage/workspace/auditRecord',
    method: 'post',
    data,
  });
};
// 审核列表
export function auditRecordList(params) {
  return request({
    url: '/manage/workspace/auditRecord',
    method: 'get',
    params,
  });
}
// 审核
export const auditRecordPut = data => {
  return request({
    url: '/manage/workspace/auditRecord',
    data,
    method: 'put',
  });
};
// 检测结果
export function getMonitoringResult(params) {
  return request({
    url: '/api/jcqlcProject/getMonitoringResult',
    method: 'get',
    params,
  });
}
// 获取监测记录查询
export function monitorRecordStatistics(params) {
  return request({
    url: '/manage/workspace/monitorRecordStatistics',
    method: 'get',
    params,
  });
}
// 获取监测进度列表
export function monitorProgress(params) {
  return request({
    url: '/manage/workspace/monitorProgress',
    method: 'get',
    params,
  });
}

// 获取辖下区域
export function jurisdiction(params) {
  return request({
    url: '/manage/workspace/jurisdiction',
    method: 'get',
    params,
  });
}
// 获取劳动者调查表预填写信息
export function previewWorker(params) {
  return request({
    url: '/manage/workspace/previewWorker',
    method: 'get',
    params,
  });
}
// 体检因素预填写
export function previewPhysicalHazard(params) {
  return request({
    url: '/manage/workspace/previewPhysicalHazard',
    method: 'get',
    params,
  });
}

// 体检总结报告预填写
export function previewCheckSum(params) {
  return request({
    url: '/manage/workspace/previewCheckSum',
    method: 'get',
    params,
  });
}
// 检测结果
export function monitoringResult(params) {
  return request({
    url: '/manage/workspace/monitoringResult',
    method: 'get',
    params,
  });
}
