<template>
  <div>
    <el-row>
      <el-col :span="24">
        <table style="width:100%;border:1px solid #ddd;" class="table-container">
          <tr>
            <th class="left-tittle table-label" colspan="4">
              机构名称
            </th>
            <th colspan="8" class="th-input">
              <span>{{ basicInfo.name }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              统一社会信用代码
            </th>
            <th colspan="8" class="th-input">
              <span>{{ basicInfo.creditCode }}</span>
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4">
              法人代表
            </th>
            <th colspan="8" class="th-input">
              <span>{{ basicInfo.legalPerson }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              邮箱
            </th>
            <th colspan="8" class="th-input">
              <span>{{ basicInfo.email }}</span>
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4">
              联系人
            </th>
            <th colspan="8" class="th-input">
              <span>{{ basicInfo.contactPerson }}</span>
            </th>
            <th class="left-tittle table-label" colspan="4">
              联系电话
            </th>
            <th colspan="8" class="th-input">
              <span>{{ basicInfo.contactPhone }}</span>
            </th>
          </tr>
          <tr>
            <th class="left-tittle table-label" colspan="4">
              所在区域
            </th>
            <th colspan="8" class="th-input">
              <ElCascader
                disabled
                style="width: 100%"
                v-model="basicInfo.zonecode"
                placeholder=""
                clearable
                :options="areaList"
                :props="{
                  label: 'dictLabel',
                  value: 'dictCode',
                  children: 'children',
                  checkStrictly: true,
                  emitPath: false
                }"
                filterable
              />
            </th>
            <th class="left-tittle table-label" colspan="4">
              单位地址
            </th>
            <th colspan="8" class="th-input">
              <span>{{ basicInfo.address }}</span>
            </th>
          </tr>
        </table>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import {getOrgDetail ,getDictDataByType} from '@/api/index.js'
export default {
  data() {
    return {
      basicInfo: {},
      areaList:[],
    }
  },
  created() {
    this.getOrgDetail()
    getDictDataByType({dictType:'area'}).then(res=>{
      this.areaList = res.data
    })
  },
  methods: {
    async getOrgDetail() {
      try {
        const res = await getOrgDetail({id:this.$route.query.orgId})
        this.basicInfo = res.data && res.data.base || {};
      } catch (error) {
        console.log("获取机构详情失败");
      }
    }
  },

};
</script>
<style scoped>
.table-container {
  table-layout: fixed;
  background: #fff;
  width: 100%;
  margin: 0 auto;
  border-collapse: collapse;

  td {
      padding: 5px 0;
  }

  th {
      font-weight: normal;
      border: 1px solid #e5e5e5;
      padding: 3px 0;
  }

  tr {
      border: 1px solid #e5e5e5;
      width: 100%;
      font-size: 14px;
  }

  .left-tittle {
      background: #ECF5FF;
  }

  .center-tittle{
      background: #ECF5FF;
  }

  .table-label{
      text-align: right;
      padding-right: 5px;
      color:#000;
      font-size: 14px;
      height:42px;
  }

  .th-input{
      text-align: center;
      padding: 2px 4px;
      span{
      color:#333;
      }
  }

  .th-radio{
      text-align: center;
      padding-left: 6px;
      padding-right: 6px;
  }

  .input-width{
      width: 200px;
      background-color: bisque;
  }

  .health-check-th{
      padding: 11px 28px;
      text-align: center;
  }

  ::v-deep .el-input.is-disabled .el-input__inner {
    background-color: #F5F7FA;
    border-color: #E4E7ED;
    color: black!important;
    cursor: not-allowed;
  }
}


</style>