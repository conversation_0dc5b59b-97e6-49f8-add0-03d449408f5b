{"name": "superuser", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --port 8886", "build": "vue-cli-service build"}, "dependencies": {"axios": "^0.19.0", "core-js": "^2.6.5", "crypto-js": "^3.1.9-1", "echarts": "^5.0.2", "element-ui": "^2.11.1", "fuse.js": "^3.4.5", "js-cookie": "^2.2.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "screenfull": "^4.2.1", "single-spa-vue": "^1.5.4", "validator": "^11.1.0", "vue": "^2.6.10", "vue-i18n": "^8.14.0", "vue-router": "^3.0.3", "vuex": "^3.1.1", "wangeditor": "^4.6.2"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "^3.9.0", "@vue/cli-service": "^3.9.0", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "runjs": "^4.3.2", "sass": "^1.90.0", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "uglifyjs-webpack-plugin": "^2.2.0", "vue-cli-plugin-single-spa": "^1.0.0-alpha.1", "vue-template-compiler": "^2.6.10"}}