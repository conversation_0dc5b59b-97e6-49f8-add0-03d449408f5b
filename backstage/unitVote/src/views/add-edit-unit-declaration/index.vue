<script>
import { getDeclarationInfo, getAssessmentModelDictionaryApi, uploadFile, deleteFile, downloadFileTemplate, getAssessmentModelApi, addDeclaration, rejectDeclarationApi } from "@/api/index";
import { downloadFile } from '@/utils';

import IndicatorTable from "./components/indicator-table/index.vue"

import Preview from "../preview.vue";

export default {
  name: "AddEditUnitDeclarationView",
  components: { IndicatorTable, Preview },
  data() {
    return {
      title: '用人单位申报初审',
      // 步骤
      step: 0,
      declaration: {
        unit_name: "",
        year: new Date().getFullYear(),
        self_assessment_status: null,
        declaration_status: null,
        score: null,
        attachment: [],
        declaration_basic_condition: [],
        declaration_indicator: [],
        city_assessment_status: null,
        city_score: null,

        // 评估模型 id
        assessment_model_id: null,
      },
      attachment: {
        letter: [],
        application: []
      },
      basicInfoFormRules: {

      },
      attachmentFormRules: {

      },
      // 评估模型字典
      assessmentModelDictionary: [],
      dialogVisible: false,
      dialogVisible_res: false,

      // 审核需修改
      auditFrom: {
        reason: null
      },
      auditFromRules: {
        reason: [
          { required: true, message: '请输入修改要求', trigger: 'blur' },
        ],
      },
      aduitDV: false,
      /* 文件预览 */
      showPreview: false,
      dialogPreviewUrl: "",
    }
  },
  created() {
    const { id } = this.$route.query
    this.getAssessmentModelDictionary()
    if (id) {
      this.getDeclaration(id)
    }
    // this.handleAssModelSelect(53)
  },
  methods: {
    // downloadFile,
    async getDeclaration(id) {
      try {
        const res = await getDeclarationInfo({ id })
        this.declaration = res.data
        // 处理附件回显
        res.data.attachment.forEach(item => {
          if (item.file_type === '0') { // 建设兵团健康企业承诺书
            this.attachment.letter.push({
              name: item.file_name,
              url: item.static_name
            })
          } else if (item.file_type === '1') { // 建设兵团健康企业承诺书
            this.attachment.application.push({
              name: item.file_name,
              url: item.static_name
            })
          }
        })
      } catch (error) {
        this.$message.error('申报数据获取失败')
      }
    },
    goBack() {
      this.$router.push({
        name: "index",
      })
    },
    async getAssessmentModelDictionary() {
      try {
        const res = await getAssessmentModelDictionaryApi()
        this.assessmentModelDictionary = res.data
      } catch (error) {
        this.$message.error("评估模型字典数据获取失败")
      }
    },
    /* 校验文件 */
    // 校验文件格式
    isFileValid(file) {
      // 最大允许 5MB
      const MAX_SIZE_MB = 5;
      const MAX_SIZE_BYTES = MAX_SIZE_MB * 1024 * 1024;

      // 文件大小校验
      if (file.size > MAX_SIZE_BYTES) {
        return false;
      }

      // 允许的扩展名列表（小写）
      const allowedExtensions = [
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', // 图片
        'pdf',                                      // PDF
        'doc', 'docx'                               // Word
      ];

      // 允许的 MIME 类型列表（小写）
      const allowedMimeTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp', // 图片
        'application/pdf',                                                 // PDF
        'application/msword',                                              // DOC
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document' // DOCX
      ];

      // 提取文件扩展名和 MIME 类型
      const extension = file.name.split('.')
        .pop()
        .toLowerCase();
      const mimeType = file.type.toLowerCase();

      // 检查扩展名或 MIME 类型是否合法
      const isExtensionValid = allowedExtensions.includes(extension);
      const isMimeTypeValid = allowedMimeTypes.includes(mimeType);

      // 满足任一条件即可（扩展名或 MIME 类型合法）
      return isExtensionValid || isMimeTypeValid;
    },
    /* 上传承诺书 */
    beforeLetterUpload(file) {
      const validRes = this.isFileValid(file);
      validRes ? '' : this.$message.warning(`${file.name} 文件格式不支持！`);
      return validRes;
    },
    async uploadLetterFile({ file }) {
      const formData = new FormData();
      formData.append('file', file);
      try {
        const { data } = await uploadFile(formData);
        this.declaration.attachment.push({
          file_name: file.name,
          static_name: data.url,
          file_type: 0
        });
        file.url = data.url;
        this.attachment.letter.push(file);
      } catch (e) {
        this.$message.error(`${file.name} 上传失败！`);
      }
    },
    handleLetterPreview(file, name) {
      //预览
      let url = file.url;
      let fileSplite = url.split(".");
      if (fileSplite[fileSplite.length - 1] === "pdf") {
        let a = document.createElement("a");
        a.href = url;
        a.target = "_blank";
        a.click();
        a.remove();
      } else {
        this.dialogPreviewUrl = url;
        this.showPreview = true;
      }
    },
    async handleLetterRemove(file) {
      const removeFile = this.attachment.letter.find(item => {
        return item.uid === file.uid;
      });
      this.declaration.attachment = this.declaration.attachment.filter(({ static_name }) => {
        return static_name !== removeFile.url;
      });
      const index = this.attachment.letter.findIndex(item => {
        return item.url === removeFile.url;
      })
      if (index > -1) {
        this.attachment.letter.splice(index, 1)
      }
      if (!removeFile) return;
      try {
        await deleteFile({ filePath: removeFile.url.replace(/\\/g, '/') });
      } catch (e) {
        this.$message.error(`${file.name} 删除失败！`);
      }
    },
    async handleDownloadLetterTemplateFile() {
      try {
        const { data } = await downloadFileTemplate({
          file_type: 'letter'
        });
        downloadFile('建设兵团健康企业承诺书', data.url);
      } catch (e) {
        console.log(e)
        this.$message.error('建设兵团健康企业承诺书下载失败！');
      }
    },
    /* 上传 */
    beforeApplicationUpload(file) {
      const validRes = this.isFileValid(file);
      validRes ? '' : this.$message.warning(`${file.name} 文件格式不支持！`);
      return validRes;
    },
    async uploadApplicationFile({ file }) {
      const formData = new FormData();
      formData.append('file', file);
      try {
        const { data } = await uploadFile(formData);
        this.declaration.attachment.push({
          file_name: file.name,
          static_name: data.url,
          file_type: 1
        });
        file.url = data.url;
        this.attachment.application.push(file);
      } catch (e) {
        this.$message.error(`${file.name} 上传失败！`);
      }
    },
    handleApplicationPreview(file, name) {

      //预览
      let url = file.url;
      let fileSplite = url.split(".");
      if (fileSplite[fileSplite.length - 1] === "pdf") {
        let a = document.createElement("a");
        a.href = url;
        a.target = "_blank";
        a.click();
        a.remove();
      } else {
        this.dialogPreviewUrl = url;
        this.showPreview = true;
      }
    },
    async handleApplicationRemove(file) {
      const removeFile = this.attachment.application.find(item => {
        return item.uid === file.uid;
      });
      this.declaration.attachment = this.declaration.attachment.filter(({ static_name }) => {
        return static_name !== removeFile.url;
      });
      const index = this.attachment.application.findIndex(item => {
        return item.url === removeFile.url;
      })
      if (index > -1) {
        this.attachment.application.splice(index, 1)
      }
      if (!removeFile) return;
      try {
        await deleteFile({ filePath: removeFile.url.replace(/\\/g, '/') });
      } catch (e) {
        this.$message.error(`${file.name} 删除失败！`);
      }
    },
    async handleDownloadApplicationTemplateFile() {
      try {
        const { data } = await downloadFileTemplate({
          file_type: 'application'
        });
        downloadFile('兵团健康企业复核申请表', data.url);
      } catch (e) {
        console.log(e)
        this.$message.error('兵团健康企业复核申请表下载失败！');
      }
    },
    async handleAssModelSelect(id) {
      if (!id) return;
      try {
        const { data } = await getAssessmentModelApi({
          id
        })
        console.log(data)
        // 处理基本条件数据
        this.declaration.declaration_basic_condition = data.basic_conditions.map(item => {
          return {
            name: item.name,
            self_assessment_result: null,
            city_assessment_result: null,
            province_assessment_result: null
          }
        })
        // 处理指标数据
        data.assessment_info.forEach(item1 => {
          item1.children.forEach(item2 => {
            item2.children = item2.children.map(item3 => {
              return {
                ...item3.item,
                name: item3.name,
                self_score: null,
                self_missing: null,
                city_score: null,
                city_missing: null,
                province_score: null,
                province_missing: null
              }
            })
          })
        })
        this.declaration.declaration_indicator = data.assessment_info
        console.log(this.declaration)
      } catch (error) {
        this.$message.error("评估模型数据获取失败")
      }
    },
    handleCancel() {
      // this.$confirm('确定取消本次申报吗？')
      //   .then(_ => {
      //     this.$router.push({
      //       name: "index",
      //     })
      //   })
      //   .catch(_ => { });
      this.aduitDV = true
    },
    handleNext() {
      console.log(this.attachment)
      // 校验数据
      this.$refs.basicInfoForm.validate((valid) => {
        if (valid) {
          this.$refs.attachmentForm.validate((valid) => {
            if (valid) {

              // 判断是否符合
              const result = this.declaration.declaration_basic_condition.every(item => {
                return item.city_assessment_result === "1"
              })

              if (result) {
                return this.step = 1
              }

              this.$confirm('企业的评选结果为不符合，无法进行接下来的评选。')
                .then(async _ => {
                  this.declaration.city_assessment_status = '0'
                  this.declaration.declaration_status = '2'
                  try {
                    await addDeclaration(this.declaration)
                    this.dialogVisible_res = true
                    this.$router.push({
                      name: "index"
                    })
                  } catch (error) {
                    this.$message.error("数据提交失败")
                  }
                })
                .catch(_ => { });

            } else {
              return false;
            }
          });

        } else {
          return false;
        }
      });
    },
    handleSubmit() {
      this.dialogVisible = true
    },
    async handleConfirm() {
      this.dialogVisible = false

      // 计算评选结果
      let totalScore = 0
      let missingScore = 0
      let score = 0
      this.declaration.declaration_indicator.forEach(item1 => {
        item1.children.forEach(item2 => {
          item2.children.forEach(item3 => {
            totalScore += item3.score
            if (item3.city_missing === '1') {
              missingScore += item3.score
            }
            if (item3.city_missing != '1') {
              score += item3.city_score
            }
          })
        })
      })
      const coefficient = (totalScore - missingScore) / totalScore
      const result = score / coefficient >= totalScore * 0.8
      this.declaration.city_score = score / coefficient
      if (result) {
        this.declaration.city_assessment_status = '1'
        this.declaration.declaration_status = '3'
      } else {
        this.declaration.city_assessment_status = '0'
        this.declaration.declaration_status = '2'
      }

      // 提交数据
      try {
        await addDeclaration(this.declaration)
        this.dialogVisible_res = true
      } catch (error) {
        this.$message.error("数据提交失败")
      }
    },
    handleResultConfirm() {
      this.dialogVisible_res = false
      this.$router.push({
        name: "index",
      })
    },
    handleAduitDVClose() {
      console.log("aaaaa")
      this.auditFrom = {
        reason: null
      }
      this.$refs.auditFrom.clearValidate()
    },
    async handleAduitDVConfirm() {
      this.$refs.auditFrom.validate(async (valid) => {
        if (valid) {
          try {
            await rejectDeclarationApi({
              id: this.declaration.id,
              reason: this.auditFrom.reason
            })
            this.aduitDV = false
            this.$router.push({
              name: "index",
            })
          } catch (error) {
            this.$message.error("操作失败")
          }
        } else {
          return false;
        }
      });


    }
  }
}
</script>

<template>
  <div class="add-edit-declaration-view">
    <el-page-header @back="goBack" :content="title"></el-page-header>

    <transition name="fade" mode="out-in">
      <!-- 基本信息 -->
      <div v-if="step === 0" key="0">
        <div class="basic-info">
          <div class="title">
            <span class="content">基本信息</span>
          </div>
          <el-form ref="basicInfoForm" :model="declaration" :rules="basicInfoFormRules" label-width="80px">
            <el-row :gutter="24">
              <el-col :span="5" style="min-width: 320px;">
                <el-form-item label="年度">
                  <el-input v-model="declaration.year" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="5" style="min-width: 320px;">
                <el-form-item label="单位名称" prop="unit_name">
                  <el-input v-model="declaration.unit_name" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="5" style="min-width: 320px;">
                <el-form-item label="评估模型" prop="assessment_model_id">
                  <el-select v-model="declaration.assessment_model_id" placeholder="请选择评估模型"
                    @change="handleAssModelSelect" disabled>
                    <el-option v-for="item in assessmentModelDictionary" :key="item.id"
                      :label="item.model_name" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

          </el-form>
        </div>
        <div class="attachment">
          <div class="title">
            <span class="content">要求文件</span>
          </div>
          <el-form ref="attachmentForm" :model="attachment" :rules="attachmentFormRules" label-position="top">
            <el-form-item label="建设兵团健康企业承诺书" prop="letter">
              <div class="download_container">
                <!-- <span>
                  <el-button size="mini" @click="handleDownloadLetterTemplateFile">下载模版</el-button>
                </span> -->
                <el-upload disabled class="upload-demo" :on-preview="handleLetterPreview"
                  :on-remove="handleLetterRemove" :file-list="attachment.letter" :http-request="uploadLetterFile"
                  :before-upload="beforeLetterUpload">
                  <el-button size="small" type="primary" disabled>上传文件</el-button>
                  <div slot="tip" class="el-upload__tip">只能上传图片、pdf、word文件，且不超过5MB</div>
                </el-upload>
              </div>
            </el-form-item>
            <el-form-item label="兵团健康企业复核申请表" prop="application">
              <div class="download_container">
                <!-- <span>
                  <el-button size="mini" @click="handleDownloadApplicationTemplateFile">下载模版</el-button>
                </span> -->
                <el-upload disabled class="upload-demo" :on-preview="handleApplicationPreview"
                  :on-remove="handleApplicationRemove" :file-list="attachment.application"
                  :http-request="uploadApplicationFile" :before-upload="beforeApplicationUpload">
                  <el-button size="small" type="primary" disabled>上传文件</el-button>
                  <div slot="tip" class="el-upload__tip">只能上传图片、pdf、word文件，且不超过5MB</div>
                </el-upload>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="basic_condition">
          <div class="title">
            <span class="content">基本条件</span>
          </div>
          <el-table :data="declaration.declaration_basic_condition" border style="width: 100%" stripe
            header-cell-style="background-color: #f5f7fa; color: #606266;height:46px; text-algin: center">
            <el-table-column type="index" label="序号" align="center" width="180">
            </el-table-column>
            <el-table-column prop="name" label="基本条件" align="center">
            </el-table-column>
            <el-table-column label="企业自评结果" width="280" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.self_assessment_result === '1'">符合</span>
                <span v-else>不符合</span>
              </template>
            </el-table-column>
            <el-table-column label="师市级复核结果" width="280" align="center">
              <template slot-scope="scope">
                <el-radio v-model="scope.row.city_assessment_result" label="1">符合</el-radio>
                <el-radio v-model="scope.row.city_assessment_result" label="0">不符合</el-radio>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="operate">
          <el-button @click="handleCancel" type="danger" plain>审核需修改</el-button>
          <el-button type="primary" @click="handleNext">下一步</el-button>
        </div>
      </div>
      <!-- 指标 -->
      <div v-else key="1">
        <div class="indicator">
          <IndicatorTable :tableData="declaration.declaration_indicator" />
        </div>
        <div class="footer">
          <div>
            自评总得分：{{ declaration.score }}(符合)
          </div>
        </div>
        <div class="operate">
          <el-button @click="handleCancel" type="danger" plain>审核需修改</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </div>
      </div>
    </transition>

    <!-- 提交 -->
    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
      <span>请确认您填写的信息，提交后将无法修改</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 评估结果 -->
    <el-dialog title="评选结果" :visible.sync="dialogVisible_res" width="30%" :before-close="handleClose">
      <div class="result_container">
        <div class="score" :class="{ success: declaration.city_assessment_status === '1' }">{{ declaration.city_score +
          "分"
        }}
        </div>
        <div class="tip">
          <template v-if="declaration.city_assessment_status === '0'">不符合兵团健康企业认定条件</template>
          <template v-else>符合兵团健康企业认定条件，将进行下一步审核</template>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleResultConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 审核需修改 -->
    <el-dialog title="提示" :visible.sync="aduitDV" width="30%" @close="handleAduitDVClose">
      <el-form :model="auditFrom" :rules="auditFromRules" ref="auditFrom" label-width="100px">
        <el-form-item label="要求" prop="reason">
          <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 8 }" placeholder="请输入内容"
            v-model="auditFrom.reason"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="aduitDV = false">取 消</el-button>
        <el-button type="primary" @click="handleAduitDVConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <preview :filePath="dialogPreviewUrl" :preview="showPreview" @fatherMethod="showPreview = false"
      style="z-index: 9999999"></preview>
  </div>
</template>

<style lang="scss" scoped>
@import url("./index.moudle.scss");
</style>