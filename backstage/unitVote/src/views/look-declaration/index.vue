<script>
import { getDeclarationInfo, getAssessmentModelDictionaryApi, uploadFile, deleteFile, downloadFileTemplate, getAssessmentModelApi } from "@/api/index";
import { downloadFile } from '@/utils';

import IndicatorTable from "./components/indicator-table/index.vue"

import Preview from "../preview.vue";

export default {
  name: "LookDeclarationView",
  components: { IndicatorTable, Preview },
  data() {
    return {
      title: '查看',
      // 步骤
      step: 0,
      declaration: {
        unit_name: "",
        year: new Date().getFullYear(),
        self_assessment_status: null,
        declaration_status: null,
        score: null,
        city_score: null,
        attachment: [],
        declaration_basic_condition: [],
        declaration_indicator: [],

        // 评估模型 id
        assessment_model_id: null,
      },
      attachment: {
        letter: [],
        application: []
      },
      basicInfoFormRules: {},
      attachmentFormRules: {},
      // 评估模型字典
      assessmentModelDictionary: [],
      /* 文件预览 */
      showPreview: false,
      dialogPreviewUrl: "",
    }
  },
  created() {
    const { id } = this.$route.query
    this.getAssessmentModelDictionary()
    if (id) {
      this.getDeclaration(id)
    }
    // this.handleAssModelSelect(53)
  },
  methods: {
    // downloadFile,
    async getDeclaration(id) {
      try {
        const res = await getDeclarationInfo({ id })
        this.declaration = res.data
        // 处理附件回显
        res.data.attachment.forEach(item => {
          if (item.file_type === '0') { // 建设兵团健康企业承诺书
            this.attachment.letter.push({
              name: item.file_name,
              url: item.static_name
            })
          } else if (item.file_type === '1') { // 建设兵团健康企业承诺书
            this.attachment.application.push({
              name: item.file_name,
              url: item.static_name
            })
          }
        })
      } catch (error) {
        this.$message.error('申报数据获取失败')
      }
    },
    goBack() {
      this.$router.go(-1)
    },
    async getAssessmentModelDictionary() {
      try {
        const res = await getAssessmentModelDictionaryApi()
        this.assessmentModelDictionary = res.data
      } catch (error) {
        this.$message.error("评估模型字典数据获取失败")
      }
    },
    /* 校验文件 */
    // 校验文件格式
    isFileValid(file) {
      // 最大允许 5MB
      const MAX_SIZE_MB = 5;
      const MAX_SIZE_BYTES = MAX_SIZE_MB * 1024 * 1024;

      // 文件大小校验
      if (file.size > MAX_SIZE_BYTES) {
        return false;
      }

      // 允许的扩展名列表（小写）
      const allowedExtensions = [
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', // 图片
        'pdf',                                      // PDF
        'doc', 'docx'                               // Word
      ];

      // 允许的 MIME 类型列表（小写）
      const allowedMimeTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp', // 图片
        'application/pdf',                                                 // PDF
        'application/msword',                                              // DOC
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document' // DOCX
      ];

      // 提取文件扩展名和 MIME 类型
      const extension = file.name.split('.')
        .pop()
        .toLowerCase();
      const mimeType = file.type.toLowerCase();

      // 检查扩展名或 MIME 类型是否合法
      const isExtensionValid = allowedExtensions.includes(extension);
      const isMimeTypeValid = allowedMimeTypes.includes(mimeType);

      // 满足任一条件即可（扩展名或 MIME 类型合法）
      return isExtensionValid || isMimeTypeValid;
    },
    /* 上传承诺书 */
    beforeLetterUpload(file) {
      const validRes = this.isFileValid(file);
      validRes ? '' : this.$message.warning(`${file.name} 文件格式不支持！`);
      return validRes;
    },
    async uploadLetterFile({ file }) {
      const formData = new FormData();
      formData.append('file', file);
      try {
        const { data } = await uploadFile(formData);
        this.declaration.attachment.push({
          file_name: file.name,
          static_name: data.url,
          file_type: '0'
        });
        file.url = data.url;
        this.attachment.letter.push(file);
      } catch (e) {
        this.$message.error(`${file.name} 上传失败！`);
      }
    },
    handleLetterPreview(file, name) {
      console.log(file, name)
      //预览
      let url = file.url;
      let fileSplite = url.split(".");
      if (fileSplite[fileSplite.length - 1] === "pdf") {
        let a = document.createElement("a");
        a.href = url;
        a.target = "_blank";
        a.click();
        a.remove();
      } else {
        this.dialogPreviewUrl = url;
        this.showPreview = true;
      }
    },
    async handleLetterRemove(file) {
      const removeFile = this.attachment.letter.find(item => {
        return item.uid === file.uid;
      });
      this.declaration.attachment = this.declaration.attachment.filter(({ static_name }) => {
        return static_name !== removeFile.url;
      });
      const index = this.attachment.letter.findIndex(item => {
        return item.url === removeFile.url;
      })
      if (index > -1) {
        this.attachment.letter.splice(index, 1)
      }
      if (!removeFile) return;
      try {
        await deleteFile({ filePath: removeFile.url.replace(/\\/g, '/') });
      } catch (e) {
        this.$message.error(`${file.name} 删除失败！`);
      }
    },
    async handleDownloadLetterTemplateFile() {
      try {
        const { data } = await downloadFileTemplate({
          file_type: 'letter'
        });
        downloadFile('建设兵团健康企业承诺书', data.url);
      } catch (e) {
        console.log(e)
        this.$message.error('建设兵团健康企业承诺书下载失败！');
      }
    },
    /* 上传 */
    beforeApplicationUpload(file) {
      const validRes = this.isFileValid(file);
      validRes ? '' : this.$message.warning(`${file.name} 文件格式不支持！`);
      return validRes;
    },
    async uploadApplicationFile({ file }) {
      const formData = new FormData();
      formData.append('file', file);
      try {
        const { data } = await uploadFile(formData);
        this.declaration.attachment.push({
          file_name: file.name,
          static_name: data.url,
          file_type: '1'
        });
        file.url = data.url;
        this.attachment.application.push(file);
      } catch (e) {
        this.$message.error(`${file.name} 上传失败！`);
      }
    },
    handleApplicationPreview(file, name) {
      console.log(file, name)
      //预览
      let url = file.url;
      let fileSplite = url.split(".");
      if (fileSplite[fileSplite.length - 1] === "pdf") {
        let a = document.createElement("a");
        a.href = url;
        a.target = "_blank";
        a.click();
        a.remove();
      } else {
        this.dialogPreviewUrl = url;
        this.showPreview = true;
      }
    },
    async handleApplicationRemove(file) {
      const removeFile = this.attachment.application.find(item => {
        return item.uid === file.uid;
      });
      this.declaration.attachment = this.declaration.attachment.filter(({ static_name }) => {
        return static_name !== removeFile.url;
      });
      const index = this.attachment.application.findIndex(item => {
        return item.url === removeFile.url;
      })
      if (index > -1) {
        this.attachment.application.splice(index, 1)
      }
      if (!removeFile) return;
      try {
        await deleteFile({ filePath: removeFile.url.replace(/\\/g, '/') });
      } catch (e) {
        this.$message.error(`${file.name} 删除失败！`);
      }
    },
    async handleDownloadApplicationTemplateFile() {
      try {
        const { data } = await downloadFileTemplate({
          file_type: 'application'
        });
        downloadFile('兵团健康企业复核申请表', data.url);
      } catch (e) {
        console.log(e)
        this.$message.error('兵团健康企业复核申请表下载失败！');
      }
    },
    async handleAssModelSelect(id) {
      if (!id) return;
      try {
        const { data } = await getAssessmentModelApi({
          id
        })
        // 处理基本条件数据
        this.declaration.declaration_basic_condition = data.basic_conditions.map(item => {
          return {
            name: item.name,
            self_assessment_result: null,
            city_assessment_result: null,
            province_assessment_result: null
          }
        })
        // 处理指标数据
        data.assessment_info.forEach(item1 => {
          item1.children.forEach(item2 => {
            item2.children = item2.children.map(item3 => {
              return {
                ...item3.item,
                name: item3.name,
                self_score: null,
                self_missing: null,
                city_score: null,
                city_missing: null,
                province_score: null,
                province_missing: null
              }
            })
          })
        })
        this.declaration.declaration_indicator = data.assessment_info
        console.log(this.declaration)
      } catch (error) {
        this.$message.error("评估模型数据获取失败")
      }
    },
  }
}
</script>

<template>
  <div class="look-declaration-view">
    <el-page-header @back="goBack" :content="title"></el-page-header>
    <div>
      <el-alert v-if="declaration.declaration_status === '1'" :title="declaration.reason" type="warning"></el-alert>
    </div>
    <div class="basic-info">
      <div class="title">
        <span class="content">基本信息</span>
      </div>
      <el-form ref="basicInfoForm" :model="declaration" :rules="basicInfoFormRules" label-width="80px">
        <el-row :gutter="24">
          <el-col :span="5" style="min-width: 320px;">
            <el-form-item label="年度">
              <el-input v-model="declaration.year" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" style="min-width: 320px;">
            <el-form-item label="单位名称" prop="unit_name">
              <el-input v-model="declaration.unit_name" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" style="min-width: 320px;">
            <el-form-item label="评估模型" prop="assessment_model_id">
              <el-select v-model="declaration.assessment_model_id" placeholder="请选择评估模型" disabled
                @change="handleAssModelSelect">
                <el-option v-for="item in assessmentModelDictionary" :key="item.id"
                  :label="item.model_name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
    </div>
    <div class="attachment">
      <div class="title">
        <span class="content">要求文件</span>
      </div>
      <el-form ref="attachmentForm" :model="attachment" :rules="attachmentFormRules" label-position="top">
        <el-form-item label="建设兵团健康企业承诺书" prop="letter">
          <div class="download_container">
            <!-- <span>
              <el-button size="mini" @click="handleDownloadLetterTemplateFile">下载模版</el-button>
            </span> -->
            <el-upload class="upload-demo" disabled :on-preview="handleLetterPreview" :on-remove="handleLetterRemove"
              :file-list="attachment.letter" :http-request="uploadLetterFile" :before-upload="beforeLetterUpload">
              <el-button size="small" type="primary" disabled>上传文件</el-button>
            </el-upload>
          </div>
        </el-form-item>
        <el-form-item label="兵团健康企业复核申请表" prop="application">
          <div class="download_container">
            <!-- <span>
              <el-button size="mini" @click="handleDownloadApplicationTemplateFile">下载模版</el-button>
            </span> -->
            <el-upload class="upload-demo" disabled :on-preview="handleApplicationPreview"
              :on-remove="handleApplicationRemove" :file-list="attachment.application"
              :http-request="uploadApplicationFile" :before-upload="beforeApplicationUpload">
              <el-button size="small" type="primary" disabled>上传文件</el-button>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="basic_condition">
      <div class="title">
        <span class="content">基本条件</span>
      </div>
      <el-table :data="declaration.declaration_basic_condition" border style="width: 100%" stripe
        header-cell-style="background-color: #f5f7fa; color: #606266;height:46px; text-algin: center">
        <el-table-column type="index" label="序号" align="center" width="180">
        </el-table-column>
        <el-table-column prop="name" label="基本条件" align="center">
        </el-table-column>
        <el-table-column label="企业自评结果" width="280" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.self_assessment_result === '1'">符合</span>
            <span v-else>不符合</span>
          </template>
        </el-table-column>
        <el-table-column label="师市复核结果" width="280" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.city_assessment_result === '1'">符合</span>
            <span v-else>不符合</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 指标 -->
    <div class="indicator">
      <div class="title">
        <span class="content">具体指标及赋分标准</span>
      </div>
      <IndicatorTable :tableData="declaration.declaration_indicator" />
    </div>

    <div class="footer">
      <div>
        自评总得分：{{ declaration.score }} (符合)
      </div>
      <div>
        师市总评分：{{ declaration.city_score }}
        <span v-if="declaration.city_assessment_status === '1'">(符合)</span>
        <span v-else>(不符合)</span>
      </div>
    </div>

    <preview :filePath="dialogPreviewUrl" :preview="showPreview" @fatherMethod="showPreview = false"
      style="z-index: 9999999"></preview>
  </div>
</template>

<style lang="scss" scoped>
@import url("./index.moudle.scss");
</style>