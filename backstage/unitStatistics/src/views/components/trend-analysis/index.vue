<script>
import * as echarts from 'echarts';

import { getYearDeclarationStatistics, getYearSelectionStatistics } from "@/api/index"

export default {
  name: "TrendAnalysisComp",
  data() {
    return {
      search: {
        type: '0',
        years: undefined
      },
      chartInstance: null,
      // chartData: [],
      years: [],
      declared: [],
      undeclared: [],

      year: [],
      pendingInitialReview: [],
      needsModification: [],
      initialReviewRejected: [],
      pendingReview: [],
      reviewRejected: [],
      reviewApproved: []
    }
  },
  mounted() {
    this.initEcharts()
    this.getYearDeclarationStatistics()
    window.addEventListener('resize', () => {
      this.chartInstance.resize();
    });
  },
  beforeDestroy() {
    this.chartInstance.dispose();
    this.chartInstance = null
    window.removeEventListener("resize")
  },
  methods: {
    async initEcharts() {
      try {
        if (!this.chartInstance) {
          this.chartInstance = echarts.init(this.$refs.chart)
        }

        this.chartInstance.clear()

        let option = {}

        if (this.search.type === '0') {
          option = {
            tooltip: {
              trigger: 'axis',
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              borderWidth: 0,
              padding: 10,
              textStyle: {
                color: '#2c3e50'
              },
              formatter: function (params) {
                // params[0] 是已申报数据，params[1] 是未申报数据
                const declaredVal = params[0].value;
                const undeclaredVal = params[1].value;
                const year = params[0].name;
                // <div style="display:flex;align-items:center;margin:5px 0;">
                //     <div style="width:12px;height:12px;background:#3498db;border-radius:2px;margin-right:8px;"></div>
                //     <div>已申报：<span style="font-weight:bold;color:#3498db;">${declaredVal.toLocaleString()}</span></div>
                // </div>

                return `
                        <div style="font-weight:bold;margin-bottom:8px;">${year}年申报数据</div>
                        <div style="display:flex;align-items:center;margin:5px 0;">
                            <div>已申报：<span>${declaredVal.toLocaleString()}</span></div>
                        </div>
                        <div style="display:flex;align-items:center;margin:5px 0;">
                            <div>未申报：<span>${undeclaredVal.toLocaleString()}</span></div>
                        </div>
                    `;
              }
            },
            legend: {
              // data: ['已申报', '未申报'],
              data: ['已申报'],
              bottom: 5
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '12%',
              top: '10%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: this.years,
              axisLine: {
                lineStyle: {
                  color: '#3498db'
                }
              },
              axisLabel: {
                color: '#2c3e50',
                fontSize: 12
              },
              name: '年份',
              nameTextStyle: {
                fontSize: 12,
                color: '#7f8c8d',
                padding: [0, 0, -5, 0]
              }
            },
            yAxis: {
              type: 'value',
              nameTextStyle: {
                fontSize: 12,
                color: '#7f8c8d',
                padding: [0, 0, 0, 20]
              },
              axisLine: {
                lineStyle: {
                  color: '#3498db'
                }
              },
              splitLine: {
                lineStyle: {
                  type: 'dashed',
                  color: '#e0e0e0'
                }
              }
            },
            series: [
              {
                name: '已申报',
                type: 'line',
                symbol: 'circle',
                symbolSize: 8,
                lineStyle: {
                  width: 3,
                  color: '#3498db'
                },
                itemStyle: {
                  color: '#3498db'
                },
                data: this.declared
              },
              // {
              //   name: '未申报',
              //   type: 'line',
              //   symbol: 'circle',
              //   symbolSize: 8,
              //   lineStyle: {
              //     width: 3,
              //     color: '#e74c3c'
              //   },
              //   itemStyle: {
              //     color: '#e74c3c'
              //   },
              //   data: this.undeclared
              // }
            ]
          }
        } else {
          option = {
            tooltip: {
              trigger: 'axis',
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              borderWidth: 0,
              padding: 10,
              textStyle: {
                color: '#2c3e50'
              },
              formatter: function (params) {
                // params[0] 是已申报数据，params[1] 是未申报数据
                const year = params[0].name;
                const pendingInitialReview = params[0].value;
                const needsModification = params[1].value;
                const initialReviewRejected = params[2].value;
                const pendingReview = params[3].value;
                const reviewRejected = params[4].value;
                const reviewApproved = params[5].value;

                return `
                        <div style="font-weight:bold;margin-bottom:8px;">${year}年申报数据</div>
                        <div style="display:flex;align-items:center;margin:5px 0;">
                            <div>待初审：<span>${pendingInitialReview.toLocaleString()}</span></div>
                        </div>
                        <div style="display:flex;align-items:center;margin:5px 0;">
                            <div>审核需修改：<span>${needsModification.toLocaleString()}</span></div>
                        </div>
                        <div style="display:flex;align-items:center;margin:5px 0;">
                            <div>初审不通过：<span>${initialReviewRejected.toLocaleString()}</span></div>
                        </div>
                        <div style="display:flex;align-items:center;margin:5px 0;">
                            <div>待审核：<span>${pendingReview.toLocaleString()}</span></div>
                        </div>
                        <div style="display:flex;align-items:center;margin:5px 0;">
                            <div>审核不通过：<span>${reviewRejected.toLocaleString()}</span></div>
                        </div>
                        <div style="display:flex;align-items:center;margin:5px 0;">
                            <div>审核通过：<span>${reviewApproved.toLocaleString()}</span></div>
                        </div>
                    `;
              }
            },
            legend: {
              data: ['待初审', '审核需修改', '初审不通过', '待审核', '审核不通过', '审核通过'],
              bottom: 5
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '12%',
              top: '10%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: this.years,
              axisLine: {
                lineStyle: {
                  color: '#3498db'
                }
              },
              axisLabel: {
                color: '#2c3e50',
                fontSize: 12
              },
              name: '年份',
              nameTextStyle: {
                fontSize: 12,
                color: '#7f8c8d',
                padding: [0, 0, -5, 0]
              }
            },
            yAxis: {
              type: 'value',
              nameTextStyle: {
                fontSize: 12,
                color: '#7f8c8d',
                padding: [0, 0, 0, 20]
              },
              axisLine: {
                lineStyle: {
                  color: '#3498db'
                }
              },
              splitLine: {
                lineStyle: {
                  type: 'dashed',
                  color: '#e0e0e0'
                }
              }
            },
            series: [
              {
                name: '待初审',
                type: 'line',
                symbol: 'circle',
                symbolSize: 8,
                data: this.pendingInitialReview
              },
              {
                name: '审核需修改',
                type: 'line',
                symbol: 'circle',
                symbolSize: 8,
                data: this.needsModification
              },
              {
                name: '初审不通过',
                type: 'line',
                symbol: 'circle',
                symbolSize: 8,
                data: this.initialReviewRejected
              },
              {
                name: '待审核',
                type: 'line',
                symbol: 'circle',
                symbolSize: 8,
                data: this.pendingReview
              },
              {
                name: '审核不通过',
                type: 'line',
                symbol: 'circle',
                symbolSize: 8,
                data: this.reviewRejected
              },
              {
                name: '审核通过',
                type: 'line',
                symbol: 'circle',
                symbolSize: 8,
                data: this.reviewApproved
              }
            ]
          }
        }
        this.chartInstance.setOption(option, true)
      } catch (error) {
        console.error('初始化图表失败:', error)
      }
    },
    async getYearDeclarationStatistics() {
      try {
        const { data } = await getYearDeclarationStatistics()
        this.years = data.years
        this.declared = data.declared
        this.undeclared = data.undeclared
        this.initEcharts()
      } catch (error) {
        this.$message.error("申报情况数据获取失败")
      }
    },
    async getYearSelectionStatistics() {
      try {
        const { data } = await getYearSelectionStatistics()
        // 处理数据
        data.forEach(item => {
          this.year.push(item.year)
          this.pendingInitialReview.push(item.pendingInitialReview)
          this.needsModification.push(item.needsModification)
          this.initialReviewRejected.push(item.initialReviewRejected)
          this.pendingReview.push(item.pendingReview)
          this.reviewRejected.push(item.reviewRejected)
          this.reviewApproved.push(item.reviewApproved)
        })
        this.initEcharts()
      } catch (error) {
        this.$message.error("申报情况数据获取失败")
      }
    },
    handleSelectChange(val) {
      if (val === '0') {
        this.getYearDeclarationStatistics()
      } else {
        this.getYearSelectionStatistics()
      }
    }
  }
}
</script>

<template>
  <div class="trend-analysis">
    <div class="header">
      <div class="title">趋势分析</div>
      <el-select v-model="search.type" placeholder="请选择" @change="handleSelectChange">
        <el-option label="申报情况" value="0"></el-option>
        <el-option label="评选情况" value="1"></el-option>
      </el-select>
    </div>
    <div class="content">
      <div ref="chart" class="chart-container"></div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import url("./index.moudle.scss")
</style>