<template>
  <div class="content">
    <el-form inline :model="query">
      <el-form-item label="年份">
        <el-date-picker
          v-model="formData.year"
          type="year"
          placeholder="请选择年份"
          value-format="yyyy"
        ></el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="地区">
        <ElCascader
          style="width: 100%"
          v-model="formData.areaCode"
          placeholder="请选择地区"
          clearable
          :options="areaList"
          :props="{
            label: 'dictLabel',
            value: 'dictCode',
            children: 'children',
            checkStrictly: false,
            emitPath: false
          }"
          filterable
        />
      </el-form-item> -->
      <!-- <el-form-item label="病种">
        <ElCascader
          style="width: 100%"
          v-model="formData.disease"
          placeholder="请选择职业病类别"
          clearable
          filterable
          :options="categoryList"
          :props="{
            label: 'dictLabel',
            value: 'dictCode',
            children: 'children',
            checkStrictly: false,
            emitPath: false
          }"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button size="mini" type="primary" @click="fetchData">查询</el-button>
        <el-button size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      style="width: 100%"
      :data="tableData"
      show-summary
      :summary-method="getSummaries"
      :default-sort="{ prop: 'year', order: 'descending' }"
      header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px"
    >
      <el-table-column type="index" label="序号" width="60" align="center"/>
      <el-table-column prop="year" sortable label="年度" align="center"/>
      <el-table-column prop="count" label="职业病鉴定人数" />
    </el-table>
  </div>
</template>

<script>
import {statisticByYear,getDictDataByType} from '@/api/index'
export default {
  data() {
    return {
      formData:{
        year: '',
        areaCode: '',
        disease: '',
        administerAreaCode:''
      },
      tableData: [],
      categoryList:[],
      areaList:[]
    };
  },
  created() {
    this.getCategoryList()
    this.fetchData()
  },
  methods: {
    // 获取职业病类别
    getCategoryList(){
      // getDictDataByType({dictType:'occupational_disease'}).then(res=>{
      //   this.categoryList = res.data
      // })
      // getDictDataByType({dictType:'area'}).then(res2=>{
      //   this.areaList = res2.data
      // })
    },
    async fetchData() {
      const res = await statisticByYear(this.formData)
      this.tableData = res.data.data
    },
    resetQuery() {
      this.formData = {
        year: '',
        areaCode: '',
        disease: '',
        administerAreaCode:''
      };
      this.tableData = []
      this.fetchData()
    },
    getSummaries(param){
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        if (index === 1) {
          sums[index] = ''
          return
        }
        const values = data.map((item) => Number(item[column.property]))
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            return !isNaN(value) ? prev + value : prev
          }, 0)
        } else {
          sums[index] = ''
        }
      })
      return sums
    }
  } 
}
</script>

<style lang="scss" scoped>
.content {
  padding: 20px;
}

</style>
