import request from '@root/publicMethods/request';

// 新建数据
export function addData(data) {
  return request({
    url: '/manage/testPaper/answer',
    data,
    method: 'post',
  });
}
// 编辑数据
export function editData(data) {
  return request({
    url: '/manage/testPaper/answer',
    data,
    method: 'put',
  });
}
// 获取列表和总数
export function getList(params) {
  return request({
    url: '/manage/testPaper/answer',
    params,
    method: 'get',
  });
}
export function getDeatil(params) {
  return request({
    url: '/manage/testPaper/answer/detail',
    params,
    method: 'get',
  });
}
// 删除数据
export function deleteItems(data) {
  return request({
    url: '/manage/testPaper/answer',
    data,
    method: 'delete',
  });
}
// 获取活动列表
export function getGameEvents(params) {
  return request({
    url: '/manage/quizGame/allGameEvents',
    params,
    method: 'get',
  });
}

export function getTestPaperDetail(params) {
  return request({
    url: '/manage/testPaper/getDetail',
    params,
    method: 'get',
  });
}

