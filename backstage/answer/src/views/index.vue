<template>
  <div :class="classObj" class="adminUser">
    <div class="main-container">
      <TopBar @reset="clearFilter"/>
      <TableList ref="tableList"/>
      <Pagination />
      <EditForm />
    </div>
  </div>
</template>
<script>
import { initEvent } from "@root/publicMethods/events";
import TopBar from "../components/TopBar";
import TableList from "../components/TableList";
import Pagination from "@/components/Pagination";
import EditForm from "@/components/EditForm";
import { mapActions } from 'vuex';
export default {
  data() {
    return {
      sidebarOpened: false,
      device: "desktop",
    };
  },
  components: {
    TopBar,
    TableList,
    Pagination,
    EditForm,
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile",
      };
    },
  },
  async created() {
    initEvent(this);
    // 获取url: ?testPaperId=Pa6NTcaYA 中的testPaperId
    this.testPaperId = this.$route.query.testPaperId;
    if (this.testPaperId) {
      this.changeSearchParams({
        testPaperIds: [this.testPaperId],
      });
    }else{
      this.getList();
    }    
  },
  methods: {
    ...mapActions('index', ['getList', 'changeSearchParams']),
    // 重置 清空筛选条件
    clearFilter() {
      this.$refs.tableList.clearFilter();
    },
  },
};
</script>

<style lang="scss" scoped>
.main-container {
  padding: 16px;
}
</style>
