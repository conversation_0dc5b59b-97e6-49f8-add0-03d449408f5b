<template>
    <div :class="classObj" class="main-container">
      <div class="questionnaire">
        <!-- 返回按钮 -->
        <el-page-header @back="goBack" :content="testPaperDetail.name || ''"></el-page-header>
        <p class="greyText">请认真填写问卷，提交后不可修改。</p>
  
        <!-- 问题列表 -->
        <el-card>
          <div v-for="(question, index) in questions" :key="question._id" class="question-item">
            <h4>
                {{ index + 1 }}. {{ question.steam }}
                <span class="question-type">[{{ questionTypes[question.topicType] }}]</span>
            </h4>
            <div class="steamPic" v-if="question.steamPic && question.steamPic.length">
                <el-image :src="img.staticSrc" :key="img._id"
                :preview-src-list="question.steamPic.map(ele => ele.staticSrc)" v-for="img in question.steamPic"></el-image>
            </div>
            <div v-if="question.topicType === 1">
                <el-radio-group v-model="question.myAnswer[0]">
                    <el-radio v-for="(option,n) in question.options" :key="option._id" :label="n">
                        {{ A_J[n] }} {{ option.optionText }}
                    </el-radio>
                </el-radio-group>
            </div>
            <div v-else-if="question.topicType === 2">
                <el-checkbox-group v-model="question.myAnswer">
                    <el-checkbox v-for="(option,n) in question.options" :key="option._id" :label="n">
                        {{ A_J[n] }} {{ option.optionText }}
                    </el-checkbox>
                </el-checkbox-group>
            </div>
            <div v-else-if="question.topicType === 3">
                <el-radio-group v-model="question.myAnswer[0]">
                    <el-radio :label="0">正确</el-radio>
                    <el-radio :label="1">错误</el-radio>
                </el-radio-group>
            </div>
            <div v-else-if="question.topicType === 4">
                <el-input v-model="question.myAnswer[0]"></el-input>
            </div>
            <div v-else-if="question.topicType === 5">
                <el-input v-model="question.myAnswer[0]" type="textarea" :rows="3"></el-input>
            </div>
          </div>  
          <el-divider>
            <span class="greyText">到底了</span>
          </el-divider>
        </el-card>
        <!-- 提交按钮 -->
        <div class="submit-btn">
          <el-button type="primary" @click="submitAnswers">提交问卷</el-button>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  import { initEvent } from "@root/publicMethods/events";
  import { getTestPaperDetail, addData } from "../api/index";
  
  export default {
    data() {
      return {
        sidebarOpened: false,
        device: "desktop",
        testPaperId: "",
        testPaperDetail: {},
        questions: [], // 问卷题目列表
        questionTypes: ["", "单选题", "多选题", "判断题", "填空题", "问答题"],
        A_J: [ 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J' ],
      };
    },
    computed: {
      classObj() {
        return {
          hideSidebar: !this.sidebarOpened,
          openSidebar: this.sidebarOpened,
          withoutAnimation: "false",
          mobile: this.device === "mobile",
        };
      },
    },
    async created() {
      initEvent(this);
      this.testPaperId = this.$route.params.id;
      this.getTestPaperDetail();
    },
    methods: {
      goBack() {
        this.$router.go(-1); // 返回上一页
      },
      // 获取问卷详情
      getTestPaperDetail() {
        getTestPaperDetail({
          _id: this.testPaperId,
        }).then((res) => {
          if (res.status === 200) {
            this.testPaperDetail = res.data;
            this.questions = res.data.questions.map((item) => ({
                ...item,
                myAnswer: [], // 初始化答案为空数组
            }));
            console.log(1111, this.questions);
          } else {
            this.$message.error(res.message || "获取问卷详情失败");
          }
        });
      },

      // 提交问卷答案
      async submitAnswers() {
        try {
          const testResult = this.questions.map((question) => {
            question.myAnswer = question.myAnswer.sort((a, b) => a - b);
            const correctAnswer = JSON.stringify(question.answer) === JSON.stringify(question.myAnswer);
            return {
              topicType: question.topicType,
              answer: question.answer,
              myAnswer: question.myAnswer,
              topicId: question._id,
              correctAnswer,
            };
          });
  
          const payload = {
            personalTrainingId: this.testPaperId,
            testResult,
            resultStatistics: {
                topicNum: this.testPaperDetail.typeSummary.map(item => item.num),
                topicScores: this.testPaperDetail.typeSummary.map(item => item.score),
            }
          };
  
          const res = await addData(payload);
          if (res.status === 200) {
            this.$message.success("问卷提交成功！");
            this.$router.push({ name: "index" }); // 跳转到问卷列表页
          } else {
            this.$message.error(res.message || "问卷提交失败！");
          }
        } catch (error) {
          console.error("提交问卷失败：", error);
          this.$message.error("提交问卷失败，请稍后重试！");
        }
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .main-container {
    padding: 16px;
  }
  .greyText{
    color: #777;
    font-size: 13px;
  }
  .question-item {
    margin-bottom: 20px;
  }
  .question-type {
    font-size: 14px;
    color: #999;
    margin-left: 10px;
  }
  .submit-btn {
    text-align: center;
    margin-top: 16px;
  }
  header h3{
    display: inline-block;
    margin-left: 15px;
    vertical-align: middle;
  }
  .steamPic{
    margin-bottom: 10px;
    .el-image{
        width: 100px; 
        height: 100px;
        margin-right: 10px;
    }
    .el-image:hover{
        border:1px solid #ddd;
    }
  }
  .el-card{
    height: calc(100vh - 200px);
    overflow-y: auto;
    max-width: 1000px;
  }
  </style>