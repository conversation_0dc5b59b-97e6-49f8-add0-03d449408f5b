import {
  addData,
  deleteItems,
  editData,
  getList,
  getGameEvents,
} from '@/api/index';

const state = {
  searchParams: { // 搜索参数
    keyWord: null,
    status: null, // []
    createdAt: null,
    testPaperIds: null, // []
  },
  list: [], // table列表数据
  pageInfo: { // 分页信息
    curPage: 1,
    pageSize: 10,
    total: 0,
    answerProgressCount: 0, // 答题进度100统计
  },
  formData: {}, // 表单数据
  dialogVisible: false, // 弹窗/表单是否显示
  testPaperList: [],
};

const mutations = {
  setData(state, data) {
    state.list = data.list;
    state.pageInfo.total = data.total;
    state.testPaperList = data.testPaperList || [];
    state.pageInfo.answerProgressCount = data.answerProgressCount || 0;
  },
  // 处理修改的搜索参数
  changeSearchParams(state, searchParams = {}) {
    const keys = Object.keys(searchParams);
    if (keys.length === 0) {
      state.searchParams = {
        keyWord: null,
        status: null,
        createdAt: null,
        testPaperIds: null,
      };
    } else {
      keys.forEach(key => {
        if ([ 'status', 'testPaperIds' ].includes(key) && searchParams[key].length) {
          state.searchParams[key] = searchParams[key].join(',');
        } else {
          state.searchParams[key] = searchParams[key];
        }
      });
    }
    state.pageInfo.curPage = 1;
  },
  // 设置活动列表
  setGameEvents(state, data) {
    state.gameEvents = data;
  },
};

const actions = {
  // 修改搜索参数
  async changeSearchParams({ commit, dispatch }, searchParams = {}) {
    commit('changeSearchParams', searchParams);
    dispatch('getList');
  },
  // 获取企业审核列表
  async getList({
    commit,
  }) {
    const query = {
      ...state.searchParams,
      curPage: state.pageInfo.curPage,
      pageSize: state.pageInfo.pageSize,
    };
    console.log('query====', query);
    const result = await getList(query);
    console.log('列表返回===', result);
    commit('setData', result.data);
  },

  // 删除多条数据
  async deleteItems({ dispatch }, ids = []) {
    console.log('删除多条数据===', ids);
    await deleteItems({ ids });
    dispatch('getList');
  },
  // 新建数据
  async addData({ dispatch }, data) {
    delete data.title;
    await addData(data);
    dispatch('getList');
  },
  // 编辑数据
  async editData({ dispatch }, data) {
    delete data.title;
    await editData(data);
    dispatch('getList');
  },
  // 获取活动列表
  async getGameEvents({ commit }) {
    await getGameEvents().then(res => {
      commit('setGameEvents', res.data);
    }
    );
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
