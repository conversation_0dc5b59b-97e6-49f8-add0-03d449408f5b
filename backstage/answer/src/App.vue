<template>
  <div id="adminorg-app" class="answer"> 
    <!-- id不要变 -->
    <router-view />
  </div>
</template>
<script>
export default {
  data() {
    return {
      sidebarOpened:false,
    }
  },
 
  components: {},
};
</script>
<style lang="scss">
.answer {
  .el-table{
    thead{
        th{
            color: #333!important;
        }
    }
  }
.el-radio-group .el-radio.is-disabled .el-radio__label,
.el-checkbox-group .el-checkbox.is-disabled .el-checkbox__label {
  color: #424242 !important; /* 设置字体颜色为黑色 */
  opacity: 1 !important; /* 确保颜色不受透明度影响 */
}

}
</style>
