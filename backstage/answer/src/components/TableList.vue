<template>
<div>
    <el-table ref="myTable" :data="list" stripe @filter-change="handleFilterChange" @sort-change="handleSortChange" :max-height="maxHeight">
        <el-table-column type="index" label="序号" :index="indexMethod" align="center"></el-table-column>
        <el-table-column label="调查表名称" column-key="testPaperIds" :filters="testPaperOptions">
            <template slot-scope="scope">
                <span>{{ scope.row.testPaper ? scope.row.testPaper.name || '' : '' }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="adminOrg.cname" label="所属单位" min-width="100"></el-table-column>
        <el-table-column label="填表人" align="center">
            <template slot-scope="scope">
                <span>{{ scope.row.adminUser ? scope.row.adminUser.name || scope.row.adminUser.userName : scope.row.user.name }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="填表时间" sortable align="center"></el-table-column>
        <el-table-column prop="updatedAt" label="提交时间" align="center"></el-table-column>
        <el-table-column label="答题进度" >
            <template slot-scope="scope">
                <el-progress :percentage="scope.row.answerProgress"></el-progress>
            </template>
        </el-table-column>
        <el-table-column label="答题分数" align="center">
            <template slot-scope="scope">
                {{ typeof scope.row.resultStatistics.actualScore === 'number' ? scope.row.resultStatistics.actualScore : '' }}
            </template>
        </el-table-column>
        <el-table-column label="总分" align="center">
            <template slot-scope="scope">
                {{ typeof scope.row.resultStatistics.totleScore === 'number' ? scope.row.resultStatistics.totleScore : '' }}
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
            <template slot="header" slot-scope="scope">
                <el-input v-model.trim="keyWord" size="mini" placeholder="输入关键字搜索" clearable/>
            </template>
            <template slot-scope="scope">
                <!-- <el-button type="primary" size="mini" @click="setForm({operate: 'view', formData: scope.row })">查看</el-button> -->
                <el-button type="primary" size="mini" @click="toDeatil(scope.row)">详情</el-button>
                <el-button type="danger" size="mini" @click="delItem(scope.row)">删除</el-button>
            </template>
        </el-table-column>
    </el-table>
</div>
</template>
<script>
import { mapGetters, mapActions } from "vuex";

export default {
    data() {
        return {
            keyWord: '', // 搜索关键字
            timerId: null, // 输入框防抖定时器
            timerInterval: 1000, // 输入框防抖间隔时间 毫秒
            statusOptions: [ // 状态名称
                { text: '启用', value: '1' },
                { text: '禁用', value: '0' },
            ],
            resetFlag: false, // 重置标志
        }
    },
    computed: {
        ...mapGetters(["list", "searchParams", "pageInfo", "testPaperList"]),
        maxHeight() {
            return document.documentElement.clientHeight - 195;
        },
        testPaperOptions() {
            return this.testPaperList.map(item => ({ text: item.name, value: item._id }));
        }
    },
    watch: {
        // 监控 搜索关键字 防抖
        keyWord(val) {
            if(this.resetFlag) return;
            if(this.timerId) clearTimeout(this.timerId);
            this.timerId = setTimeout(() => {
                this.changeSearchParams({ keyWord: val || null });
            }, this.timerInterval);
        },
    },
    methods: {
        ...mapActions('index', ['getList', 'changeSearchParams', 'deleteItems']),
        // 序号
        indexMethod(index) {
            return index + 1 + (this.pageInfo.curPage - 1) * this.pageInfo.pageSize;
        },
        toDeatil(row) {
            this.$router.push({ path: `/admin/answer/${row._id}` });
        },
        // 筛选
        handleFilterChange(filters) {
            this.changeSearchParams(filters);
        },
        // 排序
        handleSortChange({ column, prop, order }) {
            this.changeSearchParams({ [prop]: order === 'ascending' ? 1 : -1 });
        },
        // 点击删除按钮
        delItem(row) {
            this.$confirm( `确定删除该问卷调查记录吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.deleteItems([ row._id ]).then(() => {
                    this.$message.success('操作成功');
                });
            }).catch(() => {});
        },
        // 点击重置 清空筛选条件
        clearFilter() {
            this.$refs.myTable.clearFilter();
            this.$refs.myTable.clearSort();
            this.keyWord = '';
            this.resetFlag = true;
            this.changeSearchParams({});
            setTimeout(() => {
                this.resetFlag = false;
            }, 100);
        }
    },
    destroyed() {
        if(this.timerId) clearTimeout(this.timerId);
    }
};
</script>
<style lang="scss" scoped>
.el-table{
    margin-bottom: 25px;
    .el-button { // 操作按钮
        width: 48px;
        height: 24px;
        border-radius: 4px;
        line-height: 22px;
        padding: 0!important;
        margin: 0;
        font-size: 12px;
        margin-right: 10px;
    }
    .el-button:last-child{
        margin-right: 0;
    }
    .el-button--primary{
        background: #409EFF;
    }
    .el-button--danger{
        background: #F56C6C;
    }
    .status{ // 审核状态
        border-radius: 256000px;
        width: 52px;
        height: 22px;  
        padding: 1px 8px;
        gap: 6px;
        box-sizing: border-box;
        background: #fff;
        font-size: 12px;
        border: 1px solid #E4E7ED;
        color: #909399;
    }
    .status.status1{
        border: 1px solid #B3E09C;
        color: #67C23A;
    }
    .status.status2{
        border: 1px solid #FAB5B5;
        color: #F56C6C;
    }
}
</style>

