<template>
    <div class="topBar">
        <h4>总数：<span>{{ pageInfo.total }}</span> 个 &nbsp; 已完成：<span>{{ pageInfo.answerProgressCount }}</span> 个 &nbsp; 未完成：<span :class="pageInfo.total - pageInfo.answerProgressCount>0?'notComplate':''">{{ pageInfo.total - pageInfo.answerProgressCount }}</span> 个</h4>
        <div class="right">
            <el-button type="danger" plain size="mini" @click="$emit('reset')">重置</el-button>
        </div>
    </div>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
export default {
    data() {
        return {
            
        }
    },
    computed: {
        ...mapGetters(["pageInfo", "searchParams"]),
    },
    methods: {
        ...mapActions('index', ['setForm']),
    }
};
</script>
<style lang="scss" scoped>
.topBar{
    display: flex;
    justify-content: space-between;
    align-items: center;
    h4{
        color: #333;
        padding: 10px 0;
        margin: 0;
        span{
            color: #31A0FF;
        }
        .notComplate{
            color: #FF4D4F;
        }
    }
    .el-button{
        width: 48px;
        height: 24px;
        border-radius: 4px;
        line-height: 22px;
        padding: 0!important;
        margin: 0;
        font-size: 12px;
    }
    .el-button--danger.is-plain{
        margin-left: 15px;
    }
}

</style>