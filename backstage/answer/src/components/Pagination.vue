<template>
  <div class="block dr-pagination">
    <el-pagination
      background
      :hide-on-single-page="false"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="pageInfo.curPage"
      :page-sizes="[10, 30, 50, 100]"
      :page-size="pageInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pageInfo.total"
    ></el-pagination>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  computed: {
    ...mapGetters(["pageInfo"]),
  },
  methods: {
    handleSizeChange(val) {
      this.pageInfo.pageSize = val;
      this.$store.dispatch("index/getList")
    },
    handleCurrentChange(val) {
      this.pageInfo.curPage = val;
      this.$store.dispatch("index/getList")
    },
  },
};
</script>

<style lang="scss">
.dr-pagination {
  text-align: center;
  margin: 15px auto;
}
// 福州的样式
#adminorg-app .el-pagination.is-background .el-pager li{
  color:#1c1e21;
  font-weight:400;
  background-color:#fff;
  border: 1px solid #d4d9e1;
}
#adminorg-app .el-pagination.is-background .el-pager li:not(.disabled).active{
  border: 1px solid #50A4FC;
  background:#fff;
  color: #50A4FC;
}
.el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev{
  background-color:#fff !important;
  border:1px solid #d4d9e1
}
</style>
