<template>
<el-dialog :title="formData.title" :visible.sync="dialogVisible" width="60%" min-width="700" @close="setForm({operate: 'close'})" 
custom-class="xxn-dialog" :fullscreen="false" :close-on-click-modal="false" >
  <el-form :model="formData" :rules="rules" ref="ruleForm" label-width="135px" :disabled="isView">
    <el-form-item label="活动名称" prop="name">
      <el-select v-model="formData.gameEventId" placeholder="请选择">
        <el-option v-for="item in gameEvents" :key="item._id" :label="item.name" :value="item._id"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="奖品名称" prop="name">
      <el-input v-model.trim="formData.name" autocomplete="off" placeholder="请输入"></el-input>
    </el-form-item>
    <el-form-item label="奖品数量" prop="quantity" class="inline-block">
      <el-input v-model="formData.quantity" type="number" autocomplete="off" placeholder="请输入" :min="0"></el-input>
    </el-form-item>
    <el-form-item label="中奖概率" prop="winningRate" class="inline-block">
      <el-input v-model="formData.winningRate" type="number" autocomplete="off" placeholder="请输入" :min="0" :max="100">
        <template slot="append">%</template>
      </el-input>
    </el-form-item>
    <el-form-item label="奖品图片" prop="picture" class="inline-block">
      <el-upload ref="upload" class="uploader" :show-file-list="false" action=""
        :on-success="handleImgSuccess" :before-upload="beforeImgUpload" :accept="fileTypes">
        <img v-if="formData.picture" :src="formData.picture" class="img" >
        <i v-else class="el-icon-plus uploader-icon"></i>
      </el-upload>
    </el-form-item>
    <el-form-item label="是否启用" prop="enable" class="inline-block">
      <el-select v-model="formData.enable" placeholder="请选择">
        <el-option label="启用" :value="true"></el-option>
        <el-option label="禁用" :value="false"></el-option>
      </el-select>
    </el-form-item>
  </el-form>
  <div slot="footer" class="dialog-footer">
    <el-button @click="setForm({operate: 'close'})" size="small" type="info">{{isView ? '关 闭' : '取 消'}}</el-button>
    <el-button @click="submitForm('ruleForm')" type="primary" size="small" v-show="!isView">确 定</el-button>
  </div>
</el-dialog>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
export default {
    data() {
        return {
          fileTypes: ['image/png'], // 上传文件类型
          fileMaxSize: 0.5, // 上传文件大小 单位MB
          rules: {
            name: [
              { required: true, message: '请输入奖品名称', trigger: 'blur' },
              { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' }
            ],
            quantity: [
              { required: true, message: '请输入奖品数量', trigger: 'blur' },
              { validator: (rule, value, callback) => {
                if (value < 0) {
                  callback(new Error('奖品数量不能小于0'));
                } else {
                  callback();
                }
              }, trigger: 'blur' }
            ],
            winningRate: [
              { required: true, message: '请输入抽奖概率', trigger: 'blur' },
              { validator: (rule, value, callback) => {
                if (value < 0 || value > 100) {
                  callback(new Error('抽奖概率范围为0-100'));
                } else {
                  callback();
                }
              }, trigger: 'blur' }
            ],
            picture: [
              { required: true, message: '请上传奖品图片', trigger: 'change' }
            ],
            enable: [
              { required: true, message: '请选择奖品状态', trigger: 'change' }
            ],
          },
        }
    },
    computed: {
        ...mapGetters(['dialogVisible', 'formData', 'gameEvents']),
        isView() { // 是否为查看状态
          return this.formData.title && this.formData.title.includes('查看');
        }
    },
    methods: {
      ...mapActions('index', ['setForm', 'addData', 'editData']),
      // 提交表单
      submitForm(formName) {
        this.$refs[formName].validate(async (valid) => {
          if (valid) {
            const formDate = new FormData();
            for (let key in this.formData) {
              if(key === 'imgFile') {
                formDate.append('file', this.formData[key]);
              }else{
                if(key === 'picture') {
                  console.log('跳过');
                  continue;
                }
                formDate.append(key, this.formData[key]);
              }
            }
            if (this.formData._id) {
              await this.editData(this.formData.imgFile ? formDate : this.formData);
            } else {
              await this.addData(formDate);
            }
            this.$refs.upload.clearFiles();
            this.setForm({operate: 'close'});
          } else {
            return false;
          }
        });
      },
      // 图片上传前校验
      beforeImgUpload(file) {
        if(this.fileTypes.indexOf(file.type) === -1) {
          this.$message.error('上传图片的格式只能是' + this.fileTypes.join('、'));
          return false;
        }
        const isLtSize = file.size / 1024 / 1024 < this.fileMaxSize;
        if (!isLtSize) {
          this.$message.error('上传图片大小不能超过' + this.fileMaxSize + 'MB!');
          return false;
        }
        return true;
      },
      // 图片上传成功回调
      handleImgSuccess(res, file) {
        this.formData.picture = URL.createObjectURL(file.raw);
        this.formData.imgFile = file.raw; // 文件原始对象
      },
    }
}
</script>

<style lang="scss" >
.el-dialog.xxn-dialog{
  padding-right: 20px;
  padding-bottom: 10px;
  .el-select{
    width: 100%;
  }
  .uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .img {
    width: 178px;
    height: 178px;
    display: block;
  }
  .inline-block {
    width: 50%; 
    display: inline-block;
    vertical-align: top;
  }
}
</style>