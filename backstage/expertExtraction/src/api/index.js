import request from '@root/publicMethods/request';

export function getList(params) {
  return request({
    url: '/manage/expert/extractionList',
    params,
    method: 'get',
  });
}

// 新建数据
export function addData(data) {
  return request({
    url: '/manage/expert',
    data,
    method: 'post',
  });
}
// 编辑数据
export function editData(data) {
  return request({
    url: '/manage/expert',
    data,
    method: 'put',
  });
}
// 获取列表和总数
// export function getList(params) {
//   return request({
//     url: '/manage/expert',
//     params,
//     method: 'get',
//   });
// }
// 删除数据
export function deleteItems(data) {
  return request({
    url: '/manage/expert',
    data,
    method: 'delete',
  });
}
// 获取学历字典表数据
export function getEducationalQualifications(params) {
  return request({
    url: '/api/dictionary/educational-qualifications',
    params,
    method: 'get',
  });
}
// 获取行政区划字典表数据
export function getAreaCode(params) {
  return request({
    url: '/api/dictionary/area-code',
    params,
    method: 'get',
  });
}
// 获取身份证明文件类型
export function getIDtype(params) {
  return request({
    url: '/api/dictionary/ID-type',
    params,
    method: 'get',
  });
}

// 获取专家详情
export function expertDetail(params) {
  return request({
    url: '/manage/expert/detail',
    params,
    method: 'get',
  });
}

// 获取专家类别
export function expertClassifier(params) {
  return request({
    url: '/manage/expert/classifier',
    params,
    method: 'get',
  });
}

// 创建资质证书
export function createCertificate(data) {
  return request({
    url: '/manage/expert/certificate',
    data,
    headers: { 'Content-Type': 'multipart/form-data' },
    method: 'post',
  });
}

// 删除资质证书
export function deleteCertificate(data) {
  return request({
    url: '/manage/expert/certificate',
    data,
    method: 'delete',
  });
}

// 获取证书列表
export function certificateList(params) {
  return request({
    url: '/manage/expert/certificate',
    params,
    method: 'get',
  });
}

// 更新专家信息
export function updateExpert(data) {
  return request({
    url: '/manage/expert',
    data,
    method: 'put',
  });
}

// 专家推荐
export function expertRecommend(data) {
  return request({
    url: '/manage/expert/recommend',
    data,
    method: 'post',
  });
}

// 专家解聘
export function expertDismissal(data) {
  return request({
    url: '/manage/expert/dismissal',
    data,
    method: 'post',
  });
}

// 专家申报审核
export function expertDeclarationReview(data) {
  return request({
    url: '/manage/expert/declarationReview',
    data,
    method: 'post',
  });
}

export function expertExtraction(params) {
  return request({
    url: '/api/expert/extraction/random',
    params,
    method: 'get',
  });
}
