<template>
  <div :class="classObj">
    <div class="main-container">
      <div class="search">
        <el-input
          v-model.trim="keyWord"
          placeholder="关键字查询"
          clearable
          size="small"
          @keyup.enter="onSubmit"
        ></el-input>
        <el-button type="primary" @click="onSubmit" icon="el-icon-search" plain size="small"
          >查询</el-button
        >
        <el-button @click="onReset" icon="el-icon-refresh" plain size="small">重置</el-button>
        <el-button type="success" @click="toAi" plain size="small" class="Ai">专家抽取</el-button>
      </div>

      <el-table
        :data="tableData"
        tooltip-effect="light"
        @selection-change="handleSelect"
        stripe
        border
        ref="multipleTable"
      >
        <el-table-column type="index" width="50" label="序号" align="center"></el-table-column>
        <el-table-column
          label="管理要素"
          align="center"
          prop="management_element"
          width="135"
        ></el-table-column>
        <el-table-column label="工作事项" prop="work_item"></el-table-column>
        <el-table-column
          prop="created_at"
          label="抽取时间"
          align="center"
          width="135"
        ></el-table-column>
        <el-table-column prop="expert_list" label="抽取专家名单"></el-table-column>
        <el-table-column prop="item_requirements" label="事项要求"></el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pageInfo.curPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size.sync="pageInfo.pageSize"
          background
          layout="total, sizes, prev, pager, next"
          :total="totalCount"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { getList, expertExtraction } from '@/api/index.js';
import XLSX from 'xlsx';
import moment from 'moment';
import { Message } from 'element-ui';
import { initEvent } from '@root/publicMethods/events';
export default {
  data() {
    return {
      sidebarOpened: false,
      device: 'desktop',

      totalCount: 0,
      tableData: [],
      loading: false,
      keyWord: '',
      pageInfo: {
        curPage: 1,
        pageSize: 10,
      },
    };
  },
  async created() {
    initEvent(this);
    this.getList();
  },
  methods: {
    toAi() {
      this.$router.push({ name: 'extractionForm' });
    },
    async handleSizeChange(val) {
      this.pageInfo.curPage = this.pageInfo.curPage;
      this.pageInfo.pageSize = this.pageInfo.pageSize;
      this.getList();
    },
    async handleCurrentChange(val) {
      this.pageInfo.curPage = this.pageInfo.curPage;
      this.pageInfo.pageSize = this.pageInfo.pageSize;
      this.getList();
    },
    async getList() {
      try {
        const params = {
          keyWord: this.keyWord,
          ...this.pageInfo,
        };
        const res = await getList(params);
        if (res && res.data && res.data.list) {
          this.tableData = res.data.list;
          this.totalCount = res.data.total;
        }
      } catch (error) {
        Message({
          type: 'danger',
          message: error,
        });
      }
    },
    async onSubmit() {
      this.pageInfo.curPage = 1;
      this.getList();
    },

    async onReset() {
      // this.formSearch = this.$options.data()['formSearch']
      this.keyWord = this.$options.data()['keyWord'];
      this.pageInfo = this.$options.data()['pageInfo'];
      this.getList();
    },

    async exportData() {
      try {
        let params = {
          curPage: 1,
          pageSize: 2000,
        };
        if (this.formSearch.area_code) {
          params.area_code = this.formSearch.area_code;
        }
        if (this.formSearch.category_id) {
          params.category_id = this.formSearch.category_id;
        }
        if (this.formSearch.level) {
          params.level = this.formSearch.level;
        }
        if (this.formSearch.state) {
          params.state = this.formSearch.state;
        }
        if (this.formSearch.keyWord) {
          params.keyWord = this.formSearch.keyWord;
        }
        if (this.formSearch.gender) {
          params.gender = this.formSearch.gender;
        }
        const res = await getList(params);
        if (res && res.data && res.data.list && Array.isArray(res.data.list)) {
          let downloadData = [];
          res.data.list.forEach(item => {
            downloadData.push({
              专家姓名: item.name ? item.name : '',
              专家类别: item.category ? item.category : '',
              专家级别: item.level ? item.level : '',
              出生日期: item.birthday ? item.birthday : '',
              年龄: item.age ? item.age : '',
              身份证号: item.id_number ? item.id_number : '',
              联系电话: item.phone ? item.phone : '',
              专业: item.major ? item.major : '',
              工作单位: item.work_unit ? item.work_unit : '',
              学历信息: item.education ? item.education : '',
              性别: item.gender ? item.gender : '',
            });
          });

          const ws = XLSX.utils.json_to_sheet(downloadData); // 将 JSON 数据转换为工作表
          const wb = XLSX.utils.book_new(); // 创建一个新的工作簿
          XLSX.utils.book_append_sheet(wb, ws, 'Sheet1'); // 将工作表添加到工作簿中
          const wbout = XLSX.write(wb, { type: 'binary', bookType: 'xlsx' }); // 将工作簿转换为二进制字符串
          const blob = new Blob([s2ab(wbout)], { type: 'application/octet-stream' }); // 将二进制字符串转换为 Blob 对象
          saveAs(blob, moment() + '.xlsx'); // 保存文件
          // 辅助函数：将字符串转换为 ArrayBuffer
          function s2ab(s) {
            const buf = new ArrayBuffer(s.length);
            const view = new Uint8Array(buf);
            for (let i = 0; i < s.length; i++) view[i] = s.charCodeAt(i) & 0xff;
            return buf;
          }
        }
      } catch (error) {
        Message({
          type: 'danger',
          message: error,
        });
      }
    },
  },

  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: 'false',
        mobile: this.device === 'mobile',
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 15px;
}
.title::before {
  content: '';
  display: inline-block;
  width: 3px;
  height: 15px;
  background-color: #1691e0;
  margin-right: 10px;
  vertical-align: middle;
}
.main-container {
  padding: 16px;
}
.search {
  display: flex;
  .el-input {
    width: 200px;
    margin-right: 10px;
  }
}
.el-table {
  margin: 20px 0;
}
.pagination {
  display: flex;
  justify-content: center;
}
::v-deep .el-table__fixed,
::v-deep .el-table__fixed-right {
  height: 100% !important;
}
</style>
