<template>
  <div :class="classObj">
    <div class="extraction-form-container main-container">
      <el-page-header @back="goBack" content="专家抽取" class="page-title"></el-page-header>
      <el-card class="form-card">
        <div slot="header" class="form-card-header">
          <span>专家抽取条件</span>
          <small>请设置抽取条件，带 <span class="required-field">*</span> 为必填项</small>
        </div>
        <el-form
          :model="formData"
          :rules="rules"
          ref="extractionForm"
          label-width="110px"
          class="extraction-form"
          :inline="true"
          size="small"
        >
          <div class="form-section">
            <div class="form-section-title">
              <i class="el-icon-s-grid"></i>
              基本条件
            </div>
            <el-form-item label="抽取专家数量" prop="quantity" required>
              <el-input-number v-model="formData.quantity" :min="1" :max="100"></el-input-number>
            </el-form-item>

            <el-form-item label="所属区域" prop="area_code">
              <!-- <el-select v-model="formData.area_code" multiple placeholder="请选择区域" clearable>
                <el-option
                  v-for="item in areaCodeOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
              </el-select> -->
              <el-cascader
                @visible-change="visibleChange"
                style="width: 100%"
                v-model="formData.area_code"
                :props="workAdd"
                clearable
                collapse-tags
                ref="regAddCas"
                placeholder="请选择区域"
              >
              </el-cascader>
            </el-form-item>

            <el-form-item label="专家分类" prop="category_id">
              <el-select
                v-model="formData.category_id"
                multiple
                placeholder="请选择专家分类"
                clearable
              >
                <el-option
                  v-for="item in categoryOptions"
                  :key="item.id"
                  :label="item.classification_name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="专家级别" prop="level">
              <el-select v-model="formData.level" placeholder="请选择专家级别" clearable>
                <el-option label="兵团级" :value="1"></el-option>
                <el-option label="师市级" :value="2"></el-option>
                <el-option label="团镇级" :value="3"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="关键字" prop="keyWord">
              <el-input
                v-model="formData.keyWord"
                placeholder="请输入关键字，支持手机、姓名、身份证"
              ></el-input>
            </el-form-item>

            <el-form-item label="学历" prop="education">
              <el-select v-model="formData.education" placeholder="请选择学历" clearable>
                <el-option label="小学" :value="1"></el-option>
                <el-option label="初中" :value="2"></el-option>
                <el-option label="高中（含中专）及以下" :value="3"></el-option>
                <el-option label="专科（大专）" :value="4"></el-option>
                <el-option label="本科" :value="5"></el-option>
                <el-option label="硕士研究生" :value="6"></el-option>
                <el-option label="博士研究生" :value="7"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="性别" prop="gender">
              <el-select v-model="formData.gender" placeholder="请选择性别" clearable>
                <el-option label="女" value="F"></el-option>
                <el-option label="男" value="M"></el-option>
              </el-select>
            </el-form-item>
          </div>

          <div class="form-section">
            <div class="form-section-title">
              <i class="el-icon-document"></i>
              工作内容
            </div>

            <div class="work-content-row">
              <el-form-item
                label="主要管理要素"
                prop="management_element"
                required
                class="important-field work-item"
              >
                <el-input
                  v-model="formData.management_element"
                  placeholder="请输入主要管理要素"
                  type="textarea"
                  :rows="3"
                ></el-input>
              </el-form-item>

              <el-form-item
                label="工作事项"
                prop="work_item"
                required
                class="important-field work-item"
              >
                <el-input
                  v-model="formData.work_item"
                  placeholder="请输入工作事项"
                  type="textarea"
                  :rows="3"
                ></el-input>
              </el-form-item>

              <el-form-item label="事项要求" prop="item_requirements" class="work-item">
                <el-input
                  type="textarea"
                  v-model="formData.item_requirements"
                  placeholder="请输入事项要求(可详细描述事项的具体要求、背景和目标)"
                  :rows="3"
                  resize="none"
                  class="textarea-with-counter"
                ></el-input>
              </el-form-item>
            </div>
          </div>

          <el-form-item class="form-buttons">
            <el-button
              type="primary"
              @click="submitForm('extractionForm')"
              size="medium"
              icon="el-icon-check"
              >提交</el-button
            >
            <el-button
              @click="resetForm('extractionForm')"
              size="medium"
              icon="el-icon-refresh-left"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </el-card>

      <div class="extraction-result">
        <el-divider content-position="left">
          <span class="divider-title"> <i class="el-icon-data-analysis"></i> 抽取结果 </span>
        </el-divider>
        <div v-if="loading" class="loading-container">
          <el-card class="loading-card" shadow="never">
            <i class="el-icon-loading" style="font-size: 36px; color: #409eff"></i>
            <p>正在抽取专家，请稍候...</p>
            <small>根据条件匹配中，可能需要一点时间</small>
          </el-card>
        </div>
        <div v-else-if="extractionResult.length > 0">
          <el-alert title="专家抽取成功" type="success" :closable="false" show-icon>
            <template slot="title">
              成功抽取 <strong>{{ extractionResult.length }}</strong> 名专家
            </template>
          </el-alert>
          <el-table
            :data="extractionResult"
            border
            style="margin-top: 15px"
            stripe
            highlight-current-row
            class="result-table"
          >
            <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
            <el-table-column
              prop="name"
              label="专家姓名"
              width="100"
              align="center"
            ></el-table-column>
            <el-table-column prop="gender" label="性别" width="60" align="center"></el-table-column>
            <el-table-column prop="phone" label="联系电话" width="120"></el-table-column>
            <el-table-column prop="work_unit" label="工作单位"></el-table-column>
            <el-table-column prop="major" label="所学专业" width="120"></el-table-column>
            <el-table-column prop="education" label="学历" width="100"></el-table-column>
            <el-table-column prop="category" label="专家类别"></el-table-column>
            <el-table-column prop="level" label="专家级别" width="100" align="center">
              <template slot-scope="scope">
                <el-tag size="medium" type="primary" v-if="scope.row.level === 1">兵团级</el-tag>
                <el-tag size="medium" type="success" v-else-if="scope.row.level === 2"
                  >师市级</el-tag
                >
                <el-tag size="medium" type="warning" v-else-if="scope.row.level === 3"
                  >团镇级</el-tag
                >
                <span v-else>{{ scope.row.level }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-else class="empty-result">
          <el-empty description="暂无抽取结果" :image-size="120">
            <template slot="description">
              <p class="empty-text">暂无抽取结果</p>
              <small class="empty-tip">请设置抽取条件并点击提交按钮</small>
            </template>
            <el-button type="primary" size="small" icon="el-icon-s-operation" @click="scrollToForm"
              >设置抽取条件</el-button
            >
          </el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { expertExtraction, getAreaCode, expertClassifier } from '@/api/index.js';
import { Message } from 'element-ui';
import { initEvent } from '@root/publicMethods/events';
import axios from 'axios';

export default {
  name: 'ExtractionForm',
  data() {
    return {
      sidebarOpened: false,
      device: 'desktop',
      formData: {
        quantity: 1,
        area_code: [],
        category_id: [],
        level: null,
        keyWord: '',
        education: null,
        gender: null,
        management_element: '',
        work_item: '',
        item_requirements: '',
      },
      areaCodeOptions: [],
      categoryOptions: [],
      loading: false,
      rules: {
        quantity: [
          { required: true, message: '请输入抽取数量', trigger: 'blur' },
          { type: 'number', message: '抽取数量必须为数字', trigger: 'blur' },
        ],
        management_element: [{ required: true, message: '请输入主要管理要素', trigger: 'blur' }],
        work_item: [{ required: true, message: '请输入工作事项', trigger: 'blur' }],
      },
      extractionResult: [],
      workAdd: {
        lazy: true,
        checkStrictly: true,
        emitPath: false,
        value: 'area_code', // 使用area_code作为参数值
        lazyLoad(node, resolve) {
          if (node.level > 0) {
            node = node.data;
          }
          const url = '/api/address/list';
          axios({
            method: 'get',
            url: url,
            params: node,
          }).then(response => {
            try {
              const districts = response.data.data;
              // const a = districts[0];
              let nodes = districts.map(item => ({
                value: item.name,
                label: item.name,
                id: item.id,
                area_code: item.area_code,
                parent_code: item.parent_code,
                lat: item.lat,
                lon: item.lng,
                merger_name: item.merger_name,
                short_name: item.short_name,
                leaf: item.level >= 2,
              }));

              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          });
        },
      },
    };
  },
  created() {
    initEvent(this);
    this.getAreaCodeOptions();
    this.getCategoryOptions();
  },
  computed: {
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: 'false',
        mobile: this.device === 'mobile',
      };
    },
  },
  methods: {
    goBack() {
      this.$router.push({ name: 'index' });
    },
    scrollToForm() {
      // 平滑滚动到表单顶部
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    async getAreaCodeOptions() {
      try {
        const res = await getAreaCode();
        if (res && res.data) {
          this.areaCodeOptions = res.data;
        }
      } catch (error) {
        console.error('获取区域编码失败:', error);
      }
    },
    async getCategoryOptions() {
      try {
        const res = await expertClassifier();
        if (res && res.data) {
          this.categoryOptions = res.data;
        }
      } catch (error) {
        console.error('获取专家分类失败:', error);
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate(async valid => {
        if (valid) {
          this.loading = true;
          this.extractionResult = [];

          try {
            // 准备请求参数
            const params = {
              quantity: this.formData.quantity,
            };

            // 添加可选参数
            if (this.formData.area_code && this.formData.area_code.length > 0) {
              params.area_code = this.formData.area_code;
            }

            if (this.formData.category_id && this.formData.category_id.length > 0) {
              params.category_id = this.formData.category_id;
            }

            if (this.formData.level) {
              params.level = this.formData.level;
            }

            if (this.formData.keyWord) {
              params.keyWord = this.formData.keyWord;
            }

            if (this.formData.education) {
              params.education = this.formData.education;
            }

            if (this.formData.gender) {
              params.gender = this.formData.gender;
            }

            if (this.formData.management_element) {
              params.management_element = this.formData.management_element;
            }

            if (this.formData.work_item) {
              params.work_item = this.formData.work_item;
            }

            if (this.formData.item_requirements) {
              params.item_requirements = this.formData.item_requirements;
            }

            // 调用专家抽取接口
            const res = await expertExtraction(params);

            if (res && res.data) {
              this.extractionResult = res.data.list;
              Message({
                type: 'success',
                message: `成功抽取 ${this.extractionResult.length} 名专家！`,
              });
            }
          } catch (error) {
            Message({
              type: 'error',
              message: error.message || '专家抽取失败，请重试',
            });
          } finally {
            this.loading = false;
          }
        } else {
          Message({
            type: 'warning',
            message: '请填写必填项',
          });
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.extractionResult = [];
    },
  },
};
</script>

<style scoped>
.extraction-form-container {
  padding: 20px;
  background-color: #f5f7fa;
  box-sizing: border-box;
  min-height: calc(100vh - 60px);
}

.page-title {
  margin-bottom: 20px;
  font-weight: bold;
}

.form-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.form-section {
  width: 100%;
  margin-bottom: 20px;
  position: relative;
  padding: 15px 10px 5px;
  background-color: #f9fafc;
  border-radius: 6px;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.03);
}

.form-section-title {
  font-size: 15px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 15px;
  padding-left: 10px;
  border-left: 3px solid #409eff;
  display: flex;
  align-items: center;
}

.form-section-title i {
  margin-right: 6px;
  color: #409eff;
  font-size: 16px;
}

.extraction-form {
  display: flex;
  flex-wrap: wrap;
}

.extraction-form .el-form-item {
  margin-right: 20px;
  margin-bottom: 18px;
  width: calc(33.33% - 20px);
}

.extraction-form .content-field {
  width: calc(50% - 20px);
}

/* 工作内容行样式 */
.work-content-row {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  gap: 15px;
  margin-bottom: 15px;
  align-items: flex-start;
}

.work-content-row .work-item {
  flex: 1;
  margin-right: 0;
  min-width: 0;
  width: calc(33.33% - 10px);
}

.work-content-row .el-form-item__label {
  white-space: nowrap;
  width: auto !important;
  text-align: right;
  padding-right: 12px;
}

.work-content-row .el-form-item__content {
  margin-left: 0 !important;
  width: auto !important;
}

.work-content-row .el-textarea__inner {
  height: 80px !important;
  min-height: 80px !important;
}

.extraction-form .full-width-item {
  width: 100%;
  margin-right: 0;
}

/* 基础控件样式 - 已被下方的强制样式覆盖，保留以防样式回退 */
.extraction-form .el-select,
.extraction-form .el-input,
.extraction-form .el-input-number {
  width: 100%;
  box-sizing: border-box;
}

/* 统一表单控件样式 */
.extraction-form .el-input__inner,
.extraction-form .el-select .el-input__inner,
.extraction-form .el-input-number__inner {
  border-radius: 4px;
  border-color: #dcdfe6;
  transition: border-color 0.2s;
  font-size: 13px;
  /* width: auto; */
}

.extraction-form .el-input__inner:hover,
.extraction-form .el-select .el-input__inner:hover,
.extraction-form .el-input-number__inner:hover {
  border-color: #c0c4cc;
}

.extraction-form .el-input__inner:focus,
.extraction-form .el-select .el-input__inner:focus,
.extraction-form .el-input-number__inner:focus {
  border-color: #409eff;
}

/* 统一表单控件高度 */
.extraction-form .el-input__inner,
.extraction-form .el-input-number__decrease,
.extraction-form .el-input-number__increase,
.extraction-form .el-input-number,
.extraction-form .el-select .el-input {
  height: 36px;
  line-height: 36px;
}

/* 确保所有表单项的宽度一致 */
.extraction-form .el-form-item__content {
  width: calc(100% - 120px); /* 减去label宽度 */
  display: inline-block;
}

/* 强制所有表单控件宽度一致 */
.extraction-form .el-input,
.extraction-form .el-select,
.extraction-form .el-input-number,
.extraction-form .el-form-item__content .el-input,
.extraction-form .el-form-item__content .el-select,
.extraction-form .el-form-item__content .el-input-number {
  width: 100% !important;
}

/* 修复数字输入框样式 */
.extraction-form .el-input-number .el-input__inner {
  padding-left: 30px;
  padding-right: 30px;
  text-align: center;
}

/* 修复选择框样式 */
.extraction-form .el-select .el-input {
  width: 100% !important;
}

/* 确保下拉菜单宽度与选择框一致 */
.el-select-dropdown {
  min-width: 100px !important;
}

.extraction-form .el-textarea__inner {
  min-height: 36px;
  padding: 8px 12px;
  font-size: 13px;
  border-radius: 4px;
  resize: none;
}

.extraction-form .content-field .el-textarea__inner {
  min-height: 60px;
}

.extraction-form .full-width-item .el-textarea__inner {
  min-height: 80px;
}

.textarea-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  padding-left: 5px;
}

.textarea-with-counter {
  position: relative;
}

.extraction-form .el-form-item__label {
  color: #606266;
  font-weight: 500;
}

.extraction-form .important-field .el-form-item__label {
  color: #409eff;
}

.extraction-form .el-form-item.is-required .el-form-item__label:before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.form-buttons {
  width: 100%;
  margin-top: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px dashed #ebeef5;
  padding-top: 20px;
}

.form-buttons .el-button {
  padding: 12px 35px;
  font-size: 14px;
  border-radius: 4px;
  margin: 0 15px;
}

.form-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-card-header small {
  color: #909399;
  font-size: 12px;
}

.required-field {
  color: #f56c6c;
  margin: 0 2px;
}

.extraction-result {
  margin-top: 30px;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.divider-title {
  font-size: 16px;
  font-weight: bold;
  color: #409eff;
  display: flex;
  align-items: center;
}

.divider-title i {
  margin-right: 5px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-card {
  text-align: center;
  border: none;
  background-color: rgba(255, 255, 255, 0.8);
  width: 300px;
}

.loading-card p {
  margin: 15px 0 5px;
  font-size: 16px;
  color: #606266;
}

.loading-card small {
  color: #909399;
  font-size: 12px;
}

.result-table {
  margin-bottom: 20px;
}

.result-table .el-table__header-wrapper th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

.result-table .el-table__row:hover {
  background-color: #f0f9ff;
}

.empty-result {
  padding: 30px 0;
  text-align: center;
}

.empty-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 5px;
}

.empty-tip {
  color: #909399;
  font-size: 12px;
}

/* 添加一些动画效果 */
.el-form-item,
.el-card,
.el-alert,
.el-table {
  transition: all 0.3s ease-in-out;
}

.el-button {
  transition: all 0.2s ease;
}

.el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
  .extraction-form .el-form-item {
    width: calc(50% - 20px);
  }

  .work-content-row {
    flex-wrap: wrap;
  }

  .work-content-row .work-item {
    width: calc(50% - 10px);
  }
}

@media screen and (max-width: 768px) {
  .extraction-form .el-form-item {
    width: 100%;
  }

  .work-content-row {
    flex-direction: column;
  }

  .work-content-row .work-item {
    width: 100%;
  }
}
</style>
