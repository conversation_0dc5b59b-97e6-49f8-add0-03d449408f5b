import request from "@root/publicMethods/request";

export const getAreaDict = (params) => {
  return request({
    url: "/manage/quality/getAreaDict",
    method: "get",
    params,
  });
};

export const getCheckListApi = (params) => {
  return request({
    url: "/manage/quality/getCheckList",
    method: "get",
    params,
  });
};

export const getDiagnosticStatistics = (data) => {
  return request({
    url: "/manage/quality/getDiagnosticStatistics",
    method: "post",
    data,
  });
};

export const getCheckDict = (params) => {
  return request({
    url: "/manage/quality/getCheckDict",
    method: "get",
    params,
  });
};

export const getQualityRanking = (params) => {
  return request({
    url: "/manage/quality/getQualityRanking",
    method: "get",
    params,
  });
};
