<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <!-- <el-form :inline="true" :model="searchForm" class="demo-form-inline mb-4">
      <el-form-item label="用人单位名称">
        <el-input v-model="searchForm.cname" placeholder="请输入用人单位名称" clearable />
      </el-form-item>
      <el-form-item label="所在地区">

        <el-cascader @visible-change='visibleChange' v-model="searchForm.area_code"
            :props="workAdd" clearable collapse-tags ref="regAddCas" placeholder="请选择所地区">
        </el-cascader>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form> -->

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      :max-height="650"
      stripe
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px"
      style="width: 100%"
    >

      <el-table-column prop="checkUserUnit" label="执法机构" min-width="100"  width="350"/>
      <el-table-column prop="checkTime" label="执法时间" min-width="100" >
        <template slot-scope="scope">
          {{ formatDate(scope.row.checkTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="confirmText" label="执法结果" min-width="100" />
      <el-table-column prop="clue" label="处罚措施" min-width="100" />
      
      <el-table-column label="操作" width="120" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="handleViewDetail(scope.row)"
          >
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page.current"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
      />
    </div>

    <!-- 详情弹出框 -->
    <el-dialog title="执法数据详情" :visible.sync="dialogTableVisible">
      <div class="detail-container">
        <div
          class="custom-descriptions"
          :style="{ border: border ? '1px solid #ebeef5' : 'none' }"
        >
          <div
            class="custom-descriptions-item"
            v-for="item in showdetail"
            :key="item.label"
          >
            <span class="custom-descriptions-label">{{ item.label }}</span>
            <span class="custom-descriptions-content">
              <el-tag v-if="item.isTag && item.value" type="primary">{{
                item.value
              }}</el-tag>
              <span v-else>{{ item.value || "-" }}</span>
            </span>
          </div>
        </div>
      </div>
      <div class="el-grant-the-staff-foot footer-distance">
        <el-button @click="dialogTableVisible=false " size="middle">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getlist } from '@/api/enterprise';
import axios from 'axios';

export default {
  name: 'EnterpriseList',
  data() {
    return {
      loading: false,
      searchForm: {
        cname: '',
        // districtRegAdd: [],
        industryCategory: [],
        area_code: ''
      },
      industryCategoryOptions: [],
      tableData: [],
      page: {
        current: 1,
        size: 10,
        total: 0
      },
      dialogTableVisible:false,
      showdetail:[
      { label: "执法机构", value: '' ,islink: false},
      { label: "执法时间", value: '' ,islink: false},
      { label: "执法结果", value: '' ,islink: false},
      { label: "处罚措施", value: '' ,islink: false},
      
      ]
    }
  },
  created() {
    this.fetchData();
  },
  methods: {


    // 获取表格数据
    async fetchData() {
      this.loading = true;
      try {
        const params = {
          page: this.page.current,
          pageSize: this.page.size
        };
        
        const response = await getlist(params);
        console.log(response,'response');
        
        if (response.status === 200) {
          this.tableData = response.data.list;
          this.page.total = response.data.total;
          this.page.current = response.data.page;
          this.page.size = response.data.pageSize;
        } else {
          this.$message.error(response.message || '获取数据失败');
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        this.$message.error('获取数据失败');
      }
      this.loading = false;
    },
    // 查询
    handleSearch() {
      this.page.current = 1;
      this.fetchData();
    },
    // 重置表单
    resetForm() {
      this.searchForm = {
        cname: '',
        // districtRegAdd: [],
        area_code: '',
        industryCategory: []
      };
      this.handleSearch();
    },
    // 查看详情
    handleViewDetail(row) {
      console.log(row);
      this.showdetail[0].value=row.checkUserUnit
      this.showdetail[1].value=this.formatDate(row.checkTime)
      this.showdetail[2].value=row.clue
      this.showdetail[3].value=row.companyScale
      this.dialogTableVisible = true;
    },
    // 分页大小改变
    handleSizeChange(val) {
      this.page.size = val;
      this.fetchData();
    },
    // 当前页改变
    handleCurrentChange(val) {
      this.page.current = val;
      this.fetchData();
    },
    formatDate (input) {
      if (!input) return '--'

      // 直接处理 ISO 字符串（如 "2025-05-13T09:11:31.108Z"）
      if (typeof input === 'string' && input.includes('T')) {
        const date = new Date(input)
        if (isNaN(date.getTime())) return 'Invalid Date'

        const padZero = (num) => num.toString().padStart(2, '0')
        const formattedDate = `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`
        const formattedTime = `${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date.getSeconds())}`

        // 如果时分秒为 00:00:00，则只返回日期
        return formattedTime === '00:00:00' ? formattedDate : `${formattedDate} ${formattedTime}`
      }

      // 处理时间戳（兼容旧逻辑）
      const numInput = typeof input === 'string' ? parseFloat(input) : input
      const msTimestamp = numInput > 9999999999 ? numInput : numInput * 1000
      return formatDate(new Date(msTimestamp).toISOString())
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.demo-form-inline {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.mb-2 {
  margin-bottom: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.mt-1 {
  margin-top: 4px;
}

.el-tag + .el-tag {
  margin-left: 8px;
}

:deep(.el-table__empty-block) {
  min-height: 200px;
}
.custom-descriptions {
  width: 100%;
  border-radius: 4px;
  background-color: #fff;
}

.custom-descriptions-item {
  display: flex;
  margin-bottom: 15px;
  align-items: center;
}
.custom-descriptions-label {
  width: 120px;
  color: #909399;
  text-align: right;
  padding-right: 12px;
  flex-shrink: 0;
}
.custom-descriptions-content {
  flex: 1;
  color: #606266;
}
</style> 