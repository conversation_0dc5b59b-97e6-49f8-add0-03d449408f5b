<template>
  <div class="dr-toolbar xxn">
    <el-row>
      <el-col :span="4" class="statisticsWrap">
        <div class="searchYear">
          <span v-if="searchParams.dates && searchParams.dates.length" class="searchDates" :class="bigScreen?'':'smallScreen'">
            {{doMomentTime(searchParams.dates[0])}} 至 {{doMomentTime(searchParams.dates[1])}}
          </span>
          <el-date-picker v-else v-model="searchParams.year" type="year" placeholder="选择年" size="small" format="yyyy年" value-format="yyyy"></el-date-picker>
        </div>
        <div class="statistics">
          <span>项目总数</span>
          <span class="h2">{{totleCount}}</span>
        </div>
      </el-col>
      <!-- 右边搜索 -->
      <el-col :span="20" style="padding-left: 16px;">
        <div class="searchWrap">
          <el-row>
            <el-col class="keyWordsSearchWrap">
              <el-input class="keyWordsSearch" size="small" suffix-icon="el-icon-search" placeholder="用人单位搜索" v-model.trim="keyWords" clearable @keydown.enter.native="$emit('search')"></el-input>
            </el-col>
            <el-col v-show="!bigScreen">
              <el-cascader
                style="width: 100%"
                :props="districtListProps"
                @change="regaddChangeOptionFunc"
                v-model="searchParams.regAddr"
                clearable
                ref="regAddCas"
                size="small" 
                :placeholder="regAddrPlaceholder ? regAddrPlaceholder : '请选择地区'"
              >
                <template slot-scope="{ data }">
                  <span>{{ data.label }}</span>
                </template>
              </el-cascader>
            </el-col>
            <el-col class="operate">
              <el-button plain size="small" @click="$emit('downLoad')">导出</el-button>
              <el-button type="danger" plain size="small" @click="$emit('reset')">重置</el-button>
              <!-- <el-button plain size="small" @click="$emit('openGiveBackRecord')">退回记录</el-button> -->
            </el-col>
          </el-row>

          <el-row>
            <el-col class="chooseTimeType">
              <el-select v-model="searchParams.dateType" size="small" clearable style="width: 100%" placeholder="日期类型：">
                <el-option value="completedTime" label="完成时间: "></el-option>
                <el-option value="applyTime" label="上报时间: "></el-option>
              </el-select>
            </el-col>
            <el-col class="chooseTime">
              <el-date-picker v-model="searchParams.dates" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="small" style="width:100%" :picker-options="pickerOptions"> </el-date-picker>
            </el-col>
            <el-col v-show="bigScreen">
              <el-cascader 
                style="width: 100%"
                :props="districtListProps"
                @change="regaddChangeOptionFunc"
                v-model="searchParams.regAddr"
                clearable
                ref="regAddCas"
                size="small" 
                :placeholder="regAddrPlaceholder ? regAddrPlaceholder : '请选择地区'"
              >
                <template slot-scope="{ data }">
                  <span>{{ data.label }}</span>
                </template>
              </el-cascader>
            </el-col>
            <el-col >
              <el-select v-model="searchParams.org" placeholder="选择/输入机构名称" size="small" clearable style="width: 100%" filterable>
                <el-option value="" label="" >全部机构</el-option>
                <el-option :value="item._id" :label="item.name" v-for="item in orgList" :key="item._id"></el-option>
              </el-select>
            </el-col>
          </el-row>
        </div>
        
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import { getDistrictList, serviceOrgs, getList, getIndustry } from "@/api/serviceOrg";
import moment from 'moment';
import { pickerOptions } from '@root/publicMethods/timePicker';
export default {
  props: {
    searchParams: Object,
    totleCount: Number,
  },
  computed: {
    ...mapGetters(['serviceAreaOptions']),
    //将时间 转换成 简化的
    doMomentTime() {
      return function (nowTime) {
        return moment(nowTime).format("YYYY-MM-DD");
      };
    },
  },
  data() {
    return {
      pickerOptions: pickerOptions,
      regAddrPlaceholder: '', // 地址
      companyList: [], // 用人单位列表
      orgList: [], 
      visible:true,
      keyWords: '', //关键字
      industryOptions: [], // 可选行业
      district: [],
      districtListProps: {
        checkStrictly: true,
        lazy: true,
        lazyLoad(node, resolve) {
          const { level } = node;
          const params = {
            level,
          };
          if (level) {
            params.parent_code = node.value.area_code;
          }
          getDistrictList(params).then(({ data }) => {
            try {
              const nodes = Array.from(data.docs).map((item) => ({
                value: item,
                label: item.name,
                leaf: item.level >= 3,
                disabled: item.name === "市辖区" ? true : false,
              }));
              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          });

          
        },
      },
      // serviceAreaOptions: ['采矿业', '化工、石化及医药', '冶金、建材', '机械制造、电力、纺织、建筑和交通运输等行业领域', '核设施', '核技术应用'],
      bigScreen: document.body.clientWidth > 1400 ? true : false,
    };
  },
  watch: {
    'searchParams.org': {
      handler(orgId) {
        if(orgId){
          let org = this.orgList.filter(ele => ele._id == orgId);
            if(org.length){
              this.searchParams.orgCode = org[0].organization;
            }
           
        }else{
          this.searchParams.orgCode = '';
        }
         // 根据机构id获取企业列表
        // this.getCompanyList(orgId);  
        },
        deep: true
    },
    keyWords(newVal, oldVal){
      if(newVal == oldVal) return;
      setTimeout( ()=> {
        this.searchParams.keyWords = this.keyWords;
      }, 1000)
    }
  },
  created() {
    this.getOrgLits();
    this.keyWords = this.searchParams.keyWords;
     // 获取所有行业分类
    getIndustry().then(res => {
      if(res.status == 200){
        this.industryOptions = res.data || [];
      }
    })
  },
  mounted() {
    const that = this;
    window.onresize = () => {
      return (() => {
          window.screenWidth = document.body.clientWidth
          that.bigScreen = window.screenWidth > 1400 ? true : false;
      })();
    }
  },
  methods: {
    // 获取企业列表
    // getCompanyList(orgId=''){
    //   getList({org: orgId, }).then(res=>{
    //     if(res.status == 200){
    //       this.companyList = res.data;
    //       console.log('companyList:', this.lists);
    //     }
    //   })
    // },
     // 获取机构列表
    getOrgLits(){
      serviceOrgs({limit: 1000}).then(res=>{
        if(res.status == 200){
          this.orgList = res.data;
          // 判断是否已有指定机构
          let hash = location.hash.replace('#', '');
          if(hash){
            this.searchParams.year = '';
            this.searchParams.org = hash;
          }else{
            // this.getCompanyList();
          }
        }
      })
    },
    // 选择注册地
    regaddChangeOptionFunc(v) {
      // console.log(v)
      this.searchParams.regAddr = v || [];
      this. $refs.regAddCas.dropDownVisible = false;
    },
    // 选择行业分类
    industryChangeOptionFunc(v) {
      this.searchParams.industry = v || [];
      this.$refs.industry.dropDownVisible = false;
    },
  },
};
</script>
<style scoped lang="scss">
.dr-toolbar{
  margin-bottom: 16px;
  height: 110px;
  .statisticsWrap{ // 左边
    height: 110px;
    padding: 16px;
    box-sizing: border-box;
    background: url('./../../assets/bj.png') no-repeat;
    background-size: cover;
    .searchDates{ // 选择的时间段
      color: #fff;
      font-size: 15px;
      line-height: 33px;
    }
    .searchDates.smallScreen{
      font-size: 14px;
      line-height: 22px;
    }
    .statistics{
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #fff;
      font-size: 14px;
      margin-top: 11px;
      .h2{
        font-size: 28px;
        font-family: SourceHanSansCN-Bold, SourceHanSansCN;
        letter-spacing: 1px;
      }
    }
  }
  .searchWrap{ // 右边
    z-index: 10000;
    border: 1px solid #ECEEF5;
    box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.06);
    height: 110px;
    box-sizing: border-box;
    padding: 16px 2px 0 16px;
    .operate{
      text-align: right;
    }
    .el-row{
      margin-bottom: 14px;
      display: flex;
      .keyWordsSearchWrap{
        // flex: 0 0 15em;
        max-width: 26%;
      }
      .chooseTimeType{
        flex: 0 0 8em;
      }
      .chooseTime{
        flex: 0 0 22em;
      }
      .el-col{
        padding-right: 14px;
      }
    }
  }
}
</style>
