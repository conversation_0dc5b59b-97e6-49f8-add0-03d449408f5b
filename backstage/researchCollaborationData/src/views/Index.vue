<template>
  <div class="content">
    <div>
      <el-select v-model="timeway" placeholder="请选择时间筛选方式" @change="changeWay">
        <el-option label="时间点" :value="1"></el-option>
        <el-option label="时间段" :value="2"></el-option>
      </el-select>
      &nbsp;
      <el-date-picker v-show="timeway === 1" v-model="search.timePoint" value="yyyy-MM-dd"
        value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
      </el-date-picker>
      &nbsp;
      <el-date-picker v-show="timeway === 2" v-model="dateRange" type="daterange" value="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" range-separator="至"
        start-placeholder="开始日期" end-placeholder="结束日期">
      </el-date-picker>
      &nbsp;
      <el-cascader class="custom-cascader" v-model="search.areaCode" :options="addressList" clearable collapse-tags
        ref="regAddCas" placeholder="请选择地区">
      </el-cascader>
      &nbsp;
      <el-button type="primary" @click="onSearch">查询</el-button>
      <el-button @click="onReset">重置</el-button>
     </div>
     <el-divider></el-divider>
     <div class="metric-cards">
       <div class="card">
         <div class="metric-value">协作数量</div>
         <div class="metric-number">{{collaborationData.length || 0}}</div>
         <!-- <div class="metric-change">+10%</div> -->
       </div>
       <div class="card">
         <div class="metric-value">协作成果数量</div>
         <div class="metric-number">{{collaborationData.length || 0}}</div>
         <!-- <div class="metric-change">+10%</div> -->
       </div>
       <div class="card">
         <div class="metric-value">涉及部门数量</div>
         <div class="metric-number">{{summarizedByUnit.length || 0}}</div>
         <!-- <div class="metric-change">+10%</div> -->
       </div>
     </div>
     <div class="chart-row">
       <div class="chart-col">
         <div ref="chart1" class="chart-container"></div>
       </div>
       <div class="chart-col">
         <div ref="chart2" class="chart-container"></div>
       </div>
     </div>
   <div class="dr-title">
     <span class="dr-title-left">
       <span class="dr-title-text">协作类型展示</span>
     </span>
     <span class="dr-title-divider"><el-divider></el-divider></span>
     <span class="dr-title-btn"></span>
   </div>
   <el-table :data="collaborationData" border style="width: 100%;" header-cell-style="background-color: #f5f7fa; color: #606266; height: 46px">
     <el-table-column prop="name" label="科研项目名称" min-width="220"></el-table-column>
     <el-table-column prop="constructionUnit" label="建设单位" min-width="220"></el-table-column>
     <el-table-column prop="approvalStatus" label="立项审核状态" min-width="100">
       <template slot-scope="{row}">
         <el-tag type="info" v-show="row.approvalStatus == 0">未审核</el-tag>
         <el-tag type="success" v-show="row.approvalStatus == 1">审核通过</el-tag>
         <el-tag type="danger" v-show="row.approvalStatus == 2">审核未通过</el-tag>
       </template>
     </el-table-column>
     <el-table-column prop="resultTitle" label="协作成果" min-width="120" show-overflow-tooltip></el-table-column>
     <el-table-column prop="projectType" label="协作类型" min-width="120" show-overflow-tooltip></el-table-column>
     <el-table-column prop="projectTimePeriod" label="协作时间段" min-width="120" show-overflow-tooltip>
       <template slot-scope="{row}">
         <span>{{formatTimePeriod(row.projectTimePeriod)}}</span>
        </template> 
     </el-table-column>
     <el-table-column prop="deps" label="涉及部门" min-width="120" show-overflow-tooltip></el-table-column>
     <el-table-column prop="applyTime" label="申请时间" min-width="120"></el-table-column>
     <el-table-column prop="reviewOpinion" label="审批意见" min-width="120" show-overflow-tooltip></el-table-column>
   </el-table>
 </div> 
 </template>
 
 <script>
 import * as echarts from 'echarts';
 import { researchDataList,getDistrictList,getUserSession} from "@/api/index";
 export default {
   data() {
     return {
       collaborationData: [],
       summarizedByUnit:[],
       summarizedByTime:[],
       search:{
         page: 1,
         limit: 999999,
         timePoint:'',
         startTime:'',
         endTime:'',
         areaCode: '',
       },
      dateRange: [],
      timeway: 1,
      districtListProps: {
        emitPath: false,
        lazy: true,
        lazyLoad:(node, resolve)=> {
          const { level } = node;
          const params = {
            level,
          };
          if (level) {
            params.parent_code = node.data.area_code;
          }
          // 需要请求地区数据
          getDistrictList(params).then((response) => {
            try {
              const districts = Array.from(response.data.docs);
              districts.unshift({
                area_code:this.userInfo.area_code,
                name:this.userInfo.regAdd[this.userInfo.regAdd.length -1],
                level:1
              })
              let nodes = districts.map((item) => ({
                value: item.area_code,
                label: item.name,
                area_code: item.area_code,
                leaf: item.level >= 1,
                // leaf: item.hasChildren ? item.level >= 2 : item.level >= 3,
                disabled: item.name === '市辖区' ? true : false,
              }));
              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          })
        }
      },
      addressList: [],
      userInfo: {},
     };
   },
   mounted() {
     this.getUserInfo()
   },
   methods: {
    // 获取当前登录者信息
    async getUserInfo(){
      const res =  await getUserSession()
      this.userInfo = res.data && res.data.userInfo || {}
      const {data}  = await getDistrictList({level:0})
      this.addressList = data && data.docs|| []
      if (this.userInfo.regAdd && this.userInfo.area_code) {
        this.addressList.unshift({
          area_code:this.userInfo.area_code,
          name:this.userInfo.regAdd[this.userInfo.regAdd.length -1],
          level:1
        })
        this.addressList = this.addressList.map((item) => ({
          value: item.area_code,
          label: item.name,
          area_code: item.area_code,
          leaf: item.level >= 1,
          disabled: item.name === '市辖区' ? true : false,
        }));
      }
      this.fetchCollaborationData();
    },
    // 格式化项目周期
    formatTimePeriod(period) {
      if (!period || period.length < 2) return '';
      return `${period[0]} ~ ${period[1]}`;
    },
     // 获取科研数据列表
     async fetchCollaborationData() {
      if(!this.search.areaCode) this.search.areaCode = this.userInfo.area_code
      const params = {...this.search}
      params.startTime = this.dateRange && this.dateRange[0] || '';
      params.endTime = this.dateRange && this.dateRange[1] || '';
      params.areaCode = Array.isArray(params.areaCode) ? params.areaCode[0] : params.areaCode;
       try {
         const res = await researchDataList(params);
         this.collaborationData = res.data.data.list
         this.summarizeData();
       } catch (error) {
         console.error("获取协作类型展示数据失败:", error);
       }
     },
     summarizeData() {
       // 按建设单位汇总
       const unitCounts = {};
       this.collaborationData.forEach(item => {
         if (!unitCounts[item.constructionUnit]) {
           unitCounts[item.constructionUnit] = 0;
         }
         unitCounts[item.constructionUnit]++;
       });
       this.summarizedByUnit = Object.keys(unitCounts).map(unit => ({
         constructionUnit: unit,
         count: unitCounts[unit]
       }));
       this.initChart2()
       // 按申请时间汇总
       const timeCounts = {};
       this.collaborationData.forEach(item => {
         const timeKey = item.applyTime.replace(/T/, ' ').replace(/\..+/, ''); // 格式化时间字符串
         if (!timeCounts[timeKey]) {
           timeCounts[timeKey] = 0;
         }
         timeCounts[timeKey]++;
       });
       this.summarizedByTime = Object.keys(timeCounts).map(time => ({
         applyTime: time,
         count: timeCounts[time]
       }));
       this.initChart1()
     },
     initChart1() {
       try {
         const chart = echarts.init(this.$refs.chart1)
         const option = {
           title: { text: '按申请时间汇总协作数量情况' },
           tooltip: { trigger: 'axis' },
           legend: { data: ['协作数量'], top: 20 },
           xAxis: { 
             data: this.summarizedByTime.map(item => item.applyTime),
             axisLabel: { interval: 0, rotate: 30 }
           },
           yAxis: {},
           series: [{ 
             name: '协作数量', 
             type: 'bar', 
             data: this.summarizedByTime.map(item => item.count) 
           }]
         }
         chart.setOption(option)
       } catch (error) {
         console.error('初始化图表失败:', error)
       }
            
     },
     initChart2() {
       try {
         const chart = echarts.init(this.$refs.chart2)
         const option = {
           title: { 
             text: '按建设单位汇总占比',
           },
           tooltip: {
             trigger: 'item'
           },
           series: [{ 
             type: 'pie', 
             radius: '50%',
             data: this.summarizedByUnit.map(item => ({ 
               name: item.constructionUnit, 
               value: item.count 
             })),
             emphasis: {
               itemStyle: {
                 shadowBlur: 10,
                 shadowOffsetX: 0,
                 shadowColor: 'rgba(0, 0, 0, 0.5)'
               }
             }
           }]
         }
         chart.setOption(option)
       } catch (error) {
         console.error('初始化饼图失败:', error)
       }
     },
     onSearch(){
      this.fetchCollaborationData();
     },
     onReset(){
      this.search={
         page: 1,
         limit: 999999,
         timePoint:'',
         startTime:'',
         endTime:'',
         areaCode:this.userInfo.area_code,
       }
       this.dateRange= []
       this.fetchCollaborationData();
     },
     changeWay(){
      this.search.timePoint = ''
      this.search.startTime = ''
      this.search.endTime = ''
      this.dateRange=[]
     }
   },
 
 };
 </script>
 
 <style lang="scss" scoped>
 .content {
   padding: 10px 20px;
 }
 .dr-title {
     display: flex;
     justify-content: space-between;
     align-items: center;
     width: 100%;
     .dr-title-left {
       font-size: 16px;
       font-weight: 500;
       border-left: 6px solid #409eff;
       display: flex;
       height: 24px;
       line-height: 24px;
       .dr-title-text {
         margin-left: 10px;
       }
     }
     .dr-title-divider {
       flex: 1;
       padding: 0 10px;
       el-divider {
       }
     }
   }
 
 .metric-cards {
   display: grid;
   grid-template-columns: repeat(4, 1fr);
   gap: 20px;
   margin-bottom: 20px;
 }
 
 .card {
   background: white;
   padding: 15px;
   border-radius: 5px;
   box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 增加了垂直偏移量和模糊半径，调整了颜色 */
 }
 
 .metric-value {
   color: #666;
   font-size: 14px;
   margin-bottom: 5px;
 }
 
 .metric-number {
   font-size: 20px;
   font-weight: bold;
   margin-bottom: 5px;
 }
 
 .metric-change {
   color: #4CAF50;
   font-size: 12px;
 }
 
 
 .chart-row {
   display: flex;
   gap: 20px;
   margin-bottom: 20px;
 }
 
 .chart-col {
   flex: 1;
   background: white;
   padding: 15px;
   border-radius: 5px;
   box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 增加了垂直偏移量和模糊半径，调整了颜色 */
 }
 
 .chart-container {
   height: 300px;
 }
 </style>