<template>
  <div class="table">
    <TitleTag titleName="查询条件"></TitleTag>
    <el-form inline :model="searchInfo">
      <el-form-item label="患者姓名">
        <el-input v-model.trim="searchInfo.name" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="体检类别">
        <el-select v-model="searchInfo.examType" placeholder="请选择">
          <el-option v-for="item in tjTypeOption" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" @click="handleQuery">查询</el-button>
        <el-button size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <TitleTag titleName="报告列表"></TitleTag>
    <el-table :data="tableData" tooltip-effect="light" style="width: 100%" stripe border
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px">
      <el-table-column type="index" width="50" label="序号" align="center"></el-table-column>
      <el-table-column label="检查机构名称" align="center" prop="physicalOrgName" min-width="150"
      show-overflow-tooltip></el-table-column>
      <el-table-column prop="cname" label="用人单位名称" align="center" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column label="姓名" align="center" prop="name" show-overflow-tooltip></el-table-column>
      <el-table-column label="身份证号" align="center" prop="idNumber" min-width="130">
        <template slot-scope="scope">
          {{ scope.row.idNumber | handletoIdNumber }}
        </template>
      </el-table-column>
      <el-table-column label="联系电话" align="center" prop="phone">
        <template slot-scope="scope">
          {{ scope.row.phone | handletoPhone }}
        </template>
      </el-table-column>
      <el-table-column label="体检危害因素" align="center" prop="checkHazardFactors" min-width="150"
      show-overflow-tooltip>
      <template slot-scope="scope">
          {{ scope.row.checkHazardFactors.map(item => item.name).join(',') }}
        </template>
    </el-table-column>
      <el-table-column label="体检类别" align="center" prop="tjTypeName" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ getExamType(scope.row.examType) }}
        </template>
      </el-table-column>
      <el-table-column label="体检日期" align="center" prop="registerTime" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ getFormatDate(scope.row.registerTime) }}
        </template>
      </el-table-column>
      <el-table-column label="职检结论" align="center" prop="name" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ getHazardConclusionText(scope.row.jobConclusion) }}
        </template>  
      </el-table-column>
    </el-table>
    <div style="margin:10px 0;text-align:center">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="searchInfo.pageNum"
        :page-size="searchInfo.pageSize"
        :page-sizes="[5, 10, 20, 30, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="searchInfo.total">
      </el-pagination>
    </div>
  </div>  
</template>
<script>
import {getHCReportList} from '@/api/index.js'
import moment from 'moment'
import TitleTag from "../components/TitleTag.vue";
export default {
  name: 'TjReport',
  components:{
    TitleTag
  },
  data() {
    return {
      searchInfo:{
        physicalOrgID:'',
        examType:'',
        name:'',
        pageNum:1,
        pageSize:10,
        total:0
      },
      tableData: [],
      tjTypeOption: [
        { label: '岗前', value: '1' },
        { label: '在岗', value: '2' },
        { label: '离岗', value: '3' }
      ],
    }
  },
  created(){
    this.getTableList()
  },
  methods: {
    async getTableList(){
      this.searchInfo.physicalOrgID = this.$route.query.orgId
      const res = await getHCReportList(this.searchInfo)
      this.tableData = res.data.list
      this.searchInfo.total = res.data.total
   },
   handleSizeChange(val){
      this.searchInfo.pageSize = val;
      this.searchInfo.pageNum = 1;
      this.getTableList();
    },
    handleCurrentChange(val){
      this.searchInfo.pageNum = val;
      this.getTableList();
    },
    handleQuery(){
      this.searchInfo.pageNum = 1;
      this.getTableList()
    },
    resetQuery(){
      this.searchInfo = {
        physicalOrgID:'',
        examType:'',
        name:'',
        pageNum:1,
        pageSize:10,
        total:0
      }
      this.getTableList()
    },
    getFormatDate(val) {
      return moment(val).format('YYYY-MM-DD')
    },
    getExamType(val) {
      // 1: 岗前, 2: 在岗, 3: 离岗
      switch (val) {
        case '1':
          return '岗前'
        case '2':
          return '在岗'
        case '3':
          return '离岗'
        default:
          return '未知'
      }
    },
    getHazardConclusionText(conclusions) {
      const conclusionMap = {
        '1': '目前未见异常',
        '2': '复查',
        '3': '疑似职业病',
        '4': '职业禁忌证',
        '5': '其他疾病或异常',
      };

      if (Array.isArray(conclusions)) {
        // 使用 filter 移除空字符串，然后 join
        const result = conclusions
          .map(conclusion => conclusionMap[conclusion] || '')
          .filter(text => text !== ''); // 过滤掉空字符串
        return result.join(', ');
      } else {
        return conclusionMap[conclusions] || '';
      }
    },
  }
}
</script>
<style lang="scss" scoped>

</style>