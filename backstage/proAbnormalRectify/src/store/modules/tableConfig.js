
const state = {
  tableConfig: {
    title: '防护用品使用情况异常列表',
    needAdd: true,
    border: true,
    needExport: true,
    noDelete: true,
  }, // 表单页面配置
  topBarInfo: [
    {
      title: '关键字查询',
      prop: 'keyword',
      type: 'input',
      remote: true,
    },
    // {
    //   title: '是否有异常',
    //   prop: 'has_abnormal',
    //   type: 'select',
    //   options: [
    //     {
    //       label: '正常',
    //       value: true,
    //     },
    //     {
    //       label: '异常',
    //       value: false,
    //     },
    //   ],
    // },
  ], // 搜索栏内容
  tableHeader: [
    {
      label: '巡查单位',
      prop: 'createUnitName',
      align: 'center',
      width: '100',
      type: 'string',
    },
    { label: '巡查人员', prop: 'creator', align: 'center', width: '90' },
    {
      label: '巡查时间',
      prop: 'recordedAt',
      align: 'center',
      width: '140',
      type: 'date',
    },
    {
      label: '用人单位',
      prop: 'EnterpriseID',
      align: 'center',
      width: '140',
      remote: true,
      type: 'select',
    },
    {
      label: '劳动者',
      prop: 'employees',
      align: 'center',
      width: '120',
      type: 'array',
    },
    {
      label: '防护用品类型',
      prop: 'content',
      width: '160',
      align: 'center',
      type: 'array',
    },
    {
      label: '穿戴正确',
      prop: 'wornCorrectly',
      width: '90',
      align: 'center',
      type: 'bool',
      options: [ '正确', '错误' ],
    },
    {
      label: '是否异常',
      prop: 'has_abnormal',
      width: '90',
      align: 'center',
      type: 'bool',
      options: [ '正常', '异常' ],
    },
    { label: '异常描述', prop: 'description', align: 'left', minWidth: '180' },
    { label: '整改文件', prop: 'rectify_file', width: '160', align: 'center' },
    {
      label: '整改状态',
      prop: 'status',
      width: '90',
      align: 'center',
      type: 'string',
      fixed: 'right',
    },
    {
      prop: 'operation',
      label: '操作',
      width: '200',
      align: 'center',
      fixed: 'right',
    },
  ], // 表头信息
  dialogFormInfo: [
    {
      label: '巡查人员',
      prop: 'creator',
    },
    {
      label: '巡查时间',
      prop: 'recordedAt',
      type: 'date',
    },
    {
      label: '用人单位',
      prop: 'EnterpriseID',
      type: 'select',
      options: [
        {
          label: '用人单位',
          value: 'EnterpriseID',
        },
      ],
      placeholder: '请选择用人单位',
      remote: true,
    },
    {
      label: '劳动者',
      prop: 'employees',
      placeholder: '请输入劳动者姓名',
    },
    {
      label: '用品类型',
      prop: 'content',
    },
    {
      label: '穿戴正确',
      prop: 'wornCorrectly',
      type: 'bool',
      options: [ '正确', '错误' ],
    },
    // {
    //   label: '是否异常',
    //   prop: 'has_abnormal',
    //   type: 'bool',
    //   options: [ '正常', '异常' ],
    // },
    // {
    //   label: '整改状态',
    //   prop: 'status',
    //   type: 'string',
    // },
    { label: '异常描述', prop: 'description', type: 'textarea' },
  ], // 对话框内容
  dialogRules: {
    creator: [
      { required: true, message: '请输入巡查人员', trigger: 'blur' },
    ],

    employees: [{ required: true, message: '请输入劳动者', trigger: 'blur' }],
    equipment_type: [
      { required: true, message: '请选择防护用品类型', trigger: 'blur' },
    ],
    wornCorrectly: [
      { required: true, message: '请确认是否穿戴正确', trigger: 'blur' },
    ],
    // has_abnormal: [
    //   { required: true, message: '请确认是否异常', trigger: 'blur' },
    // ],
    description: [
      { required: true, message: '请输入异常描述', trigger: 'blur' },
    ],
    recordedAt: [
      { required: true, message: '请选择汇报时间', trigger: 'blur' },
    ],
  }, // 对话框规则
};

const mutations = {};

const actions = {};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
