<template>
  <div class="table-row_item">
    <div v-if="prop === 'createUnitName'">
        {{ row[prop] }}
    </div>
    <!-- 巡查人员 -->
    <div v-if="prop === 'creator'">
        {{ row[prop] }}
    </div>
    <div v-if="prop === 'EnterpriseID'">
        {{ row[prop] ? row[prop].cname : '' }}
    </div>
    <!-- 劳动者 -->
    <div v-if="prop === 'employees'">
      <el-tag
        v-for="(tag, index) in row[prop]"
        :key="index"
        size="mini"
        style="margin-right: 5px"
      >
        {{ tag }}
      </el-tag>
    </div>
    <!-- 防护用品 -->
    <div v-if="prop === 'content'">
      <el-tag
        v-for="(tag, index) in row[prop]"
        :key="index"
        size="mini"
        style="margin-right: 5px"
      >
        {{ tag }}
      </el-tag>
    </div>
    <!-- 穿戴正确 -->
    <div v-if="prop === 'wornCorrectly'">
      <el-tag
        :type="row.wornCorrectly ? 'success' : 'danger'"
        size="mini"
        class="round-tag"
        effect="plain"
      >
        {{ row.wornCorrectly ? "正确" : "错误" }}
      </el-tag>
    </div>
    <!-- 是否异常 -->
    <div v-if="prop === 'has_abnormal'">
      <el-tag
        :type="row[prop] ? 'success' : 'danger'"
        size="mini"
        class="round-tag"
        effect="plain"
      >
        {{ row[prop] ? "正常" : "异常" }}
      </el-tag>
    </div>
    <!-- 异常描述 -->
    <div v-if="prop === 'description'">
      {{ row[prop] }}
    </div>
    <!-- 汇报时间 -->
    <div v-if="prop === 'recordedAt'">
      {{ row[prop] }}
    </div>
    <!-- 整改文件 -->
    <div v-if="prop === 'rectify_file'">
      <el-upload
        ref="upload"
        accept=".pdf,.png,.jpg,.jpeg"
        :auto-upload="false"
        action="#"
        :file-list="row['files'] || fileList"
        :on-change="handleChangeFile"
        :on-remove="handleRemoveFile"
        :on-preview="handlePreviewFile"
        :before-remove="beforeRemoveFile"
        multiple
        :limit="3"
        :on-exceed="handleExceed"
      >
        <el-button size="small" type="text">点击上传</el-button>
      </el-upload>
    </div>
    <div v-if="prop === 'status'">
      <el-tag
        :type="['warning', 'success', 'danger', 'info'][+row[prop]]"
        size="mini"
        class="round-tag"
        effect="plain"
        style="margin-right: 5px"
      >
        {{ ['待整改', '已整改', '已驳回', '已完成'][+row[prop]] }}
      </el-tag>
    </div>
    <!-- 操作 -->
    <div v-if="prop === 'operation'">
      <el-button
        class="btn"
        @click="handleEdit(row, 'edit')"
        type="primary"
        size="mini"
        plain
        :disabled = "!row.editFlag"
      >
        <span class="btn-text">编辑</span>
      </el-button>
      <el-button
        class="btn"
        @click="handleEdit(row, 'del')"
        type="danger"
        size="mini"
        plain
        :disabled = "!row.editFlag"
      >
        <span class="btn-text">删除</span>
      </el-button>
      <el-button
        class="btn"
        @click="handleEdit(row, 'edit2')"
        type="warning"
        size="mini"
        plain
        :disabled = "!row.editFlag || row.status !== 1"
      >
        <span class="btn-text">整改处置</span>
      </el-button>
    </div>
  </div>
</template>

<script>
import { mapActions } from "vuex";
export default {
  props: {
    row: Object,
    prop: String,
    column: Object,
  },
  data() {
    return {
      fileList: [],
    };
  },
  mounted() {},
  methods: {
    ...mapActions("index", ["handleUploadFile", "getList", "delFile", "updateData"]),
    handleChangeFile(file, fileList) {
      this.fileList = fileList;
      const _id = this.row._id;
      // 处理文件上传
      const formData = new FormData();
      formData.append("_id", _id);
      formData.append("file", file.raw);
      this.handleUploadFile(formData);
      setTimeout(() => {
        this.getList();
      }, 500);
      
    },
    handleRemoveFile(file, fileList) {
      console.log(11111, file, fileList);
      this._id = this.row._id;
      const params = { _id: this._id, url: file.url };
      this.delFile(params).then((res) => {
        this.$message({
          message: "操作成功",
          type: "success",
        });
        this.getList();
      });
    },
    handlePreviewFile(file) {
      const { url } = file;
      window.open(url);
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
    beforeRemoveFile(file, fileList) {
      return this.$confirm(`确定移除 ${ file.name }？`);
    },
    // 操作
    handleEdit(value, type) {
      if(type === 'edit2') {
        this.$confirm(`确认用人单位${value.EnterpriseID.cname}整改完成吗？`, '提示', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确认完成',
          cancelButtonText: '驳回整改',
          type: 'warning',
        }).then(() => {
          this.updateData({
            _id: value._id,
            status: 3,
          }).then(res => {
            this.$message({
              message: '操作成功',
              type: 'success',
            });
            this.getList();
          })
        }).catch(action => {
          if (action === 'close') {
            this.$message({
              message: '已取消操作',
              type: 'info',
            });
          } else if (action === 'cancel') {
            this.updateData({
              _id: value._id,
              status: 2,
            }).then(res => {
              this.$message({
                message: '操作成功',
                type: 'success',
              });
              this.getList();
            })
          }
        });
        return;
      }
      console.log(value, type, "操作");
      const params = { payload: value, type };
      this.$emit("handleTableEdit", params);
    },
  },
};
</script>

<style lang="scss" scoped>
.table-row_item {
  div .el-tag {
    cursor: default;
  }

  .round-tag {
    border-radius: 10px; /* 圆角 */
  }

  .button-box {
    .btn {
      .btn-text {
        margin: auto 4px;
      }
    }
  }
}
</style>
