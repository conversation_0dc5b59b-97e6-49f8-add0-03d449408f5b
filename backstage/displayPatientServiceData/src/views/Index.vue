<template>
<div class="content">
  <div>
      <el-select v-model="timeway" placeholder="请选择时间筛选方式" @change="changeWay">
        <el-option label="时间点" :value="1"></el-option>
        <el-option label="时间段" :value="2"></el-option>
      </el-select>
      &nbsp;
      <el-date-picker v-show="timeway === 1" v-model="search.startDate" value="yyyy-MM-dd"
        value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
      </el-date-picker>
      &nbsp;
      <el-date-picker v-show="timeway === 2" v-model="dateRange" type="daterange"value="yyyy-MM-dd HH:mm:ss"
      value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']"  range-separator="至"
        start-placeholder="开始日期" end-placeholder="结束日期">
      </el-date-picker>
      &nbsp;
      <!-- <el-cascader class="custom-cascader" v-model="search.areaCode" :props="districtListProps" clearable collapse-tags
        ref="regAddCas" placeholder="请选择地区">
      </el-cascader> -->
      &nbsp;
      <el-button type="primary" @click="onSearch">查询</el-button>
      <el-button @click="onReset">重置</el-button>
    </div>
    <el-divider></el-divider>
  <div class="metric-cards">
    <div class="card">
      <div class="metric-value">就诊预约总人次</div>
      <div class="metric-number">{{ totalAppointments }}</div>
    </div>
    <div class="card">
      <div class="metric-value">诊疗服务总人次</div>
      <div class="metric-number">{{ totalTreatments }}</div>
    </div>
    <div class="card">
      <div class="metric-value">用药服务总人次</div>
      <div class="metric-number">{{ totalMedications }}</div>
    </div>
    <div class="card">
      <div class="metric-value">随访记录总人次</div>
      <div class="metric-number">{{ totalFollowUps }}</div>
    </div>
    <div class="card">
      <div class="metric-value">康复指导总人次</div>
      <div class="metric-number">{{ totalRecoveries }}</div>
    </div>
  </div>
    <!-- 就诊预约 -->
    <div class="dr-title">
      <span class="dr-title-left">
        <span class="dr-title-text">就诊预约服务</span>
      </span>
      <span class="dr-title-divider"><el-divider></el-divider></span>
      <span class="dr-title-btn"></span>
    </div>
    <div class="chart-row">
      <div class="chart-col">
        <BarChart
          title="按区域统计就诊预约人次"
          :data="appointmentDatabyArea"
          x-axis-name="区域"
          y-axis-name="人次"
          color="#87CEEB"
        />
      </div>
      <div class="chart-col">
        <PieChart
          title="按职业病统计就诊预约人次(TOP10)"
          :data="appointmentDatabyDiseaseType.slice(0, 10)"
        />
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-col">
        <PieChart
          title="按行业统计就诊预约人次(TOP10)"
          :data="appointmentDatabyIndustry.slice(0, 10)"
        />
      </div>
      <div class="chart-col">
        <PieChart
          title="按机构统计就诊预约人次(TOP10)"
          :data="appointmentDatabyStation.slice(0, 10)"
        />
      </div>
    </div>
  <!-- 诊疗服务 -->
  <div class="dr-title">
    <span class="dr-title-left">
      <span class="dr-title-text">诊疗服务情况</span>
    </span>
    <span class="dr-title-divider"><el-divider></el-divider></span>
    <span class="dr-title-btn"></span>
  </div>
    <div class="chart-row">
      <div class="chart-col">
        <BarChart
          title="各区域诊疗服务人次"
          :data="treatmentDataByArea"
          x-axis-name="区域"
          y-axis-name="人次"
          :color="getRandomColor()"
        />
      </div>
      <div class="chart-col">
        <PieChart
          title="按职业病统计诊疗服务人次(TOP10)"
          :data="treatmentDataByDiseaseType.slice(0, 10)"
        />
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-col">
        <PieChart
          title="按行业统计诊疗服务人次(TOP10)"
          :data="treatmentDataByIndustry.slice(0, 10)"
        />
      </div>
      <div class="chart-col">
        <PieChart
          title="按机构统计诊疗服务人次(TOP10)"
          :data="treatmentDataByStation.slice(0, 10)"
        />
      </div>
    </div>
    <!-- 用药服务 -->
    <div class="dr-title">
    <span class="dr-title-left">
      <span class="dr-title-text">用药服务情况</span>
    </span>
    <span class="dr-title-divider"><el-divider></el-divider></span>
    <span class="dr-title-btn"></span>
  </div>
  <div class="chart-row">
      <div class="chart-col">
        <BarChart
          title="各区域用药服务人次"
          :data="medicationCountByArea"
          x-axis-name="地区"
          y-axis-name="人次"
          :color="getRandomColor()"
        />
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-col">
        <BarChart
          title="各类药品用药情况"
          :data="medicationCountByMedicineType"
          x-axis-name="药品类型"
          y-axis-name="人次"
          :color="getRandomColor()"
        />
      </div>
      <div class="chart-col">
        <BarChart
          title="各地区平均用药费用"
          :data="medicationAveragePriceByArea"
          x-axis-name="地区"
          y-axis-name="费用(元)"
          :color="getRandomColor()"
        />
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-col">
        <PieChart
          title="按机构统计平均用药费用(TOP10)"
          :data="medicationAveragePriceByStation.slice(0, 10)"
        />
      </div>
      <div class="chart-col">
        <PieChart
          title="按职业病统计用药服务人次(TOP10)"
          :data="medicationCountByDiseaseType.slice(0, 10)"
        />
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-col">
        <PieChart
          title="按行业统计用药服务人次(TOP10)"
          :data="medicationCountByIndustry.slice(0, 10)"
        />
      </div>
      <div class="chart-col">
        <PieChart
          title="按机构统计用药服务人次(TOP10)"
          :data="medicationCountByStation.slice(0, 10)"
        />
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-col">
        <PieChart
          title="按职业病统计平均用药费用(TOP10)"
          :data="medicationAveragePriceByDiseaseType.slice(0, 10)"
        />
      </div>
      <div class="chart-col">
        <PieChart
          title="按行业统计平均用药费用(TOP10)"
          :data="medicationAveragePriceByIndustry.slice(0, 10)"
        />
      </div>
    </div>
    <!-- 随访记录 -->
    <div class="dr-title">
    <span class="dr-title-left">
      <span class="dr-title-text">跟踪随访情况</span>
    </span>
    <span class="dr-title-divider"><el-divider></el-divider></span>
    <span class="dr-title-btn"></span>
  </div>
    <div class="chart-row">
      <div class="chart-col">
        <BarChart
          title="各区域随访记录人次"
          :data="followUpDataByArea"
          x-axis-name="区域"
          y-axis-name="人次"
          :color="getRandomColor()"
        />
      </div>
      <div class="chart-col">
        <PieChart
          title="按职业病统计随访记录人次(TOP10)"
          :data="followUpDataByDiseaseType.slice(0, 10)"
        />
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-col">
        <PieChart
          title="按行业统计随访记录人次(TOP10)"
          :data="followUpDataByIndustry.slice(0, 10)"
        />
      </div>
      <div class="chart-col">
        <PieChart
          title="按阶段统计随访记录人次(TOP10)"
          :data="followUpDataByStage.slice(0, 10)"
        />
      </div>
    </div>

    <!-- 康复指导 -->
    <div class="dr-title">
    <span class="dr-title-left">
      <span class="dr-title-text">健康指导服务</span>
    </span>
    <span class="dr-title-divider"><el-divider></el-divider></span>
    <span class="dr-title-btn"></span>
  </div>
    <div class="chart-row">
      <div class="chart-col">
        <BarChart
          title="各区域康复指导人次"
          :data="recoveryDataByArea"
          x-axis-name="区域"
          y-axis-name="人次"
          :color="getRandomColor()"
        />
      </div>
      <div class="chart-col">
        <PieChart
          title="按职业病统计康复指导人次(TOP10)"
          :data="recoveryDataByDiseaseType.slice(0, 10)"
        />
      </div>
    </div>
    <div class="chart-row">
      <div class="chart-col">
        <PieChart
          title="按行业统计康复指导人次(TOP10)"
          :data="recoveryDataByIndustry.slice(0, 10)"
        />
      </div>
      <div class="chart-col">
        <PieChart
          title="按机构统计康复指导人次(TOP10)"
          :data="recoveryDataByStation.slice(0, 10)"
        />
      </div>
    </div>
</div>
</template>

<script>
import * as echarts from 'echarts'
import BarChart from '@/components/barChart.vue'
import PieChart from '@/components/pieChart.vue'
import { 
  getUserSession,getDistrictList,getOccupationDisease,
  appointmentByArea, appointmentByDiseaseType, appointmentByIndustry, 
  appointmentByStation, medicationGuidanceCountByArea,
  medicationGuidanceCountByDiseaseType, medicationGuidanceCountByIndustry, medicationGuidanceCountByMedicineType,
  medicationGuidanceCountByStation, followUpByArea, followUpByDiseaseType, followUpByIndustry, followUpByStage,
  treatmentInformationCountByArea,treatmentInformationCountByDiseaseType,treatmentInformationCountByIndustry,treatmentInformationCountByStation,
  recoveryInfoByArea, recoveryInfoByDiseaseType, recoveryInfoByIndustry, recoveryInfoByStation,
  averagePriceByArea, averagePriceByDiseaseType, averagePriceByIndustry, averagePriceByStation
} from '@/api/index'

export default {
  components: { BarChart, PieChart },
  data() {
    return {      
      appointmentDatabyArea: [],
      appointmentDatabyDiseaseType: [],
      appointmentDatabyIndustry: [],
      appointmentDatabyStation: [],
      medicationCountByArea: [],
      medicationCountByDiseaseType: [],
      medicationCountByIndustry: [],
      medicationCountByStation: [],
      medicationCountByMedicineType: [],
      medicationAveragePriceByArea: [],
      medicationAveragePriceByDiseaseType: [],
      medicationAveragePriceByIndustry: [],
      medicationAveragePriceByStation: [],
      totalAppointments: 0,
      totalTreatments: 0,
      totalMedications: 0,
      totalFollowUps: 0,
      totalRecoveries: 0,
      // 诊疗服务数据
      treatmentDataByArea: [],
      treatmentDataByDiseaseType: [],
      treatmentDataByIndustry: [],
      treatmentDataByStation: [],
      // 随访记录数据
      followUpDataByArea: [],
      followUpDataByDiseaseType: [],
      followUpDataByIndustry: [],
      followUpDataByStage: [],
      // 康复指导数据
      recoveryDataByArea: [],
      recoveryDataByDiseaseType: [],
      recoveryDataByIndustry: [],
      recoveryDataByStation: [],
      areaCode:'',
      search:{
         page: 1,
         limit: 999999,
         startDate:'',
         endDate:'',
         administerArea: '',
       },
       dateRange: [],
       timeway: 1,
       districtListProps: {
        emitPath: false,
        lazy: true,
        lazyLoad(node, resolve) {
          const { level } = node;
          const params = {
            level,
          };
          if (level) {
            params.parent_code = node.data.area_code;
          }
          // 需要请求地区数据
          getDistrictList(params).then((response) => {
            try {
              const districts = Array.from(response.data.docs);
              let nodes = districts.map((item) => ({
                value: item.area_code,
                label: item.name,
                area_code: item.area_code,
                leaf: item.level >= 1,
                // leaf: item.hasChildren ? item.level >= 2 : item.level >= 3,
                disabled: item.name === '市辖区' ? true : false,
              }));
              resolve(nodes);
            } catch (e) {
              console.log(e);
            }
          })
        }
      },
      occupationDiseaseList: [],
    }
  },
  created() {
    this.getOccDiseaseList()
    this.getUserInfo()
    // this.fetchData()
  },
  methods: {
    // 获取职业病种类
    getOccDiseaseList(){
      getOccupationDisease().then((res) => {
        this.occupationDiseaseList = res.data.data;
      });
    },
    getDiseaseName(code) {
      // 递归查找树形结构中的节点
      const findInTree = (tree, code) => {
        if (!tree || !Array.isArray(tree)) return null;
        
        for (let node of tree) {
          if (node.dictCode === code) {
            return node.dictLabel;
          }
          if (node.children && node.children.length > 0) {
            const result = findInTree(node.children, code);
            if (result) {
              return result;
            }
          }
        }
        return null; // 如果没有找到匹配的节点，返回 null
      };
      
      return findInTree(this.occupationDiseaseList, code) || '';
    },
    async getUserInfo() {
      try {
        const res = await getUserSession()
        if(res.data.userInfo) {
          this.areaCode = res.data.userInfo.area_code      
          this.fetchData()
        } else {
          console.error('获取区域码失败')
          this.$message.error('获取用户信息失败')
        }
      } catch (error) {
        console.error('获取用户会话失败:', error)
        this.$message.error('获取用户信息失败')
      }
    },
    getRandomColor() {
      const colors = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', 
        '#FFEEAD', '#D4A5A5', '#9B59B6', '#3498DB',
        '#1ABC9C', '#F1C40F', '#E67E22', '#E74C3C'
      ];
      return colors[Math.floor(Math.random() * colors.length)];
    },
    async fetchData() {
      try {
        const params = {...this.search}
        if(this.timeway == 2) {
          params.startDate = this.dateRange && this.dateRange[0] || '';
          params.endDate = this.dateRange && this.dateRange[1] || '';
        } else {
          params.startDate = this.search.startDate
          params.endDate = '';
        }
        params.administerArea = this.areaCode
        const [
          areaRes, diseaseRes, industryRes, stationRes,
          medicationAreaRes, medicationDiseaseRes, medicationIndustryRes, 
          medicationMedicineRes, medicationStationRes,
          avgPriceAreaRes, avgPriceDiseaseRes, avgPriceIndustryRes, avgPriceStationRes,
          treatmentAreaRes, treatmentDiseaseRes, treatmentIndustryRes, treatmentStationRes,
          // 随访记录
          followUpAreaRes, followUpDiseaseRes, followUpIndustryRes, followUpStageRes,
          // 康复指导
          recoveryAreaRes, recoveryDiseaseRes, recoveryIndustryRes, recoveryStationRes
        ] = await Promise.all([
   
          // 就诊预约
          appointmentByArea(params),
          appointmentByDiseaseType(params),
          appointmentByIndustry(params),
          appointmentByStation(params),
          // 用药服务
          medicationGuidanceCountByArea(params),
          medicationGuidanceCountByDiseaseType(params),
          medicationGuidanceCountByIndustry(params),
          medicationGuidanceCountByMedicineType(params),
          medicationGuidanceCountByStation(params),
          averagePriceByArea(params),
          averagePriceByDiseaseType(params),
          averagePriceByIndustry(params),
          averagePriceByStation(params),
          // 诊疗服务
          treatmentInformationCountByArea(params),
          treatmentInformationCountByDiseaseType(params),
          treatmentInformationCountByIndustry(params),
          treatmentInformationCountByStation(params),
          // 随访记录
          followUpByArea(params),
          followUpByDiseaseType(params),
          followUpByIndustry(params),
          followUpByStage(params),
          // 康复指导
          recoveryInfoByArea(params),
          recoveryInfoByDiseaseType(params),
          recoveryInfoByIndustry(params),
          recoveryInfoByStation(params)
        ])

        // 设置就诊预约数据
        this.appointmentDatabyArea = areaRes.data.data
        this.appointmentDatabyDiseaseType =  diseaseRes.data && diseaseRes.data.data.map(item=>{
          return{
            key: this.getDiseaseName(item.key),
            value: item.value,
          }
        })
        this.appointmentDatabyIndustry = industryRes.data.data
        this.appointmentDatabyStation = stationRes.data.data

        // 设置用药服务数据
        this.medicationCountByArea = medicationAreaRes.data.data
        this.medicationCountByDiseaseType = medicationDiseaseRes.data && medicationDiseaseRes.data.data.map(item=>{
          return{
            key: this.getDiseaseName(item.key),
            value: item.value,
          }
        })
        this.medicationCountByIndustry = medicationIndustryRes.data.data
        this.medicationCountByMedicineType = medicationMedicineRes.data.data
        this.medicationCountByStation = medicationStationRes.data.data

        // 设置平均用药费用数据
        this.medicationAveragePriceByArea = avgPriceAreaRes.data.data        
        this.medicationAveragePriceByDiseaseType = avgPriceDiseaseRes.data && avgPriceDiseaseRes.data.data.map(item=>{
          return{
            key: this.getDiseaseName(item.key),
            value: item.value,
          }
        })
        this.medicationAveragePriceByIndustry = avgPriceIndustryRes.data.data
        this.medicationAveragePriceByStation = avgPriceStationRes.data.data

        // 设置诊疗服务数据
        this.treatmentDataByArea = treatmentAreaRes.data.data
        this.treatmentDataByDiseaseType = treatmentDiseaseRes.data.data
        this.treatmentDataByIndustry = treatmentIndustryRes.data.data
        this.treatmentDataByStation = treatmentStationRes.data.data

        // 设置随访记录数据
        this.followUpDataByArea = followUpAreaRes.data.data
        this.followUpDataByDiseaseType = followUpDiseaseRes.data && followUpDiseaseRes.data.data.map(item=>{
          return{
            key: this.getDiseaseName(item.key),
            value: item.value,
          }
        })
        this.followUpDataByIndustry = followUpIndustryRes.data.data
        this.followUpDataByStage = followUpStageRes.data.data

        // 设置康复指导数据
        this.recoveryDataByArea = recoveryAreaRes.data.data
        this.recoveryDataByDiseaseType = recoveryDiseaseRes.data.data
        this.recoveryDataByIndustry = recoveryIndustryRes.data.data
        this.recoveryDataByStation = recoveryStationRes.data.data

        // Calculate totals
        this.totalAppointments = this.calculateTotal(areaRes.data.data)
        this.totalMedications = this.calculateTotal(medicationAreaRes.data.data)
        this.totalTreatments = this.calculateTotal(treatmentAreaRes.data.data)
        this.totalFollowUps = this.calculateTotal(followUpAreaRes.data.data)
        this.totalRecoveries = this.calculateTotal(recoveryAreaRes.data.data)

      } catch (error) {
        console.error('获取数据失败:', error)
        this.$message.error('获取数据失败')
      }
    },
    calculateTotal(data) {
      if (!Array.isArray(data)) return 0
      return data && data.reduce((sum, item) => sum + Number(item.value), 0)
    },
    onSearch(){
      this.fetchData();
     },
     onReset(){
      this.search={
         page: 1,
         limit: 999999,
         startDate:'',
         endDate:'',
       }
       this.dateRange= []
       this.$forceUpdate()
       this.fetchData();
     },
     changeWay(){
      this.search.startDate = ''
      this.search.endDate = ''
      this.dateRange=[]
     }
  }
}
</script>
<style lang="scss" scoped>
.content {
  padding: 10px 20px;
}
.dr-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    .dr-title-left {
      font-size: 16px;
      font-weight: 500;
      border-left: 6px solid #409eff;
      display: flex;
      height: 24px;
      line-height: 24px;
      .dr-title-text {
        margin-left: 10px;
      }
    }
    .dr-title-divider {
      flex: 1;
      padding: 0 10px;
      el-divider {
      }
    }
  }


.metric-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.card {
  background: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 增加了垂直偏移量和模糊半径，调整了颜色 */
}

.metric-value {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.metric-number {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
}

.metric-change {
  color: #4CAF50;
  font-size: 12px;
}


.chart-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-col {
  flex: 1;
  background: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 增加了垂直偏移量和模糊半径，调整了颜色 */
}

.chart-container {
  height: 300px;
}
</style>