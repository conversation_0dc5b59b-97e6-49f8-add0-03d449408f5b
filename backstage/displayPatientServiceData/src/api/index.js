import request from '@root/publicMethods/request';

export function getUserSession(params) {
  return request({
    url: '/manage/getUserSession',
    method: 'get',
    params,
  });
}

// 职业病病人全程服务数据展示 - 就诊预约
export function appointmentByArea(params) {
  return request({
    url: '/manage/display/appointmentByArea',
    method: 'get',
    params,
  });
}

export function appointmentByDiseaseType(params) {
  return request({
    url: '/manage/display/appointmentByDiseaseType',
    method: 'get',
    params,
  });
}

export function appointmentByIndustry(params) {
  return request({
    url: '/manage/display/appointmentByIndustry',
    method: 'get',
    params,
  });
}

export function appointmentByStation(params) {
  return request({
    url: '/manage/display/appointmentByStation',
    method: 'get',
    params,
  });
}

// 职业病病人全程服务数据展示 - 诊疗服务
export function treatmentInformationCountByArea(params) {
  return request({
    url: '/manage/display/treatmentInformationCountByArea',
    method: 'get',
    params,
  });
}

export function treatmentInformationCountByDiseaseType(params) {
  return request({
    url: '/manage/display/treatmentInformationCountByDiseaseType',
    method: 'get',
    params,
  });
}

export function treatmentInformationCountByIndustry(params) {
  return request({
    url: '/manage/display/treatmentInformationCountByIndustry',
    method: 'get',
    params,
  });
}

export function treatmentInformationCountByStation(params) {
  return request({
    url: '/manage/display/treatmentInformationCountByStation',
    method: 'get',
    params,
  });
}

// 职业病病人全程服务数据展示 - 用药服务
export function averagePriceByArea(params) {
  return request({
    url: '/manage/display/averagePriceByArea',
    method: 'get',
    params,
  });
}

export function averagePriceByDiseaseType(params) {
  return request({
    url: '/manage/display/averagePriceByDiseaseType',
    method: 'get',
    params,
  });
}

export function averagePriceByIndustry(params) {
  return request({
    url: '/manage/display/averagePriceByIndustry',
    method: 'get',
    params,
  });
}

export function averagePriceByStation(params) {
  return request({
    url: '/manage/display/averagePriceByStation',
    method: 'get',
    params,
  });
}

export function medicationGuidanceCountByArea(params) {
  return request({
    url: '/manage/display/medicationGuidanceCountByArea',
    method: 'get',
    params,
  });
}

export function medicationGuidanceCountByDiseaseType(params) {
  return request({
    url: '/manage/display/medicationGuidanceCountByDiseaseType',
    method: 'get',
    params,
  });
}

export function medicationGuidanceCountByIndustry(params) {
  return request({
    url: '/manage/display/medicationGuidanceCountByIndustry',
    method: 'get',
    params,
  });
}

export function medicationGuidanceCountByMedicineType(params) {
  return request({
    url: '/manage/display/medicationGuidanceCountByMedicineType',
    method: 'get',
    params,
  });
}

export function medicationGuidanceCountByStation(params) {
  return request({
    url: '/manage/display/medicationGuidanceCountByStation',
    method: 'get',
    params,
  });
}

// 职业病病人全程服务数据展示 - 随访记录
export function followUpByArea(params) {
  return request({
    url: '/manage/display/followUpByArea',
    method: 'get',
    params,
  });
}

export function followUpByDiseaseType(params) {
  return request({
    url: '/manage/display/followUpByDiseaseType',
    method: 'get',
    params,
  });
}

export function followUpByIndustry(params) {
  return request({
    url: '/manage/display/followUpByIndustry',
    method: 'get',
    params,
  });
}

export function followUpByStage(params) {
  return request({
    url: '/manage/display/followUpByStage',
    method: 'get',
    params,
  });
}

// 职业病病人全程服务数据展示 - 康复指导
export function recoveryInfoByArea(params) {
  return request({
    url: '/manage/display/recoveryInfoByArea',
    method: 'get',
    params,
  });
}

export function recoveryInfoByDiseaseType(params) {
  return request({
    url: '/manage/display/recoveryInfoByDiseaseType',
    method: 'get',
    params,
  });
}

export function recoveryInfoByIndustry(params) {
  return request({
    url: '/manage/display/recoveryInfoByIndustry',
    method: 'get',
    params,
  });
}

export function recoveryInfoByStation(params) {
  return request({
    url: '/manage/display/recoveryInfoByStation',
    method: 'get',
    params,
  });
}


export function getDistrictList(params) {
  return request({
    url: '/api/adminorgGov/address/list',
    params,
    method: 'get',
  });
}


// 职业病种类
export function getOccupationDisease() {
  return request({
    url: '/manage/display/occupationDisease',
    method: 'get',
  });
}

