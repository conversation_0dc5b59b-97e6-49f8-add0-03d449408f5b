<template>
  <div class="content">
    <div class="title">
      <el-button size="medium" @click="goBack">返回</el-button>
      <h3>检测机构详情</h3>
    </div>
    <!-- Tabs 切换栏 -->
    <el-tabs 
        v-model="activeTab" 
        tab-position="top" 
        class="custom-tabs"
      >
      <el-tab-pane label="基本信息" name="first">
        <!-- 基本信息区 -->
        <div class="section">
          <TitleTag titleName="基本信息"></TitleTag>
          <el-row>
            <el-col :span="24">
              <table style="width:100%;border:1px solid #ddd;" class="table-container">
                <tr>
                  <th class="left-tittle table-label" colspan="4">
                    机构名称
                  </th>
                  <th colspan="8" class="th-input">
                    <span>{{ basicInfo.name }}</span>
                  </th>
                  <th class="left-tittle table-label" colspan="4">
                    法人代表
                  </th>
                  <th colspan="8" class="th-input">
                    <span>{{ basicInfo.corp }}</span>
                  </th>
                </tr>
                <tr>
                  <th class="left-tittle table-label" colspan="4">
                    注册类型
                  </th>
                  <th colspan="8" class="th-input">
                    <span>{{ basicInfo.regType }}</span>
                  </th>
                  <th class="left-tittle table-label" colspan="4">
                    统一社会信用代码
                  </th>
                  <th colspan="8" class="th-input">
                    <span>{{ basicInfo.organization }}</span>
                  </th>
                </tr>
                <tr>
                  <th class="left-tittle table-label" colspan="4">
                    注册区域
                  </th>
                  <th colspan="8" class="th-input">
                    <span>{{ basicInfo.regAddr&&basicInfo.regAddr[basicInfo.regAddr.length -1] }}</span>
                  </th>
                  <th class="left-tittle table-label" colspan="4">
                    详细地址
                  </th>
                  <th colspan="8" class="th-input">
                    <span>{{ basicInfo.address }}</span>
                  </th>
                </tr>
                <tr>
                  <th class="left-tittle table-label" colspan="4">
                    联系人
                  </th>
                  <th colspan="8" class="th-input">
                    <span v-if="basicInfo.administrators&&basicInfo.administrators.length">{{ basicInfo.administrators[0].name||basicInfo.administrators[0].userName }}</span>
                    <span v-else-if="basicInfo.isReg">{{basicInfo.isReg.regPerson}}</span>
                    <span v-else-if="basicInfo.contacts && basicInfo.contacts[0]">{{basicInfo.contacts[0].regPerson}}</span>
                    <span v-else-if="basicInfo.managersAndArea[0] && basicInfo.managersAndArea[0].managers">{{basicInfo.managersAndArea[0].regPerson}}</span>
                  </th>
                  <th class="left-tittle table-label" colspan="4">
                    联系电话
                  </th>
                  <th colspan="8" class="th-input">
                    <span v-if="basicInfo.administrators&&basicInfo.administrators.length" >
                    {{ basicInfo.administrators[0].phoneNum }}
                    </span>
                    <span v-else-if="basicInfo.isReg">{{basicInfo.isReg.regPhone}}</span>
                    <span v-else-if="basicInfo.contacts && basicInfo.contacts[0]" >{{basicInfo.contacts[0].regPhone}}</span>
                    <span v-else-if="basicInfo.managersAndArea[0] && basicInfo.managersAndArea[0].managers">{{basicInfo.managersAndArea[0].regPhone}}</span>
                  </th>
                </tr>
                <!-- <tr>
                  <th class="left-tittle table-label" colspan="4">
                    服务区域
                  </th>
                  <th colspan="8" class="th-input">
                    <span>{{ basicInfo.regAddr&&basicInfo.regAddr[basicInfo.regAddr.length -1] }}</span>
                  </th>
                  <th class="left-tittle table-label" colspan="4">
                    营业执照
                  </th>
                  <th colspan="8" class="th-input">
                    <el-image style="width: 100px; height: 80px; border-radius: 4px" :src="basicInfo.img" :preview-src-list="srcList"> </el-image>
                  </th>
                </tr> -->
              </table>
            </el-col>
          </el-row>
        </div>
        <!-- 备案信息区 -->
        <div class="section">
          <TitleTag titleName="备案信息"></TitleTag>
          <el-row>
            <el-col :span="24">
              <table style="width:100%;border:1px solid #ddd;" class="table-container">
                <tr>
                  <th class="left-tittle table-label" colspan="4">
                    备案类别
                  </th>
                  <th colspan="8" class="th-input">
                    <span>{{qualifie1.lineOfBusiness?qualifie1.lineOfBusiness.join('，') :''}}</span>
                  </th>
                  <th class="left-tittle table-label" colspan="4">
                    备案回执
                  </th>
                  <th colspan="8" class="th-input">
                    <span>{{ qualifie1.mechanism_name || '' }}  {{ qualifie1.NO || '' }}</span>
                  </th>
                </tr>
              </table>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>

      <el-tab-pane label="检测报告" name="second">
        <!-- <JcReport></JcReport> -->
      </el-tab-pane>
      <el-tab-pane label="检测合同" name="third">
        <!-- <JcContract></JcContract> -->
      </el-tab-pane>
    </el-tabs>
    <div v-if="activeTab == 'second'"><JcReport :organization="basicInfo.organization" :orgId="basicInfo._id"></JcReport></div>
    <div v-if="activeTab == 'third'"><JcContract  :organization="basicInfo.organization" :name="basicInfo.name" ></JcContract></div>
  </div>
</template>

<script>
import {getQualifies} from '@/api/index.js'
import TitleTag from "../components/TitleTag.vue";
import JcReport from '../components/JcReport.vue'
import JcContract from '../components/JcContract.vue'
export default {
  name: 'detail',
  components: {
    TitleTag,
    JcReport,
    JcContract
  },
  data() {
    return {
      tableData: [],
      basicInfo: {},
      activeTab: 'first', // 默认选中「基本信息」
      srcList: [],
      qualifies:[],
      qualifie1:[]
    };
  },
  created() {
    this.getDetail()
  },
  methods: {
    // 获取健康检查机构详情
    async getDetail(){
        this.basicInfo = {...JSON.parse(this.$route.query.row)};
        this.srcList = [this.basicInfo.img];
        if (this.basicInfo.qualifies && this.basicInfo.qualifies.length) {
          // 获取资质信息
          const idsArr = this.basicInfo.qualifies.map(ele => ele.id);
          getQualifies({idsArr}).then(res=>{
            this.qualifies = res.data || [];
            console.log(this.qualifies,'this.qualifies');
            this.qualifie1 = this.qualifies.filter(ele => ele.status== true)[0];
            console.log(this.qualifie1,'this.qualifie1---->');
          })
        }else{
          this.qualifies = [];
          this.qualifie1 = {};
        }
    },
    goBack(){
      this.$router.go(-1);
    }
  } 
}
</script>

<style lang="scss" scoped>
.content {
  padding:20px;
}
.title {
  display: flex;
  align-items: center; // 使按钮与标题垂直居中
  gap: 10px; // 设置按钮与标题之间的间距
}

.title h3 {
  font-size: 18px; // 调整标题字体大小
  font-weight: bold; // 加粗标题字体
}

.title .el-button {
  margin-right: 10px; // 可选：增加按钮右侧间距，以便与其他元素保持一致
}

.table-container {
        table-layout: fixed;
        background: #fff;
        width: 100%;
        margin: 0 auto;
        border-collapse: collapse;

        td {
            padding: 5px 0;
        }

        th {
            font-weight: normal;
            border: 1px solid #e5e5e5;
            padding: 3px 0;
        }

        tr {
            border: 1px solid #e5e5e5;
            width: 100%;
            font-size: 14px;
        }

        .left-tittle {
            background: #ECF5FF;
        }

        .center-tittle{
            background: #ECF5FF;
        }

        .table-label{
            text-align: right;
            padding-right: 5px;
            color:#000;
            font-size: 14px;
            height:42px;
        }

        .th-input{
            text-align: center;
            padding: 2px 4px;
            span{
            color:#333;
            }
        }

        .th-radio{
            text-align: center;
            padding-left: 6px;
            padding-right: 6px;
        }

        .input-width{
            width: 200px;
            background-color: bisque;
        }

        .health-check-th{
            padding: 11px 28px;
            text-align: center;
        }
    }

</style>
