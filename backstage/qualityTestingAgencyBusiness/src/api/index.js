import request from '@root/publicMethods/request';

export function serviceOrgList(data) {
  return request({
    url: '/manage/adminServiceOrg/getAllOrg',
    data,
    method: 'post',
  });
}

// 获取资质信息
export function getQualifies(data) {
  return request({
    url: '/manage/adminServiceOrg/getQualifies',
    data,
    method: 'post',
  });
}

export function getProjectList(data) {
  return request({
    url: '/manage/adminServiceProject/getProjects',
    data,
    method: 'post',
  });
}

// 获取合同列表
export function getTestContract(data) {
  return request({
    url: '/manage/quality/getTestContract',
    data,
    method: 'post',
  });
}


// 获取项目检测结果
export function getCheckAssessment(params) {
  return request({
    url: '/manage/adminServiceProject/getCheckAssessment',
    params,
    method: 'get',
  });
}


export function findApplyForm(data) {
  return request({
    url: '/manage/adminorgGov/findApplyForm',
    data,
    method: 'post',
  });
}

export function findAssess(data) {
  return request({
    url: '/manage/adminorgGov/findAssess',
    data,
    method: 'post',
  });
}

export function getAllAssessmentInfo(data) {
  return request({
    url: '/manage/adminorgGov/getAllAssessmentInfo',
    data,
    method: 'post',
  });
}

export function chooseYear(data) {
  return request({
    url: '/manage/adminorgGov/chooseYear',
    data,
    method: 'post',
  });
}
