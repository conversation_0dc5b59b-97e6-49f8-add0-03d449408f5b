<template>
  <div class="table" v-loading="loading">
    <TitleTag titleName="查询条件"></TitleTag>
    <el-form inline :model="searchInfo">
      <el-form-item label="年份">
        <el-date-picker v-model="searchInfo.year" type="year" placeholder="选择年" size="small" value-format="yyyy"></el-date-picker>
      </el-form-item>
      <el-form-item label="企业名称">
        <el-input v-model.trim="searchInfo.keyWords" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="上报时间">
        <el-date-picker v-model="searchInfo.dates" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="small" style="width:100%"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button size="mini"  type="primary" @click="handleQuery">查询</el-button>
        <el-button size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <TitleTag titleName="报告列表"></TitleTag>
    <el-table :data="tableData" tooltip-effect="light" style="width: 100%" stripe border
      header-cell-style="background-color: #f5f7fa; color: #606266;height:46px">
      <el-table-column type="index" width="50" label="序号" align="center"></el-table-column>
      <el-table-column label="机构名称" align="center" prop="name" min-width="150" show-overflow-tooltip></el-table-column>
      <el-table-column label="企业名称" align="center" prop="companyName" min-width="150" show-overflow-tooltip></el-table-column>
      <el-table-column label="统一社会信用代码" align="center" prop="companyID" min-width="150" show-overflow-tooltip></el-table-column>
      <el-table-column label="联系人" align="center" prop="companyContact" show-overflow-tooltip></el-table-column>
      <el-table-column label="联系号码" align="center" prop="companyContactPhoneNumber" min-width="130"  show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="上报时间" align="center" prop="reportTime" min-width="130"  show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="完成时间" align="center" prop="completeTime" min-width="130"  show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="报告编号" align="center" prop="projectNumber" min-width="130"  show-overflow-tooltip>
      </el-table-column>
      <!-- <el-table-column label="报告书" align="center" prop="report" min-width="150">
        <template slot-scope="scope">
          <span  style="color:#3697fc;cursor:pointer" v-for="(item, index) in scope.row.report" :key="item._id" @click="openFile(item.url, item.fileName)">
            {{ item.fileName }}
            <span v-if="index < scope.row.report.length - 1">,</span>
          </span>
        </template>  
      </el-table-column> -->
      <el-table-column label="操作" align="center" width="100"  show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="openDetails(scope.row)">查看报告</el-button>
        </template>  
      </el-table-column>
    </el-table>
    <div style="margin:10px 0;text-align:center">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="searchInfo.curPage"
        :page-size="searchInfo.limit"
        :page-sizes="[5, 10, 20, 30, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
    <!-- 查看报告弹窗 -->
    <transition name="el-zoom-in-center">
      <Details v-show="showDetail" :projectDetail="projectDetail" :branch="branch"></Details>
    </transition>
  </div>  
</template>
<script>
import {getProjectList} from '@/api/index.js'
import TitleTag from "../components/TitleTag.vue";
import Details from './details.vue'
export default {
  name: 'JcReport',
  components:{
    TitleTag,
    Details
  },
  props:{
    organization:{
      type:String,
      default:'',
    },
    orgId:{
      type:String,
      default:'',
    }
  },
  data() {
    return {
      searchInfo:{
        curPage: 1,
        limit: 10,
        regAddr: null,
        keyWords: '',
        org: '', // 机构 id
        orgCode: '', // 机构的组织结构代码
        company: '', // 用人单位/企业 统一社会信用代码
        industry: [], // 所属行业
        status: '', // 上报状态
        dates: [],
        dateType: 'applyTime',
        serviceAreas: [],
        year: new Date().getFullYear() + '',
      },
      total:0,
      tableData: [],
      loading:false,
      branch: '',
      projectDetail: {
        show: false,
        data:[],
      },
    }
  },
  computed: {
    showDetail(){
      return this.projectDetail.show;
    },
  },
  created(){
    this.getTableList()
  },
  methods: {
    async getTableList(){
      this.searchInfo.orgCode = this.organization
      this.searchInfo.org = this.orgId
      const res = await getProjectList(this.searchInfo)
      this.tableData = res.data || []
      this.total = res.count.total || 0
   },
   handleSizeChange(val){
      this.searchInfo.limit = val;
      this.searchInfo.curPage = 1;
      this.getTableList();
    },
    handleCurrentChange(val){
      this.searchInfo.curPage = val;
      this.getTableList();
    },
    handleQuery(){
      this.searchInfo.curPage = 1;
      this.getTableList()
    },
    resetQuery(){
      this.searchInfo = {
        curPage: 1,
        limit: 10,
        regAddr: null,
        keyWords: '',
        org: '', // 机构 id
        orgCode: '', // 机构的组织结构代码
        company: '', // 用人单位/企业 统一社会信用代码
        industry: [], // 所属行业
        status: '', // 上报状态
        dates: [],
        dateType: 'applyTime',
        serviceAreas: [],
        year: new Date().getFullYear() + '',
      }
      this.getTableList()
    },
    openFile(url,fileName){
      this.loading = true;
      const index = url.indexOf('.');
      const fileType = url.slice(index+1);
      const file = url;
      if(fileType !== 'pdf' && fileType !== 'docx' && fileType !== 'doc'){
        // 图片下载
        let image = new Image();
        image.setAttribute("crossOrigin", "anonymous"); // 解决跨域 Canvas 污染问题
        image.onload = function() {
          let canvas = document.createElement("canvas");
          canvas.width = image.width;
          canvas.height = image.height;
          let context = canvas.getContext("2d");
          context.drawImage(image, 0, 0, image.width, image.height);
          let url2 = canvas.toDataURL("image/png"); //得到图片的base64编码数据
          let a = document.createElement("a"); // 生成一个a元素
          let event = new MouseEvent("click"); // 创建一个单击事件
          a.download = fileName; // 设置图片名称
          a.href = url2; // 将生成的URL设置为a.href属性
          a.dispatchEvent(event); // 触发a的单击事件
        };
        image.src = file; // src = 链接地址
        this.$message.success('下载成功')
        setTimeout(() => {
          this.loading = false;
        }, 1500);
      }else{
        let a = document.createElement("a");
        a.download = fileName;
        a.href = file;
        a.target = "_blank";
        a.click();
        this.$message.success('下载成功')
        setTimeout(() => {
          this.loading = false;
        }, 1500);
      }
    },
    openDetails(row){
      this.projectDetail.show = true;
      this.projectDetail.data = row;
    },
  }
}
</script>
<style lang="scss" scoped>

</style>