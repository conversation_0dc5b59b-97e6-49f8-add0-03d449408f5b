<template>
  <div class="wrap">
    <el-card class="box-card box" shadow="always">
      <div class="clearfix header">
        <span class="el-icon-error close2" @click="closeDetail"></span>
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
          <el-tab-pane name="info1">
            <span slot="label"><i class="el-icon-document"></i> 检测报告</span>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="text item" ref="content" >
        <el-form class="applyForm info1" v-show="activeName==='info1'">
          <checkResult :checkAssessment="checkAssessment" :report="report" :statistics="statistics" :branch="branch"/>
        </el-form>
      </div>
  </el-card>
</div>
  
</template>
<script>
import checkResult from "./checkResult";
import { getQualifies, getCheckAssessment } from "@/api/index";
export default {
  components: { 
    checkResult,
  },
  props: {
    projectDetail: {
      type: Object,
      default: {}
    },
    branch: {
      type: String,
      default: ''
    },
  },
  data(){
    return {
      date: '',
      activeName: 'info1',
      personsOfProject: [],
      qualifiesCode: '', // 证书编号
      checkAssessment: null, // 检测结果
      report: [], // 职业病危害检测与评价报告书
      resultDes: { // 技术服务结果描述
        totle: 0, // 共检测岗位或工种数量
        abnormal: 0,
        hazards: [], // 超标危害因素类型
      }, 
      allHazards: ['粉尘', '化学因素', '物理因素', '放射性因素', '生物因素', '其他因素。'],
      serviceArea: ['采矿业', '化工、石化及医药', '冶金、建材', '机械制造、电力、纺织、建筑和交通运输等行业领域', '核设施', '核技术应用'],
      statistics: [], // 危害因素检测数据 统计 点数 超标点数
      serviceRanges: '', // 机构的资质业务范围
      EnterpriseID: '',
      jobHealthId: '',
      serviceAreaOptions: [{// 技术服务领域
        label: '采矿业',
        value: '1',
      }, {
        label: '化工、石化及医药',
        value: '2',
      }, {
        label: '冶金、建材',
        value: '3',
      }, {
        label: '机械制造、电力、纺织、建筑和交通运输等行业领域',
        value: '4',
      }, {
        label: '核设施',
        value: '5',
      }, {
        label: '核技术应用',
        value: '6',
      }],
    }
  },
  watch: {
    async datas(data){
      // console.log('项目信息：', data);
      if(data){
        this.personsOfProject = data.personsOfProject || [];
        this.report = data.report.map(ele => {
          return {
            url: data.file_static_path + '/' + data.EnterpriseID + '/' + ele.url,
            fileName: ele.fileName,
          }
        });
        this.getQualifiesCode();
        this.getCheckAssessmentHandle(data._id); // 获取检测结果
        this.EnterpriseID = data.EnterpriseID;
        this.jobHealthId = data._id;
        this.year = data.completeTime ? new Date(data.completeTime).getFullYear() + '' : new Date(data.date).getFullYear() + '';
        // console.log(this.EnterpriseID, 'ddddddddddddddddddddddd');
      }
    },
    async show(show){
      if(show){
        // console.log(show, '1111111111111');
        this.date = new Date();
      }
    }
  },
  computed: {
    datas(){
      return this.projectDetail.data || {};
    },
    show(){
      return this.projectDetail.show;
    },
  },
  filters: {
    companyAddress(addrArr){
      // console.log(1111111,addrArr)
      return addrArr.map(ele => ele.name).join(' ');
    }
  },
  methods: {
    // 获取检测结果
    getCheckAssessmentHandle(jobHealthId){
      getCheckAssessment({ jobHealthId }).then(res => {
        this.checkAssessment = res.data;
        // 计算结果描述
        if(!this.checkAssessment) return;
        const statistics = []; // 危害因素检测数据 统计
        let totleNum = 0, totleAbnormalNum = 0;
        let hazards = new Set(), totle = new Set(), abnormal = new Set();
        for(let key in this.checkAssessment){
          let abnormalNum = 0; // 该类别的超标点数
          if((typeof this.checkAssessment[key] === 'object') && this.checkAssessment[key]){
            if(this.checkAssessment[key].formData && this.checkAssessment[key].formData.length){
              totleNum += this.checkAssessment[key].formData.length;
              let abnormalFlag = false;
              this.checkAssessment[key].formData.forEach(ele => {
                let workType = '';
                if(ele.station){
                  workType = (ele.workspace || '') + ele.station;
                }else{
                  workType = (ele.checkAddressDetail || ele.checkAddress || '') + (ele.workType || '');
                }
                totle.add(workType);
                if(ele.checkResult == '不符合' || ele.checkResult == '不合格'){
                  abnormal.add(workType);
                  abnormalFlag = true;
                  abnormalNum++;
                  totleAbnormalNum++;
                }
              });
              if(abnormalFlag){
                switch(this.checkAssessment[key].value){
                  case 'chemistryFactors': 
                    hazards.add('化学因素'); break;
                  case 'dustFactors': 
                    hazards.add('粉尘'); break;
                  case 'biologicalFactors': 
                    hazards.add('生物因素'); break;
                  case 'ionizatioSourceFactors':
                  case 'ionizatioRadialFactors': 
                    hazards.add('放射性因素'); break;
                  default: 
                    hazards.add('物理因素'); break;
                }
              }
            }
            // 处理危害因素检测数据  点数 超标点数
            statistics.push({
              name: this.checkAssessment[key].name + '点数',
              totle: this.checkAssessment[key].formData.length,
              abnormal: abnormalNum,
            })
          }
        }
        this.resultDes = {
          hazards: [...hazards],
          totle: totle.size,
          abnormal: abnormal.size
        };
        this.statistics = [{
          name: '总点数',
          totle: totleNum,
          abnormal: totleAbnormalNum,
        }].concat(statistics);
      });
    },
    getQualifiesCode(){
      getQualifies({
        organization: this.datas.serviceOrgID
      }).then(res => {
        if(res.status == 200 && res.data.length){
          this.qualifiesCode = res.data.map(ele => ele.NO).join('; ');
          if(typeof res.data[0].lineOfBusiness === 'object'){
            const serviceRanges = [];
            res.data.forEach(ele => {
              serviceRanges.push(...ele.lineOfBusiness)
            })
            for(let i in serviceRanges){
                if(serviceRanges[i].includes('核')&&serviceRanges[i].includes('工业应用') ){
                  serviceRanges[i]='核技术应用'
                }
            }
           
            this.serviceRanges = [...new Set(serviceRanges)];
            
            // console.log(this.serviceRanges,"sadaasdsad")
          }else{
            const serviceRanges = res.data.map(ele => ele.lineOfBusiness).join(',');
            this.serviceRanges = [...new Set(serviceRanges.split(','))];

            for(let i in this.serviceRanges){
                if(this.serviceRanges[i].includes('核')&& this.serviceRanges[i].includes('工业应用') ){
                  this.serviceRanges[i]='核技术应用'
                }
            }
          }
        }else{
          this.qualifiesCode = '';
          this.serviceRanges = [];
        }
      })
    },
    // 点击tab
    handleClick(tab) {
      this.activeName = tab.name;
    },
    closeDetail(){
      this.projectDetail.show = false;
      this.jobHealthId = '';
      this.activeName = 'info1';
    }
  },
};
</script>
<style scoped lang="scss">
  #org, #project{
    font-size: 14px;
    line-height: 30px;
    .lable{
      display: inline-block;
      width: 8em;
      color: #555;
    }
    .txt{
      font-weight: bold;
      color: #4b4b4e;
      .el-radio{
        margin-right: 10px;
      }
    }
    .txt.serviceRanges{
      vertical-align: top;
      display: inline-block;
      width: calc(100% - 8em);
    }
  }
  .info1 #org, .info1 #project{
    .lable{
      width: 10em;
    }
    .files{
      p{
        color: #409EFF;
        padding: 0;
        margin: 0;
        line-height: 28px;
        font-size: 14px;
      }
      p:hover{
        cursor: pointer;
        font-weight: bold;
      }
    }
  }
  .companyIndustry{
    display: inline-block;
    vertical-align: middle;
  }
  .person-table{
    width: 100%; 
    tr{
      th{
        text-align: center;
      }
      td{
        color: #606266;
        text-align: center;
      }
    }
  }
  .person-table tr:hover{
    background-color: #f5f7fa!important;
  }
  .wrap{
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba($color: (#000000), $alpha: 0.6);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    .el-card__body{
      .text.item{
        height: calc(100% - 56px);
        overflow: scroll;
      }
      .title {
        margin-bottom: 10px;
        font-weight: 700;
        // border-left: 3px solid #f56c6c;
        border-left: 0.275rem solid #25aff3;
        padding: 3px 10px;
        font-size: 15px;
        color: #333;
      }
      .header{
        position: relative;
        .close2{
        padding: 3px 0;
        font-size: 1.3em;
        cursor: pointer;
        opacity: .7;
        z-index: 100;
        :hover{
          opacity: .9;
        }
        position: absolute;
        right: 0;
        top: 0;
      }
      }
    }
    .box{
      padding: 10px 3% 30px;
      box-sizing: border-box;
      width: 75%;
      min-width: 500px;
      max-height: 88vh;
      overflow-y: hidden;
      position: relative;
      .el-row:hover{
        background-color: #f5f5f5;
      }
      .el-row{
        padding: 10px 0;
      }
    }
  }
   .text {
    font-size: 14px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
    clear: both
  }
    [v-cloak]{
        display: none;
    }
  
</style>

