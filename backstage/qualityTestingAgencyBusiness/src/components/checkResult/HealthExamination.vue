<template>
  <div v-if="this.$store.getters.remind.isratioArr !== '未体检'">
    <el-form>
      <el-table :data="tableData" style="width: 100%" :header-cell-style="thStyleFun">
        <el-table-column prop="organazation" label="检查机构" align="center">
        </el-table-column>
        <el-table-column prop="checkType" label="体检种类" align="center">
            <template slot-scope="scope">
                <div v-show="scope.row.checkType !== '3'">
                {{
                  scope.row.checkType == "0"
                    ? "上岗前"
                    : scope.row.checkType == "1"
                    ? "在岗期间"
                    : "离岗时"
                }}
              </div>
              <div v-show="scope.row.checkType == '3'">{{ "复查" }}</div>
            </template>
        </el-table-column>
        <el-table-column prop="actuallNum" label="实检人数" align="center">
        </el-table-column>
        <el-table-column label="检查结果（人数）" align="center">
          <el-table-column prop="normal" label="未见异常" align="center">
          <template slot-scope="scope">
            {{ scope.row.normal.length }}
          </template>
        </el-table-column>
        <el-table-column prop="re_examination" label="复查" align="center">
          <template slot-scope="scope">
            {{ scope.row.re_examination.length }}
          </template>
        </el-table-column>
        <el-table-column prop="suspected" label="疑似" align="center">
          <template slot-scope="scope">
            {{ scope.row.suspected.length }}
          </template>
        </el-table-column>
        <el-table-column prop="forbid" label="禁忌证" align="center">
          <template slot-scope="scope">
            {{ scope.row.forbid.length }}
          </template>
        </el-table-column>
        <el-table-column prop="illness" label="职业病" align="center">
          <template slot-scope="scope">
            {{ scope.row.illness.length }}
          </template>
        </el-table-column>
        <el-table-column prop="otherDisease" label="其他疾患" align="center">
          <template slot-scope="scope">
            {{ scope.row.otherDisease.length }}
          </template>
        </el-table-column>
        </el-table-column>
        <el-table-column prop="checkDate" label="检查日期" min-width="155" align="center">
            <template slot-scope="scope">
              <span>{{doMoment(scope.row.checkDate)}}</span>
            </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script>
import { chooseYear} from "@/api/index";
import moment from 'moment';
export default {
  props: {
    id: String,
  },
  data(){
    return{
      tableData:[],
      editItem: {},
      showEdit: {},
    }
  },
  computed: {
    //将时间 转换成 简化的 
    doMoment(nowTime){
      return function(nowTime){
        return moment(nowTime).format('YYYY-MM-DD');
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
      }
    },
  },
  created(){
    this.chooseYear();
  },
  methods:{
      async chooseYear(){
          // // 获取当前年份
          // let nowyear = new Date();
          // nowyear = nowyear.getFullYear();
          let res = await chooseYear({EnterpriseID:this.id});
          if (res.status === 200) {
            res.message = res.message === '已完成' ? '已体检' : (res.message === '未完成' ? '未体检' : res.message)
            this.$store.dispatch("adminorg/remind", {
              isOnlineDeclaration: this.$store.getters.remind.isOnlineDeclaration,
              monthD: this.$store.getters.remind.monthD,
              isjobHealth: this.$store.getters.remind.isjobHealth,
              reportTimes: this.$store.getters.remind.reportTimes,
              isAssess: this.$store.getters.remind.isAssess,
              isratioArr: res.message,
              checkDate: res.checkDate,
              isReported: this.$store.getters.remind.isReported,
            });
            if (res.data) {
              this.tableData = res.data || [];
            };
          }
      },
  }
}
</script>
