<template>
  <div class="physicFactors">
    <el-form>
      <el-table :data="datas" style="width: 100%" border size="small">
        <el-table-column align="center" min-width="100px" prop="workType" label="工种">
        </el-table-column>
        <el-table-column min-width="150px" align="center" label="检测地点">
          <el-table-column align="center" label="车间" prop="workspace">
          </el-table-column>
          <el-table-column align="center" label="岗位" prop="station">
          </el-table-column>
        </el-table-column>
        <el-table-column min-width="150px" align="center" label="检测点具体位置" prop="checkAddressDetail">
        </el-table-column>
        <el-table-column align="center" min-width="160px" prop="electricAverage" label="脉冲波电场强度平均值（V/m）">
        </el-table-column>
        <el-table-column align="center" min-width="150px" prop="eightHoursTouchLimit" label="8h职业接触限值（V/m）">
        </el-table-column>
        <el-table-column prop="checkResult" label="判定结果" align="center" min-width="170px">
          <template slot-scope="props">
            <span :class="props.row.checkResult[0]=='不'?'incompatible':''">{{props.row.checkResult}}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="reportTime" label="检测时间" align="center">
          <template slot-scope="props">
            {{doMomentmonthD(props.row.reportTime)}}
          </template>
        </el-table-column> -->
      </el-table>
    </el-form>
  </div>
</template>

<script>
import moment from 'moment';
export default {
  props: {
    id: String,
    datas: Array,
  },
  data() {
    return {
      tableData: [],
      editItem: {},
      showEdit: {},
    };
  },
  computed: {
    //将时间 转换成 简化的 
    doMomentmonthD(nowTime){
      return function(nowTime){
        // return moment(nowTime).format('YYYY-MM-DD HH:mm:ss');
        return moment(nowTime).format('YYYY-MM-DD');
      }
    },
  },
  created() {
  },
  methods: {
  },
};
</script>
