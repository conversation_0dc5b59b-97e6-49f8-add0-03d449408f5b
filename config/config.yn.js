const path = require('path');
const port = 8008;

module.exports = appInfo => {

  return {
    // 插件路径
    admin_root_path: '/static',
    // 数据库连接
    mongoose: {
      client: {
        url: `${process.env.mdbSrv}/?readPreference=nearest&ssl=false`,
        options: {
          authSource: 'admin',
          dbName: process.env.mdbName,
          user: process.env.mdbUser,
          pass: process.env.mdbPass,
          useNewUrlParser: true,
          useUnifiedTopology: true,
          useCreateIndex: true,
          keepAlive: 3000,
        },
      },
    },

    siteSeo: {
      author: '杭州职卫云科技 https://zyws.cn',
      title: '云南职卫云信息服务平台',
      description:
        '职业卫生检测与评价技术服务报送、职业健康检查（职业病体检）、职业病诊断和鉴定信息报送、职业病危害智能预警与处置、行政监管与执法。',
      keywords:
        '职业卫生,职业健康,职业卫生检测与评价,职业健康检查,职业病体检,职业病诊断,职业病鉴定,技术服务信息报送,职业病危害智能预警与处置,行政监管与执法',
    },

    // 静态目录
    static: {
      prefix: '/static',
      dir: [
        path.join(appInfo.baseDir, 'backstage/dist'),
        path.join(appInfo.baseDir, 'app/public'),
        '/opt/public/jg',
        '/opt/public',
        '/opt/public/jc',
      ],
      maxAge: 31536000,
    },
    // 加密解密
    session_secret: 'duopu_secret',
    auth_cookie_name: 'zyws_yn_super',
    encrypt_key: '243d477d-d6bb-4ab1-b0a9-a1b1c9419562',
    cms_encrypt_key: 'duopu_jkqy',
    salt_aes_key: 'duopu_jkqy',
    salt_sha2_key: '7a58917b-d493-4940-87d4-ab2b0fa8a3b7',
    encryptApp_key: '751f621ea5c8f930',
    encryptApp_vi: '2624750004598718',
    // 日志路径
    logger: {
      dir: `/opt/log/${process.env.EGG_SERVER_ENV}/jg/`,
    },

    // 获取模板的路径，直接获取文件系统目录
    report_template_path: process.cwd() + '/app/public/reportTemplate',
    // 下载根据模板生成的文件http路径，服务端接收，代码拼接：/static+ enterprise_http_path + /EnterpriseID/ + 文件名
    enterprise_http_path: '/enterprise',
    // 用户自行上传文件的文件系统目录，代码拼接: upload_path + /UserID/ + 文件名
    upload_path: '/opt/public/upload/enterprise',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_http_path + /UserID/ + 文件名
    upload_http_path: '/upload/enterprise',

    // 存放生成的培训证书的文件系统目录，代码拼接：certificate_path + 文件名
    certificate_path: '/opt/public/certificate',
    // 下载培训证书的http路径，代码拼接：/static+ certificate_http_path + 文件名
    certificate_http_path: '/certificate',
    enterprise_path: '/opt/public/enterprise',

    // 课程封面目录，代码拼接: upload_courses_path + /课程ID/ + 文件名
    upload_courses_path: '/opt/public/courses',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_courses_http_path + /课程ID/ + 文件名
    upload_courses_http_path: '/courses',

    service_upload_http_path: '/upload/jc',

    // 这玩意用于存groupID，用于通知里的须读用户类型判断，因为不同类型很多字段不一样，连表查就不一样
    groupID: {
      operateGroupID: 'E1XjEmqA', // 超级管理员，运营用户角色ID
      superGroupID: 'RMwIEyjWK', // 政府用户角色ID；添加自己，是为了上级向下级发通知，这个以后考虑
      adminGroupID: 'vGEfBpfsv', // 企业用户角色ID
      serviceGroupID: 'e4Qf2ic6-', // 机构用户角色ID
      physicalExamGroupID: 'zHLKFCyXD', // 体检端用户角色ID
      userGroupID: 'V7au7L2Rw', // 劳动者App
    },

    // 阿里云视频
    aliVideo: {
      accessKeyId: 'LTAI4G8etokKrfe3sBn6hjAX',
      secretAccessKey: '******************************',
      userId: '257893414740639727',
    },

    cluster: {
      listen: {
        port, // 杭州 8008
        hostname: '',
      },
    },

    // 不同端的域名
    domainNames: {
      super: '/',
      operate: 'https://opt.ynzwy.com',
      enterprise: 'https://qy.ynzwy.com',
      service: 'https://jc.ynzwy.com',
      tj: 'https://tj.ynzwy.com',
    },

    // 服务地址配置
    server_path: `http://localhost:${port}`,
    server_api: `http://localhost:${port}/api`,
    upload_http_report_path: '/upload/tjReport',
    auth_cookie_zyjk_name: 'zyws_yn_qy',

    recheckOverTime: 90 * 24 * 60 * 60000, // 复查超期时间 90天
    adminID: [ '2at7_s2yO' ], // 该项目最高管理员账户ID

    systemName: '职业卫生服务信息报送平台', // 大屏顶部系统名称
    editSwitch: true, // 编辑企业信息的权限开关

    statisticalTaskEnable: true, // 统计定时任务开关
    statisticsTick: process.env.hzStatisticsTick || '*/5 * * * *', // 每 5 分钟运行一次
    warningTaskEnable: true, // 预警处理定时任务开关
    warningTick: '16 1 0 */1 * ?', // 预警处理定时任务,每天凌晨1点16分执行一次

    // 新增配置请放在此行上方，branch放在最后
    // 当前所属站点，用于区分不同站点的配置，默认是master, 也就是zyws.cn
    branch: 'yn', // 当前所属站点为杭州 hzzyws.cn
    adcode: '530100000000', // 杭州市的行政区划代码
    adcodeShort: '530100', // 杭州市的行政区划代码
    adName: [ '云南省', '昆明市' ],

    platformName: '云南职卫云信息服务平台', // 平台名称 - 主菜单左上角处的名称
    platformLogoShow: false, // 平台logo显示 - 主菜单左上角名称前

  };
};
