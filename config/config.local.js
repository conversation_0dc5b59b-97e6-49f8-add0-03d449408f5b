const path = require('path');
// const isDocker = process.env.BUILD_ENV === 'docker';
// const mongohost = isDocker ? 'mongodb' : '127.0.0.1:27017';
// const mongobin = isDocker ? '' : '/Users/<USER>/Documents/frame/softs/mongodb/bin/';

module.exports = appInfo => {
  return {
    admin_root_path: 'http://localhost',
    // DEV_CONFIG_MODULES_BEGIN
    dev_modules: [
      // 'kqyIdentificationCenter', //职业病鉴定质控中心业务协同
      // 'kqydiagnosisCenter', //职业健康检查中控中心业务协同
      // 'kqycheckCenter', //职业健康检查中控中心业务协同
      // 'kqyInvestigation', //职业病诊断、鉴定跨区域用人单位调查
      // 'kqySupervise', //第三方服务机构跨区域服务监管
      // 'kqyFollowUp', //职业病人跨区域随访
      // 'jointEnforcement', //联合执法
      // 'adminRadiateProjects', // 放射卫生项目
      // 'adminServiceProject', // 职业卫生项目
      // 'adminServiceOrg', // 技术服务机构
      // 'adminSignContract', // 签约合同管理
      // 'adminAppointment', // 预约信息管理
      // 'adminInspectionReport', // 检测项目报告
      // 'appointment', // 体检预约查询
      // 'physicalExaminationOrg', // 体检机构
      // 'physicalExaminationProjects', // 体检项目
      // 'medicalInfo', // 体检人员信息
      // 'navbar', // 顶部导航
      // 'backUpData', // 数据管理
      // 'threeSimultaneous', // 三同时项目管理
      // 'dashboard', // 主页
      // 'adminorgGov', // 企业管理
      // 'workStatement', // 企业工作报告
      // 'messageNotification', // 公告管理页面
      // 'systemConfig',
      // 'systemOptionLog',
      // 'projectBack', // 报送回执
      // 'warning', // 预警列表
      // 'complaints', // 业务受理
      // 'complaints2', // 投诉举报
      // 'warningHz', // 杭州预警列表
      // 'complaints', // 投诉举报
      // 'addTraining', // 培训计划
      // 'certificate', // 证书管理
      // 'guideMap', // 执法脑图
      // 'enterpriseMap', // 企业地图
      // 'regulations', // 法律法规
      // 'qzTraining', // 衢州培训记录
      // 'Diagnostics', // 体检机构诊断信息
      // 'classification', // 分类分级
      // 'jurisdictionList', // 清单显示
      // 'superUser', // 监管账号管理
      // 'adminorg', // 运营企业列表
      // 'serviceOrg', // 运营机构列表
      // 'physicalExam', // 运营体检列表
      // 'butlerProjects', // 管家服务项目列表清单
      // 'butlerServiceOrg', // 管家服务机构清单
      // 'butlerHomePage', // 管家主页
      // 'courses', // 课程管理
      // 'questionBank', // 题库
      // 'operateLog', // 操作日志
      // 'medicalRadiation', // 医疗放射单位管理
      // 'trainVideos', // 培训视频库管理
      // 'examSyllabus', // 考试大纲管理
      // 'pxOrg', // 运营机构列表
      // 'trainingAdminorgGov',
      // 'trainingEnterpriseMap',
      // 'adminorgReview', // 注册企业审核
      // 'certificate2', // 证书管理(新)
      // 'certificateStatistics', // 证书统计
      // "enterpriseService", // 示范企业申报审核
      // 'helpOrganization', // 助企业机构
      // 'trainingRecords', // 培训记录
      // 'trainingClasses', // 发布培训班管理
      // "helpPersonnel", // 助企人员
      // 'serveEnterprise' // 服务企业
      // 'helpCount',
      // 'registrationRecords', // 培训班报名管理
      // 'cloudSupervisor' // 云监督
      // 'expertCategory', // 专家分类管理
      // 'expertManagement', // 专家管理
      // 'expertExtraction', // 专家抽取记录
      // 'expertTypeStatistics', // 专家类型分布统计
      // 'expertAgeStatistics', // 专家年龄、性别分布统计
      // 'expertEducationStatistics', // 专家年龄学历分布统计
      // 'expertMobilityStatistics', // 专家流动性统计
      // 'expertManagementAudit', // 专家审核
      // 'expertWork', // 工作任务安排与通知
      // 'patrolShiftManage', // 巡查班次管理
      // 'patrolScheduleManage', // 值班安排管理
      // 'patrolRuleManage', // 巡查规则管理
      // 'patrolRecordManage', // 巡查记录统计
      // 'proAbnormalReport', // 防护用品异常情况
      // 'proAbnormalStatistics', // 防护用品异常统计
      // 'proAbnormalPush', // 防护用品异常信息推送
      // 'proAbnormalQuery', // 防护用品异常机构管理
      // 'occProtectSelection', // 防护用品选型管理
      // 'occProtectSelectApprove', // 防护用品选型审核
      // 'proCourseManage', // 防护用品在线指导课程管理
      // 'proCourseApprove', // 防护用品在线指导课程管理
      // "healthcheckRecord", // 体检机构备案查询
      // "recordReview", // 体检机构备案审核
      // "recordTotal", // 体检机构备案统计
      // 'diagnosisRecord', // 诊断机构备案查询
      // 'diagnosisReview', // 诊断机构备案审核
      // 'diagnosisTotal', // 诊断机构备案统计
      // 'occupationalHazardAccidents', // 职业病危害事故申报
      // "evaluationSystem", // 健康企业评估体系
      // "unitVote", // 示范单位评选
      // "unitReview", // 示范单位评选
      // "unitManage", // 健康企业示范单位管理
      // "unitQuery", // 用人单位申报与评选查询
      // "unitStatistics", // 用人单位申报与评选记录统计
      // "unitNotice", // 健康企业评选活动通知
      // "unitNotice", // 健康企业评选活动通知
      // "unitResult", // 健康企业最终结果公示
      // 'hazardAccidentStatistics', // 职业病危害事故统计
      // 'diagnosisArchiveTemplate', // 职业病诊断档案模版管理
      // 'diagnosisFilesQuery', // 职业病诊断档案查询
      // 'diagnosisAnnually', // 按照年度统计每年职业病诊断人数
      // 'diagnosisCategory', // 按照年度统计每类职业病诊断人数
      // 'diagnosisResult', // 按照年度统计职业病诊断结果
      // 'diagnosisNumChange', // 历年职业病诊断人数的变化情况
      // 'diagnosisCategoryChange', // 历年每类职业病诊断的变化情况
      // 'identifyArchiveManage', // 职业病鉴定档案模版管理
      // 'identifyArchiveQuery', // 职业病鉴定档案查询
      // 'identifyAnnually', // 按照年度统计每年职业病鉴定人数
      // 'identifyCategory', // 按照年度统计每类职业病鉴定人数
      // 'identifyResult', // 按照年度统计职业病鉴定结果
      // 'identifyNumChange', // 历年职业病鉴定人数的变化情况
      // 'identifyCategoryChange', // 历年每类职业病鉴定的变化情况
      // "xjbtDeclarationMain",     // 企业申报审核
      // 'xjbtDeclarationStatistic',    // 企业申报查询统计
      // 'protectEquipmentRecordQuery',    // 建设项目职业病防护设施验收方案备案备案查询
      // 'protectRecordStatistic',     // 建设项目职业病防护设施验收方案备案备案信息统计
      // 'pdEvaluationProgressRecordQuery',   // 建设项目职业病危害控制效果评价和职业病防护设施验收工作过程报告备案备案信息查询
      // 'pdEvaluationProgressRecordStatistic',   // 建设项目职业病危害控制效果评价和职业病防护设施验收工作过程报告备案统计
      // 'medicalPdEvlationReportRecordQuery', // 医疗机构放射性职业病危害建设项目预评价报告备案子系统备案信息查询
      // 'medicalPdEvlationReportRecordStatistic',   // 医疗机构放射性职业病危害建设项目预评价报告备案子系统备案统计
      // 'medProjectFinishRecordQuery',      // 医疗机构放射性职业病危害建设项目竣工验收审核备案信息查询
      // 'medicalProjectAcceptanceCheckRecordStatistic',   // 医疗机构放射性职业病危害建设项目竣工验收审核备案统计
      // 'radiationTherapyApprovalQuery',    // 放射诊疗许可证查询
      // 'radiationFirstApplyAudit',         // 放射诊疗首发许可证审核
      // 'raditionApprovalChangeAudit',      // 放射诊疗变更许可证审核
      // 'raditionApprovalStatistic',        // 放射诊疗许可证信息统计
      // 'eHealthRecordJG', // 职业人群电子健康档案（监管端）
      // 'jgSuspectInqury',    // 疑似职业病上报查询
      // 'archiveTemplateManage', // 档案模版管理
      // 'jgHealthCheckStatistic',      //  职业健康检查统计
      // 'jgHealthArcheQuery',       // 职业健康检查档案查询
      // 'validationFunSetting',      // 校验函数设置
      // 'dataRuleSetting',            // 数据规则设置
      // 'dataQualityLevelRisk',       // 数据质量等级预警
      // 'dataWatchEveryTime',         // 数据实时监控
      // 'dataAutoAnalysis',            // 数据自动分析
      // 'hazardFactorTemplate', // 用人单位职业病危害因素检测管理子系统-档案模版管理
      // 'onlineMonitoring', // 智能设备 在线监测
      // 'displayOnlineMonitoringData', // 职业健康大数据深度开发及展示利用-显示在线监测数据
      // 'protectionCloudManagement', // 防护用品管理
      // "cloudSupervisor", // 云监督
      // 'patrolRectifyManage', // 监督检查异常情况整改
      // 'proAbnormalRectify', // 防护用品使用情况异常整改
      // 'protectionAcceptFileData', // 职业健康大数据深度开发及展示利用-建设项目职业病防护设施验收方案备案数据展示
      // 'hazardInspectionData', // 职业健康大数据深度开发及展示利用-危害项目检查数据展示
      // 'occupationalHealthExamData', // 职业健康大数据深度开发及展示利用-职业健康检查数据展示
      // 'checkLawEnforcementData', // 职业健康大数据深度开发及展示利用-监督检查、行政执法数据展示
      // 'workerHealthMonitoringData', // 职业健康大数据深度开发及展示利用-劳动者健康监护数据展示
      // 'laborProtectionData', // 职业健康大数据深度开发及展示利用-劳动防护数据展示
      // 'displayPatientServiceData', // 职业健康大数据深度开发及展示利用-职业病病人全程服务数据展示
      // 'researchCollaborationData', // 职业健康大数据深度开发及展示利用-科研协作数据展示
      // 'crossRegionalHealthData', // 职业健康大数据深度开发及展示利用-跨区域职业健康管理协作展示
      // 'crossRegionalChemicalData', // 职业健康大数据深度开发及展示利用-跨区域化学中毒救治协作展示
      // 'certificateTemplate', // 证书模版管理
      // 'dataCorrect', // 数据修正
      // 'dataManageMessage',   // 数据质量管理消息中心
      // 'hazardProjectDeclarationData', // 职业健康大数据深度开发及展示利用-职业病危害项目申报数据展示
      // 'workInjuryRecognition', // 工作认定申请
      // 'fhCourses', // 标准佩戴防护用品在线指导课程管理
      // 'fhTraining', // 标准佩戴防护用品在线指导培训
      // 'classificationManage', // 课程分类管理
      // 'personalTraining', // 报名查询
      // 'contactOfficer', // 联络员管理
      // 'talentPool', // 人才库管理
      // 'caseLib', // 案例库管理
      // 'crossRegionHealthShare', // 跨区域开设的工厂用人单位的职业健康信息共享
      // 'techVendorManagement', // 第三方技术服务机构管理
      // "qualityHealthCheckIndicator", // 质控-职业健康检查指标库
      // "qualityDiagnosisIndicator", // 质控-职业病诊断质控指标库
      // "qualityRadiateIndicator", // 质控-检测机构质控指标库
      // "dataRuleMonitoring",// 数据规则设置（修改后）
      // "dataQualityMonitoring",// 数据质量监测记录（修改后）
      // "dataAutoAnalysisMonitoring",// 数据自动分析（修改后）
      // "qualityHealthCheckList", // 质控-职业健康检查机构质量检查表
      // "qualityDiagnosisCheckList", // 质控-职业病诊断机构质量检查表
      // "qualityRadiateCheckList", // 质控-检测机构质量检查表
      // "qualityHealthEvaluate", // 质控-职业健康检查在线评价
      // "qualityDiagnosisEvaluate", // 质控-职业病诊断机构质量在线评价
      // "qualityRadiateEvaluate", // 质控-检测机构质量在线评价
      // 'serviceList', // 预约服务-服务列表
      // 'workplaceHazard', // 工作场所职业病危害因素监测
      // 'crossRegionLawEnforcementShare' // 跨区域卫生监督执法共享
      // 'crossRegionDiseaseData', // 职业病、重点职业病监测数据共享
      // 'qualityHealthExamAgencyBusiness', // 质控-查询职业健康检查机构的相关业务情况
      // 'qualityDiagnosticAgencyBusiness', // 质控-查询职业病诊断机构的相关业务情况
      // 'qualityTestingAgencyBusiness', // 质控-检测机构检测结果和检测报告查询
      // "qualityHealthResult", // 质控-职业健康检查结果汇总
      // "qualityDiagnosisResult", // 质控-职业病诊断机构质量结果汇总
      // "qualityRadiateResult", // 质控-检测机构质量结果汇总
      // "qualityHealthReport", // 质控-质控报告及异常结果上报
      // "qualityDiagnosisReport", // 质控-职业病诊断机构质控报告及异常结果上报
      // "qualityRadiateReport", // 质控-检测机构质控报告及异常结果上报
      // "qualityHealthLookReport", // 质控-质控报告及异常结果上报(查看)
      // "qualityDiagnosisLookReport", // 质控-职业病诊断机构质控报告及异常结果上报(查看)
      // "qualityRadiateLookReport", // 质控-检测机构质控报告及异常结果上报(查看)
      // 'qualityHealthOpinion', // 质控-实现职业健康检查质量督导意见管理
      // 'qualityDiagnosisOpinion', // 质控-实现职业病诊断机构质量督导意见管理
      // 'qualityRadiateOpinion', // 质控-实现检测机构质量督导意见管理
      // "qualityHealthStatistics", // 质控-职业健康检查质控统计分析
      // "qualityDiagnosticStatistics", // 质控-职业病诊断质控统计分析
      // "qualityRadiateStatistics", // 质控-检测机构质控统计分析
      // 'questionnaire', // 问卷调查表管理
      // 'answer', // 问卷调查记录（结果）
      // 'oneNetworkUnifiedData', // 职业健康大数据深度开发及展示利用-一网统管数据展示
      "sensitiveDataApproval",
    ],
    // DEV_CONFIG_MODULES_END
    mongoose: {
      client: {
        // url: 'mongodb://127.0.0.1:27017/hangzhou',
        // url: 'mongodb://127.0.0.1:27017/zyws-xjbt0530',
        // url: 'mongodb://dbadmin:<EMAIL>:25000/zyws-xjbt0414?authSource=admin',
        // url: 'mongodb://dbadmin:<EMAIL>:25000/zyws-xjbt0612?authSource=admin',
        // url: '****************************************************************************',
        url: 'mongodb://127.0.0.1:27017/zyws-xjbt0805',
        // url: 'mongodb://dbadmin:<EMAIL>:25000/frameData?authSource=admin',
        // url: 'mongodb://dbadmin:<EMAIL>:25000/frameData?authSource=admin',
        // url: '****************************************************************************',
        // url: 'mongodb://vango:<EMAIL>:62717/fujian2?authSource=admin',
        // url: 'mongodb://127.0.0.1:27017/zyws-xjbt',
        // url: '*********************************************************************',
        // url: 'mongodb://**************:27017/fujian',
        options: {
          useCreateIndex: true,
          useUnifiedTopology: true,
          keepAlive: 3000,
        },
      },
    },
    redis: {
      //   client: {
      //   // host: '************',
      //   host: "127.0.0.1",
      //   // port: 31379,
      //   port: 6379,
      //   password: "",
      //   db: null,
      // },
      // client: {
      //   port: 62719,
      //   host: 'vanbox.beasts.wang',
      //   password: 'Tc666888.',
      //   db: 0,
      // },
      client: {
        port: 63379,
        host: 'www.beasts.wang',
        password: 'Vancy0727wangxi',
        db: null,
      },
    },
    // redis: {
    //   client: {
    //     port: 62719,
    //     host: 'vanbox.beasts.wang',
    //     password: 'Tc666888.',
    //     db: 0,
    //   },
    // },
    // redis: {
    //   client: {
    //     // host: '************',
    //     host: "127.0.0.1",
    //     // port: 31379,
    //     port: 6379,
    //     password: "",
    //     db: null,
    //   },
    // },
    // mongodb相关路径
    mongodb: {
      binPath: '',
      backUpPath: path.join(appInfo.baseDir, 'databak/'),
    },
    static: {
      prefix: '/static',
      dir: [
        path.join(appInfo.baseDir, 'app/public'),
        path.join(appInfo.baseDir, 'backstage/dist'),
        // 'D:/Workspace/tj/app/public',
        // 'D:/gsProject/tj/app/public',
        // 'D:/ML/aa/3333333333/zyjk/app/public',
        // 'D:/work/zyjk/app/public',
        // 'E:/jkqyOperate/app/public',
      ],
      maxAge: 31536000,
    },
    logger: {
      dir: path.join(appInfo.baseDir, 'logs'),
    },
    server_path: 'http://127.0.0.1:7003',
    server_api: 'http://127.0.0.1:7003/api',
    auth_cookie_zyjk_name: 'zyws_xhl_qy',

    // 是否开启以版本号(手动定版)控制的静态界面更新 默认为true  注意：手动更新优先级大于自动更新
    isVersionUpdate: false,
    // 是否开启静态界面自动更新 默认为true  警告：慎重！！！如果改为false，强烈建议手动清空缓存或开启手动定版并更新版本号后再加载，否则有可能加载的并非最新界面
    isAutoUpdate: true,
    // iServiceHost: 'http://127.0.0.1:8666',
    iServiceHost: 'https://iservice.xixids.com',
    // iServiceHost: 'http://127.0.0.1:8666',
    iService2Host: 'http://localhost:3000',
    xjbtjdglHost: 'http://localhost:3000',
    upload_path: process.cwd() + '/app/public/upload/enterprise',
    enterprise_path: process.cwd() + '/app/public/enterprise',

    editSwitch: true, // 编辑企业信息的权限开关
    branch: 'xjbt', // 不填默认是master
    // systemName: '职业健康数字化管理系统', // 大屏顶部平台名称

    fzUserInfo: 'http://*************:8088/api/jkoa/sso/getHrmInfoByJson',

    fzToken: 'http://*************:8088/api/jkoa/sso/applytokenByJson',

    fzUserDetail: 'http://*************:8088/api/hrm/resful/getHrmUserInfoWithPage', // 福州获取个人信息

    // fzLoginVerify: 'http://ip:port/api/cube/restful/interface/getModeDataPageList/tyqx_hqjsid001', // 福州登录人权限验证

    fzUserPriVerifier:
      'http://*************:8088/api/cube/restful/interface/getModeDataPageList/tyqx_hqjsid001', // 福州登录人员权限验证(在配置文件的名单中的人员可以访问)
    fzLoginSystemid: 'OA', // 福州系统标识
    fzLoginPassword: 'OA123', // 福州 MD5组成中的密码
    fzLoginRoleList: [ 'admin_zyjk', 'zyjk_ceshi' ],
    // cockpikAccount: '***********',
    // 不同端的域名 用于企业列表登录跳转企业端
    domainNames: {
      // cms: 'https://news.zyws.net',
      // super: 'https://jg.zyws.net',
      // operate: 'https://opt.zyws.net',
      enterprise: 'http://127.0.0.1:7001',
      // enterprise: 'https://zyws.net',
      // service: 'https://jc.zyws.net',
      // px: 'https://px.zyws.net',
    },
    jgEcdhOptions: {
      publicKey:
        'BJJjGs7NQ0e8dDwVCTC27DTl6f7AHG+wEQJzY+VlZorevBgFD79nKhQzsgHX+/5J7RsbgQzGTjG9qmN0l+Fy6k4=',
      privateKey: 'IVWPS8OQ0IeXAC5l8ALFPpFHnMBEkD7eYgOSn3HIoWI=',
    },
    qyFzEcdhOptions: {
      publicKey:
        'BKd3or+Xh9vZ3pAmRWUCkOV4rjNnL6zECIopC75fACBZq/48+RM34B24VrN1etcLKe7hIufmjgdg+P3mDqGdwiI=',
      sharedKey: 'NEbhf0HzVUS1L6CY9terLyk6Zw22kI6RwtFU9czcMJM=',
    },
    rabbitmq: {
      url: 'amqp://guest:<EMAIL>:5673',
      queue: [
        {
          name: 'zyjk_jg_file_export',
          callback: 'jgFileExport',
        },
      ],
      // 其余配置 参考 https://www.npmjs.com/package/amqplib
    },
    dpStatisticalTaskEnable: false, // 大屏统计定时任务开关
    ahStatisticalTaskEnable: false, // 安徽培训大屏统计定时任务开关
    statisticalTaskEnable: false, // 大屏统计定时任务开关
    dpStatisticsTick: '0 */15 * * * ?', // 大屏统计定时任务
    statisticsTick: '*/5 * * * *', // 每 5 分钟运行一次
    adcode: '340000000000',
    adcodeShort: '330100', // 杭州市的行政区划代码
    adName: [ '浙江省', '杭州市' ],
    // 跨区域管理开关
    crossRegionManage: false,

    get platformName() {
      const baseOrg = '新疆生产建设兵团';
      const systemName = this.crossRegionManage
        ? '跨区域职业健康管理协作子系统'
        : '职业健康监测管理信息系统';
      return `<span style="font-size:12px">${baseOrg}<br/>${systemName}</span>`;
    },
    platformLogoShow: false, // 平台logo显示 - 主菜单左上角名称前

    // platformLogoShow: false,

    PEISIntergrationEnable: true, // 是否启用体检系统集成（包含普通体检类型）,隐藏点击单位名称跳转到企业详情

    // 证书配置
    certificateConfig: {
      effectiveYear: 1, // 证书有效时间 单位：年
      trainingType: [ 1 ], // 1监管要求的培训 2自主/公开培训 3企业组织的员工培训
    },

    // 福州国密url
    fzgmBaseUrl: process.env.FZGM_BASE_URL || 'http://192.168.19.15',
    // 福州国密业务key
    fzgmKey: process.env.FZGM_KEY || 'n6beoq8b9g7te7e5',
    // 签名算法
    hmacSignAlgorithm: process.env.HMAC_SIGN_ALGORITHM || 'FZsm3',
    // 密码加密算法
    passwordEncryptionAlgorithm: process.env.PASSWORD_ENCRYPTION_ALGORITHM || 'FZsm3',
    // 是否开启数据库加密
    dbEncryption: false,
    // dbEncryption: process.env.DB_ENCRYPTION === 'true',
    // 是否允许福州国密接口调用
    ALLOW_FZGM: process.env.ALLOW_FZGM === 'true',
    jgOidc: {
      // 门户oidc认证系统
      oidcInfo: {
        api_host: 'http://127.0.0.1:7002',
        realm_name: '',
        client_id: 'QNjEc6bkquj7RICthG-Wg',
        client_secret: 'bMErbsH20DWBXDpS9mwrk',
        scope: 'read',
        grant_type: 'authorization_code',
        redirect_uri: 'http://127.0.0.1:7003/xjbtcallback',
      },
    },
    // 新疆兵团GM接口配置
    xjbtGM: {
      baseUrl: process.env.XJBT_BASE_URL || 'http://***************:8236',
      appId: process.env.XJBT_APP_ID || 'fg5F1DA3CC04376A23',
      keyId: process.env.XJBT_KEY || 'f67b41ec0d304844b2363895c06885ce',
      hmacAlgorithm: process.env.HMAC_SIGN_ALGORITHM || 'SM3',
    },
    // 登录有效时间
    superUserMaxAge: 1000 * 60 * 60, // 60min   后台登录可用，此框架暂为加入后台登录, 登录有效时长
    // 职业病诊断测试环境配置
    iServiceZdHost: process.env.iServiceZdHost || 'https://xjbtzdbackend.jkqy.cn',
    // 职业病鉴定测试环境配置
    iServiceJdHost: process.env.iServiceJdHost || 'https://xjbtjdbackend.jkqy.cn',
    // 科研协作数据展示测试环境配置
    iServiceKyxzHost: process.env.iServiceKyxzHost || 'https://xjbtkywbxzbackend.jkqy.cn',
    // 职业病病人全程服务数据展示测试环境配置
    iServiceBzHost: process.env.iServiceBzHost || 'https://xjbtrehabbackend.jkqy.cn',
    warningTaskEnable2: false, // 预警处理定时任务开关
    // platformName: '杭州企卫通服务平台', // 平台名称 - 主菜单左上角处的名称
    // systemName: '杭州企卫通服务平台', // 大屏顶部系统名称
  };
};
