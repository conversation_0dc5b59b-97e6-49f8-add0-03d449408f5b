const path = require('path');
const fs = require('fs');
const version = (fs.readFileSync('version', 'utf-8') || '').replace(/(\n|VERSION=)/g, '');

module.exports = appInfo => {
  return {
    session: {
      key: 'DUOPU_SESS',
      maxAge: 24 * 3600 * 1000, // 1 day
      httpOnly: true,
      encrypt: true,
      renew: true, // 延长会话有效期
    },
    // redis相关配置
    redis: {
      client: {
        host: process.env.redis_host || 'valkey.valkey.svc',
        port: +process.env.redis_port || 6379,
        password: process.env.redis_pass || '',
        db: process.env.redis_db || null,
        retryStrategy: times => {
          // 重试策略
          return Math.min(times * 50, 2000); // 每次重试时间间隔 50ms 递增，最大 2s
        },
      },
    },

    siteSeo: {
      author: '杭州职卫云科技 https://zyws.cn',
      title: '职业健康数字化监管平台 职业卫生分类监督执法 职业健康在线',
      description:
        '职业健康数字化监管平台，专注于职业卫生监管数字化智能化解决方案，分类分级，智能预警，互联网+监督执法。',
      keywords:
        '职业卫生,职业健康,分类分级,互联网智慧监管,数字化平台,职业卫生分类监督执法,职业健康在线',
    },

    // 这玩意用于存groupID，用于通知里的须读用户类型判断，因为不同类型很多字段不一样，连表查就不一样
    groupID: {
      operateGroupID: 'E1XjEmqA', // 超级管理员，运营用户角色ID
      superGroupID: 'RMwIEyjWK', // 政府用户角色ID
      adminGroupID: 'vGEfBpfsv', // 企业用户角色ID
      enterpriseGroupID: 'WnsDSP177', // 企业端集团用户角色ID
      serviceGroupID: 'e4Qf2ic6-', // 机构用户角色ID
      jcqlcGroupID: '8e5D3G8s3', // 机构VIP(全流程)用户角色ID
      physicalExamGroupID: 'zHLKFCyXD', // 体检用户角色ID
      userGroupID: 'V7au7L2Rw', // 劳动者用户角色ID
      pxGroupID: 'TOR4t7OX4', // 培训机构用户角色ID
      helpPersonnelGroupID: 'Ayf_9WV59', // 助企人员ID
    },
    China: {
      area_code: '100000',
      name: '中国',
    },
    // 登录有效时间
    superUserMaxAge: +process.env.superUserMaxAge || 1000 * 60 * 30, // 30min   后台登录可用，此框架暂为加入后台登录, 登录有效时长
    // 设置网站图标
    siteFile: {
      '/favicon.ico': fs.readFileSync(
        path.join(appInfo.baseDir, 'app/public/favicon.ico')
      ),
    },
    // gzip压缩
    compress: {
      threshold: 2048,
    },
    middleware: [
      'xframe',
      'proxyMiddleware',
      'notfoundHandler',
      'crossHeader',
      'compress',
      'authAdminEnabel',
      'authAdminToken',
      'authAdminPower',
    ],
    // api跨域
    crossHeader: {
      match: ['/api', '/manage'],
    },
    // 后台token校验
    authAdminToken: {
      match: ['/manage', '/admin', '/qy', '/user'],
    },
    // 后台权限校验
    authAdminPower: {
      match: ['/manage'],
    },
    bodyParser: {
      formLimit: '100mb',
      jsonLimit: '100mb',
      textLimit: '100mb',
      // 值的大小可以根据自己的需求修改 这里只做演示
    },
    // 文件上传
    multipart: {
      fields: '100',
      fileSize: '50mb',
      mode: 'stream',
      fileExtensions: [
        '.doc',
        '.docx',
        '.pdf',
        '.doc',
        '.docx',
        'xlsx',
        'mp4',
        'avi',
        '.jfif',
        'pptx',
        'txt',
        'xls',
      ], // 扩展几种上传的文件格式
    },
    imageType: ['.png', '.jpeg', '.jpg', '.bmp', '.jfif'],
    imagePDFType: ['.png', '.jpeg', '.jpg', '.bmp', '.jfif', '.pdf'],
    imagePDFWordType: [
      '.png',
      '.jpeg',
      '.jpg',
      '.bmp',
      '.jfif',
      '.pdf',
      '.docx',
      '.doc',
    ],

    // 存放生成的河北回执单的文件系统目录，代码拼接：certificate_path + 文件名
    receipt_path: process.cwd() + '/app/public/receipt',

    radiaApproveCertificate_path: process.cwd() + '/app/public/radiaApproveCerticate', // 放射诊疗许可证
    radiaApproveCertificate_http_path: '/radiaApproveCerticate',

    medPreEvaluate_path: process.cwd() + '/app/public/medPreEvaluateDoc', // 医疗机构预评价批复文件
    medPreEvaluate_http_path: '/medPreEvaluateDoc',

    medProFinish_path: process.cwd() + '/app/public/medProFinishDoc',
    medProFinish_http_path: '/medProFinishDoc',

    // 用户自行上传文件的文件系统目录，代码拼接: upload_path + /UserID/ + 文件名
    receipt_http_path: '/receipt',
    upload_path: process.cwd() + '/app/public/upload/images',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_http_path + /UserID/ + 文件名
    upload_http_path: '/upload/enterprise',
    // // 用户下载填写的模板http路径，代码拼接 /static + datatemplate_path
    // data_template_path: process.cwd() + '/app/public/dataTemplate',
    // // 存放根据模板生成的文件目录，服务端接收，代码拼接：super_path + /serviceID/ + 文件名
    // service_path: process.cwd() + '/app/public/services',
    // // 下载根据模板生成的文件http路径，服务端接收，代码拼接：/static+ super_http_path + /superID/ + 文件名
    // service_http_path: '/services',
    // 下载根据模板生成的文件http路径，服务端接收，代码拼接：/static+ enterprise_http_path + /EnterpriseID/ + 文件名
    enterprise_http_path: '/enterprise',
    enterprise_path: '/opt/public/enterprise',
    // 用于生成监督执法手册的word模板存放位置，直接获取文件系统目录
    report_template_path: process.cwd() + '/app/public/reportTemplate',
    // 存放生成的监督执法手册的文件系统目录，代码拼接：super_path + /superID/ + 文件名
    super_path: process.cwd() + '/app/public/super',
    // 下载监督执法手册的http路径，代码拼接：/static+ super_http_path + /superID/ + 文件名
    super_http_path: '/super',
    service_upload_http_path: '/upload/service', //+++++++++++++

    // 存放生成的培训证书的文件系统目录，代码拼接：certificate_path + 文件名
    certificate_path: process.cwd() + '/app/public/certificate',
    // 下载培训证书的http路径，代码拼接：/static+ certificate_http_path + 文件名
    certificate_http_path: '/certificate',

    // 存放生成的云监督指导意见书的文件系统目录
    cloud_supervision_path: process.cwd() + '/app/public/cloudSupervision',
    // 下载云监督指导意见书的http路径 代码拼接：/static+ cloud_supervision_http_path + 文件名
    cloud_supervision_http_path: '/cloudSupervision',

    // 用户自行上传体检报告目录，代码拼接: upload_report_path + /体检机构ID/ + 文件名
    upload_record_path: process.cwd() + '/app/public/upload/record',
    // 上行用户自行上传体检报告的http路径， 代码拼接：/static + upload_http_report_path + /体检机构ID/ + 文件名
    upload_http_record_path: '/upload/record',

    // 课程封面目录，代码拼接: upload_courses_path + /课程ID/ + 文件名
    upload_courses_path: process.cwd() + '/app/public/upload/courses',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_courses_http_path + /课程ID/ + 文件名
    upload_courses_http_path: '/upload/courses',

    // 用户自行上传体检报告目录，代码拼接: upload_report_path + /体检机构ID/ + 文件名
    upload_report_path: process.cwd() + '/app/public/upload/tjReport',
    // 上行用户自行上传体检报告的http路径， 代码拼接：/static + upload_http_report_path + /体检机构ID/ + 文件名
    upload_http_report_path: '/upload/tjReport',

    // 检索代号：#0001  后台管理根目录
    admin_base_path: '/admin',
    qy_base_path: '/qy',
    user_base_path: '/user',

    // 是否开启以版本号(手动定版)控制的静态界面更新 默认为true  注意：手动更新优先级大于自动更新
    isVersionUpdate: true,
    // 版本号
    version,
    // 是否开启静态界面自动更新 默认为false  警告：慎重！！！如果改为false，强烈建议手动清空缓存或开启手动定版并更新版本号后再加载，否则有可能加载的并非最新界面
    isAutoUpdate: false,

    keys: 'frame',
    cluster: {
      listen: {
        port: 7003,
        hostname: '',
      },
    },
    serviceOrgRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/adminServiceOrg'),
        ctx => ctx.path.startsWith('/manage/adminPxOrg'),
        ctx => ctx.path.startsWith('/api/serviceOrg'),
        ctx => ctx.path.startsWith('/api/pxOrg'),
        ctx => ctx.path.startsWith('/manage/projectBack'),
        ctx => ctx.path.startsWith('/api/projectBack'),
        ctx => ctx.path.startsWith('/manage/adminServiceProject'),
        ctx => ctx.path.startsWith('/manage/detectionMechanism'),
        ctx => ctx.path.startsWith('/manage/adminRadiateProjects'),
      ],
    },
    adminorgRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/adminorgGov'),
        ctx => ctx.path.startsWith('/api/adminorgGov'),
        ctx => ctx.path.startsWith('/manage/workStatement'),
      ],
    },
    waringAndSupervisionRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/warning'),
        ctx => ctx.path.startsWith('/api/warning'),
        ctx => ctx.path.startsWith('/manage/supervision'),
        ctx => ctx.path.startsWith('/api/supervision'),
      ],
    },
    // 培训
    trainingRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/adminTraining'),
        ctx => ctx.path.startsWith('/manage/employeeTraining'),
      ],
    },
    // 备案管理
    recordManageRouter: {
      match: [ctx => ctx.path.startsWith('/manage/recordManage')],
    },

    jointEnforcementRouter: {
      match: [ctx => ctx.path.startsWith('/manage/jointEnforcement')],
    },

    crossRegionLawEnforcementShareRouter: {
      match: [ctx => ctx.path.startsWith('/manage/crossRegionLawEnforcementShare')],
    },

    kqycheckCenterRouter: {
      match: [ctx => ctx.path.startsWith('/manage/kqycheckCenter')],
    },

    kqydiagnosisCenterRouter: {
      match: [ctx => ctx.path.startsWith('/manage/kqydiagnosisCenter')],
    },

    kqyIdentificationCenterRouter: {
      match: [ctx => ctx.path.startsWith('/manage/kqyIdentificationCenter')],
    },

    kqyInvestigationRouter: {
      match: [ctx => ctx.path.startsWith('/manage/kqyInvestigation')],
    },

    kqySuperviseRouter: {
      match: [ctx => ctx.path.startsWith('/manage/kqySupervise')],
    },

    kqyFollowUpRouter: {
      match: [ctx => ctx.path.startsWith('/manage/kqyFollowUp')],
    },

    checkLawEnforcementDataRouter: {
      match: [ ctx => ctx.path.startsWith('/manage/checkLawEnforcementData') ],
    },

    // evaluationSystem
    evaluationSystemRouter: {
      match: [ctx => ctx.path.startsWith('/manage/evaluationSystem')],
    },

    // 质控管理
    qualityRouter: {
      match: [ctx => ctx.path.startsWith('/manage/quality')],
    },

    unitResultRouter: {
      match: [ctx => ctx.path.startsWith('/manage/unitResult')],
    },

    // 培训
    videoLibRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/videoLib'),
        ctx => ctx.path.startsWith('/manage/videoLib'),
      ],
    },
    // 三同时
    threeSimultaneousRouter: {
      match: [ctx => ctx.path.startsWith('/manage/threeSimultaneous')],
    },
    // 体检机构
    physicalExaminationOrgRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/physicalExaminationOrg'),
        ctx => ctx.path.startsWith('/manage/physicalExamDetectionMechanism'),
        ctx => ctx.path.startsWith('/manage/physicalExaminationProjects'),
        ctx => ctx.path.startsWith('/manage/medicalInfo'),
        ctx => ctx.path.startsWith('/api/physicalExaminationOrg'),
        ctx => ctx.path.startsWith('/manage/healthcheck'),
        ctx => ctx.path.startsWith('/manage/jobHealthStatistics'),
        ctx => ctx.path.startsWith('/manage/healthCheckStatistics'),
      ],
    },
    // // 体检机构争端列表
    diagnosticsRouter: {
      match: [ctx => ctx.path.startsWith('/manage/diagnostics')],
    },
    questionBankRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/questionBank'),
        ctx => ctx.path.startsWith('/api/questionBank'),
      ],
    },
    contactOfficerRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/contactOfficer'),
        ctx => ctx.path.startsWith('/api/contactOfficer'),
      ],
    },

    talentPoolRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/talentPool'),
        ctx => ctx.path.startsWith('/api/talentPool'),
      ],
    },

    caseLibRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/caseLib'),
        ctx => ctx.path.startsWith('/api/caseLib'),
      ],
    },

    crossRegionHealthShareRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/crossRegionHealthShare'),
        ctx => ctx.path.startsWith('/api/crossRegionHealthShare'),
      ],
    },

    techVendorManagementRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/techVendorManagement'),
        ctx => ctx.path.startsWith('/api/techVendorManagement'),
      ],
    },
    crossRegionDiseaseDataRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/crossRegionDiseaseData'),
        ctx => ctx.path.startsWith('/api/crossRegionDiseaseData'),
      ],
    },

    serviceAppointmentRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/serviceAppointment'),
        ctx => ctx.path.startsWith('/api/serviceAppointment'),
      ],
    },

    // 培训证书
    certificateRouter: {
      match: [ctx => ctx.path.startsWith('/manage/certificate')],
    },
    // 考试大纲
    // examSyllabusRouter: {
    //   match: [ ctx => ctx.path.startsWith('/manage/examSyllabus') ],
    // },
    // 工具
    toolsRouter: {
      match: [ctx => ctx.path.startsWith('/manage/guideMap')],
    },
    // 法律法规
    regulationsRouter: {
      match: [ctx => ctx.path.startsWith('/manage/regulations')],
    },
    operateLogRouter: {
      match: [ctx => ctx.path.startsWith('/manage/operateLog'), ctx => ctx.path.startsWith('/manage/dataQuality')],
    },
    medicalRadiationRouter: {
      match: [ctx => ctx.path.startsWith('/manage/medicalRadiation')],
    },
    enterpriseServeRouter: {
      match: [ctx => ctx.path.startsWith('/manage/enterpriseServe')],
    },
    trainingRecordsRouter: {
      match: [ctx => ctx.path.startsWith('/manage/trainingRecords')],
    },
    registrationRecordsRouter: {
      match: [ctx => ctx.path.startsWith('/manage/registrationRecords')],
    },
    trainingClassesRouter: {
      match: [ctx => ctx.path.startsWith('/manage/trainingClasses')],
    },
    // 职业病危害事故申报
    occupationalHazardAccidentsRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/occupationalHazardAccidents'),
      ],
    },
    // 防护协同巡查管理
    patrolManageRouter: {
      match: [ctx => ctx.path.startsWith('/manage/patrolManage')],
    },
    // 在线监测
    onlineMonitoringRouter: {
      match: [ctx => ctx.path.startsWith('/manage/onlineMonitoring')],
    },
    defendproductsRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/defendproducts'),
        ctx => ctx.path.startsWith('/manage/scrapProduct'),
        ctx => ctx.path.startsWith('/api/defendproducts'),
      ],
    },
    // 场所监测
    workspaceRouter: {
      match: [ ctx => ctx.path.startsWith('/manage/workspace') ],
    },

    crossRegionalChemicalDataRouter: {
      match: [ ctx => ctx.path.startsWith('/manage/crossRegionalChemicalData') ],
    },

    // 定时任务，福州同步内控系统疾控人员账号
    accountSyncEnable: false,
    accountSyncTick: '5 3 0 */1 * ?', // 每天凌晨3点5分执行一次  cron表达式  */1表示刚开始0点触发一次，之后每一天0点触发一次
    dpStatisticalTaskEnable: false, // 大屏统计定时任务开关
    dpStatisticsTick: process.env.dpStatisticsTick || '16 5 0 */1 * ?', // 大屏统计定时任务
    statisticalTaskEnable: false, // 统计定时任务开关
    statisticsTick: '16 3 0 */1 * ?', // 统计定时任务
    warningTaskEnable: false, // 预警处理定时任务开关
    warningTick: '16 1 0 */1 * ?', // 预警处理定时任务,每天凌晨1点16分执行一次

    warningTaskEnable2: process.env.warningTaskEnable2 === 'true', // 预警处理定时任务开关
    warningTick2: process.env.warningTick2 || '15 1 0 */1 * ?', // 预警处理定时任务,每天凌晨1点15分执行一次

    ahStatisticalTaskEnable: false, // 安徽培训大屏统计定时任务开关
    // 危害因素检测超期时间
    jcOverMonths: parseInt(process.env.jcOverMonths || 12), // 检测超期时间 单位：月，默认12个月

    // cdn域名
    origin: 'http://localhost:7003',
    // 加密解密
    session_secret: 'duopu_secret',
    auth_cookie_name: 'duopu_jkqy_super',
    auth_cookie_zyjk_name: 'duopu_jkqy',
    encrypt_key: '243d477d-d6bb-4ab1-b0a9-a1b1c9419562',
    cms_encrypt_key: 'duopu_jkqy',
    salt_aes_key: 'duopu_jkqy',
    salt_sha2_key: '7a58917b-d493-4940-87d4-ab2b0fa8a3b7',
    encryptApp_key: '751f621ea5c8f930',
    encryptApp_vi: '2624750004598718',
    // 密码有效期
    passwordExpiresIn: 90 * 24 * 60 * 60 * 1000,
    passwordValidityPeriod: true, // 是否启用密码有效期功能
    limitLoginAttemps: 3, // 登录尝试次数
    loginAttemptsTimeRange: 10 * 60 * 1000, // 登录连续尝试时间范围
    lockedLoginTime: 10 * 60 * 1000, // 登录锁定时间
    ifAutoUnlock: true, // 是否自动解锁
    // 安全性校验
    security: {
      csrf: {
        enable: false,
      },
      xframe: {
        // 允许iframe跨域全部访问
        enable: false,
      },
    },
    // nunjucks模板引擎
    view: {
      defaultViewEngine: 'nunjucks',
      mapping: {
        '.html': 'nunjucks',
      },
    },

    aliVideo: {
      accessKeyId: 'LTAI4G8etokKrfe3sBn6hjAX',
      secretAccessKey: '******************************',
      userId: '257893414740639727',
    },

    proxy: true,
    ipHeaders: 'X-Real-IP, X-Forwarded-For',
    // 输出日志到终端
    logger: {
      disableConsoleAfterReady: false,
      dir: '/opt/log/jg/',
    },

    // 不同端的域名
    domainNames: {
      cms: 'http://127.0.0.1:8080',
      super: '127.0.0.1:7003',
      operate: 'http://127.0.0.1:7005',
      enterprise: '127.0.0.1:7001',
      service: '127.0.0.1:7007',
      px: 'http://127.0.0.1:3001',
      tj: 'http://127.0.0.1:7008',
      portal: process.env.PORTAL_URI || 'https://xjbtportal.jkqy.cn',
    },

    xjbtportal: 'https://xjbtportal.jkqy.cn',

    // 国际化
    i18n: {
      defaultLocale: 'zh-CN',
    },
    // 百度地图 url和ak
    bmap: {
      url: 'https://api.map.baidu.com/geocoding/v3/',
      ak: 'k14GSWM76SjqfMZcdOWacMb3XmV9sdGl', // old: klt8PdaqU2RBUn3noMpzKtRw5xvMml9y
      styleId: 'bf656ab5c64b329624efdd27a48e953b', // old:c5212ceaa7fe7147d983983febb8ecbd
    },
    gmap: {
      url: 'https://restapi.amap.com/v3/geocode/geo',
      key: 'c2755a184bf2bcbd83cb73aa0b501187',
    },
    tiandimap: {
      url: 'https://api.tianditu.gov.cn',
      key: process.env.TIANDITU_KEY || '86c8250b076733e242f4f3c39adb035a',
      front_key: 'c12f57a65df6ea3e27dcb1355bdc05d9',
    },
    // 单点登录 originUrl白名单
    signInWhitelist: [
      // 'https://platform.jiandu-dev.ilabservice.cloud', // 释普开发环境
      'https://wj.platform.ilabservice.cloud', // 释普生产环境
      'http://127.0.0.1:7001', // 本地测试
      'ahphi.com', // 安徽培训
      'jkqy.cn', // 测试环境
      'http://192.168.19.53:3006', // 协同指挥
      'https://xjbtxtzh.jkqy.cn', // 新疆兵团 协同指挥
    ],

    // 当前所属分支
    branch: '', // 'hf', 'yc' 不填默认是master
    platformName: process.env.platformName || '职业健康数字化平台', // 平台名称 - 主菜单左上角处的名称
    platformLogoShow: process.env.platformLogoShow !== '0', // 平台主菜单左上角logo显示 - 环境变量配置时请用 0 设置隐藏
    systemName: '职业健康数字化管理系统', // 大屏顶部系统名称

    recheckOverTime: 90 * 24 * 60 * 60000, // 复查超期时间 90天

    adminID: ['2at7_s2yO'], // 该项目最高管理员账户ID

    editSwitch: false, // 编辑企业信息的权限开关[zhj新需求，在运营端的监管用户哪里进行动态设置是否可以编辑企业的信息]
    iServiceHost: process.env.iServiceHost || 'http://iservice',
    iService2Host: process.env.iService2Host || 'http://iservicev2',
    xjbtjdglHost: process.env.xjbtjdglHost || 'https://xjbtjdglywbackend.jkqy.cn',
    coolHost: 'https://cool.zyws.cn',
    ecdhOptions: {
      publicKey:
        'BJJjGs7NQ0e8dDwVCTC27DTl6f7AHG+wEQJzY+VlZorevBgFD79nKhQzsgHX+/5J7RsbgQzGTjG9qmN0l+Fy6k4=',
      privateKey: 'IVWPS8OQ0IeXAC5l8ALFPpFHnMBEkD7eYgOSn3HIoWI=',
    },
    optEcdhOptions: {
      publicKey:
        'BKd3or+Xh9vZ3pAmRWUCkOV4rjNnL6zECIopC75fACBZq/48+RM34B24VrN1etcLKe7hIufmjgdg+P3mDqGdwiI=',
      sharedKey: 'NEbhf0HzVUS1L6CY9terLyk6Zw22kI6RwtFU9czcMJM=',
    },
    loginEcdhOptions: {
      publicKey:
        'BBUDmOU+e3Qn0crYi/qUgoCL2kUWZEu3EmqP9rWZfINOOIdFQ3iJ6ZKJAVQPcR7eGAa71tzjaBmzljIHqGN/53Q=',
      privateKey: 'Bu9yx05RM2L+PzUZArOROUf5gblZu9ngNGCE+lKRxCY=',
      sharedKey: '2U8//VqSBjze1nja0N5jDX2arBceo5qJWjpgrwGpWx0=',
      // "BPYpo+ceP7OV0D7s2GC6Le6Wy8sfSggGz9uOGYe8/HztEuSZl6rSMq1CRHnShrRLdjGj6zYGsXCupkBBDymo86g="
      // "IbI2Ujoh7vWB1cgH9W85Dz+ai7qw6jRR9astcTpramY="
    },
    rabbitmq: {
      url: `amqp://${process.env.rmqUser}:${process.env.rmqPass}@${process.env.rmqHost}:${process.env.rmqPort}`,
      queue: [
        {
          name: 'zyjk_jg_file_export',
          callback: 'jgFileExport',
        },
      ],
    },
    io: {
      init: {}, // 传递给 engine.io
      namespace: {
        '/': {
          connectionMiddleware: [],
          packetMiddleware: [],
        },
      },
    },
    PEISIntergrationEnable: false, // 是否启用体检系统集成

    // 声网配置
    agora: {
      appId: process.env.AGORA_APP_ID,
      appCertificate: process.env.AGORA_APP_CERTIFICATE,
      customerKey: process.env.AGORA_CUSTOMER_KEY,
      customerSecret: process.env.AGORA_CUSTOMER_SECRET,
    },

    // 证书配置
    certificateConfig: {
      effectiveYear: 3, // 证书有效时间 单位：年
      trainingType: [1], // 1监管要求的培训 2自主/公开培训 3企业组织的员工培训
    },
    // 培训管理配置
    trainingConfig: {
      trainingClassesReviewLevel: [2, 3, 4], // 1 省 2 市3 区/县 4 街道/镇
      trainingRecordsReviewLevel: [2, 3, 4], // 1 省 2 市3 区/县 4 街道/镇
    },

    // 是否开启数据库加密，true为开启，false为关闭
    dbEncryption: process.env.DB_ENCRYPTION === 'true',
    // 是否允许福州国密接口调用
    ALLOW_FZGM: process.env.ALLOW_FZGM === 'true',
    // 是否允许新疆兵团GM接口调用
    ALLOW_XJBTGM: process.env.ALLOW_XJBTGM === 'true',
    // 职业病诊断测试环境配置
    iServiceZdHost: process.env.iServiceZdHost || 'http://*************:8888',
    // 职业病鉴定测试环境配置
    iServiceJdHost: process.env.iServiceJdHost || 'http://*************:8889',
    // 科研协作数据展示测试环境配置
    iServiceKyxzHost: process.env.iServiceKyxzHost || 'https://xjbtkywbxzbackend.jkqy.cn',
    // 职业病病人全程服务数据展示测试环境配置
    iServiceBzHost: process.env.iServiceBzHost || 'http://*************:8012',
    xjbthazardStatisticsTick: {
      corn: process.env.xjbthazardStatisticsTick || '0 0 0 * * ?',
      disable: process.env.xjbthazardStatisticsTickDisable === 'true',
    },
    // 重定向登录页
    redirectLoginPage: process.env.LOGIN_PAGE,

    // 数据质量监测定时任务配置
    dataQualityMonitoringTick: process.env.dataQualityMonitoringTick || '10 1 0 */1 * ?', // 默认每天凌晨1点10分执行一次
    dataQualityMonitoringEnable: process.env.dataQualityMonitoringEnable === 'true', // 任务开关
  };
};
