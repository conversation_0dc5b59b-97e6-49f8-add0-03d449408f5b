const path = require('path');
exports.security = {
  enable: true,
  package: 'egg-security',
};
exports.mongoose = {
  enable: true,
  package: 'egg-mongoose',
};

exports.jk_adminorg = {
  enable: true,
  package: 'egg-jk-adminorg',
  path: path.join(__dirname, '../lib/plugin/egg-jk-adminorg'),
};

exports.nunjucks = {
  enable: true,
  package: 'egg-view-nunjucks',
};

exports.validate = {
  enable: true,
  package: 'egg-dora-validate',
};

exports.jk_serviceOrg = {
  enable: true,
  package: 'egg-jk-serviceOrg',
  path: path.join(__dirname, '../lib/plugin/egg-jk-serviceOrg'),
};

// doraUploadFilePluginBegin
exports.doraUploadFile = {
  enable: true,
  package: 'egg-dora-uploadfile',
};
// doraUploadFilePluginEnd
exports.jk_waringAndSupervision = {
  enable: true,
  package: 'egg-jk-waringAndSupervision',
  path: path.join(__dirname, '../lib/plugin/egg-jk-waringAndSupervision'),
};
exports.jk_training = {
  enable: true,
  package: 'egg-jk-training',
  path: path.join(__dirname, '../lib/plugin/egg-jk-training'),
};

exports.jk_videoLib = {
  enable: true,
  package: 'egg-jk-videoLib',
  path: path.join(__dirname, '../lib/plugin/egg-jk-videoLib'),
};
exports.jk_threeSimultaneous = {
  enable: true,
  package: 'egg-jk-threeSimultaneous',
  path: path.join(__dirname, '../lib/plugin/egg-jk-threeSimultaneous'),
};

exports.jk_diagnostics = {
  enable: true,
  package: 'egg-jk-diagnostics',
  path: path.join(__dirname, '../lib/plugin/egg-jk-diagnostics'),
};

exports.jk_certificate = {
  enable: true,
  package: 'egg-jk-certificate',
  path: path.join(__dirname, '../lib/plugin/egg-jk-certificate'),
};

exports.jk_checkLawEnforcementData = {
  enable: true,
  package: 'egg-jk-checkLawEnforcementData',
  path: path.join(__dirname, '../lib/plugin/egg-jk-checkLawEnforcementData'),
};

exports.jk_examSyllabus = {
  enable: true,
  package: 'egg-jk-examSyllabus',
  path: path.join(__dirname, '../lib/plugin/egg-jk-examSyllabus'),
};

exports.jk_eHealthRecord = {
  enable: true,
  package: 'egg-jk-eHealthRecord',
  path: path.join(__dirname, '../lib/plugin/egg-jk-eHealthRecord'),
};

exports.jk_physicalExaminationOrg = {
  enable: true,
  package: 'egg-jk-physicalExaminationOrg',
  path: path.join(__dirname, '../lib/plugin/egg-jk-physicalExaminationOrg'),
};

exports.jk_tools = {
  enable: true,
  package: 'egg-jk-tools',
  path: path.join(__dirname, '../lib/plugin/egg-jk-tools'),
};

exports.jk_regulations = {
  enable: true,
  package: 'egg-jk-regulations',
  path: path.join(__dirname, '../lib/plugin/egg-jk-regulations'),
};

exports.jk_serviceAppointment = {
  enable: true,
  package: 'egg-jk-serviceAppointment',
  path: path.join(__dirname, '../lib/plugin/egg-jk-serviceAppointment'),
};

exports.jk_butlerServe = {
  enable: true,
  package: 'egg-jk-butlerServe',
  path: path.join(__dirname, '../lib/plugin/egg-jk-butlerServe'),
};

exports.jk_enterpriseServe = {
  enable: true,
  package: 'egg-jk-enterpriseServe',
  path: path.join(__dirname, '../lib/plugin/egg-jk-enterpriseServe'),
};

exports.jk_testPaper = {
  enable: true,
  package: 'egg-jk-testPaper',
  path: path.join(__dirname, '../lib/plugin/egg-jk-testPaper'),
};

exports.jk_questionBank = {
  enable: true,
  package: 'egg-jk-questionBank',
  path: path.join(__dirname, '../lib/plugin/egg-jk-questionBank'),
};
exports.jk_operateLog = {
  enable: true,
  package: 'egg-jk-operateLog',
  path: path.join(__dirname, '../lib/plugin/egg-jk-operateLog'),
};
exports.jk_medicalRadiation = {
  enable: true,
  package: 'egg-jk-medicalRadiation',
  path: path.join(__dirname, '../lib/plugin/egg-jk-medicalRadiation'),
};
exports.jk_trainingRecords = {
  enable: true,
  package: 'egg-jk-trainingRecords',
  path: path.join(__dirname, '../lib/plugin/egg-jk-trainingRecords'),
};
exports.jk_registrationRecords = {
  enable: true,
  package: 'egg-jk-registrationRecords',
  path: path.join(__dirname, '../lib/plugin/egg-jk-registrationRecords'),
};
exports.jk_trainingClasses = {
  enable: true,
  package: 'egg-jk-trainingClasses',
  path: path.join(__dirname, '../lib/plugin/egg-jk-trainingClasses'),
};
exports.jk_recordManage = {
  enable: true,
  package: 'egg-jk-recordManage',
  path: path.join(__dirname, '../lib/plugin/egg-jk-recordManage'),
};

exports.jk_quality = {
  enable: true,
  package: 'egg-jk-quality',
  path: path.join(__dirname, '../lib/plugin/egg-jk-quality'),
};

// EvaluationSystemService
exports.jk_evaluationSystem = {
  enable: true,
  package: 'egg-jk-evaluationSystem',
  path: path.join(__dirname, '../lib/plugin/egg-jk-evaluationSystem'),
};

exports.jk_unitResult = {
  enable: true,
  package: 'egg-jk-unitResult',
  path: path.join(__dirname, '../lib/plugin/egg-jk-unitResult'),
};

exports.jk_occupationalHazardAccidents = {
  enable: true,
  package: 'egg-jk-occupationalHazardAccidents',
  path: path.join(
    __dirname,
    '../lib/plugin/egg-jk-occupationalHazardAccidents'
  ),
};

exports.jk_OnlineDeclaration = {
  enable: true,
  package: 'egg-jk-onlineDeclaration',
  path: path.join(__dirname, '../lib/plugin/egg-jk-onlineDeclaration'),
};
exports.jk_dataRuleManage = {
  enable: true,
  package: 'egg-jk-dataRuleManage',
  path: path.join(__dirname, '../lib/plugin/egg-jk-dataRuleManage'),
};
exports.jk_onlineMonitoring = {
  enable: true,
  package: 'egg-jk-onlineMonitoring',
  path: path.join(__dirname, '../lib/plugin/egg-jk-onlineMonitoring'),
};

exports.jk_Defend_Products = {
  enable: true,
  package: 'egg-jk-defendproducts',
  path: path.join(__dirname, '../lib/plugin/egg-jk-defendproducts'),
};

exports.jk_Radiation_Therapy = {
  enable: true,
  package: 'egg-jk-radiationTherapy',
  path: path.join(__dirname, '../lib/plugin/egg-jk-radiationTherapy'),
};

exports.jk_jointEnforcement = {
  enable: true,
  package: 'egg-jk-jointEnforcement',
  path: path.join(__dirname, '../lib/plugin/egg-jk-jointEnforcement'),
};

exports.jk_crossRegionLawEnforcementShare = {
  enable: true,
  package: 'egg-jk-crossRegionLawEnforcementShare',
  path: path.join(__dirname, '../lib/plugin/egg-jk-crossRegionLawEnforcementShare'),
};

exports.jk_kqycheckCenter = {
  enable: true,
  package: 'egg-jk-kqycheckCenter',
  path: path.join(__dirname, '../lib/plugin/egg-jk-kqycheckCenter'),
};

exports.jk_kqydiagnosisCenter = {
  enable: true,
  package: 'egg-jk-kqydiagnosisCenter',
  path: path.join(__dirname, '../lib/plugin/egg-jk-kqydiagnosisCenter'),
};

exports.jk_kqyIdentificationCenter = {
  enable: true,
  package: 'egg-jk-kqyIdentificationCenter',
  path: path.join(__dirname, '../lib/plugin/egg-jk-kqyIdentificationCenter'),
};

exports.jk_kqyInvestigation = {
  enable: true,
  package: 'egg-jk-kqyInvestigation',
  path: path.join(__dirname, '../lib/plugin/egg-jk-kqyInvestigation'),
};

exports.jk_kqySupervise = {
  enable: true,
  package: 'egg-jk-kqySupervise',
  path: path.join(__dirname, '../lib/plugin/egg-jk-kqySupervise'),
};

exports.jk_kqyFollowUp = {
  enable: true,
  package: 'egg-jk-kqyFollowUp',
  path: path.join(__dirname, '../lib/plugin/egg-jk-kqyFollowUp'),
};

exports.jk_contactOfficer = {
  enable: true,
  package: 'egg-jk-contactOfficer',
  path: path.join(__dirname, '../lib/plugin/egg-jk-contactOfficer'),
};

exports.jk_talentPool = {
  enable: true,
  package: 'egg-jk-talentPool',
  path: path.join(__dirname, '../lib/plugin/egg-jk-talentPool'),
};

exports.jk_caseLib = {
  enable: true,
  package: 'egg-jk-caseLib',
  path: path.join(__dirname, '../lib/plugin/egg-jk-caseLib'),
};

exports.jk_crossRegionDiseaseData = {
  enable: true,
  package: 'egg-jk-crossRegionDiseaseData',
  path: path.join(__dirname, '../lib/plugin/egg-jk-crossRegionDiseaseData'),
};

exports.jk_crossRegionHealthShare = {
  enable: true,
  package: 'egg-jk-crossRegionHealthShare',
  path: path.join(__dirname, '../lib/plugin/egg-jk-crossRegionHealthShare'),
};

exports.jk_crossRegionalChemicalData = {
  enable: true,
  package: 'egg-jk-crossRegionalChemicalData',
  path: path.join(__dirname, '../lib/plugin/egg-jk-crossRegionalChemicalData'),
};

exports.jk_workspace = {
  enable: true,
  package: 'egg-jk-workspace',
  path: path.join(__dirname, '../lib/plugin/egg-jk-workspace'),
};

exports.jk_techVendorManagement = {
  enable: true,
  package: 'egg-jk-techVendorManagement',
  path: path.join(__dirname, '../lib/plugin/egg-jk-techVendorManagement'),
};

// // rabbitmq amqplib
// exports.amqplib = {
//   enable: true,
//   package: 'amqplib',
// };

exports.io = {
  enable: true,
  package: 'egg-socket.io',
};

exports.redis = {
  enable: true,
  package: 'egg-redis',
};
