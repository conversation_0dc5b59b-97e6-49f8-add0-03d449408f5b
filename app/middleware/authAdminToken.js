const {
  authToken,
} = require('@utils');
const _ = require('lodash');
const moment = require('moment');
module.exports = (options, app) => {

  const routeWhiteList = [
    '/admin/login',
    '/qy/login',
    '/user/login',
    '/jk-admin',
    '/admin/dashboard/visualizationFz',
    '/sse',
  ];

  return async function authAdminToken(ctx, next) {
    const url = ctx.request.url;
    ctx.session.superUserInfo = '';
    let userToken = '';
    // const presentUser = await ctx.model.SuperUser.findOne({ phoneNum: '13003699705' });// 福州的账号
    // let password;// 密码
    // let userName;// 用户名
    // console.log(presentUser, 'presentUsersssssssssss');
    const getTokenFromCookie = ctx.get('Authorization') || ctx.cookies.get('admin_' + app.config.auth_cookie_name);
    if (ctx.request.method === 'GET') {
      userToken = ctx.query.token || getTokenFromCookie;
    } else if (ctx.request.method === 'POST') {
      userToken = ctx.request.body.token || getTokenFromCookie;
    } else if (ctx.request.method === 'PUT') {
      userToken = ctx.request.body.token || getTokenFromCookie;
    } else if (ctx.request.method === 'DELETE') {
      userToken = ctx.request.body.token || getTokenFromCookie;
    }
    if (userToken) {
      if (url === '/manage/emptyRequest') {
        // 更新cookies时间
        ctx.cookies.set('admin_' + app.config.auth_cookie_name, userToken, {
          path: '/',
          maxAge: app.config.superUserMaxAge,
          signed: true,
          httpOnly: false,
        });
      }
      const checkToken = await authToken.checkToken(userToken, app.config.encrypt_key);
      // ctx.auditLog('有chekToken的情况', checkToken, 'info');
      if (checkToken) {
        if (typeof checkToken === 'object') {
          const _id = checkToken._id;

          const targetUser = await ctx.service.superUser.item(ctx, {
            query: {
              _id,
            },
            populate: [
              {
                path: 'group',
                select: 'power _id enable name',
              },
              {
                path: 'members.group',
                select: 'power _id enable name',
              },
            ],
            files: {
              password: 0,
              email: 0,
            },
          }, { lean: 1 });
          // ctx.auditLog('是否有targetUser', targetUser, 'info');
          if (!_.isEmpty(targetUser)) {
            const {
              _id,
              group,
              cname,
              crossRegionAdd,
              phoneNum,
              date,
              powerStatus,
              user_role_power_hmac,
              user_role_power_hmac_algorithm,
              crossRegionArea,
              orgCode = null,
            } = targetUser;
            let { regAdd, area_code, openId = null } = targetUser;
            const { crossRegionManage } = ctx.app.config;
            if (crossRegionManage) {
              regAdd = crossRegionAdd;
              area_code = crossRegionArea;
            }

            // 先判断是否是成员用户，并获取成员信息
            const isManage = checkToken.userName === targetUser.userName;
            let currentMember = null;
            if (!isManage) {
              currentMember = targetUser.members.find(m => m.userName === checkToken.userName);
              if (currentMember.openId) {
                openId = currentMember.openId;
              }
            }

            // 获取用户基本信息
            const userName = checkToken.userName;
            const name = isManage ? (targetUser.name || targetUser.userName) : (currentMember && currentMember.name || '');
            const singId = isManage ? _id : (currentMember && currentMember._id || '');
            const hmac = isManage ? (user_role_power_hmac || '') : (currentMember && currentMember.user_role_power_hmac || '');

            if (!app.config.crossRegionManage && _.isEmpty(group) && (!currentMember || !currentMember.group)) {
              ctx.session = null;
              ctx.cookies.set('admin_' + ctx.app.config.auth_cookie_name, null);
              ctx.cookies.set('admin_doracmsapi', null);
              ctx.body = {
                code: 500,
                msg: '用户信息异常',
              };
              return;
            }

            // 获取权限
            let power = targetUser.power;
            if (!powerStatus) {
              if (!isManage && currentMember && currentMember.group) {
                // 成员用户权限
                power = _.uniq(_.flatMap(currentMember.group, g =>
                  ((g.power && Array.isArray(g.power)) ? g.power : [])
                ));
              } else {
                // 管理员权限
                power = _.uniq(_.flatMap(group, g =>
                  ((g.power && Array.isArray(g.power)) ? g.power : [])
                ));
              }
            }

            if (app.config.dbEncryption && _.isEmpty(hmac)) {
              ctx.session = null;
              ctx.cookies.set('admin_' + ctx.app.config.auth_cookie_name, null);
              ctx.cookies.set('admin_doracmsapi', null);
              ctx.body = {
                code: 500,
                msg: '用户信息异常',
              };
              return;
            }

            if (hmac && app.config.dbEncryption) {
              // 签名验证人员角色权限
              const groupIds = isManage
                ? group.map(g => g._id)
                : (currentMember && currentMember.group ? currentMember.group.map(g => g._id) : []);

              const signString = singId +
                (groupIds.length > 0 ? groupIds.sort().join('') : '') +
                moment(date).format('YYYY-MM-DD HH:mm:ss') +
                powerStatus +
                power.join('');

              ctx.auditLog('监管签名验证字符串', signString, 'info');
              const hmacCheck = await ctx.helper.verifySm3(
                signString,
                user_role_power_hmac_algorithm,
                hmac
              );
              if (!hmacCheck) {
                ctx.auditLog('签名验证失败', signString, 'info');
                ctx.session = null;
                ctx.cookies.set('admin_' + ctx.app.config.auth_cookie_name, null);
                ctx.cookies.set('admin_doracmsapi', null);
                ctx.body = {
                  code: 500,
                  msg: '签名验证失败',
                };
                return;
              }
            }

            ctx.session.superUserInfo = {
              userName,
              cname,
              _id,
              group: isManage
                ? Array.isArray(group)
                  ? group.map(g => g._id)
                  : []
                : currentMember && currentMember.group
                  ? currentMember.group.map(g => g._id)
                  : [],
              regAdd,
              name,
              phoneNum,
              isManage,
              clientId: checkToken.clientId,
              area_code,
              openId,
              orgCode,
              singId,
            };
            // 通过检测密码是否过期来判断是否需要重置密码
            if (app.config.passwordValidityPeriod && targetUser.passwordExpiresAt && ctx.session.superUserInfo.isManage) {
              if ((targetUser.passwordExpiresAt.getTime() + app.config.passwordExpiresIn) < new Date().getTime() && !ctx.session.redirected) {
                console.log('密码已过期，请重置密码');
                ctx.session.redirected = true; // 设置会话中的标识为true
                ctx.redirect('/admin/dashboard?isSetting=true');
              }
            }
            // 检索代号：#0001
            // 根据url动态设置后台根路径 可以用其他的，譬如jwt，这里临时使用session，ctx.state 无效
            if (url.indexOf('/admin') === 0) {
              // console.log(' vvvvvvvvvvvvvvvvvvvvv',app.config.admin_base_path);
              ctx.session.basePath = app.config.admin_base_path;
            } else if (url.indexOf('/qy') === 0) {
              ctx.session.basePath = app.config.qy_base_path;
            } else if (url.indexOf('/user') === 0) {
              ctx.session.basePath = app.config.user_base_path;
            }

            await next();
          } else {
            // ctx.auditLog('在这里重定向吗', url, 'info');
            if (url.indexOf('/admin') === 0) {
              ctx.redirect(`${app.config.admin_base_path}/login`);
            } else if (url.indexOf('/qy') === 0) {
              ctx.redirect(`${app.config.qy_base_path}/login`);
            } else if (url.indexOf('/user') === 0) {
              ctx.redirect(`${app.config.user_base_path}/login`);
            }
            // ctx.redirect('/admin/login');
          }
        } else {
          if (url.indexOf('/admin') === 0) {
            ctx.redirect(`${app.config.admin_base_path}/login`);
          } else if (url.indexOf('/qy') === 0) {
            ctx.redirect(`${app.config.qy_base_path}/login`);
          } else if (url.indexOf('/user') === 0) {
            ctx.redirect(`${app.config.user_base_path}/login`);
          }
          // ctx.redirect('/admin/login');
        }

      } else {
        if (url.indexOf('/admin') === 0) {
          ctx.redirect(`${app.config.admin_base_path}/login`);
        } else if (url.indexOf('/qy') === 0) {
          ctx.redirect(`${app.config.qy_base_path}/login`);
        } else if (url.indexOf('/user') === 0) {
          ctx.redirect(`${app.config.user_base_path}/login`);
        }
        // ctx.redirect('/admin/login');
      }
    } else {
      // 没有配置但是包含在白名单内的路由校验
      if (!_.isEmpty(routeWhiteList)) {
        const checkWhiteRouter = _.filter(routeWhiteList, item => {
          return ctx.originalUrl.indexOf(item) >= 0;
        });
        if (!_.isEmpty(checkWhiteRouter)) {
          if (url === '/admin/dashboard/visualizationFz') {
            const formObj = {
              phoneNum: app.config.cockpikAccount,
            };
            const presentUser = await ctx.service.superUser.item(ctx, {
              query: {
                $or: [
                  formObj, // 管理员
                  { members: { $elemMatch: formObj } }, // 成员
                ],
              },
              populate: [
                {
                  path: 'group',
                  select: 'power _id enable name',
                },
              ],
              files:
                'enable password _id userName members loginAttempts loginAttemptsTimestamp passwordExpiresAt',
            });
            // ctx.auditLog('可以进到这里', presentUser, 'info');
            let password; // 密码
            let userName; // 用户名
            if (!_.isEmpty(presentUser)) {
              password = presentUser.password;
              userName = presentUser.userName;
              await ctx.service.adminUser.createToken(
                { userName, password },
                presentUser
              );
              ctx.redirect(
                `${app.config.admin_base_path}/dashboard/visualizationFz`
              );
            }
          } else {
            if (url.indexOf('/admin/login') !== -1) {
              await next();
            } else {
              ctx.redirect(`${app.config.admin_base_path}/login`);
            }
          }
        } else {
          if (url.indexOf('/admin') === 0) {
            ctx.redirect(`${app.config.admin_base_path}/login`);
          } else if (url.indexOf('/qy') === 0) {
            ctx.redirect(`${app.config.qy_base_path}/login`);
          } else if (url.indexOf('/user') === 0) {
            ctx.redirect(`${app.config.user_base_path}/login`);
          }
        }
      } else {
        // if (true) { // 如何是福州则不重定向，直接登录
        //   console.log('sssssssssssssssscccccc');
        //   await ctx.service.adminUser.loginAction({ userName: 'hzwsjkzfd', password: '123456' });
        // } else {
        // ctx.auditLog('当前分支', app.config.branch, 'info');
      }

    }

  };

};
