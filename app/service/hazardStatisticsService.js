/* eslint-disable no-dupe-keys */

const axios = require('axios');
const Service = require('egg').Service;
const moment = require('moment');

class HazardStatisticsService extends Service {
  /**
   * 计算并更新统计数据
   * @param {Object} options - 计算选项
   * @param {String} options.timeFrame - 时间维度 (daily, weekly, monthly, quarterly, yearly, all)
   * @param {Object} options.filter - 筛选条件
   * @return {Object} - 更新后的统计数据
   */
  async calculateAndUpdateStatistics(options = {}) {
    const { timeFrame = 'all', filter = {} } = options;
    const { ctx } = this;

    // 1. 设置查询时间点
    const timePoint = this.getTimePoint(timeFrame);

    // 2. 构建基础查询条件
    const baseQuery = await this.buildBaseQuery(filter);

    try {
      // 3. 计算各项统计数据
      const [
        enterpriseCount,
        hazardFactorsStats,
        industryStats,
        regionStats,
        mapData,
      ] = await Promise.all([
        this.calculateEnterpriseCount(baseQuery),
        this.calculateHazardFactorsStats(baseQuery),
        this.calculateIndustryStats(baseQuery),
        this.calculateRegionStats(baseQuery),
        this.generateMapData(baseQuery),
      ]);

      // 4. 保存或更新统计结果
      const statsDoc = await ctx.model.HazardStatistics.findOneAndUpdate(
        {
          timeFrame,
          timePoint,
          'filterConditions.regionCode': filter.regionCode || null,
          'filterConditions.industryCode': filter.industryCode || null,
        },
        {
          enterpriseCount,
          hazardFactorsStats,
          industryStats,
          regionStats,
          mapData,
          filterConditions: {
            hazardFactors: filter.hazardFactors || [],
            regionCode: filter.regionCode || null,
            industryCode: filter.industryCode || null,
            dateRange: {
              start: filter.startDate || null,
              end: filter.endDate || null,
            },
          },
          updatedAt: new Date(),
          lastCalculationTime: new Date(),
        },
        {
          new: true,
          upsert: true, // 如果不存在则创建
          setDefaultsOnInsert: true,
        }
      );

      return statsDoc;
    } catch (error) {
      this.ctx.logger.error('计算统计数据失败:', error);
      throw error;
    }
  }

  /**
   * 根据时间维度获取时间点
   * @param {String} timeFrame - 时间维度 (daily, weekly, monthly, quarterly, yearly, all)
   * @return {Date} - 时间点
   */
  getTimePoint(timeFrame) {
    const now = moment();

    switch (timeFrame) {
      case 'daily':
        return now.startOf('day').toDate();
      case 'weekly':
        return now.startOf('week').toDate();
      case 'monthly':
        return now.startOf('month').toDate();
      case 'quarterly':
        return now.startOf('quarter').toDate();
      case 'yearly':
        return now.startOf('year').toDate();
      case 'all':
      default:
        return new Date(2000, 0, 1); // 固定参考点
    }
  }

  /**
   * 构建基础查询条件
   * @param {Object} filter - 筛选条件
   * @return {Object} - 查询条件
   */
  async buildBaseQuery(filter) {
    const { ctx } = this;
    const andConds = [];
    const base = { isDelete: false, productionStatus: '2' };

    // 时间范围
    if (filter.startDate && filter.endDate) {
      andConds.push({ reportTime: { $gte: new Date(filter.startDate), $lte: new Date(filter.endDate) } });
    }

    // 用户区域筛选（code -> 名称）
    if (filter.regionCode) {
      try {
        const districtDoc = await ctx.model.District.findOne({ area_code: filter.regionCode }).lean();
        if (districtDoc && districtDoc.name) {
          const regionName = districtDoc.name;
          andConds.push({ $or: [
            { 'workAddress.districts': regionName },
            { districtRegAdd: { $in: [ regionName ] } },
          ] });
        }
      } catch (e) {
        this.ctx.logger.warn('区域筛选解析失败:', e.message);
      }
    }

    // 管理权限范围筛选（仅允许查看自己辖区的数据）
    try {
      const sessionUser = ctx.session && ctx.session.superUserInfo ? ctx.session.superUserInfo : null;
      if (sessionUser) {
        const crossRegionManage = ctx.app.config && ctx.app.config.crossRegionManage;
        const rawAdds = (crossRegionManage && Array.isArray(sessionUser.crossRegionAdd) && sessionUser.crossRegionAdd.length > 0)
          ? sessionUser.crossRegionAdd
          : (Array.isArray(sessionUser.regAdd) ? sessionUser.regAdd : []);

        const GENERIC = new Set([ '中国', '中华人民共和国', '新疆维吾尔自治区', '新疆维吾尔自治区直辖', '建设兵团', '新疆生产建设兵团' ]);
        const allowed = new Set();
        if (Array.isArray(rawAdds) && rawAdds.length > 0) {
          if (rawAdds.some(v => Array.isArray(v))) {
            rawAdds.forEach(path => { if (Array.isArray(path) && path.length > 0) { const last = path[path.length - 1]; if (last && !GENERIC.has(last)) allowed.add(last); } });
          } else {
            const last = rawAdds[rawAdds.length - 1];
            if (last && !GENERIC.has(last)) allowed.add(last);
          }
        }
        if (allowed.size > 0) {
          andConds.push({ $or: [
            { 'workAddress.districts': { $in: Array.from(allowed) } },
            { districtRegAdd: { $in: Array.from(allowed) } },
          ] });
        }
      }
    } catch (e) {
      // 忽略权限筛选异常，保持基础查询
      this.ctx.logger.warn('权限范围筛选解析失败:', e.message);
    }

    // 行业筛选
    if (Array.isArray(filter.industryCodes) && filter.industryCodes.length > 0) {
      andConds.push({ industryCategory: { $elemMatch: { $elemMatch: { $in: filter.industryCodes } } } });
    } else if (filter.industryCode) {
      andConds.push({ industryCategory: { $elemMatch: { $elemMatch: { $eq: filter.industryCode } } } });
    }

    const query = { ...base };
    if (andConds.length > 0) query.$and = andConds;
    try {
      ctx.logger.info('[hazard.buildBaseQuery] %s', JSON.stringify(query));
    } catch (e) {
      // ignore
    }
    return query;
  }

  /**
   * 计算企业数量统计
   * @param {Object} baseQuery - 基础查询条件
   * @return {Object} - 企业数量统计结果
   */
  async calculateEnterpriseCount(baseQuery) {
    const { ctx } = this;

    // 总企业数
    const total = await ctx.model.Adminorg.countDocuments(baseQuery);

    // 已检查企业数(有报告时间的企业)
    const checked = await ctx.model.Adminorg.countDocuments({
      ...baseQuery,
      reportTime: { $exists: true, $ne: null },
    });

    // 超标企业数(任一检测结果超标)
    const exceedingQuery = {
      ...baseQuery,
      $or: [
        { 'checkResult.dust.exceed': { $exists: true, $ne: '0' } },
        { 'checkResult.chemical.exceed': { $exists: true, $ne: '0' } },
        { 'checkResult.noise.exceed': { $exists: true, $ne: '0' } },
        { 'checkResult.radiation.exceed': { $exists: true, $ne: '0' } },
        { 'checkResult.biological.exceed': { $exists: true, $ne: '0' } },
        { 'checkResult.heat.exceed': { $exists: true, $ne: '0' } },
      ],
    };

    const exceeding = await ctx.model.Adminorg.countDocuments(exceedingQuery);

    return { total, checked, exceeding };
  }

  /**
   * 计算危害因素统计
   * @param {Object} baseQuery - 基础查询条件
   * @return {Array} - 危害因素统计结果
   */
  async calculateHazardFactorsStats(baseQuery) {
    const { ctx } = this;
    const stats = [];

    // 获取所有危害因素类型
    const hazardTypes = [
      { type: 'dust', name: '粉尘' },
      { type: 'chemical', name: '化学物质' },
      { type: 'noise', name: '噪声' },
      { type: 'radiation', name: '辐射' },
      { type: 'biological', name: '生物因素' },
      { type: 'heat', name: '高温' },
    ];

    // 逐一计算各类型统计
    for (const hazard of hazardTypes) {
      // 查询具有该危害因素的企业
      const hazardQuery = {
        ...baseQuery,
        [`checkResult.${hazard.type}.point`]: { $exists: true, $ne: null },
      };

      const enterprises = await ctx.model.Adminorg.find(
        hazardQuery,
        `_id checkResult.${hazard.type}`
      );

      if (enterprises.length === 0) {
        continue; // 跳过没有数据的危害因素
      }

      // 计算统计数据
      let total = 0;
      let exceed = 0;

      enterprises.forEach(enterprise => {
        const checkResult = enterprise.checkResult[hazard.type];
        if (checkResult) {
          total += parseInt(checkResult.point || 0, 10);
          exceed += parseInt(checkResult.exceed || 0, 10);
        }
      });

      // 计算超标率
      const exceedRate = total > 0 ? (exceed / total) * 100 : 0;

      stats.push({
        type: hazard.type,
        name: hazard.name,
        total,
        exceed,
        exceedRate: parseFloat(exceedRate.toFixed(2)),
        affectedEnterprises: enterprises.length,
        unit: '',
      });
    }

    return stats;
  }

  /**
   * 计算行业分布统计
   * @param {Object} baseQuery - 基础查询条件
   * @return {Array} - 行业分布统计结果
   */
  async calculateIndustryStats(baseQuery) {
    const { ctx } = this;

    // 获取行业分布聚合
    const industryAggregation = await ctx.model.Adminorg.aggregate([
      { $match: baseQuery },
      { $unwind: '$industryCategory' },
      {
        $project: {
          industryCategory: 1,
        },
      },
      {
        $group: {
          _id: {
            $arrayElemAt: [ '$industryCategory', 0 ],
          },
          count: { $sum: 1 },
          enterprises: { $push: '$_id' },
        },
      },
      { $sort: { count: -1 } },
      {
        $lookup: {
          from: 'industryCategory',
          localField: '_id',
          foreignField: 'value',
          as: 'result',
        },
      },
      {
        $addFields: {
          label: { $ifNull: [{ $arrayElemAt: [ '$result.label', 0 ] }, '其他' ] },
        },
      },
      {
        $project: {
          _id: 0,
          name: '$label',
          code: '$_id',
          count: 1,
          enterprises: 1,
        },
      },
    ]);
    // 转换为所需格式
    const stats = [];
    let totalEnterprises = 0;

    // 计算企业总数
    for (const industry of industryAggregation) {
      totalEnterprises += industry.count;
    }

    // 生成行业统计
    for (let i = 0; i < industryAggregation.length; i++) {
      const industry = industryAggregation[i];

      // 计算超标企业数
      const exceedQuery = {
        ...baseQuery,
        _id: { $in: industry.enterprises },
        $or: [
          { 'checkResult.dust.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.chemical.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.noise.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.radiation.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.biological.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.heat.exceed': { $exists: true, $ne: '0' } },
        ],
      };

      const exceedCount = await ctx.model.Adminorg.countDocuments(exceedQuery);

      stats.push({
        code: industry.code,
        name: industry.name,
        level: 1, // 默认为一级，可根据编码规则调整
        parentCode: '', // 根据实际层级填充
        count: industry.count,
        exceedCount,
        percentage: totalEnterprises > 0 ?
          parseFloat(((industry.count / totalEnterprises) * 100).toFixed(2)) : 0,
        ranking: i + 1,
      });
    }

    return stats;
  }

  /**
   * 计算区域分布统计
   * @param {Object} baseQuery - 基础查询条件
   * @return {Array} - 区域分布统计结果
   */
  async calculateRegionStats(baseQuery) {
    const { ctx } = this;

    // 获取区域分布聚合
    const regionAggregation = await ctx.model.Adminorg.aggregate([
      { $match: baseQuery },
      { $match: { districtRegAdd: { $exists: true, $ne: null } } },
      {
        $project: {
          regionCode: {
            $cond: {
              if: { $gte: [{ $size: '$districtRegAdd' }, 2 ] },
              then: { $arrayElemAt: [ '$districtRegAdd', 1 ] },
              else: '其他',
            },
          },
          _id: 1,
        },
      },
      {
        $group: {
          _id: '$regionCode',
          count: { $sum: 1 },
          enterprises: { $push: '$_id' },
        },
      },
      { $sort: { count: -1 } },
      {
        $project: {
          _id: 0,
          name: '$_id',
          count: 1,
          enterprises: 1,
        },
      },
    ]);

    // 转换为所需格式
    const stats = [];
    let totalEnterprises = 0;

    // 计算企业总数
    for (const region of regionAggregation) {
      totalEnterprises += region.count;
    }

    // 生成区域统计
    for (let i = 0; i < regionAggregation.length; i++) {
      const region = regionAggregation[i];

      // 计算超标企业数
      const exceedQuery = {
        ...baseQuery,
        _id: { $in: region.enterprises },
        $or: [
          { 'checkResult.dust.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.chemical.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.noise.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.radiation.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.biological.exceed': { $exists: true, $ne: '0' } },
          { 'checkResult.heat.exceed': { $exists: true, $ne: '0' } },
        ],
      };

      const exceedCount = await ctx.model.Adminorg.countDocuments(exceedQuery);

      stats.push({
        code: region.code,
        name: region.name,
        level: this.getRegionLevel(region.code), // 省/市/区县级别
        parentCode: this.getRegionParentCode(region.code), // 父级区域编码
        count: region.count,
        exceedCount,
        percentage: totalEnterprises > 0 ?
          parseFloat(((region.count / totalEnterprises) * 100).toFixed(2)) : 0,
        ranking: i + 1,
      });
    }

    return stats;
  }

  /**
   * 根据区域代码判断区域级别
   * @param {String} areaCode - 区域编码
   * @return {Number} - 区域级别(1:省级, 2:市级, 3:区县级)
   */
  getRegionLevel(areaCode) {
    if (!areaCode) return 0;

    const codeStr = areaCode.toString();

    if (codeStr.endsWith('0000')) return 1; // 省级
    if (codeStr.endsWith('00')) return 2; // 市级
    return 3; // 区县级
  }

  /**
   * 获取区域父级编码
   * @param {String} areaCode - 区域编码
   * @return {String} - 父级区域编码
   */
  getRegionParentCode(areaCode) {
    if (!areaCode) return '';

    const codeStr = areaCode.toString();
    const level = this.getRegionLevel(areaCode);

    if (level === 1) return ''; // 省级无父级

    if (level === 2) { // 市级，父级为省级
      return codeStr.substring(0, 2) + '0000';
    }

    // 区县级，父级为市级
    return codeStr.substring(0, 4) + '00';
  }

  /**
   * 生成地图数据
   * @param {Object} baseQuery - 基础查询条件
   * @return {Array} - 地图标记点数据
   */
  async generateMapData(baseQuery) {
    const { ctx } = this;

    // 查询所有满足条件的企业
    const enterprises = await ctx.model.Adminorg.find(
      {
        ...baseQuery,
        'workAddress.point': { $exists: true, $ne: [] },
      },
      'cname industryCategory workAddress checkResult'
    );// 限制数量，避免数据过大

    const mapData = [];

    // 预取所有企业在职员工数量，避免循环内多次查询
    const enterpriseIds = enterprises.map(e => e._id);
    const employeeCountsAgg = await ctx.model.Employee.aggregate([
      { $match: { status: 1, $or: [{ EnterpriseID: { $in: enterpriseIds } }, { enterpriseId: { $in: enterpriseIds } }] } },
      { $project: { enterpriseRef: { $ifNull: [ '$EnterpriseID', '$enterpriseId' ] } } },
      { $group: { _id: '$enterpriseRef', count: { $sum: 1 } } },
    ]);
    const employeeCountById = new Map(employeeCountsAgg.map(i => [ String(i._id), i.count ]));

    for (const enterprise of enterprises) {
      // 处理每个工作地址
      for (const workAddr of enterprise.workAddress || []) {
        if (!workAddr.point || workAddr.point.length < 2) continue;

        // 提取危害因素
        const hazardFactors = [];
        const checkResult = enterprise.checkResult || {};

        for (const key of Object.keys(checkResult)) {
          if (checkResult[key] && checkResult[key].point && checkResult[key].point !== '0') {
            hazardFactors.push({
              // {{ AURA: Modify - 增加 type 字段以支持前端按英文类型筛选（dust/chemical/noise/...） }}
              type: key,
              name: checkResult[key].name || key,
              value: parseInt(checkResult[key].point || 0, 10),
              unit: '',
              isExceed: checkResult[key].exceed && checkResult[key].exceed !== '0',
            });
          }
        }

        // 获取行业名称
        const industryName = [];
        if (enterprise.industryCategory && enterprise.industryCategory.length > 0 && enterprise.industryCategory[0][0]) {
          for (const industry of enterprise.industryCategory) {
            const industryRes = await ctx.model.IndustryCategory.findOne({ value: industry[0] });
            industryName.push(industryRes.label);
          }
        }
        if (!workAddr.point[0]) {
          const url = `${this.config.tiandimap.url}/geocoder?ds=${encodeURIComponent(JSON.stringify({ keyWord: enterprise.cname }))}&tk=${this.config.tiandimap.key}`;

          const response = await axios.get(url);
          const data = response.data;
          workAddr.point = [ data.location.lon, data.location.lat ];
          await ctx.model.Adminorg.findByIdAndUpdate(enterprise._id, { $set: { 'workAddress.0.point': workAddr.point } });
        }
        mapData.push({
          enterpriseId: enterprise._id,
          name: enterprise.cname,
          industry: industryName,
          address: workAddr.address || '',
          location: {
            longitude: workAddr.point[0],
            latitude: workAddr.point[1],
          },
          hazardFactors,
          // 在职员工数（预聚合）
          employeeCount: employeeCountById.get(String(enterprise._id)) || 0,
        });
      }
    }

    return mapData;
  }

  /**
   * 根据筛选条件获取最近的统计数据
   * @param {Object} filter - 筛选条件
   * @return {Object} - 统计数据
   */
  async getStatistics(filter = {}) {
    const { ctx } = this;

    // 统一参数：支持前端传入的 region/industry/dateRange 等
    const normalizedFilter = { ...filter };
    // region -> regionCode
    if (!normalizedFilter.regionCode && normalizedFilter.region) {
      normalizedFilter.regionCode = normalizedFilter.region;
    }
    // industry(JSON/string/array) -> industryCode/industryCodes（取每个路径的末级编码）
    if ((!normalizedFilter.industryCode || !normalizedFilter.industryCodes) && normalizedFilter.industry) {
      let industryCode = null; // 首个路径的末级编码（用于缓存命中）
      let industryCodes = []; // 多选末级编码集合（用于查询）
      try {
        const parsed = typeof normalizedFilter.industry === 'string'
          ? JSON.parse(normalizedFilter.industry)
          : normalizedFilter.industry;
        // 可能是多选：[[level1, level2, level3], ...]
        if (Array.isArray(parsed) && parsed.length > 0) {
          parsed.forEach((path, idx) => {
            if (Array.isArray(path) && path.length > 0) {
              const code = path[path.length - 1];
              if (idx === 0) industryCode = code;
              industryCodes.push(code);
            }
          });
        }
      } catch (e) {
        // 如果不是 JSON 数组，直接当作编码使用
        if (typeof normalizedFilter.industry === 'string') {
          industryCode = normalizedFilter.industry;
          industryCodes = [ normalizedFilter.industry ];
        }
      }
      if (industryCode) normalizedFilter.industryCode = industryCode;
      if (industryCodes && industryCodes.length > 0) normalizedFilter.industryCodes = industryCodes;
    }
    // 日期范围标准化：如果传入 dateRange，则展开为 startDate/endDate
    if (!normalizedFilter.startDate && !normalizedFilter.endDate && Array.isArray(normalizedFilter.dateRange) && normalizedFilter.dateRange.length === 2) {
      [ normalizedFilter.startDate, normalizedFilter.endDate ] = normalizedFilter.dateRange;
    }

    // hazardFactors 归一化：支持数组/字符串/逗号分隔/JSON/多键形式
    if (!normalizedFilter.hazardFactors) {
      const keys = Object.keys(filter || {});
      const collected = [];
      // 形式一：hazardFactors[]=dust 或为数组
      if (Array.isArray(filter['hazardFactors[]'])) {
        collected.push(...filter['hazardFactors[]']);
      } else if (filter['hazardFactors[]']) {
        collected.push(filter['hazardFactors[]']);
      }
      // 形式二：hazardFactors=JSON 或 逗号分隔 或 单值
      if (filter.hazardFactors) {
        if (Array.isArray(filter.hazardFactors)) {
          collected.push(...filter.hazardFactors);
        } else if (typeof filter.hazardFactors === 'string') {
          try {
            const parsed = JSON.parse(filter.hazardFactors);
            if (Array.isArray(parsed)) collected.push(...parsed);
            else collected.push(filter.hazardFactors);
          } catch (e) {
            // 逗号分隔或单值
            filter.hazardFactors.split(',').forEach(s => s && collected.push(s));
          }
        }
      }
      // 形式三：hazardFactors[0]=dust&hazardFactors[1]=noise
      keys.filter(k => /^hazardFactors\[\d+\]$/.test(k)).forEach(k => {
        const v = filter[k];
        if (v) collected.push(v);
      });
      if (collected.length > 0) normalizedFilter.hazardFactors = Array.from(new Set(collected));
    }

    // 构建缓存命中查询条件
    const query = { timeFrame: 'all' };
    if (normalizedFilter.regionCode) {
      query['filterConditions.regionCode'] = normalizedFilter.regionCode;
    }
    if (normalizedFilter.industryCode) {
      // 仅记录单一编码用于缓存命中；多选不参与缓存条件，以避免误命中
      query['filterConditions.industryCode'] = normalizedFilter.industryCode;
    }

    // 查询最新的统计数据
    let stats = await ctx.model.HazardStatistics.findOne(query)
      .sort({ updatedAt: -1 });

    // 判断数据是否过期(超过1天)
    const isStale = !stats ||
      (new Date() - new Date(stats.updatedAt)) > 24 * 60 * 60 * 1000;

    // 如果数据不存在或已过期，重新计算
    if (isStale) {
      stats = await this.calculateAndUpdateStatistics({
        timeFrame: 'all',
        filter: normalizedFilter,
      });
    }

    // 根据筛选条件过滤危害因素（仅后端生效，前端无需再次过滤）
    if (stats && normalizedFilter.hazardFactors && normalizedFilter.hazardFactors.length > 0) {
      // 将 'physical' 归并到 ['noise','heat']
      const expandFactors = new Set();
      normalizedFilter.hazardFactors.forEach(f => {
        if (f === 'physical') {
          expandFactors.add('noise');
          expandFactors.add('heat');
        } else {
          expandFactors.add(f);
        }
      });

      // 严格过滤危害因素统计
      if (Array.isArray(stats.hazardFactorsStats)) {
        stats.hazardFactorsStats = stats.hazardFactorsStats.filter(h => expandFactors.has(h.type));
      }

      // 地图点位：与筛选一致，仅保留包含这些危害因素的企业（兼容历史数据仅有中文名称的情况）
      if (Array.isArray(stats.mapData)) {
        const mapNameToCode = nameOrType => {
          if (!nameOrType) return '';
          const val = String(nameOrType);
          const codes = [ 'dust', 'chemical', 'noise', 'radiation', 'biological', 'heat' ];
          if (codes.includes(val)) return val; // 已是英文 code
          if (val.includes('粉尘')) return 'dust';
          if (val.includes('化学')) return 'chemical';
          if (val.includes('噪声')) return 'noise';
          if (val.includes('辐射')) return 'radiation';
          if (val.includes('生物')) return 'biological';
          if (val.includes('高温')) return 'heat';
          return '';
        };
        stats.mapData = stats.mapData.filter(item => {
          if (!Array.isArray(item.hazardFactors) || item.hazardFactors.length === 0) return false;
          return item.hazardFactors.some(hf => {
            if (!hf) return false;
            const code = hf.type ? mapNameToCode(hf.type) : mapNameToCode(hf.name);
            return expandFactors.has(code);
          });
        });
      }
    }

    return stats;
  }
}

module.exports = HazardStatisticsService;
