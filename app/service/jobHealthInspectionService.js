const Service = require('egg').Service;

class JobHealthInspectionService extends Service {
  async queryAll(rawFilter = {}) {
    const { ctx } = this;
    const filter = this.normalizeFilter(rawFilter);

    // 构建 JobHealth 基础筛选
    const baseMatch = this.buildJobHealthMatch(filter);

    // 权限范围（辖区）限制，仅在未显式传入 regionCode 时生效；基于 JobHealth.workPlaces.workAdd 过滤
    const allowedWorkAdds = await this.getAllowedWorkAddsBySession();
    if (!filter.regionCode && allowedWorkAdds && allowedWorkAdds.length > 0) {
      baseMatch.$and = baseMatch.$and || [];
      baseMatch.$and.push({
        workPlaces: { $elemMatch: { workAdd: { $in: allowedWorkAdds } } },
      });
    }

    // 读取 JobHealth + Adminorg 基础数据
    const jobAgg = [
      { $match: baseMatch },
      {
        $lookup: {
          from: 'adminorgs',
          localField: 'EnterpriseID',
          foreignField: '_id',
          as: 'adminorg',
        },
      },
      { $unwind: '$adminorg' },
      {
        $match: {
          'adminorg.isactive': { $ne: '2' },
          $or: [{ 'adminorg.isDelete': false }, { 'adminorg.isDelete': null }],
          'adminorg.productionStatus': { $ne: '0' },
        },
      },
      {
        $project: {
          _id: 1,
          EnterpriseID: 1,
          completeTime: 1,
          formData: 1,
          workPlaces: 1,
          adminorg: {
            _id: 1,
            cname: 1,
            industryCategory: 1,
            workAddress: 1,
            checkResult: 1,
          },
        },
      },
    ];

    // 行业筛选：在联查到 adminorg 后执行，确保以企业行业末级编码过滤
    if (filter.industryCodes && filter.industryCodes.length > 0) {
      jobAgg.push({
        $match: { 'adminorg.industryCategory': { $elemMatch: { $elemMatch: { $in: filter.industryCodes } } } },
      });
    } else if (filter.industryCode) {
      jobAgg.push({
        $match: { 'adminorg.industryCategory': { $elemMatch: { $elemMatch: { $eq: filter.industryCode } } } },
      });
    }

    const docs = await ctx.model.JobHealth.aggregate(jobAgg);

    // 如果前端传了危害因素筛选，在内存中过滤一次（基于 formData.factors 映射）
    let filteredDocs = docs;
    if (filter.hazardFactors && filter.hazardFactors.length > 0) {
      const expanded = this.expandHazardFactors(filter.hazardFactors);
      const factorSet = new Set(expanded);
      filteredDocs = docs.filter(d =>
        this.hasAnyHazardFactor(d.formData, factorSet) ||
        this.hasAnyHazardFactorFromAdminorg(d.adminorg && d.adminorg.checkResult, factorSet)
      );
    }

    // 预取员工数量
    const enterpriseIds = Array.from(new Set(filteredDocs.map(d => String(d.EnterpriseID))));
    const employeeAgg = await ctx.model.Employee.aggregate([
      { $match: { status: 1, $or: [{ EnterpriseID: { $in: enterpriseIds } }, { enterpriseId: { $in: enterpriseIds } }] } },
      { $project: { enterpriseRef: { $ifNull: [ '$EnterpriseID', '$enterpriseId' ] } } },
      { $group: { _id: '$enterpriseRef', count: { $sum: 1 } } },
    ]);
    const employeeCountById = new Map(employeeAgg.map(i => [ String(i._id), i.count ]));

    // 预取行业字典（按企业一级行业编码）
    const topIndustryCodes = new Set();
    filteredDocs.forEach(d => {
      const arr = d.adminorg && Array.isArray(d.adminorg.industryCategory) ? d.adminorg.industryCategory : [];
      if (arr.length > 0 && Array.isArray(arr[0]) && arr[0][0]) topIndustryCodes.add(arr[0][0]);
    });
    const industryDocs = (topIndustryCodes.size > 0)
      ? await ctx.model.IndustryCategory.find({ value: { $in: Array.from(topIndustryCodes) } }, 'value label').lean()
      : [];
    const codeToIndustryLabel = new Map(industryDocs.map(d => [ String(d.value), d.label ]));

    // 组装地图数据（去重：按 enterpriseId 合并，多项目/多记录仅保留一个点位，合并危害因素）
    const markerMap = new Map();
    for (const d of filteredDocs) {
      const adminorg = d.adminorg || {};
      const enterpriseId = String(adminorg._id || '');
      if (!enterpriseId) continue;
      const workAddr = (adminorg.workAddress && adminorg.workAddress.length > 0) ? adminorg.workAddress[0] : null;
      const industryNames = [];
      const ic = adminorg.industryCategory || [];
      if (ic.length > 0 && Array.isArray(ic[0]) && ic[0][0]) {
        const top = String(ic[0][0]);
        if (codeToIndustryLabel.has(top)) industryNames.push(codeToIndustryLabel.get(top));
      }
      let hazardFactors = this.extractHazardFactorsFromFormData(d.formData);
      if (!hazardFactors || hazardFactors.length === 0) {
        hazardFactors = this.extractHazardFactorsFromAdminorg(adminorg.checkResult);
      }

      const existing = markerMap.get(enterpriseId);
      if (!existing) {
        // 新建
        const location = (workAddr && Array.isArray(workAddr.point) && workAddr.point.length >= 2)
          ? { longitude: workAddr.point[0], latitude: workAddr.point[1] }
          : null;
        markerMap.set(enterpriseId, {
          enterpriseId,
          name: adminorg.cname || '',
          industry: industryNames,
          address: (workAddr && workAddr.address) || '',
          location,
          hazardFactors: Array.isArray(hazardFactors) ? hazardFactors : [],
          employeeCount: employeeCountById.get(enterpriseId) || 0,
        });
      } else {
        // 合并 hazardFactors；若之前无坐标而本次有坐标则补充
        if (!existing.location && workAddr && Array.isArray(workAddr.point) && workAddr.point.length >= 2) {
          existing.location = { longitude: workAddr.point[0], latitude: workAddr.point[1] };
          existing.address = workAddr.address || existing.address;
        }
        // 合并行业（保持唯一）
        if (Array.isArray(industryNames) && industryNames.length > 0) {
          const set = new Set([ ...(existing.industry || []), ...industryNames ]);
          existing.industry = Array.from(set);
        }
        // 合并危害因素（按 type+name 去重，value 取较大值，isExceed 取或）
        const hfMap = new Map();
        (existing.hazardFactors || []).forEach(h => {
          const key = (h.type || '') + '|' + (h.name || '');
          hfMap.set(key, { ...h });
        });
        (hazardFactors || []).forEach(h => {
          const key = (h.type || '') + '|' + (h.name || '');
          if (!hfMap.has(key)) hfMap.set(key, { ...h });
          else {
            const cur = hfMap.get(key);
            const newVal = isNaN(parseInt(h.value, 10)) ? cur.value : Math.max(parseInt(h.value, 10) || 0, parseInt(cur.value, 10) || 0);
            hfMap.set(key, { ...cur, value: newVal, isExceed: Boolean(cur.isExceed || h.isExceed) });
          }
        });
        existing.hazardFactors = Array.from(hfMap.values());
      }
    }
    // 输出为数组，并过滤掉没有坐标的点
    let mapData = Array.from(markerMap.values()).filter(m => m.location && typeof m.location.longitude === 'number' && typeof m.location.latitude === 'number');
    // 若前端选择了危害因素，则仅保留含所选危害因素的点位，并裁剪显示的危害因素明细
    if (filter.hazardFactors && filter.hazardFactors.length > 0) {
      const selected = new Set(this.expandHazardFactors(filter.hazardFactors));
      mapData = mapData
        .map(m => ({
          ...m,
          hazardFactors: (m.hazardFactors || []).filter(h => h && selected.has(h.type)),
        }))
        .filter(m => Array.isArray(m.hazardFactors) && m.hazardFactors.length > 0);
    }

    // 危害因素分类统计
    let hazardFactorStats = this.calculateHazardFactorStats(filteredDocs);
    if (filter.hazardFactors && filter.hazardFactors.length > 0) {
      const selected = new Set(this.expandHazardFactors(filter.hazardFactors));
      hazardFactorStats = hazardFactorStats.filter(h => selected.has(h.type));
    }

    // 行业分布统计（按企业一级行业）
    const industryStats = this.calculateIndustryStats(filteredDocs, codeToIndustryLabel);

    // 区域分布统计（按 workPlaces.workAdd 汇总）
    // 依据账号区域层级或筛选区域，生成下一级区域分布
    const baseRegionCode = (filter && filter.regionCode) ? String(filter.regionCode) : ((allowedWorkAdds && allowedWorkAdds.length > 0) ? String(allowedWorkAdds[0]) : '');
    const regionStats = await this.calculateRegionStats(filteredDocs, baseRegionCode);

    return { mapData, hazardFactorsStats: hazardFactorStats, industryStats, regionStats };
  }

  normalizeFilter(f = {}) {
    const out = { ...f };
    // region -> regionCode
    if (!out.regionCode && out.region) out.regionCode = out.region;
    // dateRange -> startDate / endDate
    if ((!out.startDate || !out.endDate) && Array.isArray(out.dateRange) && out.dateRange.length === 2) {
      [ out.startDate, out.endDate ] = out.dateRange;
    }
    // industry(JSON/string/array) -> industryCode/industryCodes（末级编码集合）
    if ((!out.industryCode && !out.industryCodes) && out.industry) {
      try {
        const parsed = typeof out.industry === 'string' ? JSON.parse(out.industry) : out.industry;
        if (Array.isArray(parsed) && parsed.length > 0) {
          const codes = [];
          parsed.forEach(path => {
            if (Array.isArray(path) && path.length > 0) codes.push(String(path[path.length - 1]));
          });
          if (codes.length > 0) { out.industryCodes = codes; out.industryCode = codes[0]; }
        }
      } catch (e) {
        if (typeof out.industry === 'string' && out.industry) { out.industryCode = out.industry; out.industryCodes = [ out.industry ]; }
      }
    }
    // hazardFactors 统一为数组
    if (out.hazardFactors && !Array.isArray(out.hazardFactors)) {
      try {
        const parsed = JSON.parse(out.hazardFactors);
        if (Array.isArray(parsed)) out.hazardFactors = parsed;
        else out.hazardFactors = [ out.hazardFactors ];
      } catch (e) {
        if (typeof out.hazardFactors === 'string') {
          out.hazardFactors = out.hazardFactors.split(',').map(s => s && s.trim()).filter(Boolean);
        } else out.hazardFactors = [];
      }
    }
    return out;
  }

  buildJobHealthMatch(filter) {
    const and = [
      { status: 1 },
      { projectStop: { $ne: true } },
    ];
    // 时间：completeTime
    if (filter.startDate && filter.endDate) {
      const start = new Date(filter.startDate);
      const end = new Date(filter.endDate);
      and.push({ completeTime: { $gte: start, $lte: end } });
    } else {
      // 只要存在完成时间
      and.push({ completeTime: { $exists: true, $ne: null } });
    }
    // 区域：严格以 JobHealth 检测项目地点 workPlaces.workAdd 为准
    if (filter.regionCode) {
      const code = String(filter.regionCode);
      // 传入 6 位时匹配其辖下所有 12 位编码（前 6 位等于 code）
      if (code.length === 6) {
        and.push({ workPlaces: { $elemMatch: { workAdd: { $regex: `^${code}` } } } });
      } else {
        and.push({ workPlaces: { $elemMatch: { workAdd: code } } });
      }
    }
    return { $and: and };
  }

  async getAllowedWorkAddsBySession() {
    const { ctx } = this;
    try {
      const user = ctx.session && ctx.session.superUserInfo ? ctx.session.superUserInfo : null;
      if (!user) return [];
      const crossRegionManage = ctx.app.config && ctx.app.config.crossRegionManage;
      const rawAdds = (crossRegionManage && Array.isArray(user.crossRegionAdd) && user.crossRegionAdd.length > 0)
        ? user.crossRegionAdd
        : (Array.isArray(user.regAdd) ? user.regAdd : []);
      const GENERIC = new Set([ '中国', '中华人民共和国', '新疆维吾尔自治区', '新疆维吾尔自治区直辖', '建设兵团', '新疆生产建设兵团' ]);
      const names = [];
      if (Array.isArray(rawAdds) && rawAdds.length > 0) {
        if (rawAdds.some(v => Array.isArray(v))) {
          rawAdds.forEach(path => { if (Array.isArray(path) && path.length > 0) { const last = path[path.length - 1]; if (last && !GENERIC.has(last)) names.push(last); } });
        } else {
          const last = rawAdds[rawAdds.length - 1];
          if (last && !GENERIC.has(last)) names.push(last);
        }
      }
      if (names.length === 0) return [];
      // 将地区中文名映射为 area_code（取末级）
      const districts = await ctx.model.District.find({ name: { $in: names } }, 'area_code name').lean();
      const codes = districts.map(d => String(d.area_code));
      // jobHealth 存储为 12 位 area_code
      return codes.map(c => (c.length === 6 ? c + '000000' : c));
    } catch (e) {
      this.ctx.logger.warn('[getAllowedWorkAddsBySession] %s', e.message);
      return [];
    }
  }

  factorNameToCode(nameOrCode) {
    const s = String(nameOrCode || '').toLowerCase();
    if (!s) return '';
    const lut = [
      { code: 'dust', kws: [ 'dust', '粉尘' ] },
      { code: 'chemical', kws: [ 'chemical', '化学' ] },
      { code: 'noise', kws: [ 'noise', '噪声' ] },
      { code: 'radiation', kws: [ 'radiation', '辐射' ] },
      { code: 'biological', kws: [ 'biological', '生物' ] },
      { code: 'heat', kws: [ 'heat', '高温', '温度' ] },
    ];
    for (const it of lut) {
      if (it.kws.some(k => s.includes(k))) return it.code;
    }
    return '';
  }

  expandHazardFactors(arr) {
    const set = new Set();
    (arr || []).forEach(vRaw => {
      const v = (vRaw == null) ? '' : String(vRaw);
      // 统一映射到英文代码
      let code = this.factorNameToCode(v);
      // 兼容中文分类后缀“类”
      if (!code && v.endsWith('类')) code = this.factorNameToCode(v.slice(0, -1));
      // 兼容“物理因素类” → physical（再展开为 noise/heat）
      if (!code && (v.includes('物理') || v.toLowerCase().includes('physical'))) code = 'physical';
      if (!code) code = v; // 保底，若前端已传英文 code

      if (code === 'physical') {
        set.add('noise');
        set.add('heat');
      } else if (code) {
        set.add(code);
      }
    });
    return Array.from(set);
  }

  hasAnyHazardFactor(formData, factorSet) {
    if (!Array.isArray(formData) || formData.length === 0) return false;
    for (const f of formData) {
      const code = this.factorNameToCode((f && f.factors) || (f && f.type));
      if (code && factorSet.has(code)) return true;
    }
    return false;
  }

  extractHazardFactorsFromFormData(formData) {
    const result = [];
    if (!Array.isArray(formData)) return result;
    for (const f of formData) {
      const code = this.factorNameToCode((f && f.factors) || (f && f.type));
      if (!code) continue;
      const nameMap = { dust: '粉尘类', chemical: '化学物质类', noise: '噪声', radiation: '放射性因素类', biological: '生物因素类', heat: '高温' };
      result.push({ type: code, name: nameMap[code] || code, value: 1, unit: '' });
    }
    return result;
  }

  hasAnyHazardFactorFromAdminorg(checkResult, factorSet) {
    if (!checkResult || typeof checkResult !== 'object') return false;
    for (const key of Object.keys(checkResult)) {
      const entry = checkResult[key];
      if (!entry) continue;
      const code = this.factorNameToCode(key) || this.factorNameToCode(entry.name);
      const point = parseFloat(entry.point || 0);
      const exceed = parseFloat(entry.exceed || 0);
      if (code && factorSet.has(code) && (point > 0 || exceed > 0)) return true;
    }
    return false;
  }

  extractHazardFactorsFromAdminorg(checkResult) {
    const result = [];
    if (!checkResult || typeof checkResult !== 'object') return result;
    const nameMap = { dust: '粉尘类', chemical: '化学物质类', noise: '噪声', radiation: '放射性因素类', biological: '生物因素类', heat: '高温' };
    for (const key of Object.keys(checkResult)) {
      const entry = checkResult[key];
      if (!entry) continue;
      const code = this.factorNameToCode(key) || this.factorNameToCode(entry.name);
      if (!code) continue;
      const value = typeof entry.point !== 'undefined' ? parseFloat(entry.point || 0) : 0;
      const isExceed = parseFloat(entry.exceed || 0) > 0;
      if (value > 0 || isExceed) {
        result.push({ type: code, name: nameMap[code] || (entry.name || code), value: isNaN(value) ? 1 : value, unit: '', isExceed });
      }
    }
    return result;
  }

  calculateHazardFactorStats(docs) {
    // {{ AURA: Modify - 将“单位数”按企业去重统计；同一企业在同一危害分类下只计一次，超标数亦按企业去重统计 }}
    const order = [ 'dust', 'chemical', 'noise', 'radiation', 'biological', 'heat' ];
    const nameMap = { dust: '粉尘类', chemical: '化学物质类', noise: '噪声', radiation: '放射性因素类', biological: '生物因素类', heat: '高温' };

    // 使用 Set 存企业ID，按分类去重
    const buckets = new Map(); // code -> { name, type, _enterpriseSet, _exceedSet }
    const ensureBucket = (code) => {
      if (!buckets.has(code)) {
        buckets.set(code, { name: nameMap[code] || code, type: code, _enterpriseSet: new Set(), _exceedSet: new Set() });
      }
      return buckets.get(code);
    };

    for (const d of docs) {
      const enterpriseId = String((d.adminorg && d.adminorg._id) || d.EnterpriseID || '');
      if (!enterpriseId) continue;

      // 防止同一 doc 内重复添加同一分类
      const seenInDoc = new Set();

      // 来源一：formData（代表有该危害因素参与检测），不包含“是否超标”信息
      (d.formData || []).forEach(fd => {
        const code = this.factorNameToCode((fd && fd.factors) || (fd && fd.type));
        if (!code || seenInDoc.has(code)) return;
        seenInDoc.add(code);
        const b = ensureBucket(code);
        b._enterpriseSet.add(enterpriseId);
      });

      // 来源二：adminorg.checkResult（包含 point/exceed 信息）
      const adminorg = d.adminorg || {};
      const list = this.extractHazardFactorsFromAdminorg(adminorg.checkResult);
      list.forEach(it => {
        const code = it.type;
        if (!code) return;
        const b = ensureBucket(code);
        b._enterpriseSet.add(enterpriseId);
        if (it.isExceed) b._exceedSet.add(enterpriseId);
      });
    }

    // 汇总为数组结构
    const result = order
      .filter(code => buckets.has(code))
      .map(code => {
        const b = buckets.get(code);
        const total = b._enterpriseSet.size; // 检查单位数：分类内企业去重
        const exceed = b._exceedSet.size; // 超标单位数：分类内企业去重
        const exceedRate = total > 0 ? +((exceed * 100) / total).toFixed(2) : 0;
        return { name: b.name, type: b.type, total, exceed, exceedRate, unit: '' };
      });

    return result;
  }

  calculateIndustryStats(docs, codeToIndustryLabel) {
    const countByCode = new Map();
    const enterprisesByCode = new Map();
    for (const d of docs) {
      const ic = d.adminorg && Array.isArray(d.adminorg.industryCategory) ? d.adminorg.industryCategory : [];
      if (ic.length === 0 || !Array.isArray(ic[0]) || !ic[0][0]) continue;
      const top = String(ic[0][0]);
      countByCode.set(top, (countByCode.get(top) || 0) + 1);
      const set = enterprisesByCode.get(top) || new Set();
      set.add(String(d.EnterpriseID));
      enterprisesByCode.set(top, set);
    }
    const total = Array.from(countByCode.values()).reduce((a, b) => a + b, 0) || 1;
    const arr = Array.from(countByCode.entries()).map(([ code, count ]) => ({
      code,
      name: codeToIndustryLabel.get(code) || '其他',
      level: 1,
      parentCode: '',
      count,
      exceedCount: 0,
      percentage: +((count * 100) / total).toFixed(2),
    }));
    arr.sort((a, b) => b.count - a.count);
    arr.forEach((it, idx) => (it.ranking = idx + 1));
    return arr;
  }

  async calculateRegionStats(docs, baseRegionCode = '') {
    const { ctx } = this;
    // 推断基础层级
    const base6 = baseRegionCode ? String(baseRegionCode).slice(0, 6) : '';
    const baseLevel = base6
      ? (base6.endsWith('0000') ? 1 : (base6.endsWith('00') ? 2 : 3))
      : 1; // 无法判断时，按省级
    const targetLevel = Math.min(3, baseLevel + 1);

    // 统计：将 workAdd 归一化到目标层级下的编码（以 6 位编码为准便于查字典）
    const countByGroup6 = new Map();
    for (const d of docs) {
      (d.workPlaces || []).forEach(wp => {
        if (!wp || !wp.workAdd) return;
        const code12 = String(wp.workAdd);
        const code6 = code12.slice(0, 6);

        // 如果提供了 baseRegionCode，则确保在其辖区下
        if (base6) {
          if (baseLevel === 1 && code6.slice(0, 2) !== base6.slice(0, 2)) return;
          if (baseLevel === 2 && code6.slice(0, 4) !== base6.slice(0, 4)) return;
          if (baseLevel === 3 && code6 !== base6) return;
        }

        let group6;
        if (targetLevel === 1) group6 = code6.slice(0, 2) + '0000';
        else if (targetLevel === 2) group6 = code6.slice(0, 4) + '00';
        else group6 = code6; // 3
        countByGroup6.set(group6, (countByGroup6.get(group6) || 0) + 1);
      });
    }

    const total = Array.from(countByGroup6.values()).reduce((a, b) => a + b, 0) || 1;
    const groupCodes6 = Array.from(countByGroup6.keys());
    // 拉取名称：兼容库内存 12 位编码（常见为 6 位 + 6 个 0）
    const queryCodes = [];
    groupCodes6.forEach(c6 => {
      queryCodes.push(c6);
      queryCodes.push(c6 + '000000');
    });
    const districts = queryCodes.length > 0
      ? await ctx.model.District.find({ area_code: { $in: queryCodes } }, 'area_code name parent_code level').lean()
      : [];
    const map = new Map(districts.map(d => [ String(d.area_code), d ]));

    const arr = groupCodes6.map(code6 => {
      const count = countByGroup6.get(code6) || 0;
      // 优先用 6 位命中，其次 12 位
      let d = map.get(code6);
      if (!d) d = map.get(code6 + '000000');
      // 特殊兜底：兵团 660000
      let name = (d && d.name) || '';
      if (!name) {
        if (code6.startsWith('66')) name = '新疆生产建设兵团';
        else if (code6.startsWith('65')) name = '新疆维吾尔自治区';
        else name = code6;
      }
      const parent6 = (targetLevel === 1)
        ? ''
        : (targetLevel === 2 ? (code6.slice(0, 2) + '0000') : (code6.slice(0, 4) + '00'));
      return {
        code: code6, // 返回 6 位编码，更通用
        name,
        level: targetLevel,
        parentCode: parent6,
        count,
        exceedCount: 0,
        percentage: +((count * 100) / total).toFixed(2),
      };
    });
    arr.sort((a, b) => b.count - a.count);
    arr.forEach((it, idx) => (it.ranking = idx + 1));
    return arr;
  }
}

module.exports = JobHealthInspectionService;

