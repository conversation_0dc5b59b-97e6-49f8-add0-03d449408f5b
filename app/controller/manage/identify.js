const Controller = require('egg').Controller;


class DiagnosisController extends Controller {

  // 获取鉴定档案列表
  async getIdentificationList() {
    const { ctx } = this;
    try {
      const {
        administerArea = '',
        workerName = '',
        idCardType = '',
        idCardCode = '',
        workEmpCreditCode = '',
        workEmpName = '',
        startDate = '',
        endDate = '',
        type = '',
        status = '',
        pageNum = 1,
        pageSize = 10,
        institutionName = '',
      } = ctx.query;
      const query = {
        administerArea,
        workerName,
        idCardType,
        idCardCode,
        workEmpCreditCode,
        startDate,
        endDate,
        workEmpName,
        type,
        status,
        pageNum,
        pageSize,
        institutionName,
      };
      const { crossRegionManage = false } = ctx.app.config;
      if (crossRegionManage) {
        delete query.administerArea;
        const { area_code } = ctx.session.superUserInfo;
        query.crossAreaCodes = area_code;
      }
      const list = await ctx.curl(
        `${this.config.iServiceJdHost}/identificationInfo`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );
      ctx.helper.renderSuccess(ctx, {
        data: list.data.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 统计每年职业病鉴定人数
  async statisticByYear() {
    const { ctx } = this;
    const params = ctx.query;
    const { area_code } = ctx.session.superUserInfo;
    params.administerAreaCode = params.administerAreaCode ? params.administerAreaCode : area_code;
    try {
      const list = await ctx.curl(
        `${this.config.iServiceJdHost}/identification/statistic/byYear`,
        {
          data: params,
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 按照年度统计职业病鉴定结果
  async statisticByResultAndYear() {
    const { ctx } = this;
    const params = ctx.query;
    const { area_code } = ctx.session.superUserInfo;
    params.administerAreaCode = params.administerAreaCode ? params.administerAreaCode : area_code;
    console.log(params, 'params---->');
    try {
      const list = await ctx.curl(
        `${this.config.iServiceJdHost}/identification/statistic/byResultAndYear`,
        {
          data: params,
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 获取鉴定机构分页列表
  async getInstitutionInfo() {
    const { ctx } = this;
    const {
      institutionName = '',
      administerAreaCode = '',
      areaCode = '',
      pageNum = 1,
      pageSize = 10,
    } = ctx.query;
    const query = {
      institutionName,
      administerAreaCode,
      areaCode,
      pageNum,
      pageSize,
    };
    const { area_code } = ctx.session.superUserInfo;
    query.administerAreaCode = area_code;
    try {
      const list = await ctx.curl(
        `${this.config.iServiceJdHost}/institutionInfo`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );
      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 获取数据字典数据
  async getDictData() {
    const { ctx } = this;
    try {
      const { dictType } = ctx.query;
      const list = await ctx.curl(
        `${this.config.iServiceJdHost}/dictData/byType/${dictType}`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      ctx.helper.renderSuccess(ctx, {
        data: list.data.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }


  // 按照年度统计每类职业病鉴定人数
  async statisticByDiseaseAndYear() {
    const { ctx } = this;
    const params = ctx.query;
    const { area_code } = ctx.session.superUserInfo;
    params.administerAreaCode = params.administerAreaCode ? params.administerAreaCode : area_code;
    console.log(params, 'params---->');
    try {
      const list = await ctx.curl(
        `${this.config.iServiceJdHost}/identification/statistic/byDiseaseAndYear`,
        {
          data: params,
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
}

module.exports = DiagnosisController;
