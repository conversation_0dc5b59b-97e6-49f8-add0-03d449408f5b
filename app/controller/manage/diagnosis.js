const Controller = require('egg').Controller;


class DiagnosisController extends Controller {
  // 获取职业病诊断信息列表
  async getDiaList() {
    const { ctx } = this;
    try {
      const {
        administerArea = '',
        workerName = '',
        idCardType = '',
        idCardCode = '',
        workEmpCreditCode = '',
        startDate = '',
        endDate = '',
        status = '',
        diseaseCodeList = '',
        pageNum = 1,
        pageSize = 10,
        institutionName = '',
      } = ctx.query;
      const query = {
        administerArea,
        workerName,
        idCardType,
        idCardCode,
        workEmpCreditCode,
        startDate,
        endDate,
        status,
        diseaseCodeList,
        pageNum,
        pageSize,
        institutionName,
      };
      const { crossRegionManage = false } = ctx.app.config;
      if (crossRegionManage) {
        delete query.administerArea;
        const { area_code } = ctx.session.superUserInfo;
        query.crossAreaCodes = area_code;
      }
      const list = await ctx.curl(
        `${this.config.iServiceZdHost}/diagnosis`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: query,
        }
      );
      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病诊断档案模版管理-获取分页列表
  async getDiagnosisTemplate() {
    const { ctx } = this;
    const {
      name = '',
      module = '',
      createBy = '',
      pageNum = 1,
      pageSize = 10,
    } = ctx.query;
    const query = {
      name,
      module,
      createBy,
      pageNum,
      pageSize,
    };
    try {
      const list = await ctx.curl(`${this.config.iServiceZdHost}/diagnosisTemplate`, {
        method: 'GET',
        dataType: 'json', // 返回的数据类型
        data: query,
      });
      ctx.helper.renderSuccess(ctx, {
        data: list.data.data,
        status: list.status,
        message: '获取成功',
      });

    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病诊断档案模版管理-新增
  async addDiagnosisTemplate() {
    const { ctx } = this;
    try {
      const data = await ctx.request.body;
      const detail = await ctx.curl(`${this.config.iServiceZdHost}/diagnosisTemplate`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data,
        headers: {
          'Content-Type': 'application/json', // 确保请求头中Content-Type正确
        },
      });
      ctx.helper.renderSuccess(ctx, {
        data: detail.data.data,
        status: detail.status,
        message: '新增成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }


  // 职业病诊断档案模版管理-详情
  async detailDiagnosisTemplate() {
    const { ctx } = this;
    try {
      const { id } = ctx.params;
      const detail = await ctx.curl(`${this.config.iServiceZdHost}/diagnosisTemplate/${id}`, {
        method: 'GET',
        dataType: 'json', // 返回的数据类型
      });
      ctx.helper.renderSuccess(ctx, {
        data: detail.data.data,
        status: detail.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病诊断档案模版管理-编辑
  async editDiagnosisTemplate() {
    const { ctx } = this;
    try {
      const data = await ctx.request.body;
      const { id } = await ctx.request.body;
      const detail = await ctx.curl(`${this.config.iServiceZdHost}/diagnosisTemplate/${id}`, {
        method: 'PUT',
        dataType: 'json', // 返回的数据类型
        data,
        headers: {
          'Content-Type': 'application/json', // 确保请求头中Content-Type正确
        },
      });

      ctx.helper.renderSuccess(ctx, {
        data: detail.data.data,
        status: detail.status,
        message: '编辑成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病诊断档案模版管理-删除
  async deleteDiagnosisTemplate() {
    const { ctx } = this;
    try {
      const { id } = ctx.query;
      const detail = await ctx.curl(`${this.config.iServiceZdHost}/diagnosisTemplate/${id}`, {
        method: 'DELETE',
        dataType: 'json', // 返回的数据类型
      });
      console.log(detail, 'detail');

      ctx.helper.renderSuccess(ctx, {
        data: detail.data.data,
        status: detail.status,
        message: '删除成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病诊断模版分类管理-列表查询
  async getDiagnosisTemplateCategory() {
    const { ctx } = this;
    const {
      module = '',
      createBy = '',
    } = ctx.query;
    const query = {
      module,
      createBy,
    };
    try {
      const list = await ctx.curl(`${this.config.iServiceZdHost}/diagnosisTemplateCategory`, {
        method: 'GET',
        dataType: 'json', // 返回的数据类型
        data: query,
      });

      ctx.helper.renderSuccess(ctx, {
        data: list.data.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病诊断模版分类管理-新增
  async addDiagnosisTemplateCategory() {
    const { ctx } = this;
    try {
      const data = await ctx.request.body;
      const detail = await ctx.curl(`${this.config.iServiceZdHost}/diagnosisTemplateCategory`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data,
        headers: {
          'Content-Type': 'application/json', // 确保请求头中Content-Type正确
        },
      });
      ctx.helper.renderSuccess(ctx, {
        data: detail.data.data,
        status: detail.status,
        message: '新增成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病诊断模版分类管理-编辑
  async editDiagnosisTemplateCategory() {
    const { ctx } = this;
    try {
      const data = await ctx.request.body;
      const id = data.id;
      const detail = await ctx.curl(`${this.config.iServiceZdHost}/diagnosisTemplateCategory/${id}`, {
        method: 'PUT',
        dataType: 'json', // 返回的数据类型
        data,
        headers: {
          'Content-Type': 'application/json', // 确保请求头中Content-Type正确
        },
      });
      ctx.helper.renderSuccess(ctx, {
        data: detail.data.data,
        status: detail.status,
        message: '编辑成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 职业病诊断模版分类管理-删除
  async deleteDiagnosisTemplateCategory() {
    const { ctx } = this;
    try {
      const { id } = ctx.query;
      const detail = await ctx.curl(`${this.config.iServiceZdHost}/diagnosisTemplateCategory/${id}`, {
        method: 'DELETE',
        dataType: 'json', // 返回的数据类型
      });
      ctx.helper.renderSuccess(ctx, {
        data: detail.data.data,
        status: detail.status,
        message: '删除成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 统计每年职业病诊断人数
  async statisticDiagnosisByYear() {
    const { ctx } = this;
    const params = ctx.query;
    const { area_code } = ctx.session.superUserInfo;
    params.administerAreaCode = params.administerAreaCode ? params.administerAreaCode : area_code;
    try {
      const detail = await ctx.curl(`${this.config.iServiceZdHost}/diagnosis/statistic/byYear`, {
        data: params,
        method: 'GET',
        dataType: 'json', // 返回的数据类型
      });
      ctx.helper.renderSuccess(ctx, {
        data: detail.data,
        status: detail.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 按照年度统计每类职业病诊断人数
  async statisticDiagnosisByDiseaseAndYear() {
    const { ctx } = this;
    const params = ctx.query;
    try {
      const detail = await ctx.curl(`${this.config.iServiceZdHost}/diagnosis/statistic/byDiseaseAndYear`, {
        data: params,
        method: 'GET',
        dataType: 'json', // 返回的数据类型
      });
      ctx.helper.renderSuccess(ctx, {
        data: detail.data,
        status: detail.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 获取诊断机构分页列表
  async getDiagnosisInstitution() {
    const { ctx } = this;
    const {
      institutionName = '',
      administerAreaCode = '',
      areaCode = '',
      pageNum = 1,
      pageSize = 10,
    } = ctx.query;
    const query = {
      institutionName,
      administerAreaCode,
      areaCode,
      pageNum,
      pageSize,
    };
    query.administerAreaCode = ctx.session.superUserInfo.area_code;
    console.log(query, 'query----->');

    try {
      const list = await ctx.curl(`${this.config.iServiceZdHost}/institutionInfo`, {
        method: 'GET',
        dataType: 'json', // 返回的数据类型
        data: query,
      });

      ctx.helper.renderSuccess(ctx, {
        data: list.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 按照年度统计诊断结果人数
  async statisticDiagnosisByConclusionAndYear() {
    const { ctx } = this;
    const params = ctx.query;
    try {
      const detail = await ctx.curl(`${this.config.iServiceZdHost}/diagnosis/statistic/byConclusionAndYear`, {
        data: params,
        method: 'GET',
        dataType: 'json', // 返回的数据类型
      });
      ctx.helper.renderSuccess(ctx, {
        data: detail.data,
        status: detail.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 获取数据字典数据
  async getDictData() {
    const { ctx } = this;
    try {
      const { dictType } = ctx.query;
      const list = await ctx.curl(
        `${this.config.iServiceZdHost}/dictData/byType/${dictType}`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      ctx.helper.renderSuccess(ctx, {
        data: list.data.data,
        status: list.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 根据机构id获取开展诊断的病种
  async getDiseaseByInstitutionId() {
    const { ctx } = this;
    try {
      const data = await ctx.curl(`${this.config.iServiceZdHost}/institutionInfo/disease/${ctx.query.id}`, {
        method: 'get',
        dataType: 'json',
      });
      ctx.helper.renderSuccess(ctx, {
        data: data.data,
        status: data.status,
        message: '获取成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
}

module.exports = DiagnosisController;
