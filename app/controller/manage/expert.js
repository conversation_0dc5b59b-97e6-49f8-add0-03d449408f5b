// 职业健康专家管理
const expertController = {
  // 获取专家列表
  async list(ctx) {
    const { app } = this;
    try {
      const query = ctx.query;
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/expert`,
        {
          method: 'GET',
          dataType: 'json',
          data: query,
        }
      );
      if (data.code === 200) {
        const superUserInfo = ctx.session.superUserInfo;
        data.data.superUserInfo = { cname: superUserInfo.cname, regAdd: superUserInfo.regAdd };
        await ctx.helper.handleIserviceRes(ctx, data, '获取成功', '获取失败');
      } else {
        ctx.helper.renderCustom(ctx, {
          status: data.code || 500,
          message: data.message || '数据获取失败',
          data: data.data || {},
        });
      }

    } catch (err) {
      console.log(4444, err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '数据获取失败',
        data: err,
      });
    }
  },

  // 新增专家
  async add(ctx) {
    const { app } = this;
    const fields = ctx.request.body || {};
    try {
      const { name, id_number, level } = fields;
      if (!name) throw new Error('name不能为空');
      if (!id_number) throw new Error('证件编号不能为空');
      if (!level) throw new Error('专家级别不能为空');
      fields.declaration_status = 1; // 申报状态 1-已申报
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/expert`,
        {
          method: 'POST',
          contentType: 'json',
          data: fields,
          dataType: 'json',
        }
      );
      if (data.code === 200) {
        await ctx.helper.handleIserviceRes(ctx, data, '新增成功', '新增失败');
      } else {
        ctx.helper.renderCustom(ctx, {
          status: data.code || 500,
          message: data.message || '操作失败',
          data: data.data,
        });
      }

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '新增失败',
        data: err,
      });
    }
  },

  // 修改专家信息
  async update(ctx) {
    const { app } = this;
    const fields = ctx.request.body || {};
    try {
      const { id, name, id_number } = fields;
      if (!id) throw new Error('id不能为空');
      if (!name) throw new Error('name不能为空');
      if (!id_number) throw new Error('证件编号不能为空');

      const { data } = await ctx.curl(
        `${app.config.iService2Host}/expert/${id}`,
        {
          method: 'PUT',
          contentType: 'json',
          data: fields,
          dataType: 'json',
        }
      );
      await ctx.helper.handleIserviceRes(ctx, data, '修改成功', '修改失败');
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '修改失败',
        data: err,
      });
    }
  },
  // 获取专家详情
  async detail(ctx) {
    const { app } = this;
    try {
      const { id } = ctx.query;
      if (!id) throw new Error('id不能为空');
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/expert/${id}`,
        {
          method: 'GET',
          dataType: 'json',
        }
      );
      if (data.code === 200) {
        await ctx.helper.handleIserviceRes(ctx, data, '获取成功', '获取失败');
      } else {
        ctx.helper.renderCustom(ctx, {
          status: data.code || 500,
          message: data.message || '数据获取失败',
          data: data.data || {},
        });
      }

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '数据获取失败',
        data: err,
      });
    }
  },
  //   // 删除专家
  //   async delete(ctx) {
  //     const { app } = this;
  //     const fields = ctx.request.body || {};
  //     try {
  //       const { id } = fields;
  //       if (!id) throw new Error('id不能为空');
  //       const { data } = await ctx.curl(
  //         `${app.config.iService2Host}/expert/${+id}`,
  //         {
  //           method: 'DELETE',
  //           dataType: 'json',
  //         }
  //       );
  //       await ctx.helper.handleIserviceRes(ctx, data, '删除成功', '删除失败');
  //     } catch (err) {
  //       ctx.helper.renderFail(ctx, {
  //         message: err.message || '删除失败',
  //         data: err,
  //       });
  //     }
  //   },

  // 专家推荐 各师市可以向兵团推荐行业内的优秀专家，兵团审批通过后，该专家可同时成为兵团级专家
  async recommend(ctx) {
    const { app } = this;
    try {
      const { id, expert_id, operate, message } = ctx.request.body;
      if (!expert_id) throw new Error('expert_id');
      if (!operate) throw new Error('operate不能为空');
      if ([ 1, 2, 3 ].indexOf(+operate) === -1) throw new Error('operate只能为1\\2\\3');
      if (+operate === 3 && !message) throw new Error('operate为3时，message不能为空');
      if (+operate !== 1 && !id) throw new Error('id不能为空');
      // operate: 1提交申请、2 同意申请、3驳回申请
      const superUserInfo = ctx.session.superUserInfo;
      const people = `${superUserInfo.cname} - ${superUserInfo.name || superUserInfo.userName || superUserInfo.phoneNum}`;
      let applicant = '',
        reviewer = '';
      if (+operate === 1) {
        applicant = people;
      } else {
        reviewer = people;
      }

      const { data } = await ctx.curl(
        `${app.config.iService2Host}/expert/recommend`,
        {
          method: 'POST',
          contentType: 'json',
          data: { expert_id: +expert_id, id: +id, operate: +operate, review_comment: message, applicant, reviewer },
          dataType: 'json',
        }
      );
      await ctx.helper.handleIserviceRes(ctx, data, '操作成功', '操作失败');
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '操作失败',
        data: err,
      });
    }
  },

  // 专家解聘
  async dismissal(ctx) {
    const { app } = this;
    try {
      const { id, expert_id, operate, message } = ctx.request.body;
      if (!expert_id) throw new Error('expert_id');
      if (!operate) throw new Error('operate不能为空');
      if ([ 1, 2, 3 ].indexOf(+operate) === -1) throw new Error('operate只能为1\\2\\3');
      if (+operate === 3 && !message) throw new Error('operate为3时，message不能为空');
      if (+operate !== 1 && !id) throw new Error('id不能为空');
      // operate: 1提交申请、2 同意申请、3驳回申请
      const superUserInfo = ctx.session.superUserInfo;
      const people = `${superUserInfo.cname} - ${superUserInfo.name || superUserInfo.userName || superUserInfo.phoneNum}`;
      let applicant = '',
        reviewer = '';
      if (+operate === 1) {
        applicant = people;
      } else {
        reviewer = people;
      }
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/expert/dismissal`,
        {
          method: 'POST',
          contentType: 'json',
          data: { expert_id: +expert_id, id: +id, operate: +operate, review_comment: message, applicant, reviewer },
          dataType: 'json',
        }
      );
      await ctx.helper.handleIserviceRes(ctx, data, '操作成功', '操作失败');
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '操作失败',
        data: err,
      });
    }
  },

  // 专家申报审核
  async declarationReview(ctx) {
    const { app } = this;
    try {
      const { id, operate, message } = ctx.request.body;
      if (!id) throw new Error('专家id不能为空');
      if (![ 0, 1 ].includes(+operate)) throw new Error('operate传参错误，只能为0或1');
      if (+operate === 0 && !message) throw new Error('审核不通过时，审核意见不能为空');

      const superUserInfo = ctx.session.superUserInfo;
      const reviewer = `${superUserInfo.cname} - ${superUserInfo.name || superUserInfo.userName || superUserInfo.phoneNum}`;
      const res = await ctx.curl(
        `${app.config.iService2Host}/expert/declaration/review`,
        {
          method: 'PUT',
          contentType: 'json',
          data: { id: +id, review_status: +operate === 1 ? 1 : 3, review_comment: message, reviewer },
          dataType: 'json',
        }
      );
      await ctx.helper.handleIserviceRes(ctx, res.data, '审核成功', '审核失败');
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '操作失败',
        data: err,
      });
    }
  },

  // 审核记录列表
  async reviewList(ctx) {
    const { app } = this;
    try {
      const query = ctx.query;
      Object.keys(query).forEach(key => {
        if (query[key]) {
          query[key] = +query[key];
        }
      });
      const review_type = +query.review_type;
      if (review_type && ![ 1, 2, 3 ].includes(review_type)) throw new Error('review_type参数错误,只能为1\\2\\3');

      // const { regAdd } = ctx.session.superUserInfo;
      // console.log(111, regAdd);

      const { data } = await ctx.curl(
        `${app.config.iService2Host}/expert/review/list`,
        {
          method: 'GET',
          dataType: 'json',
          data: query,
        }
      );
      if (data.code === 200) {
        await ctx.helper.handleIserviceRes(ctx, data, '获取成功', '获取失败');
      } else {
        ctx.helper.renderCustom(ctx, {
          status: data.code || 500,
          message: data.message || '数据获取失败',
          data: data.data || {},
        });
      }
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '数据获取失败',
        data: err,
      });
    }
  },

  // 专家抽取记录
  async extractionList(ctx) {
    const { app } = this;
    try {
      const query = ctx.query;
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/expert/extraction/list`,
        {
          method: 'GET',
          dataType: 'json',
          data: query,
        }
      );
      if (data.code === 200) {
        await ctx.helper.handleIserviceRes(ctx, data, '获取成功', '获取失败');
      } else {
        ctx.helper.renderCustom(ctx, {
          status: data.code || 500,
          message: data.message || '数据获取失败',
          data: data.data || {},
        });
      }
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '数据获取失败',
        data: err,
      });
    }
  },

  // 专家工作任务列表
  async workList(ctx) {
    const { app } = this;
    try {
      const query = ctx.query;
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/expert/work/list`,
        {
          method: 'GET',
          dataType: 'json',
          data: query,
        }
      );
      if (data.code === 200) {
        await ctx.helper.handleIserviceRes(ctx, data, '获取成功', '获取失败');
      } else {
        ctx.helper.renderCustom(ctx, {
          status: data.code || 500,
          message: data.message || '数据获取失败',
          data: data.data || {},
        });
      }
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '数据获取失败',
        data: err,
      });
    }
  },
  // 专家抽取
  async expertExtraction(ctx) {
    const { app } = this;
    try {
      const query = ctx.query;
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/expert/extraction/random`,
        {
          method: 'GET',
          dataType: 'json',
          data: query,
        }
      );
      if (data.code === 200) {
        await ctx.helper.handleIserviceRes(ctx, data, '获取成功', '获取失败');
      } else {
        ctx.helper.renderCustom(ctx, {
          status: data.code || 500,
          message: data.message || '数据获取失败',
          data: data.data || {},
        });
      }
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '数据获取失败',
        data: err,
      });
    }
  },

};

module.exports = expertController;
