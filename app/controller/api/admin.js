const Controller = require('egg').Controller;
const jwt = require('jsonwebtoken');
// const _ = require('lodash');
const validator = require('validator');
const axios = require('axios');
const MD5 = require('md5');
// const moment = require('moment');
const { siteFunc, validatorUtil, tools } = require('@utils');
const svgCaptcha = require('svg-captcha');
const fs = require('fs');
const shortId = require('shortid');
const qs = require('qs');
const crypto = require('crypto');

const awaitWriteStream = require('await-stream-ready').write;
const sendToWormhole = require('stream-wormhole');
const mkdirp = require('mkdirp');
const path = require('path');
const shortid = require('shortid');
const moment = require('moment');

class AdminController extends Controller {
  // 职爱网入口页
  async zaw() {
    const { ctx, app } = this;
    try {
      // 获取页脚信息
      const envConfig = await ctx.service.adminGroup.getEnvConfig();
      await ctx.render('manage/enter/zaw.html', {
        siteSeo: app.config.siteSeo,
        staticRootPath: app.config.static.prefix,
        imgRootPath: app.config.static.prefix + '/images/zaw',
        baseUrl: 'zhiaiwang.com/admin/login',
        ...envConfig,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
  // 杭州入口页
  async hz() {
    const { ctx } = this;
    try {
      const configs = await ctx.helper.reqJsonData('systemConfig/getConfig');
      const {
        showImgCode,
      } = configs || [];
      // 获取页脚信息
      const envConfig = await ctx.service.adminGroup.getEnvConfig();
      await ctx.render('manage/enter/hz.html', {
        siteSeo: this.app.config.siteSeo,
        staticRootPath: this.app.config.static.prefix,
        showImgCode,
        ...envConfig,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 山西焦煤入口页
  async sxcc() {
    const { ctx } = this;
    try {
      const configs = await ctx.helper.reqJsonData('systemConfig/getConfig');
      const {
        showImgCode,
      } = configs || [];
      // 获取页脚信息
      const envConfig = await ctx.service.adminGroup.getEnvConfig();
      await ctx.render('manage/enter/sxcc.html', {
        siteSeo: this.app.config.siteSeo,
        staticRootPath: this.app.config.static.prefix,
        showImgCode,
        ...envConfig,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 云南入口页
  async yn() {
    const { ctx } = this;
    try {
      const configs = await ctx.helper.reqJsonData('systemConfig/getConfig');
      const {
        showImgCode,
      } = configs || [];
      // 获取页脚信息
      const envConfig = await ctx.service.adminGroup.getEnvConfig();
      await ctx.render('manage/enter/yn.html', {
        siteSeo: this.app.config.siteSeo,
        staticRootPath: this.app.config.static.prefix,
        showImgCode,
        ...envConfig,
        domainNames: this.app.config.domainNames,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  async index() {
    const { ctx } = this;
    try {
      // 检索代号：#0001  ctx.session.basePath 后台根路径
      ctx.auditLog('重定向首页', '1111111111111', 'info');
      ctx.redirect(`${ctx.session.basePath}/dashboard`);
    } catch (e) {
      ctx.helper.renderFail(ctx, {
        message: e,
      });
    }
  }

  // master和各分支的登录页
  async login() {
    const { ctx, app } = this;

    try {
      // ctx.session.superUserInfo = {
      //   userName: 'hzwsjkzfd',
      //   _id: 'nPY1q6s8g',
      //   group: 'RMwIEyjWK',
      //   regAdd: [ '浙江省', '杭州市' ],
      //   name: 'hzwsjkzfd',
      //   phoneNum: '13003699705',
      //   isManage: true,
      // };
      if (ctx.session.superUserInfo) {
        ctx.auditLog('重定向首页', '222222222222222222', 'info');
        ctx.redirect('/admin/dashboard');
      } else {
        if (app.config.redirectLoginPage) {
          ctx.redirect(app.config.redirectLoginPage);
          return;
        }

        const systemConfigs = await ctx.service.systemConfig.find({
          isPaging: '0',
        });
        const {
          showImgCode,
        } = systemConfigs[0];
        // 获取页脚信息
        const envConfig = await ctx.service.adminGroup.getEnvConfig();
        const branch = app.config.branch && app.config.branch !== 'master' ? `/${app.config.branch}` : '.html';
        const publicKey = await ctx.service.adminGroup.getPublicKey();
        let loginPage = 'manage/login' + branch;
        // 如果分支登录页不存在，使用'manage/login/common.html'登录页
        if (!fs.existsSync(app.baseDir + '/app/view/' + loginPage)) {
          loginPage = 'manage/login/common.html';
        }
        await ctx.render(loginPage, {
          siteSeo: this.app.config.siteSeo,
          staticRootPath: this.app.config.static.prefix,
          showImgCode,
          title: app.config.systemName || '职业健康数字化管理系统',
          ...envConfig,
          ...publicKey,
        })
          .catch(async error => {
            console.log('！！！！！！！分支登录页渲染失败，使用master登录页', error);
            // 如果渲染失败（不存在分支对应的.html)，尝试渲染master分支的登录页
            await ctx.render('manage/login.html', {
              siteSeo: this.app.config.siteSeo,
              staticRootPath: this.app.config.static.prefix,
              showImgCode,
              ...envConfig,
              ...publicKey,
            });
          });


      }
    } catch (error) {
      console.log(44444, error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }

  }

  async loginAction() {
    const { ctx } = this;
    try {
      const superUserToken = await ctx.service.adminUser.loginAction(ctx.request.body);
      if (superUserToken.code && superUserToken.code === -1) {
        ctx.helper.renderFail(ctx, {
          message: ctx.__('validate_login_notSuccess'),
        });
      } else {
        ctx.helper.renderSuccess(ctx, {
          data: {
            token: superUserToken,
          },
        });
      }

    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }

    // const {
    //   ctx,
    //   config,
    // } = this;
    // try {

    //   const fields = ctx.request.body || {};
    //   const systemConfigs = await ctx.service.systemConfig.find({
    //     isPaging: '0',
    //   });
    //   const {
    //     showImgCode,
    //   } = systemConfigs[0];

    //   let errMsg = '';
    //   if (showImgCode && (!fields.imageCode || fields.imageCode !== ctx.session.imageCode)) {
    //     errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_imageCode') ]);
    //   }

    //   if (errMsg) {
    //     throw new Error(errMsg);
    //   }
    //   const userName = fields.userName.trim();
    //   const formObj = {
    //     userName,
    //   };

    //   ctx.validate(superUserRule.login(ctx), Object.assign({}, formObj, {
    //     password: fields.password,
    //   }));

    //   const user = await ctx.service.superUser.item(ctx, {
    //     query: {
    //       $or: [
    //         formObj, // 管理员
    //         { members: { $elemMatch: formObj } }, // 成员
    //       ],
    //     },
    //     populate: [{
    //       path: 'group',
    //       select: 'power _id enable name',
    //     }],
    //     files: 'enable password _id userName members',
    //   });
    //   // console.log(121212, user);
    //   if (!_.isEmpty(user)) {

    //     const hashPassword = ctx.helper.hashSha256(fields.password, config.salt_sha2_key);
    //     let userPsd = user.password; // 管理员的密码
    //     if (userName !== user.userName) { // 普通成员
    //       userPsd = user.members.filter(ele => ele.userName === userName)[0].password;
    //     }
    //     if (userPsd !== hashPassword) throw new Error(ctx.__('validate_login_notSuccess_1'));

    //     if (!user.enable) {
    //       throw new Error(ctx.__('validate_user_forbiden'));
    //     }

    //     const superUserToken = jwt.sign({
    //       userName: fields.userName.trim(),
    //       _id: user._id,
    //     }, this.app.config.encrypt_key, {
    //       expiresIn: '30day',
    //     });
    //     ctx.cookies.set('admin_' + this.app.config.auth_cookie_name, superUserToken, {
    //       path: '/',
    //       maxAge: this.app.config.superUserMaxAge,
    //       signed: true,
    //       httpOnly: false,
    //     }); // cookie 有效期30天

    //     // 记录登录日志
    //     const clientIp = ctx.header['x-forwarded-for'] || ctx.header['x-real-ip'] || ctx.request.ip;
    //     const loginLog = {
    //       type: 'login',
    //       // logs: user.userName + ' login，ip:' + clientIp,
    //       logs: fields.userName.trim() + ' login，ip:' + clientIp,
    //     };

    //     if (!_.isEmpty(ctx.service.systemOptionLog)) {
    //       await ctx.service.systemOptionLog.create(loginLog);
    //     }
    //     ctx.auditLog('登录操作', `未知用户正在通过用户名 ${formObj.userName} 执行登录。`);
    //     ctx.service.operateLog.create('Superusers', { optType: 'login', supplementaryNotes: '用户登录', optUserId: user._id });
    //     ctx.helper.renderSuccess(ctx, {
    //       data: {
    //         token: superUserToken,
    //       },
    //     });

    //   } else {
    //     ctx.helper.renderFail(ctx, {
    //       message: ctx.__('validate_login_notSuccess'),
    //     });
    //   }

    // } catch (err) {
    //   // console.log('--err--', err)
    //   ctx.helper.renderFail(ctx, {
    //     message: err,
    //   });

    // }
  }

  async sendVerificationCode() {
    const { config, ctx } = this;
    try {
      const fields = ctx.request.body || {};
      const phoneNum = fields.phoneNum.trim();
      const countryCode = fields.countryCode;
      const messageType = fields.messageType;

      let cacheKey = '';
      const systemConfigs = await ctx.service.systemConfig.find({
        isPaging: '0',
      });
      const {
        showImgCode,
      } = systemConfigs[0];
      let errMsg = '';
      if (showImgCode && (!fields.imageCode || !(ctx.session.imageCode >= fields.imageCode - 5 && ctx.session.imageCode <= fields.imageCode + 5))) {
        errMsg = ctx.__('图片滑动验证失败');
        // if (showImgCode && (!fields.imageCode || fields.imageCode !== ctx.session.imageCode)) {
        // errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_imageCode') ]);
        ctx.helper.renderFail(ctx, {
          message: errMsg,
        });
        return;
      }

      if (!phoneNum || !validator.isNumeric(phoneNum.toString())) {
        errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_phoneNum') ]);
      }

      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [ ctx.__('label_user_countryCode') ]);
      }

      if (!messageType) {
        errMsg = ctx.__('validate_error_params');
      }

      switch (messageType) {
        case '1': { // 登录
          const userCount = await ctx.model.SuperUser.findOne({
            $or: [
              { phoneNum }, // 管理员
              { members: { $elemMatch: { phoneNum } } }, // 成员
            ],
          });
          ctx.auditLog('查询到的用户', `用户：${userCount}`, 'info');
          if (!userCount) {
            ctx.helper.renderFail(ctx, {
              message: '对不起！该手机号未绑定账号！请使用密码登录！',
            });
            return;
          }
          cacheKey = '_sendMessage_login_';
          break;
        }
        case '7': { // 修改手机号
          // 校验现在账号的手机号和需要发送短信的手机号是否为同一个
          const token = ctx.cookies.get('admin_' + this.app.config.auth_cookie_name);
          const { _id } = jwt.decode(token, this.app.config.encrypt_key) || fields;
          const user = await ctx.service.superUser.item(ctx, {
            query: { _id },
            files: this.getAuthUserFields('login'),
          });
          if (user.phoneNum === phoneNum) {
            ctx.helper.renderFail(ctx, {
              message: '对不起！此为原手机号',
            });
            return;
          }
          cacheKey = '_sendMessage_update_';
          break;
        }
        default: {
          errMsg = ctx.__('validate_error_params');
          break;
        }
      }

      if (errMsg) {
        ctx.helper.renderFail(ctx, {
          message: errMsg,
        });
        return;
      }

      // 生成短信验证码
      const currentStr = siteFunc.randomString(6, '123456789');

      const endStr = countryCode + phoneNum;
      const currentKey = config.session_secret + cacheKey + endStr;
      ctx.helper.setCache(currentKey, currentStr, 1000 * 60 * 15); // 验证码缓存15分钟
      console.log('验证码====', currentStr);
      // 发送短消息
      const { data } = await this.ctx.curl(`${this.config.iServiceHost}/api/sendSMS`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data: {
          templateCodeName: 'verificationCode',
          TemplateParam: JSON.stringify({ code: currentStr.toString() }),
          PhoneNumbers: phoneNum,
        },
      });
      if (data.data && data.data.Code !== 'OK') {
        this.ctx.auditLog(`短信发送失败 - ${phoneNum}`, JSON.stringify(data), 'error');
      }

      ctx.helper.renderSuccess(ctx, {
        message: ctx.__('restful_api_response_success', [ ctx.__('user_action_tips_sendMessage') ]),
        data: {
          status: 200,
          // messageCode: currentStr,
        },
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async smloginAction() {
    const { app, ctx } = this;
    try {

      const fields = ctx.request.body || {};
      const messageCode = fields.messageCode.trim(),
        phoneNum = fields.phoneNum.trim(),
        countryCode = fields.countryCode,
        cacheKey = '_sendMessage_login_';
      let errMsg = '';

      if (!phoneNum || !validatorUtil.checkPhoneNum((phoneNum).toString())) {
        errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_phoneNum') ]);
      }

      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [ ctx.__('label_user_countryCode') ]);
      }

      const params = countryCode + phoneNum;
      const currentCode = ctx.helper.getCache(app.config.session_secret + cacheKey + params);

      console.log(currentCode);
      if (!messageCode || !validator.isNumeric((messageCode).toString()) || (messageCode).length !== 6) {
        errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_imageCode') ]);
      }

      if (!currentCode || !validator.isNumeric((currentCode).toString()) || (currentCode).length !== 6) {
        errMsg = '对不起！验证码错误！请重新发送验证码！';
      }

      if (errMsg) {
        // 依照需求决定是否使用严格的校验方式 现方式允许用户手机号或验证码写错后不用重新发送验证码
        // ctx.helper.clearCache(params, cacheKey);
        throw new Error(errMsg);
      }

      const queryUserObj = {
        $or: [
          { phoneNum },
          { phoneNum: '0' + phoneNum },
          { members: { $elemMatch: { phoneNum } } }, // 成员
        ],
        countryCode,
      };
      const userCount = await ctx.service.superUser.count(queryUserObj);
      if (userCount > 0) {
        const options = {
          returnOptions: {
            phoneNum: {
              returnPlaintext: true, // 返回明文密码
            },
          },
        };
        const user = await ctx.service.superUser.item(
          ctx,
          {
            query: queryUserObj,
            files: this.getAuthUserFields('login'),
          },
          options
        );
        if (!user) {
          ctx.helper.clearCache(params, cacheKey);
          throw new Error(ctx.__('validate_login_notSuccess_1'));
        } else {
          if (!user.enable) {
            ctx.helper.clearCache(params, cacheKey);
            throw new Error(ctx.__('validate_user_forbiden'));
          }

          if (currentCode !== messageCode) {
            // 依照需求决定是否使用严格的校验方式 现方式允许用户手机号或验证码写错后不用重新发送验证码
            // ctx.helper.clearCache(params, cacheKey);
            ctx.helper.renderFail(ctx, {
              message: '对不起！验证码错误！',
            });
            return;
          }
          // 清除因用户名和密码错误的锁定
          await ctx.service.superUser.update(ctx, user._id, {
            loginAttempts: 0,
            loginAttemptsTimestamp: [],
          });
        }
        let userName = phoneNum; // 非管理员 用户名就是手机号
        let optUserId = user._id;
        if (phoneNum === user.phoneNum) {
          userName = user.userName; // 是管理员
        } else {
          // 普通成员
          const member = user.members.filter(ele => ele.userName === phoneNum)[0];
          optUserId = member._id;
        }
        const superUserToken = tools.convertToEditJson(user);
        superUserToken.token = jwt.sign(
          {
            userName,
            _id: user._id,
            clientId: user._id + shortId.generate(),
          },
          app.config.encrypt_key,
          {
            expiresIn: '30day',
          }
        );

        ctx.cookies.set(
          'admin_' + app.config.auth_cookie_name,
          superUserToken.token,
          {
            path: '/',
            maxAge: app.config.superUserMaxAge,
            signed: true,
            httpOnly: false,
          }
        ); // cookie 有效期return _removeAll

        // 重置验证码
        ctx.helper.clearCache(params, cacheKey);
        ctx.auditLog(
          '登录操作',
          `未知用户正在通过手机号 ${phoneNum} 执行登录。`
        );
        ctx.service.operateLog.create('SuperUser', {
          optType: 'login',
          supplementaryNotes: '用户登录',
          optUserId,
        });
        ctx.helper.renderSuccess(ctx, {
          data: superUserToken,
          message: ctx.__('validate_user_loginOk'),
        });
      } else {
        ctx.helper.clearCache(params, cacheKey);
        ctx.helper.renderFail(ctx, {
          message: '对不起！此账号不存在！如有疑问请联系管理员！',
        });
      }
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async updateInfo() {
    const {
      ctx,
    } = this;
    let { password, phoneNum } = ctx.request.body;
    let errMsg = '';
    const token = ctx.cookies.get('admin_' + this.app.config.auth_cookie_name) || ctx.request.body.token;
    const userSession = jwt.decode(token, this.app.config.encrypt_key);
    const { _id } = userSession;
    let user = await ctx.service.superUser.item(ctx, {
      query: { _id },
      files: 'enable password _id email userName logo members',
    });
    user = JSON.parse(JSON.stringify(user));
    if (password) {
      password = password.trim();
      if (password.indexOf('$') === -1) {
        // 正则判断
        const regPassword = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#%^&*()_+{}\[\]:;<>,.?~\/\\-]).{8,12}$/;
        if (!regPassword.test(password)) {
          errMsg = ctx.__('密码格式不符合规范');
          ctx.helper.renderFail(ctx, {
            message: errMsg,
          });
          return;
        }
        if (user.userName === userSession.userName) { // 管理员
          await ctx.service.superUser.update(ctx, _id, {
            password,
            passwordExpiresAt: new Date(),
          });
        } else { // 普通成员
          // await ctx.model.SuperUser.updateOne(
          //   { _id, 'members.userName': userSession.userName },
          //   { $set: { 'members.$.password': password } });
          const members = user.members;
          for (let i = 0; i < members.length; i++) {
            if (members[i].userName === userSession.userName) {
              members[i].password = password;
            }
          }
          await ctx.service.db.updateOne('SuperUser', { _id }, {
            $set: {
              members,
              passwordExpiresAt: new Date(),
            },
          });
          // await ctx.service.db.updateOne('Superuser', { _id, 'members.userName': userSession.userName }, { $set: { 'members.$.password': password, passwordExpiresAt: new Date() } });
        }

        ctx.helper.renderSuccess(ctx, {
          status: 200,
          message: ctx.__('restful_api_response_success', [ ctx.__('lc_password') ]),
        });
      } else {
        errMsg = ctx.__('validate_error_params');
      }
    } else if (phoneNum) {
      phoneNum = phoneNum.trim();
      const messageCode = ctx.request.body.messageCode.trim();
      const countryCode = ctx.request.body.countryCode;
      const cacheKey = '_sendMessage_update_';
      if (!phoneNum || !validator.isNumeric(phoneNum.toString())) {
        errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_phoneNum') ]);
      }
      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [ ctx.__('label_user_countryCode') ]);
      }
      if (!messageCode || !validator.isNumeric((messageCode).toString()) || (messageCode).length !== 6) {
        errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_imageCode') ]);
      }
      const queryUserObj = {
        $or: [
          { phoneNum },
          { phoneNum: '0' + phoneNum },
          { members: { $elemMatch: { phoneNum } } }, // 成员
        ],
        countryCode,
      };
      const userCount = await ctx.service.superUser.count(queryUserObj);
      if (userCount > 0) {
        errMsg = '该手机号已被使用！';
      }
      const params = countryCode + phoneNum;
      const currentCode = ctx.helper.getCache(this.app.config.session_secret + cacheKey + params);
      if (!currentCode || !validator.isNumeric((currentCode).toString()) || (currentCode).length !== 6) {
        errMsg = '对不起！手机号错误！';
      }
      if (errMsg) {
        ctx.helper.renderFail(ctx, {
          message: errMsg,
        });
        return;
      }
      if (currentCode !== messageCode) {
        // 依照需求决定是否使用严格的校验方式 现方式允许用户手机号或验证码写错后不用重新发送验证码
        // ctx.helper.clearCache(params, cacheKey);
        ctx.helper.renderFail(ctx, {
          message: '对不起！验证码错误！',
        });
        return;
      }
      if (user.userName === userSession.userName) { // 管理员
        await ctx.service.superUser.update(ctx, _id, {
          phoneNum,
        });
      } else { // 普通成员
        // await ctx.model.SuperUser.updateOne(
        //   { _id, 'members.userName': userSession.userName },
        //   { $set: { 'members.$.phoneNum': phoneNum, 'members.$.userName': phoneNum } } // 普通成员的手机号和用户名需要同步修改
        // );
        await ctx.service.db.updateOne('SuperUser', { _id, 'members.userName': userSession.userName }, { $set: { 'members.$.phoneNum': phoneNum, 'members.$.userName': phoneNum } });

        const superUserToken = jwt.sign({
          userName: phoneNum,
          _id,
        }, this.app.config.encrypt_key, {
          expiresIn: '30day',
        });

        ctx.cookies.set('admin_' + this.app.config.auth_cookie_name, superUserToken, {
          path: '/',
          maxAge: this.app.config.superUserMaxAge,
          signed: true,
          httpOnly: false,
        }); // cookie 有效期30天
      }

      ctx.helper.renderSuccess(ctx, {
        status: 200,
        data: phoneNum,
        message: ctx.__('restful_api_response_success', [ ctx.__('label_user_phoneNum') ]),
      });

    } else {
      errMsg = ctx.__('validate_error_params');
    }
    if (errMsg) {
      ctx.helper.renderFail(ctx, {
        message: errMsg,
      });
    }
  }

  getAuthUserFields(type = '') {
    let fieldStr = 'id userName category group logo date enable state name IDcard email password';
    if (type === 'login') {
      fieldStr = 'id userName name password regAdd category group logo date enable state phoneNum countryCode email comments IDcard position loginActive birth members ';
    } else if (type === 'base') {
      fieldStr = 'id userName name password category group logo date enable state phoneNum countryCode email watchers followers comments IDcard favorites favoriteCommunityContent despises comments profession experience industry introduction birth creativeRight gender';
    } else if (type === 'session') {
      fieldStr = 'id userName name password category group logo date enable state phoneNum countryCode watchers followers praiseContents praiseMessages praiseCommunityContent watchSpecials watchCommunity watchTags favorites favoriteCommunityContent despises despiseMessage despiseCommunityContent IDcard position gender vip email comments';
    }
    return fieldStr;
  }

  // 单点登录 - 释普对接（限制在衢州市内的监管单位）
  async signIn() {
    const { app, ctx } = this;
    let restrictedArea = '衢州市'; // 允许访问的区域
    if (app.config.branch === 'xjbt') {
      restrictedArea = '';
    }
    // 1、访问域名校验
    const requestOrigin = ctx.request.header.origin;
    if (!(requestOrigin && app.config.signInWhitelist.includes(requestOrigin))) {
      ctx.helper.renderFail(ctx, {
        message: `对不起！您的域名（${requestOrigin}）禁止访问！`,
      });
      return;
    }

    try {
      const fields = ctx.request.body || {};
      const _phoneNum = fields.phoneNum.trim(),
        countryCode = fields.countryCode || 86;
      // 2、手机号码解密
      const phoneNum = await this.decrypt(_phoneNum);
      console.log(2222, phoneNum);
      // 3、参数校验
      let errMsg = '';
      if (!phoneNum || !validatorUtil.checkPhoneNum((phoneNum).toString())) {
        errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_phoneNum') ]);
      }
      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [ ctx.__('label_user_countryCode') ]);
      }
      if (errMsg) {
        throw new Error(errMsg);
      }
      // 4、查询用户
      const queryUserObj = {
        $or: [
          { phoneNum },
          { phoneNum: '0' + phoneNum },
          { members: { $elemMatch: { phoneNum } } }, // 成员
        ],
        countryCode,
      };
      const userCount = await ctx.service.superUser.count(queryUserObj);
      if (userCount > 0) {
        const user = await ctx.service.superUser.item(ctx, {
          query: queryUserObj,
          files: this.getAuthUserFields('login'),
        });
        if (!user) {
          throw new Error(ctx.__('validate_login_notSuccess_1'));
        } else {
          // 5、访问区域限制
          if (restrictedArea && !user.regAdd.includes(restrictedArea)) {
            ctx.helper.renderFail(ctx, {
              message: '访问区域被限制',
            });
            return;
          }
          if (!user.enable) {
            throw new Error(ctx.__('validate_user_forbiden'));
          }
        }
        // 6、生成token
        let userName = phoneNum; // 非管理员 用户名就是手机号
        if (phoneNum === user.phoneNum) userName = user.userName; // 是管理员
        const superUserToken = tools.convertToEditJson(user);
        superUserToken.token = jwt.sign({
          _id: user._id,
          userName,
        }, app.config.encrypt_key, {
          expiresIn: '30day',
        });
        // 7、返回数据
        ctx.auditLog('登录操作', `衢州区域的监管单位正在释普平台通过手机号 ${phoneNum} 执行登录（ip：${ctx.request.ip}）。`);
        ctx.service.operateLog.create('SuperUser', { optType: 'login', supplementaryNotes: '用户登录' });
        ctx.helper.renderSuccess(ctx, {
          data: {
            token: superUserToken.token,
            name: superUserToken.name,
            phoneNum: superUserToken.phoneNum,
            countryCode: superUserToken.countryCode,
          },
          message: ctx.__('validate_user_loginOk'),
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: '对不起！此账号不存在！如有疑问请联系管理员！',
        });
      }
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
  // async pad2(n) { return n < 10 ? '0' + n : n; }

  // async generateTimeReqestNumber() {
  //   const date = new Date();
  //   return date.getFullYear().toString() + this.pad2(date.getMonth() + 1) + this.pad2(date.getDate()) + this.pad2(date.getHours()) + this.pad2(date.getMinutes()) + this.pad2(date.getSeconds());
  // }
  hasDuplicateData(arr1, arr2) {
    const set1 = new Set(arr1);
    return arr2.some(item => set1.has(item));
  }
  // 单点登录 - 福州对接
  async fzSignIn() {
    const { app, ctx } = this;
    try {
      const fields = ctx.request.body || {};
      ctx.auditLog('对方传递的请求参数', JSON.stringify(fields), 'info');
      const tokenParams = qs.stringify({
        params: `{"syscode":"zyws","loginid":"${fields.username}","token":"${fields.token}"}`,
      });
      const fzToken = await axios({
        method: 'POST',
        url: app.config.fzToken + '?' + tokenParams,
      });
      if (fzToken && fzToken.data && fzToken.data.code === 'S') {
        if (fields.username) {
          const userInfoParams = qs.stringify({
            params: `{"syscode":"zyws","loginid":"${fields.username}"}`, // 因为sysadmin没有人员信息loginid的参数后面换成fields.username,
          });
          const resUser = await axios.post(
            app.config.fzUserDetail + '?' + userInfoParams
          );
          ctx.auditLog('登陆中查看resUser数据', resUser, 'info');
          // 登陆人是否在验证名单中
          const currentDateTime = this.transferTime(new Date());
          const fzLoginSystemid = app.config.fzLoginSystemid; // 福州系统标识;
          const password = app.config.fzLoginPassword;
          const loginVerifyMD = MD5(
            `${fzLoginSystemid}${password}${currentDateTime}`
          ); // systemid+password+currentDateTime
          const ryID = resUser.data.data.id;
          ctx.auditLog('验证当前登陆人id!!!!!', `当前登陆人id:${ryID}`, 'info');
          const datajson = JSON.stringify({
            operationinfo: {
              operator: ryID,
            },
            mainTable: {
              ry: '',
            },
            pageInfo: {
              pageNo: '1',
              pageSize: '10',
            },
            header: {
              systemid: fzLoginSystemid,
              currentDateTime,
              Md5: loginVerifyMD,
            },
          }); // 验证当前登陆人是否在名单中
          const options = {
            method: 'POST',
            data: qs.stringify({ datajson }),
            url: app.config.fzUserPriVerifier,
          };
          const loginVerify = await axios(options);
          ctx.auditLog('第二种情况', loginVerify, 'info');
          const fzLoginRoleList = app.config.fzLoginRoleList;
          let loginRole = loginVerify.data.result;
          ctx.auditLog('该登录人员的角色', loginRole, 'info');
          const rolesArr = [];
          let hasLoginPower; // 是否在可以登录的角色里
          if (loginRole) {
            loginRole = JSON.parse(loginRole);
            if (loginRole.length) {
              for (let i = 0; i < loginRole.length; i++) {
                const item = loginRole[i].mainTable;
                rolesArr.push(item.js);
              }
            }
            hasLoginPower = this.hasDuplicateData(rolesArr, fzLoginRoleList);
          }
          ctx.auditLog('验证角色权限', hasLoginPower, 'info');
          if (resUser.data.code === 'S') {
            ctx.auditLog('获取个人信息', JSON.stringify(resUser.data), 'info');
            if (hasLoginPower) {
              // 当前登陆人的角色可以登录
              const userInfo = resUser.data.data;
              ctx.auditLog('userinfo中的lastname', userInfo.lastname, 'info');
              const phoneNum = userInfo.mobile ? userInfo.mobile.trim() : ''; // 那边的人员的手机号(mobile,loginid)
              const userName = fields.username;
              const name = userInfo.lastname;
              ctx.auditLog('登陆人手机号', phoneNum, 'info');
              ctx.auditLog('登陆人用户名', userName, 'info');
              ctx.auditLog('登陆人姓名', name, 'info');
              // 4、查询用户
              let matchOr = [];
              if (phoneNum) {
                matchOr = matchOr.concat([
                  { phoneNum },
                  { phoneNum: '0' + phoneNum },
                  { 'members.phoneNum': phoneNum },
                ]);
              } else if (userName) {
                matchOr = matchOr.concat([
                  { userName },
                  { 'members.userName': userName },
                ]);
              } else if (name) {
                matchOr = matchOr.concat([{ name }, { 'members.name': name }]);
              }
              const queryUserObj = {
                state: '1',
                $or: matchOr,
              };
              ctx.auditLog(
                '搜索当前人搜索条件',
                JSON.stringify(queryUserObj),
                'info'
              );
              const options = {
                returnOptions: {
                  phoneNum: {
                    returnPlaintext: true, // 返回明文密码
                  },
                },
              };
              let user = await ctx.model.SuperUser.findOne(
                queryUserObj
              ).setOptions(options);
              ctx.auditLog('搜索到的当前登陆人', user, 'info');
              if (!user) {
                const newMember = {
                  name,
                  phoneNum,
                  userName: userName || phoneNum, // 默认是手机号
                  jobTitle: userInfo.jobtitleName || '',
                };
                ctx.auditLog(
                  'fzcdc新建成员',
                  JSON.stringify(newMember),
                  'info'
                );
                await ctx.service.superUser.addMember2(
                  { state: '1', userName: 'fzcdc' },
                  newMember
                );
                user = await ctx.model.SuperUser.findOne(queryUserObj);
                ctx.auditLog(
                  'fzcdc新建成员结果',
                  JSON.stringify(user.members),
                  'info'
                );
              }
              if (user) {
                const cname = userInfo.subcompanyname;
                if (
                  cname &&
                  user.userName !== 'fzcdc' &&
                  user.cname !== cname
                ) {
                  await ctx.model.SuperUser.updateOne(queryUserObj, {
                    $set: { cname },
                  });
                  ctx.auditLog('登陆人公司名换成了', cname, 'info');
                }
                if (!user.enable) {
                  throw new Error(ctx.__('该账户在本系统已被停用'));
                }
                let optUserId = user._id;
                if (phoneNum !== user.phoneNum) {
                  // 普通成员
                  const member = user.members.filter(
                    ele => ele.userName === phoneNum
                  )[0];
                  optUserId = member._id;
                }
                // 6、生成toke
                ctx.auditLog(
                  '用户名赋值',
                  userName === user.userName ? userName : phoneNum,
                  'info'
                );
                const superUserToken = tools.convertToEditJson(user);
                superUserToken.token = jwt.sign(
                  {
                    _id: user._id,
                    userName: userName === user.userName ? userName : phoneNum,
                  },
                  app.config.encrypt_key,
                  {
                    expiresIn: '30day',
                  }
                );
                ctx.cookies.set(
                  'admin_' + app.config.auth_cookie_name,
                  superUserToken.token,
                  {
                    path: '/',
                    maxAge: app.config.superUserMaxAge,
                    signed: true,
                    httpOnly: false,
                  }
                );

                // 7、返回数据
                ctx.auditLog(
                  '登录成功',
                  `福州的监管单位正在其平台通过手机号 ${phoneNum}。`,
                  'info'
                );
                ctx.service.operateLog.create('SuperUser', {
                  optType: 'login',
                  supplementaryNotes: '用户登录',
                  optUserId,
                });
                ctx.helper.renderSuccess(ctx, {
                  data: {
                    token: superUserToken.token,
                  },
                  message: ctx.__('validate_user_loginOk'),
                });
                ctx.redirect('/admin/dashboard'); // 也可以那边重定向
                ctx.auditLog(
                  '登录成功',
                  '福州的监管单位跳转重定向到主页',
                  'info'
                );
              } else {
                ctx.helper.renderFail(ctx, {
                  message: '对不起！此账号不存在！如有疑问请联系管理员！',
                });
              }
            } else {
              ctx.auditLog(
                '登录失败',
                '福州的监管单位正在其平台调用登陆人验证登录接口,当前登陆人不在验证名单中',
                'error'
              );
              ctx.redirect('/admin/login');
            }
          } else {
            ctx.helper.renderFail(ctx, {
              message: '获取个人信息失败',
            });
            ctx.auditLog(
              '登录失败',
              '福州的监管单位正在其平台调用登录接口,获取个人信息失败',
              'error'
            );
            ctx.redirect('/admin/login');
          }
        } else {
          ctx.helper.renderFail(ctx, {
            message: '没有username',
          });
          ctx.auditLog(
            '登录失败',
            '福州的监管单位正在其平台调用登录接口,缺少username字段',
            'error'
          );
          ctx.redirect('/admin/login');
        }
      } else {
        ctx.auditLog(
          'fz登录获取token',
          app.config.fzToken + '?' + tokenParams,
          'info'
        );
        console.log(123456, fzToken);
        ctx.auditLog(
          'fz登录失败,token不通过',
          JSON.stringify(fzToken),
          'error'
        );
        ctx.helper.renderFail(ctx, {
          message: 'token不通过',
        });
        ctx.redirect('/admin/login');
      }
    } catch (err) {
      ctx.auditLog('登录失败', err.message, 'error');
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  }
  async xjbtCallback() {
    const { ctx } = this;
    try {
      const {
        api_host,
        client_id,
        client_secret,
        grant_type,
        redirect_uri,
        scope,
      } = this.config.jgOidc.oidcInfo;
      const code = ctx.query.code;
      ctx.auditLog('oidc认证登录', `code:${code}`, 'info');
      // 通过code获取token
      const params = {
        grant_type,
        client_id,
        client_secret,
        code,
        scope,
        redirect_uri,
      };

      // 构建 URL 查询参数
      // const queryString = new URLSearchParams(params).toString();
      const tokenRes = await ctx.curl(`${api_host}/oauth2/token`, {
        method: 'POST',
        data: params,
        dataType: 'json', // 自动解析响应为 JSON
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      // 解析返回结果
      // const { id } = tokenRes.data.user;
      const { access_token } = tokenRes.data;

      // 通过token获取用户信息
      const userInfo = await axios.post(
        `${api_host}/oauth2/userInfo?access_token=${access_token}`
      );
      console.log('xjbt userInfo', userInfo.data);
      if (userInfo.data.code !== '0') {
        throw new Error(`获取用户信息失败:${userInfo.data.msg}`);
      }
      // 根据user_name对应unitCode
      // 先在adminuser表中查找
      const { _id, userName, phoneNum, name, zonecode, orgname, email, orgcode: orgCode, roleCodes } =
        userInfo.data.data;
      if (!orgCode) {
        throw new Error('机构编码不存在');
      }
      let superUserInfo = await ctx.model.SuperUser.findOne({
        orgCode,
      }).lean();
      if (superUserInfo) {
        if (superUserInfo.openId === _id) {
          await ctx.model.SuperUser.updateOne({
            orgCode,
          }, {
            $set: {
              userName,
              name,
              email,
              phoneNum,
              ...(this.app.config.crossRegionManage ? { crossRegionRoles: roleCodes } : { group: roleCodes }),
              ...(!superUserInfo.crossRegionAdd || !superUserInfo.crossRegionAdd.length ? {
                regAdd: superUserInfo.regAdd,
                area_code: superUserInfo.area_code,
              } : {}),
            },
          });
        } else {
          const membersIndex = superUserInfo.members.findIndex(item => item.openId === _id);
          if (membersIndex !== -1) {
            superUserInfo.members[membersIndex] = {
              ...superUserInfo.members[membersIndex],
              userName,
              name,
              phoneNum,
              ...(this.app.config.crossRegionManage ? { crossRegionRoles: roleCodes } : { group: roleCodes }),
              openId: _id,
            };
            await ctx.model.SuperUser.updateOne({
              orgCode,
            }, {
              $set: {
                members: superUserInfo.members,
              },
            });
          } else {
            superUserInfo.members.push({
              userName,
              name,
              phoneNum,
              openId: _id,
              ...(this.app.config.crossRegionManage ? { crossRegionRoles: roleCodes } : { group: roleCodes }),
            });
            await ctx.model.SuperUser.updateOne({
              orgCode,
            }, {
              $set: {
                members: superUserInfo.members,
              },
            });
          }
        }
      } else {
        const areaNames = await this.getAreaNames(zonecode);
        if (areaNames.length === 0) {
          throw new Error(`当前区域编码不存在:${zonecode}`);
        }
        const userInfo = {
          type: 1,
          cname: orgname,
          regAdd: areaNames, // 根据上级区域的数量，数组长度会动态变化
          userName,
          area_code: zonecode,
          name,
          email,
          ...(this.app.config.crossRegionManage ? { crossRegionRoles: roleCodes } : { group: roleCodes }),
          phoneNum,
          orgCode,
          openId: _id,
          enable: true,
        };
        superUserInfo = await ctx.model.SuperUser.create(userInfo);
      }
      const superUserToken = tools.convertToEditJson(superUserInfo);
      superUserToken.token = jwt.sign(
        {
          userName,
          _id: superUserInfo._id,
          clientId: superUserInfo._id + shortId.generate(),
        },
        ctx.app.config.encrypt_key,
        {
          expiresIn: '30day',
        }
      );
      ctx.cookies.set(
        'admin_' + ctx.app.config.auth_cookie_name,
        superUserToken.token,
        {
          path: '/',
          maxAge: ctx.app.config.superUserMaxAge,
          signed: true,
          httpOnly: false,
        }
      ); // cookie 有效期return _removeAll
      ctx.auditLog(
        '登录成功',
        '监管单位通过门户平台登录。',
        'info'
      );
      ctx.service.operateLog.create('SuperUser', {
        optType: 'login',
        supplementaryNotes: '用户登录',
        optUserId: superUserInfo._id,
      });

      ctx.redirect('/admin/dashboard');
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
      ctx.auditLog('oidc认证登录错误', `${error}`, 'error');
    }
  }

  async getAreaNames(zonecode) {
    const { ctx } = this;
    const areaNames = [];
    let currentZonecode = zonecode;

    while (currentZonecode) {
      const areaCode = await ctx.model.District.findOne({
        area_code: currentZonecode,
      }).lean();

      if (!areaCode) {
        break;
      }

      areaNames.unshift(areaCode.name); // 将名称添加到数组的开头
      currentZonecode = areaCode.parent_code; // 获取上一级的区域编码
    }

    return areaNames;
  }

  transferTime(date) {
    const year = date.getFullYear();
    const month = ('0' + (date.getMonth() + 1)).slice(-2);
    const day = ('0' + date.getDate()).slice(-2);
    const hours = ('0' + date.getHours()).slice(-2);
    const minutes = ('0' + date.getMinutes()).slice(-2);
    const seconds = ('0' + date.getSeconds()).slice(-2);
    return `${year}${month}${day}${hours}${minutes}${seconds}`;
  }

  // 加密,这个是给第三方用的，放在这里只是为了做测试, 利用charCodeAt把字符转为 Unicode 编码
  async encryption(code) {
    let c = String.fromCharCode(code.charCodeAt(0) + code.length);
    for (let i = 1; i < code.length; i++) {
      c += String.fromCharCode(code.charCodeAt(i) + code.charCodeAt(i - 1));
    }
    return (escape(c));
  }

  // 解密
  async decrypt(code) {
    code = unescape(code);
    let c = String.fromCharCode(code.charCodeAt(0) - code.length);
    for (let i = 1; i < code.length; i++) {
      c += String.fromCharCode(code.charCodeAt(i) - c.charCodeAt(i - 1));
    }
    return c;
  }

  // 判断数据库连接状态
  async adoConnection() {
    const { ctx } = this;
    try {
      const district = await ctx.model.District.findOne({}, { area_code: 1 });
      const version = fs.readFileSync('version', 'utf-8') || '';
      if (district && district.area_code) {
        ctx.helper.renderSuccess(ctx, {
          message: 'mongoose connected',
          data: {
            version: version.replace(/(\n|VERSION=)/g, ''),
          },
        });
      } else {
        ctx.helper.renderCustom(ctx, {
          status: 500,
          message: 'mongoose disconnected',
          data: version,
        });
      }
    } catch (err) {
      ctx.helper.renderCustom(ctx, {
        status: 500,
        message: JSON.stringify(err),
      });
    }
  }
  // 空请求
  async emptyRequest() {
    this.ctx.body = {
      status: 200,
      message: '空请求',
    };
  }
  async getAuthCookieName() {
    const { ctx, config } = this;
    ctx.helper.renderCustom(ctx, {
      message: '获取成功',
      data: 'admin_' + config.auth_cookie_name,
    });
  }
  // 获取验证码
  async getImgCode(ctx) {
    const captcha = svgCaptcha.createMathExpr({
      size: 4, // 随机字符串的数量
      fontSize: 40,
      ignoreChars: 'Ooli', // 过滤掉一些字符，如0o1i
      width: 100,
      height: 40,
      noise: 2,
      color: false,
      background: '#ffffff',
      mathMin: 0,
      mathMax: 9,
      mathOperator: '+-',
    });
    ctx.session.imageCode = captcha.text; // text及data都是函数返回的string属性的对象
    ctx.response.type = 'image/svg+xml'; // 返回的类型
    ctx.body = captcha.data; // 返回一张图片
  }

  // 获取验证码
  async getSlideCode(ctx) {
    // 验证码：60~200 的随机数
    const imageCode = Math.floor(Math.random() * (200 - 60) + 60);
    // 加密
    let sharedKey = '2U8//VqSBjze1nja0N5jDX2arBceo5qJWjpgrwGpWx0=';
    sharedKey = Buffer.from(sharedKey, 'base64');
    const cipher = crypto.createCipheriv(
      'aes-256-cbc',
      sharedKey,
      Buffer.alloc(16)
    );
    let encrypted = cipher.update(imageCode + '', 'utf8', 'base64');
    encrypted += cipher.final('base64');
    // 存入session
    ctx.session.imageCode = imageCode;
    // 返回加密后的验证码
    ctx.body = {
      status: 200,
      data: encrypted,
    };
  }

  // 限制登陆
  async getAuthLogin() {
    const { ctx } = this;
    const { isLimitOperation, limitOperationDoc } = await ctx.service.systemConfig.authAdminEnabel();
    ctx.helper.renderSuccess(ctx, {
      message: '获取成功',
      data: {
        isLimitOperation,
        limitOperationDoc,
      },
    });
  }

  async healthDecalaration() {
    try {
      const { ctx } = this;
      const ids = ctx.request.body;
      const res = await ctx.model.Declaration.aggregate([
        {
          $match: {
            isSubmit: true,
            _id: {
              $in: ids,
            },
          },
        },
        {
          $lookup: {
            from: 'adminorgs',
            localField: 'enterpriseId',
            foreignField: '_id',
            pipeline: [
              {
                $project: {
                  cname: 1,
                  code: 1,
                  contract: 1,
                  phoneNum: 1,
                  regAdd: 1,
                  industryCategory: 1,
                  companyScale: 1,
                  regType: 1,
                },
              },
            ],
            as: 'enterpriseInfo',
          },
        },
        {
          $unwind: '$enterpriseInfo', // 将数组展开为单个对象
        },
        // {
        //   $skip: (Number(currentPage) - 1) * Number(limit)
        // },
        // {
        //   $limit: Number(limit)
        // }
      ]);

      ctx.helper.renderSuccess(ctx, {
        message: '获取成功',
        data: res,
      });
    } catch (error) {
      console.log(error);
    }
  }

  // healthDecalarationScoring
  async healthDecalarationScoring() {
    try {
      const { ctx } = this;
      if (!ctx.request.headers['role-info']) {
        throw new Error('invalid auth');
      }
      const res = await ctx.model.Declaration.update({
        _id: ctx.request.body.decalarationId,
      }, {
        $set: {
          score: Number(ctx.request.body.score),
        },
      });


      ctx.helper.renderSuccess(ctx, {
        message: '更新成功',
        data: res,
      });
    } catch (error) {
      console.log(error);
    }
  }

  async uploadFile() {
    const { ctx, app } = this;
    const EnterpriseID = ctx.session.superUserInfo && ctx.session.superUserInfo._id ? ctx.session.superUserInfo._id : 'default';
    const uploadPath = `${app.config.upload_path}/${EnterpriseID}`;
    const uploadHttpPath = path.join('/static', app.config.upload_http_path, EnterpriseID);

    const parts = ctx.multipart({ autoFields: true });
    let part;
    while ((part = await parts()) != null) {
      if (!part.filename) continue;
      const staticName = moment().format('YYYYMMDD') + shortid.generate() + path.extname(part.filename);
      const writePath = path.join(uploadPath, `/${staticName}`);
      if (!fs.existsSync(uploadPath)) {
        await mkdirp(uploadPath);
      }
      const writeStream = fs.createWriteStream(writePath);
      try {
        await awaitWriteStream(part.pipe(writeStream));
        return ctx.helper.renderSuccess(ctx, {
          message: '上传成功',
          data: {
            url: path.join(uploadHttpPath, staticName),
          },
        });
      } catch (error) {
        // 如果出现错误，关闭管道,防止浏览器响应卡死
        await sendToWormhole(part);
        writeStream.destroy();
        return ctx.helper.renderCustom(ctx, {
          status: 500,
          message: '上传失败: ' + error.message,
        });
      }
    }
    return ctx.helper.renderCustom(ctx, {
      status: 400,
      message: '未上传文件',
    });
  }
  async deleteFile() {
    const { ctx, app } = this;
    const { filePath } = ctx.query;
    if (!filePath) {
      return ctx.helper.renderCustom(ctx, {
        status: 400,
        message: '缺少参数: filePath',
      });
    }
    try {
      const filePathArr = filePath.split('/');
      const fileName = filePathArr[filePathArr.length - 1];
      const EnterpriseID = filePathArr[filePathArr.length - 2];
      const actualFilePath = path.join(app.config.upload_path, EnterpriseID, fileName);
      if (!fs.existsSync(actualFilePath)) {
        return ctx.helper.renderCustom(ctx, {
          status: 400,
          message: '文件不存在',
        });
      }
      // 删除文件
      fs.unlinkSync(actualFilePath);
      return ctx.helper.renderSuccess(ctx, {
        message: '删除成功',
      });
    } catch (error) {
      return ctx.helper.renderCustom(ctx, {
        status: 500,
        message: '删除失败: ' + error.message,
      });
    }
  }
}

module.exports = AdminController;
