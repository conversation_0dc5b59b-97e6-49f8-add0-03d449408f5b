const Service = require('egg').Service;
const moment = require('moment');
const { RtcTokenBuilder } = require('agora-access-token');
const axios = require('axios').default;
class WarningService extends Service {
  // 监管端同意修改预警
  async applyModify(jobHealthId = '', modifiedStatus = 3, reason) {
    if (!jobHealthId) return '原数据id必须传';
    const { ctx } = this;
    const warningDetail = await ctx.model.Warning.findOne(
      { $or: [{ jobHealthId }, { _id: jobHealthId }], delete: false },
      { modifiedStatus: 1 }
    );
    if (!warningDetail || !warningDetail._id) return `未找到jobHealthId为${jobHealthId}的相关预警`;
    if (warningDetail.modifiedStatus !== 2) return '申请状态已变更，因此不做任操作';
    const updateUserName = await this.findSuperName();
    // await ctx.model.Warning.updateOne(
    //   { _id: warningDetail._id },
    //   {
    //     modifiedStatus,
    //     $push: {
    //       process: {
    //         time: Date.now(),
    //         thing: `${modifiedStatus === 3 ? '通过' : '驳回'}修改上报数据申请`,
    //         remark: `${updateUserName}${modifiedStatus === 3 ? '通过' : '驳回'}了修改上报数据的申请（备注：${reason || '无'}）`,
    //       },
    //     },
    //   },
    //   {
    //     upsert: true,
    //     new: true,
    //   }
    // );
    await ctx.service.db.updateOne('Warning', { _id: warningDetail._id },
      {
        modifiedStatus,
        $push: {
          process: {
            time: Date.now(),
            thing: `${modifiedStatus === 3 ? '通过' : '驳回'}修改上报数据申请`,
            remark: `${updateUserName}${modifiedStatus === 3 ? '通过' : '驳回'}了修改上报数据的申请（备注：${reason || '无'}）`,
          },
        },
      },
      {
        upsert: true,
        new: true,
      });
    return '操作成功';
  }
  // 获取当前监管单位的名称
  async findSuperName(superUserId) {
    const { ctx } = this;
    const { cname } = ctx.session.superUserInfo;
    if (cname) {
      return `${cname} - ${ctx.session.superUserInfo.name}`;
    }
    const org = await ctx.model.SuperUser.findOne(
      { _id: superUserId || ctx.session.superUserInfo._id },
      { cname: 1, name: 1 }
    );
    let name = org.name || '';
    if (ctx.session.superUserInfo.name !== name) {
      name = ctx.session.superUserInfo.name;
    }
    const curUserName = `${org.cname} - ${name}`;
    return superUserId ? org.cname : curUserName;
  }
  // 根据id找到企业联系人信息
  async findCompanyContactById(_id) {
    const options = {
      returnOptions: {
        phoneNum: {
          returnPlaintext: true, // 返回明文密码
        },
      },
    };
    return await this.ctx.model.AdminUser.findOne({ _id }).setOptions(options);
  }
  // 驳回整改并修改状态
  async reject(_id, remark) {
    const updateUserName = await this.findSuperName();
    // return await this.ctx.model.Warning.update(
    //   { _id },
    //   {
    //     status: 4,
    //     $push: {
    //       process: {
    //         time: Date.now(),
    //         thing: `驳回整改 (${updateUserName})`,
    //         remark,
    //       },
    //     },
    //   },
    //   {
    //     upsert: true,
    //     new: true,
    //   }
    // );
    return await this.ctx.service.db.updateOne('Warning', { _id },
      {
        status: 4,
        $push: {
          process: {
            time: Date.now(),
            thing: `驳回整改 (${updateUserName})`,
            remark,
          },
        },
      },
      {
        upsert: true,
        new: true,
      });
  }
  // 解除预警
  async liftWarning(_id, remark, files = [], documents = []) {
    const updateUserName = await this.ctx.service.warning.findSuperName();
    // return await this.ctx.model.Warning.update(
    //   { _id },
    //   {
    //     status: 3,
    //     $push: {
    //       process: {
    //         time: Date.now(),
    //         thing: `解除预警 (${updateUserName})`,
    //         remark,
    //         files,
    //         documents,
    //       },
    //     },
    //   },
    //   {
    //     upsert: true,
    //     new: true,
    //   }
    // );
    return await this.ctx.service.db.updateOne('Warning', { _id },
      {
        status: 3,
        $push: {
          process: {
            time: Date.now(),
            thing: `解除预警 (${updateUserName})`,
            remark,
            files,
            documents,
          },
        },
      },
      {
        upsert: true,
        new: true,
      });
  }
  // 已给一条预警发送短信后修改状态和插入事件经过
  async afterSendMsg(_id, content) {
    const updateUserName = await this.ctx.service.warning.findSuperName();
    // return await this.ctx.model.Warning.update(
    //   { _id },
    //   {
    //     status: 1,
    //     modifiedStatus: 1,
    //     $push: {
    //       process: {
    //         time: Date.now(),
    //         thing: `处理预警 (${updateUserName})`,
    //         remark: '短信督促：' + content,
    //       },
    //     },
    //   },
    //   {
    //     upsert: true,
    //     new: true,
    //   }
    // );
    return await this.ctx.service.db.updateOne('Warning', { _id },
      {
        status: 1,
        modifiedStatus: 1,
        $push: {
          process: {
            time: Date.now(),
            thing: `处理预警 (${updateUserName})`,
            remark: '短信督促：' + content,
          },
        },
      },
      {
        upsert: true,
        new: true,
      });
  }
  // 给一条预警插入处置方案,并修改状态
  async disposalWarningPlan(_id, disposalPlan) {
    const updateUserName = await this.ctx.service.warning.findSuperName();
    // return await this.ctx.model.Warning.update(
    //   { _id },
    //   {
    //     status: 1,
    //     modifiedStatus: 1,
    //     $push: {
    //       process: {
    //         time: Date.now(),
    //         thing: `处置方案 (${updateUserName})`,
    //         disposalPlan,
    //       },
    //     } },
    //   {
    //     upsert: true,
    //     new: true,
    //   }
    // );
    return await this.ctx.service.db.updateOne('Warning', { _id },
      {
        status: 1,
        modifiedStatus: 1,
        $push: {
          process: {
            time: Date.now(),
            thing: `处置方案 (${updateUserName})`,
            disposalPlan,
          },
        } },
      {
        upsert: true,
        new: true,
      });
  }
  // 修改预警状态
  async updateStatus(_id, status) {
    // return await this.ctx.model.Warning.update(
    //   { _id },
    //   { status }
    // );
    return await this.ctx.service.db.updateOne('Warning', { _id },
      { status });
  }
  // 删除一条预警
  async deleteWarning(_id, thoroughly = false) {
    if (thoroughly) { // 彻底删除
      // return await this.ctx.model.Warning.deleteOne(
      //   { _id }
      // );
      return await this.ctx.service.db.deleteOne('Warning', { _id });
    }
    // 移入回收站  假删除
    // return await this.ctx.model.Warning.update(
    //   { _id },
    //   { delete: true }
    // );
    return await this.ctx.service.db.updateOne('Warning', { _id },
      { delete: true });
  }

  // 预警列表 带分页
  async find(params) {
    const { ctx } = this;
    let query = {
      delete: false,
      $and: [],
    };
    if (params.keyWords) {
      const warnCompany = [];
      const orgReg = new RegExp(params.keyWords.trim(), 'i');
      const orgQuery = await ctx.model.Adminorg.aggregate([
        { $match: { cname: { $regex: orgReg } } },
        { $project: { _id: 1 } },
        {
          $lookup: {
            from: 'warning',
            localField: '_id',
            foreignField: 'companyId',
            as: 'warning',
          },
        },
      ]);
      if (orgQuery && Object.keys(orgQuery).length !== 0) {
        // 遍历orgQuery,将orgQuery中warning中的companyName放入warnCompany中
        orgQuery.forEach(ele => {
          if (ele.warning && ele.warning.length !== 0) {
            ele.warning.forEach(warn => {
              warnCompany.push(warn.companyName);
            });
          }
        });
        if (warnCompany && warnCompany.length !== 0) {
          params.keyWords = warnCompany;
        }
        query = await this.addSearchAddrAddYear(query, params.regAddr, params.year, params.keyWords, params.lever, params.tabName);
      } else {
        // 跳出函数
        return [];
      }
    } else {
      query = await this.addSearchAddrAddYear(query, params.regAddr, params.year, params.keyWords, params.lever, params.tabName);
    }
    if (params.status || params.status === 0) query.status = params.status;
    if (params.modifiedStatus) query.modifiedStatus = +params.modifiedStatus;
    if (params.type) query.type = +params.type;
    if (query.$and && query.$and.length === 0) delete query.$and;
    params.limit = +params.limit || 10;
    if (!params.curPage) params.curPage = 1;
    query.delete = false;
    const res = await ctx.model.Warning.aggregate([
      { $match: query },
      { $sort: { ctime: -1 } },
      { $skip: params.limit * (params.curPage - 1) },
      { $limit: params.limit },
      { $lookup: { from: 'adminorgs', localField: 'companyId', foreignField: '_id', as: 'company' } },
      { $lookup: { from: 'adminusers', localField: 'company.adminUserId', foreignField: '_id', as: 'adminuser' } },
    ]);
    // const res = await ctx.model.Warning.find(query)
    //   .sort({ ctime: -1 })
    //   .skip(params.limit * (params.curPage - 1))
    //   .limit(params.limit)
    //   .populate('companyId', 'cname contract phoneNum adminUserId');
    return res;
  }

  // 获取预警视频token
  async getWarningVideoToken(params) {
    const { channel } = params;
    // 只要后4位
    const uid = await this.getPhoneNumFour();
    const data = {
      channel,
      uid,
    };
    const keySecret = await this.generateRtcToken(data);
    return keySecret;
  }

  // 根据meetingId获取预约数据的通道信息
  async getReserveChannel(meetingId) {
    const res = await this.ctx.model.Reserve.findOne({
      meetingId,
    }).lean();
    if (res) {
      const data = await this.generateRtcToken({
        channel: res.channel,
        uid: res.contactPhoneNum ? res.contactPhoneNum.slice(-4) : '',
      });
      return data;
    }
    throw new Error('未找到该会议');

  }

  // 获取声网rtctoken
  async generateRtcToken(data) {
    const { channel, uid } = data;
    const { appId, appCertificate } = this.config.agora;
    if (!appId || !appCertificate) {
      throw new Error('请设置 AGORA_CUSTOMER_KEY 和 AGORA_CUSTOMER_SECRET');
    }
    if (!channel) {
      throw new Error('必须传入channel');
    }
    if (!uid) {
      throw new Error('必须传入uid');
    }
    const channelInfo = await this.getReserveChannelStatus(channel);
    if (!channelInfo) {
      throw new Error('当前会议未开启');
    }
    // const userCount = await this.getChannelHosts(channel, appId);
    // if (userCount >= 2) {
    //   throw new Error('房间已满');
    // }

    const key = RtcTokenBuilder.buildTokenWithUid(
      appId,
      appCertificate,
      channel,
      uid
    );
    const keySecret = {
      appId,
      channel,
      uid,
      token: key,
    };
    return keySecret;
  }

  // 获取当前用户的手机后四位
  async getPhoneNumFour() {
    const { phoneNum, _id, userName, isManage } = this.ctx.session.superUserInfo;
    if (isManage) {
      return Number(phoneNum.toString().slice(-4));
    }
    const loginUserInfo = await this.ctx.model.SuperUser.aggregate([
      { $match: { _id } },
      {
        $unwind: '$members',
      },
      {
        $match: { 'members.userName': userName },
      },
    ]);
    if (
      loginUserInfo &&
        loginUserInfo.length &&
        loginUserInfo[0]._id === _id
    ) {
      if (loginUserInfo[0].members && loginUserInfo[0].members.phoneNum) {
        return Number(loginUserInfo[0].members.phoneNum.toString().slice(-4));
      }
      throw new Error('获取用户手机后四位失败');

    }

  }

  // 获取频道内用户数量
  async getChannelHosts(channelName, appId) {
    const Authorization = this.agroaAuth();
    const options = {
      method: 'GET',
      url: `https://api.sd-rtn.com/dev/v1/channel/user/${appId}/${channelName}`,
      headers: { Accept: 'application/json', Authorization },
    };

    try {
      const { data } = await axios.request(options);
      return data.data.total || 0;
    } catch (error) {
      console.error(error);
    }
  }

  // auth声网认证
  agroaAuth() {
    // 客户 ID
    // 需要设置环境变量 AGORA_CUSTOMER_KEY
    const { customerKey, customerSecret } = this.config.agora;
    if (!customerKey || !customerSecret) {
      throw new Error('请设置 AGORA_CUSTOMER_KEY 和 AGORA_CUSTOMER_SECRET');
    }
    // 拼接客户 ID 和客户密钥
    const plainCredential = customerKey + ':' + customerSecret;
    // 使用 base64 进行编码
    const encodedCredential = Buffer.from(plainCredential).toString('base64');
    // 创建 authorization header
    const authorizationField = 'Basic ' + encodedCredential;
    return authorizationField;
  }

  // 获取预警预约通道是否已开启
  async getReserveChannelStatus(channel) {
    const nowTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
    const res = await this.ctx.model.Reserve.findOne({
      channel,
      reserveDate: { $size: 2 },
      'reserveDate.0': { $lte: nowTime },
      'reserveDate.1': { $gte: nowTime },
    });
    if (res) {
      return true;
    }
    return false;

  }

  // 获取当前通道是否开启
  async getChannelStatus() {
    const Authorization = this.agroaAuth();
    const appId = this.config.agora.appId;
    const options = {
      method: 'GET',
      url: `https://api.sd-rtn.com/dev/v1/channel/${appId}`,
      headers: { Accept: 'application/json', Authorization },
    };

    try {
      const { data } = await axios.request(options);
      return data.data.status || 0;
    } catch (error) {
      console.error(error);
    }
  }

  // 统计数量
  async count(params) {
    const query = await this.addSearchAddrAddYear({ delete: false }, params.regAddr, params.year, params.keyWords, params.lever, params.tabName);
    if (params.status || params.status === 0) query.status = params.status;
    if (params.type) query.type = +params.type;
    if (params.modifiedStatus) query.modifiedStatus = +params.modifiedStatus;
    return await this.ctx.model.Warning.count(query);
  }

  // 预警列表 统计预警总数
  async count4(params) {
    const query = await this.addSearchAddrAddYear({ delete: false }, params.regAddr, params.year, params.keyWords, params.lever, params.tabName);
    // const addr = (params.regAddr && params.regAddr.length) ? params.regAddr[params.regAddr.length - 1].name : '';
    // if (addr) {
    //   query.$or = [ // 此处如果是筛选区域就不筛选关键字了
    //     { 'company.workAddress': { $elemMatch: { districts: { $in: [ addr ] } } } },
    //     { 'company.districtRegAdd': { $in: [ addr ] } },
    //   ];
    //   const result = await this.ctx.model.Warning.aggregate([
    //     { $lookup: { from: 'adminorgs', localField: 'companyId', foreignField: '_id', as: 'company' } },
    //     { $match: query },
    //   ]);
    //   return result;
    // }
    return await this.ctx.model.Warning.find(query, { status: 1, companyId: 1, ctime: 1 });
  }

  // 统计分页用的总数
  async count2(params) {
    // const reg = new RegExp(params.keyWords.trim(), 'i'); // 不区分大小写
    let query = {
      delete: false,
      // supervisionId: { $elemMatch: { $eq: params.supervisionId } },
      // $or: [
      //   { companyName: { $regex: reg } },
      //   { content: { $regex: reg } },
      // ],
    };
    query = await this.addSearchAddrAddYear(query, params.regAddr, params.year, params.keyWords, params.lever, params.tabName);

    // if (params.status && params.status.length) query.status = { $in: params.status };
    if (params.status || params.status === 0) query.status = params.status;
    if (params.type) query.type = +params.type;
    if (params.modifiedStatus) query.modifiedStatus = +params.modifiedStatus;

    // const addr = (params.regAddr && params.regAddr.length) ? params.regAddr[params.regAddr.length - 1].name : '';
    // if (addr) {
    //   query.$or = [ // 此处如果是筛选区域就不筛选关键字了
    //     { 'company.workAddress': { $elemMatch: { districts: { $in: [ addr ] } } } },
    //     { 'company.districtRegAdd': { $in: [ addr ] } },
    //   ];
    //   const result = await this.ctx.model.Warning.aggregate([
    //     { $lookup: { from: 'adminorgs', localField: 'companyId', foreignField: '_id', as: 'company' } },
    //     { $match: query },
    //   ]);
    //   return result.length;
    // }
    return await this.ctx.model.Warning.count(query);
  }

  // 监管端导航栏中的预警总数统计
  async count3() {
    const { ctx, config } = this;
    let query = {
      delete: false,
      lever: { $ne: 4 },
      status: { $in: [ 0, 1, 2, 4 ] },
    };
    if (config.branch === 'hz') {
      const curYear = new Date().getFullYear();
      query.ctime = { $gte: new Date(curYear, 0, 1) };
    }
    query = await this.addSearchAddrAddYear(query);
    return await ctx.model.Warning.count(query);
  }

  // 创建一条预警
  async add(params) {
    // return this.ctx.model.Warning.create(params);
    return this.ctx.service.db.create('Warning', params);
  }

  // 创建一条视频预约
  async createReserve(params) {
    await this.ctx.service.db.updateOne('Adminorg', { _id: params.EnterpriseId }, { $set: { isReserve: true } }, { upsert: true });
    const res = await this.ctx.model.Reserve.create(params);
    return res;
  }

  // 通过监管端id获取座机电话
  async findLandlineBySupervisionId(_id) {
    return this.ctx.model.SuperUser.findOne({ _id, state: 1 }, { password: 0 });
  }

  // 发送预警督促信息
  async sendAlertMessage(companyName, content, surpervision, phoneNum, surpervisionPhone) {
    try {
      const TemplateParam = {
        cname: companyName,
        content,
        sname: surpervision,
        phone: surpervisionPhone,
      };
      // 发送短消息
      const { data } = await this.ctx.curl(`${this.config.iServiceHost}/api/sendSMS`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data: {
          templateCodeName: 'alertRemind',
          TemplateParam: JSON.stringify(TemplateParam),
          PhoneNumbers: phoneNum,
        },
      });
      if (data.Message === 'OK') return true;
      this.ctx.auditLog(`预警短信发送失败 - ${phoneNum}`, JSON.stringify(data), 'error');
    } catch (err) {
      this.ctx.helper.renderFail(this.ctx, {
        message: err,
      });
    }
  }
  // 发送会议信息
  async sendMeetingMessage(companyName, meetingId, time, surpervision, superName, phoneNum, surpervisionPhone) {
    try {
      const TemplateParam = {
        cname: companyName,
        meetingId,
        time,
        superOrg: surpervision,
        superName,
        phone: surpervisionPhone,
      };
      // 发送短消息
      const { data } = await this.ctx.curl(`${this.config.iServiceHost}/api/sendSMS`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data: {
          templateCodeName: 'meetingRemind',
          TemplateParam: JSON.stringify(TemplateParam),
          PhoneNumbers: phoneNum,
        },
      });
      console.log('视频预约短信>>>>>>>>>>>>>>>>>>>>>>', data);
      if (data.data.Message === 'OK') return true;
      this.ctx.auditLog(`视频预约短信发送失败 - ${phoneNum}`, JSON.stringify(data), 'error');
    } catch (err) {
      this.ctx.helper.renderFail(this.ctx, {
        message: err,
      });
    }
  }

  // cancelMeetingMsg
  async cancelMeetingMsg(companyName, time, surpervision, superName, phoneNum, surpervisionPhone) {
    try {
      const TemplateParam = {
        cname: companyName,
        time,
        superOrg: surpervision,
        superName,
        phone: surpervisionPhone,
      };
      // 发送短消息
      const data = await this.ctx.curl(`${this.config.iServiceHost}/api/sendSMS`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data: {
          templateCodeName: 'cancelMeeting',
          TemplateParam: JSON.stringify(TemplateParam),
          PhoneNumbers: phoneNum,
        },
      });
      console.log('取消会议>>>>>>>>>>>>>>>>>>>>', data);
      if (data.data.data.Message === 'OK') return true;
      this.ctx.auditLog(`视频预约短信发送失败 - ${phoneNum}`, JSON.stringify(data), 'error');
    } catch (err) {
      this.ctx.helper.renderFail(this.ctx, {
        message: err,
      });
    }
  }
  // 更新预警
  async update(_id, query = {}) {
    if (query.supervisionId) { // 下放、上报预警
      const supervisionId = query.supervisionId;
      const curUserName = await this.findSuperName();
      const updateUserName = await this.findSuperName(supervisionId);
      const handleName = query.reportWarning ? '上报' : '下放';
      query = {
        $push: {
          process: {
            time: Date.now(),
            thing: `${handleName}预警 (${curUserName.split('-')[0].trim()})`,
            remark: `${curUserName}将该条预警${handleName}给了${updateUserName}`,
          },
          supervisionId,
        },
      };
    }
    // return await this.ctx.model.Warning.update(
    //   { _id },
    //   query,
    //   { new: true, upsert: true }
    // );
    return await this.ctx.service.db.updateOne('Warning', { _id },
      query,
      { new: true, upsert: true });
  }

  // 回收站预警列表 带分页
  async findDeleted(params) {
    const query = await this.addSearchAddrAddYear({ delete: true }, '', '', params.keyWords);
    params.limit = +params.limit || 10;
    if (!params.curPage) params.curPage = 1;
    return await this.ctx.model.Warning.aggregate([
      { $match: query },
      { $lookup: { from: 'adminorgs', localField: 'companyId', foreignField: '_id', as: 'company' } },
      { $sort: { ctime: -1 } },
      { $skip: params.limit * (params.curPage - 1) },
      { $limit: params.limit },
    ]);
  }
  // 预警回收站 总数
  async count5(params) {
    const query = await this.addSearchAddrAddYear({ delete: true }, '', '', params.keyWords);
    return await this.ctx.model.Warning.count(query);
  }
  // 清空回收站
  async clearRecycleBin() {
    const superUserInfo = this.ctx.session.superUserInfo;
    if (superUserInfo.regAdd && superUserInfo.regAdd[0] === '中国') {
      return '中国范围内不可执行清空预警回收站操作';
    }
    const query = await this.addSearchAddrAddYear({
      delete: true,
    });
    // return await this.ctx.model.Warning.deleteMany(query);
    return await this.ctx.service.db.deleteMany('Warning', query);
  }

  async findOne(query) {
    return await this.ctx.model.Warning.findOne(query);
  }

  isTwoDimensionalArray(arr) {
    // 先检查是否是数组
    if (!Array.isArray(arr)) return false;
    // 空数组视为二维数组（边界情况）
    if (arr.length === 0) return true;
    // 检查每个元素都是数组
    return arr.every(item => Array.isArray(item));
  }

  buildPrefixMatchAggregation(prefixes) {
    const orConditions = prefixes.map(prefix => ({
      $eq: [
        { $slice: [ '$$item', prefix.length ] },
        prefix,
      ],
    }));

    return {
      $anyElementTrue: {
        $map: {
          input: '$workAddress',
          as: 'item',
          in: {
            $or: orConditions,
          },
        },
      },
    };
  }

  // 处理预警的查询地址, searchAddrs里面嵌套的是对象, 以及查询年份和关键字
  async addSearchAddrAddYear(query = {}, searchAddrs = [], searchYear, keyWords, lever = '', tabName = '') {
    const superUserInfo = this.ctx.session.superUserInfo;
    const { crossRegionManage } = this.ctx.app.config;
    const regAdd = superUserInfo.regAdd,
      supervisionId = superUserInfo._id;
    let searchAddr = (searchAddrs && searchAddrs.length) ? searchAddrs[searchAddrs.length - 1] : '';
    if (typeof searchAddr === 'object' && searchAddr !== null) searchAddr = searchAddr.name || '';
    if (regAdd.length === 1) {
      if (regAdd[0] === '中国') { // 查全国
        if (searchAddr) query.workAddress = { $elemMatch: { $elemMatch: { $all: [ searchAddr ] } } };
      } else { // 省级单位
        if (![ 'fj', 'xjbt' ].includes(this.config.branch)) query.supervisionId = { $elemMatch: { $eq: supervisionId } };
        if (searchAddr) {
          query.workAddress = { $elemMatch: { $elemMatch: { $all: [ searchAddr ] } } };
        } else if ([ 'fj', 'xjbt' ].includes(this.config.branch)) { // 福建省
          query.workAddress = { $elemMatch: { $elemMatch: { $all: regAdd } } };
        }
        console.log(22222, JSON.stringify(query.workAddress));
      }
    } else if (!crossRegionManage) { // 市级及以下单位
      if (searchAddr) {
        query.workAddress = { $elemMatch: { $elemMatch: { $all: [ searchAddr ] } } };
      } else {
        query.$or = [
          { supervisionId: { $elemMatch: { $eq: supervisionId } } },
          { workAddress: { $elemMatch: { $elemMatch: { $all: [ regAdd[regAdd.length - 1] ] } } } },
        ];
      }
    } else if (crossRegionManage && this.isTwoDimensionalArray(regAdd)) {
      console.log('处理跨区域情况', regAdd);
      query.$expr = this.buildPrefixMatchAggregation(regAdd);
    }
    if (searchYear) {
      const year = new Date(searchYear + '').getFullYear();
      query.ctime = {
        $lte: new Date(+year + 1, 0, 1),
        $gt: new Date(+year, 0, 1),
      };
      if (query.ctime && (!query.ctime.$lte || !query.ctime.$gt)) {
        this.ctx.auditLog('预警列表查询条件异常', `${JSON.stringify(query)} 。`, 'error');
        delete query.ctime;
      }
    }
    if (typeof (keyWords) === 'string') {
      if (keyWords && keyWords.trim()) {
        const reg = new RegExp(keyWords.trim(), 'i'); // 不区分大小写
        if (!query.$and) query.$and = [];
        query.$and.push({
          $or: [
            { companyName: { $regex: reg } },
            { content: { $regex: reg } },
            { _id: { $regex: reg } },
          ],
        });
      }
    } else {
      if (keyWords && keyWords.length !== 0) {
        query.companyName = { $in: keyWords };
      }
    }
    // 区分预警和超标提示
    if (tabName === '2') {
      query.lever = 4;
    } else if (tabName === '1') {
      if (lever && lever !== 'all') {
        query.lever = +lever; // 选了预警等级
      } else {
        query.lever = { $ne: 4 };
      }
    } else {
      if (lever && lever !== 'all') query.lever = +lever;
    }
    if (query.$and && query.$and.length === 0) delete query.$and;
    return query;
  }
  // 退回自动解除的预警
  async retreat(_id, remark = '') {
    const updateUserName = await this.findSuperName();
    return await this.ctx.model.Warning.update(
      { _id },
      {
        status: 1, // 变成待整改
        $push: {
          process: {
            time: Date.now(),
            thing: `退回整改 (${updateUserName})`,
            remark,
          },
        },
      },
      {
        upsert: true,
        new: true,
      }
    );
  }
  // 发送预警退回信息
  async sendRetreatMessage(warningId, companyName, phoneNum) {
    try {
      const flag = /^1[3-9][0-9]{9}$/.test(phoneNum); // 手机号校验
      if (!flag) {
        await this.addSMSrecord(
          warningId,
          '发送失败：号码格式不对',
          phoneNum
        );
        return;
      }
      const oldWaring = await this.ctx.model.Warning.findOne({ _id: warningId }, { rectifyTime: 1, process: 1 });
      let rectifyTime = oldWaring.rectifyTime || oldWaring.process[oldWaring.process.length - 1].time; // 整改时间
      rectifyTime = rectifyTime ? moment(rectifyTime).format('YYYY年MM月DD日') : '';
      // 发送短消息
      const { data } = await this.ctx.curl(`${this.config.iServiceHost}/api/sendSMS`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data: {
          templateCodeName: 'retreatWarning',
          PhoneNumbers: phoneNum,
          TemplateParam: JSON.stringify({
            cname: companyName,
            date: rectifyTime, // 整改时间
          }),
        },
      });
      // 短信记录
      const sendContent = `${companyName}，您单位于${rectifyTime}提交的预警解除申请已经被驳回，请登录杭州企卫通服务平台https://hzzyws.cn 查看原因并重新提交整改报告。`;
      await this.addSMSrecord(
        warningId,
        data.data && data.data.Code === 'OK' ? '短信发送成功' : '短信发送失败：' + data.message || data.data.Message || '',
        phoneNum,
        sendContent
      );
      if (data.data && data.data.Message === 'OK') return true;
      this.ctx.auditLog(`预警退回短信发送失败 - ${phoneNum}`, JSON.stringify(data), 'error');
    } catch (err) {
      this.ctx.helper.renderFail(this.ctx, {
        message: err,
      });
    }
  }
  // 添加预警短信发送记录
  async addSMSrecord(_id, sendResult, sendToPhone = '', content = '') {
    await this.ctx.model.Warning.updateOne(
      { _id },
      {
        $push: {
          SMSrecord: { content, sendToPhone, sendResult, time: new Date() },
        },
      }
    );
  }

  // 预警统计获取列表
  async statisticsList(params) {
    const query = await this.addSearchAddrAddYear({ delete: false }, params.regAddr, params.year, params.keyWords, params.lever, params.tabName);
    return await this.ctx.model.Warning.find(query, { status: 1, ctime: 1 });
  }

  // 获取杭州大屏预警统计数据
  async getStatistics(year, regAddr) { // regAddr 中文 或者对象
    const { ctx } = this;
    const params = {
      year,
      regAddr,
      tabName: '1',
    };

    // 统计预警总数
    const list = await this.statisticsList({
      ...params,
      status: null,
      type: '',
    });

    // 预警状态统计
    const statusStatistics = {
      toDoNum: 0, // 待处理 0
      processed: 0, // 已处理预警 1
      toBeRectified: 0, // 待整改预警 2
      over: 0, // 已解除预警 3
      reject: 0, // 已驳回 4
      revoke: 0, // 已撤销 5
    };

    // 按照月份统计预警总数以及已解除的预警（不包含已撤销的）
    const monthStatistics = Array.from({ length: 12 }, () => ({ total: 0, over: 0 }));

    list.forEach(ele => {
      if (ele.status in statusStatistics) {
        statusStatistics[ele.status === 0 ? 'toDoNum' : ele.status === 1 ? 'processed' : ele.status === 2 ? 'toBeRectified' : ele.status === 3 ? 'over' : ele.status === 4 ? 'reject' : 'revoke'] += 1;
      } else {
        ctx.auditLog('预警status错误', `${ele._id}的预警状态为${ele.status}。`, 'error');
      }

      const curMonth = new Date(ele.ctime).getMonth();
      if (ele.status !== 5) {
        monthStatistics[curMonth].total += 1;
        if (ele.status === 3) monthStatistics[curMonth].over += 1;
      }
    });

    // 预警总数(不包含已撤销的)
    const total = statusStatistics.processed + statusStatistics.toBeRectified + statusStatistics.over + statusStatistics.reject + statusStatistics.toDoNum;
    // 预警分类统计
    const classificationStatistics = await this.getClassificationStatistics(params);

    // 预警等级统计（不包含已撤销的）
    const leverStatistics = await this.getLeverStatistics(params);

    // 按照辖区统计
    const { areaStatistics, curSearchArea } = await this.getAreaStatistics(year, regAddr, ctx);

    // 当前管辖区域的统计数据
    const curAreaStatistics = await this.getCurAreaStatistics(year, curSearchArea);

    // 返回数据
    return {
      total,
      classificationStatistics,
      statusStatistics,
      leverStatistics,
      areaStatistics,
      monthStatistics,
      curSearchArea,
      curAreaStatistics,
    };
  }

  // 预警分类统计
  async getClassificationStatistics(params) {
    return {
      diagnosis: await this.count({ ...params, type: 3, status: { $ne: 5 } }), // 诊断
      physical: await this.count({ ...params, type: 2, status: { $ne: 5 } }), // 体检
      detect: await this.count({ ...params, type: 1, status: { $ne: 5 } }), // 检测
      diagnosisOver: await this.count({ ...params, type: 3, status: 3 }),
      physicalOver: await this.count({ ...params, type: 2, status: 3 }),
      detectOver: await this.count({ ...params, type: 1, status: 3 }),
    };
  }

  // 预警等级统计（不包含已撤销的）
  async getLeverStatistics(params) {
    return {
      lever1: await this.count({ ...params, lever: 1, status: { $ne: 5 } }),
      lever2: await this.count({ ...params, lever: 2, status: { $ne: 5 } }),
      lever3: await this.count({ ...params, lever: 3, status: { $ne: 5 } }),
      lever1Over: await this.count({ ...params, lever: 1, status: 3 }),
      lever2Over: await this.count({ ...params, lever: 2, status: 3 }),
      lever3Over: await this.count({ ...params, lever: 3, status: 3 }),
    };
  }

  // 当前管辖区域的统计数据
  async getCurAreaStatistics(year, curSearchArea) {
    return {
      areaName: curSearchArea,
      total: await this.count({ year, regAddr: [ curSearchArea ], tabName: '1', status: { $ne: 5 } }),
      over: await this.count({ year, regAddr: [ curSearchArea ], tabName: '1', status: 3 }), // 已解除
      revoke: await this.count({ year, regAddr: [ curSearchArea ], tabName: '1', status: 5 }), // 已撤销
      processed: await this.count({ year, regAddr: [ curSearchArea ], tabName: '1', status: 1 }), // 已处理/已退回
      toDoNum: await this.count({ year, regAddr: [ curSearchArea ], tabName: '1', status: 0 }), // 待处理
    };
  }

  // 按照辖区统计
  async getAreaStatistics(year, regAddr, ctx) {
    let areaStatistics = [];
    let jurisdiction = [];
    let curSearchArea = '';

    if (regAddr && regAddr.length) {
      let searchAddr = regAddr[regAddr.length - 1];
      if (typeof searchAddr === 'object') searchAddr = searchAddr.name || '';
      const district = await ctx.model.District.findOne({ name: searchAddr }, { area_code: 1 });
      jurisdiction = district ? await ctx.model.District.find({ parent_code: district.area_code }, { name: 1 }) : [];
      curSearchArea = searchAddr;
    } else {
      const superUser = await ctx.model.SuperUser.findOne({ _id: ctx.session.superUserInfo._id }, { area_code: 1 });
      const regAddCode = superUser ? superUser.area_code : '';
      jurisdiction = await ctx.model.District.find({ parent_code: regAddCode }, { name: 1 });
      const regAdd = ctx.session.superUserInfo.regAdd;
      curSearchArea = regAdd ? regAdd[regAdd.length - 1] : '';
    }

    const areaPromises = jurisdiction.map(async district => {
      const curDistrict = [ district.name ];
      return {
        areaName: district.name,
        total: await this.count({ year, regAddr: curDistrict, tabName: '1', status: { $ne: 5 } }),
        over: await this.count({ year, regAddr: curDistrict, tabName: '1', status: 3 }), // 已解除
        revoke: await this.count({ year, regAddr: curDistrict, tabName: '1', status: 5 }), // 已撤销
        processed: await this.count({ year, regAddr: curDistrict, tabName: '1', status: 1 }), // 已处理/已退回
        toDoNum: await this.count({ year, regAddr: curDistrict, tabName: '1', status: 0 }), // 待处理
      };
    });

    areaStatistics = await Promise.all(areaPromises);
    areaStatistics.sort((a, b) => a.total - b.total);

    return { areaStatistics, curSearchArea };
  }

  // 获取预警权限配置
  async getWarningConfig(level) {
    const { ctx } = this;
    let warningConfig = await ctx.helper.getRedis('warningConfig') || [];
    if (warningConfig[0] && typeof warningConfig[0] === 'string') {
      warningConfig = warningConfig.map(item => JSON.parse(item));
    }
    if (level) {
      level = +level;
      if ([ 1, 2, 3, 4 ].includes(level)) {
        return warningConfig.find(item => item.level === level);
      }
      throw new Error('level参数错误');
    }
    return warningConfig.sort((a, b) => a.level - b.level);
  }


}

module.exports = WarningService;
