/**
 * @file 跨区域化学中毒救治协作展示模块中间件
 * @description 处理跨区域化学中毒救治协作展示模块相关路由请求
 */

const crossRegionalChemicalDataController = require('../controller/manage/crossRegionalChemicalData');

module.exports = (options, app) => {
  return async function crossRegionalChemicalDataRouter(ctx, next) {
    const pluginConfig = app.config.jk_crossRegionalChemicalData;

    if (ctx.request.url.startsWith('/manage/crossRegionalChemicalData')) {
      await app.initPluginRouter(ctx, pluginConfig, crossRegionalChemicalDataController);
    }
    await next();
  };
};
