/**
 * @service CrossRegionalChemicalData 服务
 */
const Service = require('egg').Service;

class CrossRegionalChemicalDataService extends Service {
  // -----远程诊疗接口-----

  // 获取毒物信息数据库统计
  async getToxicDbList() {
    const { ctx } = this;
    try {
      const res = await ctx.curl(
        `${this.config.iService2Host}/hxzd/toxicDb`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      return res.data;
    } catch (err) {
      ctx.logger.error('获取数据失败', err);
      throw err;
    }
  }

  // 获取中毒病例数据库统计情况
  async getCaseDbList() {
    const { ctx } = this;
    try {
      const res = await ctx.curl(
        `${this.config.iService2Host}/hxzd/caseDb`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      return res.data;
    } catch (err) {
      ctx.logger.error('获取数据失败', err);
      throw err;
    }
  }

  // 获取远程会诊统计数情况
  async getApplyList() {
    const { ctx } = this;
    try {
      const res = await ctx.curl(
        `${this.config.iService2Host}/hxzd/applyList`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      console.log(res, '远程会诊统计数情况');
      return res.data;
    } catch (err) {
      ctx.logger.error('获取数据失败', err);
      throw err;
    }
  }

  // 获取远程门诊统计数情况
  async getConsultList() {
    const { ctx } = this;
    try {
      const res = await ctx.curl(
        `${this.config.iService2Host}/hxzd/consultList`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      return res.data;
    } catch (err) {
      ctx.logger.error('获取数据失败', err);
      throw err;
    }
  }

  // 现场救治指导统计分析情况
  async getRemoteLiveList() {
    const { ctx } = this;
    try {
      const res = await ctx.curl(
        `${this.config.iService2Host}/hxzd/remoteLive`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      return res.data;
    } catch (err) {
      ctx.logger.error('获取数据失败', err);
      throw err;
    }
  }

  // 获取化学中毒患者管理情况
  async getPoisonPatientManageList() {
    const { ctx } = this;
    try {
      const res = await ctx.curl(
        `${this.config.iService2Host}/hxzd/poisonPatientManage`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      return res.data;
    } catch (err) {
      ctx.logger.error('获取数据失败', err);
      throw err;
    }
  }

  // 获取中毒病例远程监测情况
  async getRemotePoisoingList() {
    const { ctx } = this;
    try {
      const res = await ctx.curl(
        `${this.config.iService2Host}/hxzd/remotePoisoing`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      return res.data;
    } catch (err) {
      ctx.logger.error('获取数据失败', err);
      throw err;
    }
  }

  // 获取化学中毒救治专家库管理情况
  async getExpertManageList() {
    const { ctx } = this;
    try {
      const res = await ctx.curl(
        `${this.config.iService2Host}/hxzd/expertManage`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      return res.data;
    } catch (err) {
      ctx.logger.error('获取数据失败', err);
      throw err;
    }
  }

  // 获取毒物检测数据
  async getSampleCollectionList(query) {
    const { ctx } = this;
    const { startTime, endTime } = query; // 解构获取时间参数
    try {
      // 构建请求参数（包含时间范围）
      const requestParams = {};
      if (startTime) requestParams.startTime = startTime;
      if (endTime) requestParams.endTime = endTime;
      const res = await ctx.curl(
        `${this.config.iService2Host}/hxzd/sampleCollection`,
        {
          method: 'GET',
          dataType: 'json',
          data: requestParams, // 将时间参数传递给第三方接口
        }
      );
      // 检查第三方接口返回状态
      if (res.data && res.data.code !== 200) {
        throw new Error(res.data.message || '第三方接口返回异常');
      }
      return res.data;
    } catch (err) {
      ctx.logger.error('获取毒物检测数据失败', err);
      throw err;
    }
  }

  // 获取毒物检测数据库样本转运情况
  // async getSampleTransportList() {
  //   const { ctx } = this;
  //   try {
  //     const res = await ctx.curl(
  //       `${this.config.iService2Host}/hxzd/sampleTransport`,
  //       {
  //         method: 'GET',
  //         dataType: 'json', // 返回的数据类型
  //       }
  //     );
  //     return res.data;
  //   } catch (err) {
  //     ctx.logger.error('获取数据失败', err);
  //     throw err;
  //   }
  // }

  // 获取生产技术能力和交通转运能力的储备情况
  async getProductionPlanList() {
    const { ctx } = this;
    try {
      const res = await ctx.curl(
        `${this.config.iService2Host}/hxzd/productionPlan`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      return res.data;
    } catch (err) {
      ctx.logger.error('获取数据失败', err);
      throw err;
    }
  }

  // 获取化学中毒救治知识培训评估情况
  async getTrainingEvaluationList() {
    const { ctx } = this;
    try {
      const res = await ctx.curl(
        `${this.config.iService2Host}/hxzd/trainingEvaluation`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      return res.data;
    } catch (err) {
      ctx.logger.error('获取数据失败', err);
      throw err;
    }
  }

  // 获取化学中毒救治相关标准及政策法规、相关专著拟定与管理情况
  async getScientificResearchList() {
    const { ctx } = this;
    try {
      const res = await ctx.curl(
        `${this.config.iService2Host}/hxzd/policyManagement`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      return res.data;
    } catch (err) {
      ctx.logger.error('获取数据失败', err);
      throw err;
    }
  }

  // 获取化学灾害医学救援和化学中毒救治学术会议举办情况
  async getAcademicConferencesList() {
    const { ctx } = this;
    try {
      const res = await ctx.curl(
        `${this.config.iService2Host}/hxzd/academicConferences`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      return res.data;
    } catch (err) {
      ctx.logger.error('获取数据失败', err);
      throw err;
    }
  }

  // 获取化学灾害医学应急演练预案情况
  async getContingencyManagementList() {
    const { ctx } = this;
    try {
      const res = await ctx.curl(
        `${this.config.iService2Host}/hxzd/contingencyManagement`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      return res.data;
    } catch (err) {
      ctx.logger.error('获取数据失败', err);
      throw err;
    }
  }

  // 获取学灾害医学应急救援队伍演练组织情况
  async getOrganiseDrillsList() {
    const { ctx } = this;
    try {
      const res = await ctx.curl(
        `${this.config.iService2Host}/hxzd/organiseDrills`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
        }
      );
      return res.data;
    } catch (err) {
      ctx.logger.error('获取数据失败', err);
      throw err;
    }
  }
}

module.exports = CrossRegionalChemicalDataService;
