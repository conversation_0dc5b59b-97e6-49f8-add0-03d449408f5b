/**
 * @controller CrossRegionalChemicalData 管理
 */

const CrossRegionalChemicalDataController = {
  // -----远程诊疗接口-----

  // 获取毒物信息数据库统计
  async getToxicDb(ctx) {
    try {
      const result = await ctx.service.crossRegionalChemicalData.getToxicDbList();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 获取中毒病例数据库统计情况
  async getCaseDb(ctx) {
    try {
      const result = await ctx.service.crossRegionalChemicalData.getCaseDbList();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 获取远程会诊统计数情况
  async getApplyList(ctx) {
    try {
      const result = await ctx.service.crossRegionalChemicalData.getApplyList();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 获取远程门诊统计数情况
  async getConsultList(ctx) {
    try {
      const result = await ctx.service.crossRegionalChemicalData.getConsultList();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 现场救治指导统计分析情况
  async getRemoteLive(ctx) {
    try {
      const result = await ctx.service.crossRegionalChemicalData.getRemoteLiveList();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 获取化学中毒患者管理情况
  async getPoisonPatientManage(ctx) {
    try {
      const result = await ctx.service.crossRegionalChemicalData.getPoisonPatientManageList();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 获取中毒病例远程监测情况
  async getRemotePoisoing(ctx) {
    try {
      const result = await ctx.service.crossRegionalChemicalData.getRemotePoisoingList();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 获取化学中毒救治专家库管理情况
  async getExpertManage(ctx) {
    try {
      const result = await ctx.service.crossRegionalChemicalData.getExpertManageList();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 获取毒物检测数据统计情况
  async getSampleCollection(ctx) {
    try {
      // 从请求参数中获取开始时间和结束时间
      const { startTime, endTime } = ctx.query;

      // 调用服务层方法并传递时间参数
      const result = await ctx.service.crossRegionalChemicalData.getSampleCollectionList({
        startTime,
        endTime,
      });

      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 获取生产技术能力和交通转运能力的储备情况
  async getProductionPlan(ctx) {
    try {
      const result = await ctx.service.crossRegionalChemicalData.getProductionPlanList();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 化学中毒救治知识培训评估情况
  async getTrainingEvaluation(ctx) {
    try {
      const result = await ctx.service.crossRegionalChemicalData.getTrainingEvaluationList();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 获取化学中毒救治相关标准及政策法规、相关专著拟定与管理情况
  async getScientificResearch(ctx) {
    try {
      const result = await ctx.service.crossRegionalChemicalData.getScientificResearchList();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 化学灾害医学救援和化学中毒救治学术会议举办情况
  async getAcademicConferences(ctx) {
    try {
      const result = await ctx.service.crossRegionalChemicalData.getAcademicConferencesList();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 化学灾害医学应急演练预案情况
  async getContingencyManagement(ctx) {
    try {
      const result = await ctx.service.crossRegionalChemicalData.getContingencyManagementList();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 化学灾害医学应急救援队伍演练组织情况
  async getOrganiseDrills(ctx) {
    try {
      const result = await ctx.service.crossRegionalChemicalData.getOrganiseDrillsList();
      ctx.helper.renderSuccess(ctx, {
        data: result,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 获取毒物检测数据库样本转运情况
  // async getSampleTransport(ctx) {
  //   try {
  //     const result = await ctx.service.crossRegionalChemicalData.getSampleTransportList();
  //     ctx.helper.renderSuccess(ctx, {
  //       data: result,
  //     });
  //   } catch (err) {
  //     ctx.helper.renderFail(ctx, {
  //       message: err.message,
  //     });
  //   }
  // },
};

module.exports = CrossRegionalChemicalDataController;
