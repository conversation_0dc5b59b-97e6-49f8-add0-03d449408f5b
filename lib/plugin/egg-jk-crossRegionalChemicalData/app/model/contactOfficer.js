/**
 * @file ContactOfficer 模型
 * @description 联络员管理实体
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const path = require('path');
  const shortid = require('shortid');
  const regionQueryTransformPlugin = require(path.join(process.cwd(), 'app/utils/regionQueryTransformPlugin'));
  const { crossRegionManage = false } = app.config;

  const ContactOfficerSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: {
      type: String,
      required: [ true, '姓名不能为空' ],
    },
    gender: {
      type: String,
      required: [ true, '性别不能为空' ],
      enum: [ 'male', 'female' ],
    },
    age: {
      type: Number,
      required: [ true, '年龄不能为空' ],
      min: [ 0, '年龄不能小于0' ],
      max: [ 150, '年龄不能大于150' ],
    },
    phone: {
      type: String,
      required: [ true, '联系电话不能为空' ],
      match: [ /^(\d{3,4}-?)?\d{7,8}$/, '联系电话格式不正确' ],
    },
    mobile: {
      type: String,
      required: [ true, '手机号码不能为空' ],
      match: [ /^1[3-9]\d{9}$/, '手机号码格式不正确' ],
    },
    regAdd: {
      type: Array,
      required: [ true, '所在区域不能为空' ],
      default: [],
    },
    area_code: {
      type: String,
      required: [ true, '所在区域不能为空' ],
    },
    address: {
      type: String,
      required: [ true, '住址不能为空' ],
    },
    email: {
      type: String,
      required: [ true, '电子邮箱不能为空' ],
      match: [ /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/, '电子邮箱格式不正确' ],
    },
    organization: {
      type: String,
      required: [ true, '所属机构不能为空' ],
      enum: [ 'healthDepartment', 'supervisionInstitution' ], // 卫生健康行政部门、监督机构
    },
    state: {
      type: String,
      default: '1', // 1正常，0删除
      enum: [ '0', '1' ],
    },
  }, {
    timestamps: true,
    versionKey: false,
    minimize: false,
    strict: true,
  });

  // 创建索引
  ContactOfficerSchema.index({ name: 1 });
  // ContactOfficerSchema.index({ mobile: 1 }, { unique: true });
  // ContactOfficerSchema.index({ email: 1 }, { unique: true });
  crossRegionManage && ContactOfficerSchema.plugin(regionQueryTransformPlugin, {
    _regionFields: 'regAdd',
  });


  return mongoose.model('ContactOfficer', ContactOfficerSchema);
};
