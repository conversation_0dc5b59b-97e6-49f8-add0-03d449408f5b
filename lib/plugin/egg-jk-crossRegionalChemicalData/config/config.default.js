exports.jk_crossRegionalChemicalData = {
  alias: 'crossRegionalChemicalData', // 插件目录
  pkgName: 'egg-jk-crossRegionalChemicalData', // 插件包名
  enName: 'jk_crossRegionalChemicalData', // 插件名
  name: '跨区域化学中毒救治协作展示 模块', // 插件名称
  description: '统计数据', // 插件描述
  adminApi: [{
    url: 'crossRegionalChemicalData/getToxicDb',
    method: 'get',
    controllerName: 'getToxicDb',
    details: '获取毒物信息数据库统计',
  }, {
    url: 'crossRegionalChemicalData/getCaseDb',
    method: 'get',
    controllerName: 'getCaseDb',
    details: '获取中毒病例数据库统计',
  }, {
    url: 'crossRegionalChemicalData/getApplyList',
    method: 'get',
    controllerName: 'getApplyList',
    details: '获取远程会诊统计数情况',
  }, {
    url: 'crossRegionalChemicalData/getConsultList',
    method: 'get',
    controllerName: 'getConsultList',
    details: '获取远程门诊统计数情况',
  }, {
    url: 'crossRegionalChemicalData/getRemoteLive',
    method: 'get',
    controllerName: 'getRemoteLive',
    details: '获取现场救治指导统计分析情况',
  }, {
    url: 'crossRegionalChemicalData/getPoisonPatientManage',
    method: 'get',
    controllerName: 'getPoisonPatientManage',
    details: '获取化学中毒患者管理统计',
  }, {
    url: 'crossRegionalChemicalData/getRemotePoisoing',
    method: 'get',
    controllerName: 'getRemotePoisoing',
    details: '获取中毒病例远程监测统计',
  }, {
    url: 'crossRegionalChemicalData/getExpertManage',
    method: 'get',
    controllerName: 'getExpertManage',
    details: '获取化学中毒救治专家库管理统计',
  }, {
    url: 'crossRegionalChemicalData/getSampleCollection',
    method: 'get',
    controllerName: 'getSampleCollection',
    details: '获取毒物检测管理统计',
  }, {
    url: 'crossRegionalChemicalData/getProductionPlan',
    method: 'get',
    controllerName: 'getProductionPlan',
    details: '获取生产技术能力和交通转运能力的储备情况',
  }, {
    url: 'crossRegionalChemicalData/getTrainingEvaluation',
    method: 'get',
    controllerName: 'getTrainingEvaluation',
    details: '获取化学中毒救治知识培训评估情况',
  }, {
    url: 'crossRegionalChemicalData/getScientificResearch',
    method: 'get',
    controllerName: 'getScientificResearch',
    details: '获取化学中毒救治相关标准及政策法规、相关专著拟定与管理情况',
  }, {
    url: 'crossRegionalChemicalData/getAcademicConferences',
    method: 'get',
    controllerName: 'getAcademicConferences',
    details: '获取化学灾害医学救援和化学中毒救治学术会议举办情况',
  }, {
    url: 'crossRegionalChemicalData/getContingencyManagement',
    method: 'get',
    controllerName: 'getContingencyManagement',
    details: '获取化学灾害医学应急演练预案情况',
  }, {
    url: 'crossRegionalChemicalData/getOrganiseDrills',
    method: 'get',
    controllerName: 'getOrganiseDrills',
    details: '获取化学灾害医学应急救援队伍演练组织情况',
  },
  // {
  //   url: 'crossRegionalChemicalData/getSampleTransport',
  //   method: 'get',
  //   controllerName: 'getSampleTransport',
  //   details: '获取毒物检测数据库样本转运情况',
  // }
  ],
  pluginsConfig: ` 
    exports.jk_crossRegionalChemicalData = {\n
        enable: true,\n        package: 'egg-jk-crossRegionalChemicalData',
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
    crossRegionalChemicalDataRouter:{\n
        match: [ctx => ctx.path.startsWith('/manage/crossRegionalChemicalData')],\n
    },\n
    `, // 插入到 config.default.js 中的配置
};
