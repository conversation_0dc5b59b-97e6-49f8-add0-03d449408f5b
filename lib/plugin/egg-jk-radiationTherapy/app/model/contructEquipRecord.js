// 收费标准设置
module.exports = app => {
    const mongoose = app.mongoose;
    const shortid = require('shortid');
    const Schema = mongoose.Schema;

    const contructEquipRecordSchema = new Schema(
        {
            _id: {
                type: String,
                default: shortid.generate,
            },
            projectName:{
                type:String     // 项目名称
            },
            cname:{
                type:String    // 申请单位
            },
            code:{
                type:String    // 社会统一信用代码
            },
            projectType:{
                type:Number    // 项目性质   1新建  2改建  3扩建  4技术改造  5 技术引进
            },
            projectCategory:{
                type:String     // 项目类别
            },
            harmFactor:{
                type:Array     // 危害因素
            },
            companyAddress:{
                type:Array     // 单位地址
            },
            companyDetailAddress:{
                type:String     // 单位详细地址
            },
            projectAddress:{
                type:Array      // 项目所在地址
            },
            projectDetailAddress:{
                type:String      // 项目详细地址
            },
            inspectionUnit:{
                type: String     // 验收单位
            },
            inspectionUnitAddress:{
                type:Array     // 验收单位地址
            },
            cuLegalPerson:{
                type:String      // 建设单位法人
            },
            investDetail:{
                type:String      // 总投资情况
            },
            projectDutyPerson:{
                type:String     // 项目负责人
            },
            phone:{
                type:String     // 联系电话
            },
            reportWriteUnit:{
                type:String      // 报告编制单位
            },
            reviewTime:{
                type:Date       // 评审时间
            },
            reviewUnitContact:{
                type:String       // 评审单位联系人
            },
            reviewUnitPhone:{
                type:String      // 评审单位联系电话
            },
            controlResultWriteUnit:{
                type:String      // 职业病危害控制效果评价报告编制单位
            },
            cpHazardLevel:{
                type: Number    // 建设项目职业病危害风险分类    1一般 2较重 3严重
            },
            controlResultWriteUnitPhone:{
                type:String      // 职业病危害控制效果评价报告编制单位联系方式
            },
            controlResultWriteUnitContact:{
                type:String      // 职业病危害控制效果评价报告编制单位联系人
            },
            checkAcceptTime:{
                type:Date     // 验收具体时间
            },
            checkAcceptPosition:{
                type:String      // 验收地点
            },
            peopleData:{
                type:Array          // 各类成员
            },
            status:{
                type:Number      // 提交状态    1已提交  2 已暂存  3 已删除
            },
            recordNumber:{
                type:String     //  备案编号
            },
            noticeFileUrl:{
                type:Array      // 自动校验通过后生成告知书
            },
            recordApplyTableFile:{
                type:Array     // 建设项目职业病防护设施竣工验收(备案)申请书
            },
            designApproveTableFile:{
                type:Array     // 建设项目设计专项批复文件
            },
            projectInitiationApprovalFile:{
                type:Array    // 建设项目立项审批文件
            },
            checkSelfReportFile:{
                type:Array     // 建设项目职业病防护设施竣工自行验收情况报告
            },
            finishRecordCommonFile:{
                type:Array      // 申请建设项目职业病防护设施竣工验收（备案）的公函
            },
            unitCertificateFile:{
                type:Array        // 单位资质证明
            },
            cId:{
                type: String,
                ref: 'Adminorg',    // 申请单位id
            },
            districtCode:{
                type:String      // 项目所在地区码
            },
            constructionContent:{
                type:String      // 建设内容
            },
            materials:{
                type:String      // 主要原辅材料
            },
            process:{
                type:String    // 主要工艺
            },
            workSystem:{
                type:String   // 工作制度与劳动定员
            },
            testRun:{
                type:String   // 试运行情况简介
            },
            prevalueCheck:{
                type:Number    // 各评审意见   1同意  2拒绝
            },
            askReportCount:{
                type:Number    // 汇报答疑数
            },
            occupationalHazards:{
                type:Number     // 产生的职业病危害因素种类以及接触人数
            },
            exceedStandard:{
                type:String    // 职业病危害因素检测超标情况
            },

            yOrganizationUnit:{
                type:String     // 职业病危害预评价执行情况---报告编制单位
            },
            yContact:{
                type:String     // 职业病危害预评价执行情况---联系人
            },
            yNumber:{
                type:String     // 职业病危害预评价执行情况---联系电话
            },
            yReviewTime:{
                type:Date    // 职业病危害预评价执行情况---评审时间
            },
            contructEquProjectRecordNoticeFile:{
                type:String      // 告知书
            }
        },
        {
            timestamps: true
        }
    );
    return mongoose.model('ContructEquipRecord', contructEquipRecordSchema, 'ContructEquipRecords');
}