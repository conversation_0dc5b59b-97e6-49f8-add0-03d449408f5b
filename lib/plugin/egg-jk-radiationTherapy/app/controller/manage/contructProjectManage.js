// 建设项目防护设施备案
const moment = require('moment')
function generateRandomSixDigitNumber() {
    return Math.floor(100000 + Math.random() * 900000).toString().padStart(6, '0');
}
const contructProjectManageController = {
    // 备案申请 自动校验 提交即备案成功
    async addProtectEquAcceptanceRecord(ctx) {
        try {
            let params = ctx.request.body;
            const qyId = ctx.session.adminUserInfo.EnterpriseID;
            params = { ...params, cId: qyId }
            const recordNumber = moment().format('YYYYMMDDhhmmss') + generateRandomSixDigitNumber()
            params.recordNumber = recordNumber
            const res = await ctx.service.contructProjectManage.addProtectEquAcceptanceRecord(params);
            ctx.helper.renderSuccess(ctx, {
                data: res,
                message: '备案成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },

    async getContructproEquRecordList(ctx) {
        try {
            let params = ctx.request.query;
            // 获取该监管账号地区编号，用于做数据隔离
            const areaCode = String(ctx.session.superUserInfo.area_code).slice(0, 6)
            .replace(/00$/, '')
            .replace(/00$/, '');
            params.areaCode = areaCode
            const res = await ctx.service.contructProjectManage.getContructproEquRecordList(params);
            ctx.helper.renderSuccess(ctx, {
                data: res,
                message: '获取列表成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },

    async getContructproEquRecordDetail(ctx){
        try {
            let params = ctx.request.query
            const res = await ctx.service.contructProjectManage.getContructproEquRecordDetail(params);
            ctx.helper.renderSuccess(ctx, {
                data: res,
                message: '获取详情成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },

    async getContructProgressRecordList(ctx){
        try {
            let params = ctx.request.query;
            // 获取该监管账号地区编号，用于做数据隔离
            const areaCode = String(ctx.session.superUserInfo.area_code).slice(0, 6)
            .replace(/00$/, '')
            .replace(/00$/, '');
            params.areaCode = areaCode
            const res = await ctx.service.contructProjectManage.getContructProgressRecordList(params);
            ctx.helper.renderSuccess(ctx, {
                data: res,
                message: '获取列表成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },

    async getContructProgressRecordDetail(ctx){
        try {
            let params = ctx.request.query
            const res = await ctx.service.contructProjectManage.getContructProgressRecordDetail(params);
            ctx.helper.renderSuccess(ctx, {
                data: res,
                message: '获取详情成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },

    // 防护设施备案统计
    async getContructProEquStatisticData(ctx){
        try {
            let params = ctx.request.query;
            const areaCode = params.areaCode ? String(params.areaCode) : String(ctx.session.superUserInfo.area_code)
            params.areaCode = areaCode
            const result = await ctx.service.contructProjectManage.getAttachDistrict({areaCode})
            let tempArr = []
            // 对得到的结果进行循环
            for(let i=0;i<result.length;i++){
                const item = result[i]
                let query = {
                    // areaCode:item.area_code.slice(0, 6).replace(/00$/, '').replace(/00$/, '')
                    areaCode:item.area_code.slice(0, 9).replace(/000$/, '').replace(/00$/, '').replace(/00$/, '')
                }
                const res = await ctx.service.contructProjectManage.getContructProEquStatisticData(query);
                const obj = JSON.parse(JSON.stringify(item))
                obj.result = res.length   // 总的备案数
                let result1 = 0
                let result2 = 0
                let result3 = 0
                let result4 = 0
                let result5 = 0
                res.forEach(element => {
                    if(Number(element.projectType) === 1){
                        result1 ++
                    }
                    if(Number(element.projectType) === 2){
                        result2 ++
                    }
                    if(Number(element.projectType) === 3){
                        result3 ++
                    }
                    if(Number(element.projectType) === 4){
                        result4 ++
                    }
                    if(Number(element.projectType) === 5){
                        result5 ++
                    }
                })
                obj.result1 = result1
                obj.result2 = result2
                obj.result3 = result3
                obj.result4 = result4
                obj.result5 = result5
                // 备案完整性   统一全部完整
                obj.recordAll = res.length
                obj.recordPart = 0

                // 备案质量
                obj.dataExcellent = res.length
                obj.dataNormal = 0
                obj.dataBad = 0

                // 备案状态
                obj.hasRecord = res.length
                obj.noRecord = 0
                tempArr.push(obj)
            }
        
            ctx.helper.renderSuccess(ctx, {
                data: tempArr,
                message: '获取数据成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },


    // 防护设施控制效果评价统计数据
    async getContructControlStatisticData(ctx){
        try {
            let params = ctx.request.query;
            const areaCode = params.areaCode ? String(params.areaCode) : String(ctx.session.superUserInfo.area_code)
            params.areaCode = areaCode
            const result = await ctx.service.contructProjectManage.getAttachDistrict({areaCode})    // 获得管辖范围数据

            let tempArr = []
            // 对得到的结果进行循环
            for(let i=0;i<result.length;i++){
                const item = result[i]
                let query = {
                    areaCode:item.area_code.slice(0, 9).replace(/000$/, '').replace(/00$/, '').replace(/00$/, '')
                }
                // console.log(item.area_code,item.name,'==========地区及其对应码',query.areaCode)
                let res = await ctx.service.contructProjectManage.getContructControlStatisticData(query);

                const obj = JSON.parse(JSON.stringify(item))
                obj.result = res.length   // 总的备案数
                let result1 = 0
                let result2 = 0
                let result3 = 0
                let result4 = 0
                let result5 = 0
                res.forEach(element => {
                    if(Number(element.pId.projectType) === 1){
                        result1 ++
                    }
                    if(Number(element.pId.projectType) === 2){
                        result2 ++
                    }
                    if(Number(element.pId.projectType) === 3){
                        result3 ++
                    }
                    if(Number(element.pId.projectType) === 4){
                        result4 ++
                    }
                    if(Number(element.pId.projectType) === 5){
                        result5 ++
                    }
                })
                obj.result1 = result1
                obj.result2 = result2
                obj.result3 = result3
                obj.result4 = result4
                obj.result5 = result5
                // 备案完整性   统一全部完整
                obj.recordAll = res.length
                obj.recordPart = 0

                // 备案质量
                obj.dataExcellent = res.length
                obj.dataNormal = 0
                obj.dataBad = 0

                // 备案状态
                obj.hasRecord = res.length
                obj.noRecord = 0
                tempArr.push(obj)
            }

            ctx.helper.renderSuccess(ctx, {
                data: tempArr,
                message: '获取数据成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },


    // 获取兵团师市团镇建设项目的备案数并进行排名
    async getAreaRecordCountRank(ctx){
        try {
            let params = ctx.request.query;
            let areaCode = params.areaCode ? String(params.areaCode) : String(ctx.session.superUserInfo.area_code)
            params.areaCode = areaCode
            const { startTime, endTime } = params

            const result = await ctx.service.contructProjectManage.getAttachDistrict({areaCode})
            let tempArr = []
            // 对得到的结果进行循环
            for(let i=0;i<result.length;i++){
                const item = JSON.parse(JSON.stringify(result[i]))
                let query = {
                    areaCode:item.area_code.slice(0, 9).replace(/000$/, '').replace(/00$/, '').replace(/00$/, ''),
                    startTime,
                    endTime
                }
                const res = await ctx.service.contructProjectManage.getAreaRecordCountRank(query);
                item.value = res.length
                tempArr.push(item)
            }
            tempArr.sort((a, b) => b.value - a.value);   // 排序
            tempArr = tempArr.filter(item => item.value > 0);    // 过滤掉 count 为 0 的项（新增代码）
            ctx.helper.renderSuccess(ctx, {
                data: tempArr,
                message: '获取列表成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },

    // 项目性质数目和占比
    async getTypeRecordCountes(ctx){
        try {
            let params = ctx.request.query;
            const { startTime, endTime } = params
            let areaCode = params.areaCode ? String(params.areaCode) : String(ctx.session.superUserInfo.area_code)
            const result = await ctx.service.contructProjectManage.getAttachDistrict({areaCode})  // 获取当前管辖区域内的下一级
            let tempArr = []
            if(result && result.length > 0){
                for(let i=0;i<result.length;i++){
                    let obj = JSON.parse(JSON.stringify(result[i]))
                    areaCode = obj.area_code.slice(0, 9).replace(/000$/, '').replace(/00$/, '').replace(/00$/, '')
                    const query = {
                        areaCode,
                        startTime,
                        endTime
                    }
                    const res = await ctx.service.contructProjectManage.getAreaRecordCountRank(query);
                    let newCount = 0
                    let changeCount = 0
                    let addCount = 0
                    let tChangeCount = 0
                    let tImport = 0
                    if(res && res.length > 0){
                        res.forEach(item => {
                            switch(Number(item.projectType)){
                                case 1:newCount = newCount + 1
                                    break;
                                case 2:changeCount = changeCount + 1
                                    break;
                                case 3:addCount = addCount + 1
                                    break;
                                case 4:tChangeCount = tChangeCount + 1
                                    break;
                                case 5:tImport = tImport + 1
                                    break;
                                default:
                                    return
                            }
                        })
                    }

                    obj.newCount = newCount
                    obj.changeCount = changeCount
                    obj.addCount = addCount
                    obj.tChangeCount = tChangeCount
                    obj.tImport = tImport

                    tempArr.push(obj)
                }
            }
            ctx.helper.renderSuccess(ctx, {
                data: tempArr,
                message: '获取数据成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },

    // 危害风险类别数目和占比
    async getRiskRecordCount(ctx){
        try {
            // let params = ctx.request.query;
            // let areaCode = params.areaCode ? String(params.areaCode) : String(ctx.session.superUserInfo.area_code)
            // areaCode = areaCode.slice(0, 9).replace(/000$/, '').replace(/00$/, '').replace(/00$/, '')
            // params.areaCode = areaCode
            // const res = await ctx.service.contructProjectManage.getRiskRecordCount(params);


            let params = ctx.request.query;
            const { startTime, endTime } = params
            let areaCode = params.areaCode ? String(params.areaCode) : String(ctx.session.superUserInfo.area_code)
            const result = await ctx.service.contructProjectManage.getAttachDistrict({areaCode})  // 获取当前管辖区域内的下一级
            let tempArr = []
            if(result && result.length > 0){
                for(let i=0;i<result.length;i++){
                    let obj = JSON.parse(JSON.stringify(result[i]))
                    areaCode = obj.area_code.slice(0, 9).replace(/000$/, '').replace(/00$/, '').replace(/00$/, '')
                    const query = {
                        areaCode,
                        startTime,
                        endTime
                    }

                    const res = await ctx.service.contructProjectManage.getAreaRecordCountRank(query);
                    let normalCount = 0
                    let badCount = 0
                    let moreBadCount = 0

                    if(res && res.length > 0){
                        res.forEach(item => {
                            switch(Number(item.cpHazardLevel)){
                                case 1:normalCount = normalCount + 1
                                    break;
                                case 2:badCount = badCount + 1
                                    break;
                                case 3:moreBadCount = moreBadCount + 1
                                    break;
                                default :
                                return
                            }
                        })
                    }

                    obj.normalCount = normalCount
                    obj.badCount = badCount
                    obj.moreBadCount = moreBadCount

                    tempArr.push(obj)
                }
            }

            ctx.helper.renderSuccess(ctx, {
                data: tempArr,
                message: '获取数据成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },

    // 项目性质各地区数目
    async getTypeRecordAreaCount(ctx){
        try {
            let params = ctx.request.query;
            let areaCode = params.areaCode ? String(params.areaCode) : String(ctx.session.superUserInfo.area_code)
            params.areaCode = areaCode
            const { startTime,endTime } = params

            const result = await ctx.service.contructProjectManage.getAttachDistrict({areaCode})
            let tempArr = []
            // 对得到的结果进行循环

            for(let i=0;i<result.length;i++){
                const item = result[i]
                let query = {
                    areaCode:item.area_code.slice(0, 9).replace(/000$/, '').replace(/00$/, '').replace(/00$/, ''),
                    projectType:Number(params.projectType),
                    startTime,
                    endTime
                }
                const res = await ctx.service.contructProjectManage.getContructProEquStatisticData(query);

                const obj = JSON.parse(JSON.stringify(item))
                obj.count = res.length   // 总的备案数
                tempArr.push(obj)
            }
        
            ctx.helper.renderSuccess(ctx, {
                data: tempArr,
                message: '获取数据成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },

    // 危害风险类别地区数目
    async getRiskRecordAreaCount(ctx){
        try {
            let params = ctx.request.query;
            let areaCode = params.areaCode ? String(params.areaCode) : String(ctx.session.superUserInfo.area_code)
            params.areaCode = areaCode
            const { startTime, endTime} = params

            const result = await ctx.service.contructProjectManage.getAttachDistrict({areaCode})
            let tempArr = []
            // 对得到的结果进行循环

            for(let i=0;i<result.length;i++){
                const item = result[i]
                let query = {
                    areaCode:item.area_code.slice(0, 9).replace(/000$/, '').replace(/00$/, '').replace(/00$/, ''),
                    cpHazardLevel:Number(params.cpHazardLevel),
                    startTime,
                    endTime
                }
                const res = await ctx.service.contructProjectManage.getContructProEquStatisticData(query);

                const obj = JSON.parse(JSON.stringify(item))
                obj.count = res.length   // 总的备案数
                tempArr.push(obj)
            }
        
            ctx.helper.renderSuccess(ctx, {
                data: tempArr,
                message: '获取数据成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },

    // 上面2个接口后期需要封装成一个，冗余

    // 项目类型
    async getContructProjectCategory(ctx){
        try {
            let params = ctx.request.query;
            let areaCode = params.areaCode ? String(params.areaCode) : String(ctx.session.superUserInfo.area_code)
            areaCode = areaCode.slice(0, 9).replace(/000$/, '').replace(/00$/, '').replace(/00$/, '')
            params.areaCode = areaCode
            const res = await ctx.service.contructProjectManage.getContructProjectCategory(params);
            ctx.helper.renderSuccess(ctx, {
                data: res,
                message: '获取数据成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },

    // 验收单位地址分布
    async checkUnitDistribution(ctx){
        try {
            let params = ctx.request.query;
            let areaCode = params.areaCode ? String(params.areaCode) : String(ctx.session.superUserInfo.area_code)
            params.areaCode = areaCode
            const { startTime,endTime } = params
            const res = await ctx.service.contructProjectManage.getSubDistrictArr(params)   // 获取归属地区列表
            const finResult = []
            if( res.length > 0 ){
                for(let i=0;i<res.length;i++){
                    let query = {
                        areaCode : res[i].area_code,
                        startTime,
                        endTime
                    }
                    const result = await ctx.service.contructProjectManage.getBelongtoCompany(query)
                    const tempData = {
                        name:res[i].name,
                        count: result ? result : 0
                    }
                    finResult.push(tempData)
                }
            }
            ctx.helper.renderSuccess(ctx, {
                data: finResult,
                message: '获取数据成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },

    // 参与人员职责与分工
    async checkPeopleAndFY(ctx){
        try {
            let params = ctx.request.query;
            let areaCode = params.areaCode ? String(params.areaCode) : String(ctx.session.superUserInfo.area_code)
            params.areaCode = areaCode.slice(0, 9).replace(/000$/, '').replace(/00$/, '').replace(/00$/, '')
            const result = await ctx.service.contructProjectManage.checkPeopleAndFY(params)

            ctx.helper.renderSuccess(ctx, {
                data: result,
                message: '获取数据成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },


    // 各备案结果的建设项目数
    async recordTypeCount(ctx){
        try {
            let params = ctx.request.query;
            let areaCode = params.areaCode ? String(params.areaCode) : String(ctx.session.superUserInfo.area_code)
            params.areaCode = areaCode.slice(0, 9).replace(/000$/, '').replace(/00$/, '').replace(/00$/, '')
            const result = await ctx.service.contructProjectManage.recordTypeCount(params)

            ctx.helper.renderSuccess(ctx, {
                data: result,
                message: '获取数据成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },


    // 建设项目落实整改情况统计
    async implementRectification(ctx){
        try {
            let params = ctx.request.query;
            let areaCode = params.areaCode ? String(params.areaCode) : String(ctx.session.superUserInfo.area_code)
            params.areaCode = areaCode.slice(0, 9).replace(/000$/, '').replace(/00$/, '').replace(/00$/, '')
            const result = await ctx.service.contructProjectManage.implementRectification(params)

            ctx.helper.renderSuccess(ctx, {
                data: result,
                message: '获取数据成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },

    // 各评审意见建设项目数
    async prevaluteCheckCount(ctx){
        try {
            let params = ctx.request.query;
            let areaCode = params.areaCode ? String(params.areaCode) : String(ctx.session.superUserInfo.area_code)
            params.areaCode = areaCode.slice(0, 9).replace(/000$/, '').replace(/00$/, '').replace(/00$/, '')
            const result = await ctx.service.contructProjectManage.prevaluteCheckCount(params)

            ctx.helper.renderSuccess(ctx, {
                data: result,
                message: '获取数据成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    },

    // 汇报答疑数
    async answerAndReportCount(ctx){
        try {
            let params = ctx.request.query;
            let areaCode = params.areaCode ? String(params.areaCode) : String(ctx.session.superUserInfo.area_code)
            params.areaCode = areaCode.slice(0, 9).replace(/000$/, '').replace(/00$/, '').replace(/00$/, '')
            const result = await ctx.service.contructProjectManage.answerAndReportCount(params)

            ctx.helper.renderSuccess(ctx, {
                data: result,
                message: '获取数据成功',
                status: 200,
            })
        } catch (error) {
            ctx.helper.renderFail(ctx, {
                message: error.message,
            })
        }
    }
}
module.exports = contructProjectManageController;