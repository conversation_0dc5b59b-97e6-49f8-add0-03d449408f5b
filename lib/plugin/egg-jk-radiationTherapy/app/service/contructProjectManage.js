const Service = require('egg').Service;
class contructProjectManageService extends Service {
    async addProtectEquAcceptanceRecord(query) {
        const { ctx } = this
        const res = await ctx.model.ContructEquipRecord.create(query)
        return res
    }

    async getContructproEquRecordList(query) {
        const pageNum = Number(query.pageNum);
        const pageSize = Number(query.pageSize);
        const areaCode = query.areaCode
        const { ctx } = this;
        let filter = {}
        // 添加地区筛选条件
        if (areaCode) {
            filter.districtCode = { $regex: `^${areaCode}` };
        }
        if (query.projectName) {
            filter.projectName = { $regex: query.projectName, $options: 'i' };
        }
        if (query.projectType) {
            filter.projectType = query.projectType
        }
        // 日期范围筛选
        if (query.startDate && query.endDate) {
            filter.createdAt = {
                $gte: new Date(query.startDate),  // 开始时间（包含）
                $lte: new Date(query.endDate)     // 结束时间（包含）
            };
        }
        if (query.agreeStatus) {
            filter.agreeStatus = query.agreeStatus
        }
        if (query.projectType) {
            filter.projectType = query.projectType
        }
        filter.status = { $nin: [2, 3, 4] };

        const res = await ctx.model.ContructEquipRecord.find(filter)
            .skip((pageNum - 1) * pageSize)
            .limit(pageSize)
            .sort({ createdAt: -1 });

        const sum = await ctx.model.ContructEquipRecord.find(filter)
        const total = sum.filter(item => item._id).length;
        return {
            data: res,
            total
        }
    }

    async getContructproEquRecordDetail(query) {
        const { ctx } = this;
        const searchCondition = {};
        if (query.id) {
            searchCondition._id = { _id: query.id };
        }
        const res = await ctx.model.ContructEquipRecord.findOne(searchCondition)
        return res
    }


    // async getContructProgressRecordList(query){
    //     const pageNum = Number(query.pageNum);
    //     const pageSize = Number(query.pageSize);
    //     const areaCode = query.areaCode
    //     const { ctx } = this;
    //     let filter = {}
    //     let match = {}
    //     // 添加地区筛选条件
    //     if (areaCode) {
    //         filter.districtCode = { $regex: `^${areaCode}` };
    //     }
    //     filter.status = { $nin: [2, 3,4] };
    //     // 日期范围筛选
    //     if(query.startDate && query.endDate) {
    //         filter.createdAt = {
    //             $gte: new Date(query.startDate),  // 开始时间（包含）
    //             $lte: new Date(query.endDate)     // 结束时间（包含）
    //         };
    //     }

    //     if(query.projectName) {
    //         match.projectName = { $regex: query.projectName, $options: 'i' };
    //     }
    //     if(query.projectType){
    //         match.projectType = Number(query.projectType)
    //     }
    //     match.status = { $nin: [2, 3,4] };

    //     const records = await ctx.model.ControlProtectWorkProgressRecord.find(filter)
    //     .populate({
    //         path: 'pId',
    //         model: 'ContructEquipRecord',
    //         select: 'projectName projectAddress cuLegalPerson projectType',
    //         match
    //     })
    //     .skip((pageNum - 1) * pageSize)
    //     .limit(pageSize)
    //     .sort({ createdAt: -1 });

    //     const res = records.map(record => {
    //         const flatRecord = record.toObject();
    //         if (flatRecord.pId) {
    //           Object.assign(flatRecord, {
    //             projectName: flatRecord.pId.projectName,
    //             projectAddress: flatRecord.pId.projectAddress,
    //             cuLegalPerson: flatRecord.pId.cuLegalPerson,
    //             projectType: flatRecord.pId.projectType
    //           });
    //           delete flatRecord.pId;
    //         }

    //         return flatRecord;
    //     });

    //     const sum = await ctx.model.ControlProtectWorkProgressRecord.find(filter)
    //     const total = sum.filter(item => item._id).length;
    //     return {
    //         data:res,
    //         total
    //     }
    // }

    async getContructProgressRecordDetail(query) {
        const { ctx } = this;
        const searchCondition = {};
        if (query.id) {
            searchCondition._id = { _id: query.id };
        }
        const res = await ctx.model.ControlProtectWorkProgressRecord.findOne(searchCondition)
            .populate({
                path: 'pId',
                model: 'ContructEquipRecord',
                select: 'projectName projectAddress cuLegalPerson projectType projectCategory projectDutyPerson projectDetailAddress investDetail harmFactor'
            })

        console.log(res, '==========res')

        const flatRecord = res.toObject();
        if (flatRecord.pId) {
            Object.assign(flatRecord, {
                projectName: flatRecord.pId.projectName,
                projectAddress: flatRecord.pId.projectAddress,
                cuLegalPerson: flatRecord.pId.cuLegalPerson,
                projectType: flatRecord.pId.projectType,
                projectDutyPerson: flatRecord.pId.projectDutyPerson,
                projectDetailAddress: flatRecord.pId.projectDetailAddress,
                investDetail: flatRecord.pId.investDetail,
                projectCategory: flatRecord.pId.projectCategory,
                harmFactor: flatRecord.pId.harmFactor
            });
            delete flatRecord.pId;
        }
        return flatRecord
    }


    async getContructProgressRecordList(query) {
        const pageNum = Number(query.pageNum);
        const pageSize = Number(query.pageSize);
        const areaCode = query.areaCode;
        const { ctx } = this;

        let pipeline = [];

        // 添加地区筛选条件
        let filter = {};
        if (areaCode) {
            filter.districtCode = { $regex: `^${areaCode}` };
        }
        filter.status = { $nin: [2, 3, 4] };

        // 日期范围筛选
        if (query.startDate && query.endDate) {
            filter.createdAt = {
                $gte: new Date(query.startDate),  // 开始时间（包含）
                $lte: new Date(query.endDate)    // 结束时间（包含）
            };
        }

        // 添加过滤条件到聚合管道
        pipeline.push({ $match: filter });

        // 添加项目名称和项目类型筛选条件
        pipeline.push({
            $lookup: {
                from: 'ContructEquipRecords',  // 对应的集合名
                localField: 'pId',
                foreignField: '_id',
                as: 'projectDetails'
            }
        });
        pipeline.push({
            $unwind: {
                path: '$projectDetails',
                preserveNullAndEmptyArrays: true
            }
        });

        if (query.projectName) {
            pipeline.push({
                $match: {
                    'projectDetails.projectName': { $regex: query.projectName, $options: 'i' }
                }
            });
        }
        if (query.projectType) {
            pipeline.push({
                $match: {
                    'projectDetails.projectType': Number(query.projectType)
                }
            });
        }

        // 添加分页和排序
        pipeline.push({
            $sort: { createdAt: -1 }
        });
        pipeline.push({
            $skip: (pageNum - 1) * pageSize
        });
        pipeline.push({
            $limit: pageSize
        });

        // 添加需要返回的字段
        pipeline.push({
            $project: {
                _id: 1,
                pId: 1,
                evaluateReportWriteUnit: 1,
                evaluateCheckTime: 1,
                contact: 1,
                phone: 1,
                proDisEquAcceptTime: 1,
                cpHazardLevel: 1,
                dealMethodDetail: 1,
                exposedHazardPersonCount: 1,
                preWorkHealthCheckPeopleCount: 1,
                findTabooPeopleCount: 1,
                overHazardData: 1,
                protectEquData: 1,
                cId: 1,
                districtCode: 1,
                status: 1,
                recordNumber: 1,
                createdAt: 1,
                updatedAt: 1,
                projectName: '$projectDetails.projectName',
                projectAddress: '$projectDetails.projectAddress',
                cuLegalPerson: '$projectDetails.cuLegalPerson',
                projectType: '$projectDetails.projectType'
            }
        });

        // 执行聚合查询
        const res = await ctx.model.ControlProtectWorkProgressRecord.aggregate(pipeline);

        // 计算总数
        const totalPipeline = [{ $match: filter }];
        const totalRes = await ctx.model.ControlProtectWorkProgressRecord.aggregate(totalPipeline);
        const total = totalRes.length;

        return {
            data: res,
            total
        };
    }

    // 聚合没写出来，先用笨方法。。。待优化
    async getContructProEquStatisticData(query) {
        const { ctx } = this;
        const filter = {}
        filter.districtCode = { $regex: `^${query.areaCode}` };
        if (query.projectType) {
            filter.projectType = { $eq: query.projectType }
        }
        if (query.cpHazardLevel) {
            filter.cpHazardLevel = { $eq: query.cpHazardLevel }
        }
        filter.status = { $nin: [2, 3, 4] };
        const res = await ctx.model.ContructEquipRecord.find(filter)
        return res
    }

    // 获取地区码归属下的下一级地区
    async getAttachDistrict(query) {
        const { ctx } = this;
        const areaCode = query.areaCode;
        // 获取指定区域下的所有子区域
        const res = await ctx.model.District.find({ parent_code: areaCode }).select('name area_code');
        return res
    }

    // 获取控制效果评价备案统计数据
    async getContructControlStatisticData(query) {
        const { ctx } = this;
        const filter = {}
        filter.districtCode = { $regex: `^${query.areaCode}` };
        filter.status = { $nin: [2, 3, 4] };

        const res = await ctx.model.ControlProtectWorkProgressRecord.find(filter)
            .populate({
                path: 'pId',
                model: 'ContructEquipRecord',
                select: 'projectName projectAddress cuLegalPerson projectType',
            })
        return res
    }

    // 排名
    async getAreaRecordCountRank(query) {
        const { ctx } = this;
        const { areaCode, startTime, endTime } = query;
        const filter = {}
        filter.districtCode = { $regex: `^${areaCode}` };
        if (startTime && endTime) {
            filter.createdAt = {
                $gte: startTime,
                $lte: endTime
            }
        }
        filter.status = { $eq: 1 };
        const res = await ctx.model.ContructEquipRecord.find(filter)
        return res
    }

    async getTypeRecordCountes(query) {
        const { ctx } = this;
        const { areaCode, startTime, endTime } = query;
        const filter = {};
        filter.districtCode = { $regex: `^${areaCode}` };
        if (startTime && endTime) {
            filter.createdAt = {
                $gte: new Date(startTime), // 转换为Date对象
                $lte: new Date(endTime)
            }
        }

        // 定义所有可能的 projectType 和对应的名称
        const projectTypes = [
            { projectType: 1, projectTypeName: "新建" },
            { projectType: 2, projectTypeName: "改建" },
            { projectType: 3, projectTypeName: "扩建" },
            { projectType: 4, projectTypeName: "技术改造" },
            { projectType: 5, projectTypeName: "技术引进" }
        ];

        // 查询数据库中实际存在的 projectType 记录
        const res = await ctx.model.ContructEquipRecord.aggregate([
            // 筛选符合地区条件的记录
            { $match: filter },

            // 按 projectType 分组并统计数量
            {
                $group: {
                    _id: "$projectType",
                    count: { $sum: 1 }
                }
            },

            // 添加 projectTypeName 字段
            {
                $addFields: {
                    projectTypeName: {
                        $switch: {
                            branches: [
                                { case: { $eq: ["$_id", 1] }, then: "新建" },
                                { case: { $eq: ["$_id", 2] }, then: "改建" },
                                { case: { $eq: ["$_id", 3] }, then: "扩建" },
                                { case: { $eq: ["$_id", 4] }, then: "技术改造" },
                                { case: { $eq: ["$_id", 5] }, then: "技术引进" }
                            ],
                            default: "未知类型" // 处理可能的其他值
                        }
                    }
                }
            },

            // 重命名 _id 为 projectType 并调整字段顺序
            {
                $project: {
                    _id: 0,
                    projectType: "$_id",
                    projectTypeName: 1,
                    count: 1
                }
            }
        ]);

        // 补充不存在的 projectType
        const result = projectTypes.map(pt => {
            const found = res.find(item => item.projectType === pt.projectType);
            return found || { projectType: pt.projectType, projectTypeName: pt.projectTypeName, count: 0 };
        });

        // 计算总数量
        const total = result.reduce((acc, item) => acc + item.count, 0);

        // 计算每个 projectType 的占比
        const finalResult = result.map(item => ({
            projectType: item.projectType,
            projectTypeName: item.projectTypeName,
            count: item.count,
            percentage: total > 0 ? (item.count / total * 100).toFixed(2) : "0.00"
        }));

        return finalResult;
    }

    async getRiskRecordCount(query) {
        const { ctx } = this;
        const { areaCode, startTime, endTime } = query;
        const filter = {};
        filter.districtCode = { $regex: `^${areaCode}` };

        if (startTime && endTime) {
            filter.createdAt = {
                $gte: new Date(startTime), // 转换为Date对象
                $lte: new Date(endTime)
            }
        }

        // 定义所有可能的 cpHazardLevel 和对应的名称
        const cpHazardLevels = [
            { cpHazardLevel: 1, cpHazardLevelName: "一般" },
            { cpHazardLevel: 2, cpHazardLevelName: "较重" },
            { cpHazardLevel: 3, cpHazardLevelName: "严重" }
        ];

        // 查询数据库中实际存在的 cpHazardLevel 记录
        const res = await ctx.model.ContructEquipRecord.aggregate([
            // 筛选符合地区条件的记录
            { $match: filter },

            // 按 cpHazardLevel 分组并统计数量
            {
                $group: {
                    _id: "$cpHazardLevel",
                    count: { $sum: 1 }
                }
            },

            // 添加 cpHazardLevelName 字段
            {
                $addFields: {
                    cpHazardLevelName: {
                        $switch: {
                            branches: [
                                { case: { $eq: ["$_id", 1] }, then: "一般" },
                                { case: { $eq: ["$_id", 2] }, then: "较重" },
                                { case: { $eq: ["$_id", 3] }, then: "严重" }
                            ],
                            default: "未知风险" // 处理可能的其他值
                        }
                    }
                }
            },

            // 重命名 _id 为 cpHazardLevel 并调整字段顺序
            {
                $project: {
                    _id: 0,
                    cpHazardLevel: "$_id",
                    cpHazardLevelName: 1,
                    count: 1
                }
            }
        ]);

        // 补充不存在的 cpHazardLevel
        const result = cpHazardLevels.map(cpl => {
            const found = res.find(item => item.cpHazardLevel === cpl.cpHazardLevel);
            return found || { cpHazardLevel: cpl.cpHazardLevel, cpHazardLevelName: cpl.cpHazardLevelName, count: 0 };
        });

        // 计算总数量
        const total = result.reduce((acc, item) => acc + item.count, 0);

        // 计算每个 cpHazardLevel 的占比
        const finalResult = result.map(item => ({
            cpHazardLevel: item.cpHazardLevel,
            cpHazardLevelName: item.cpHazardLevelName,
            count: item.count,
            percent: total > 0 ? parseFloat((item.count / total * 100).toFixed(2)) : 0
        }));

        return finalResult;
    }

    async getContructProjectCategory(query) {
        const { ctx } = this;
        const { areaCode, startTime, endTime } = query;
        const filter = {};
        filter.districtCode = { $regex: `^${areaCode}` };

        if (startTime && endTime) {
            filter.createdAt = {
                $gte: new Date(startTime), // 转换为Date对象
                $lte: new Date(endTime)
            }
        }

        // 查询并统计各个projectCategory的个数
        const projectCategoryStats = await ctx.model.ContructEquipRecord.aggregate([
            { $match: filter }, // 应用过滤条件
            {
                $group: {
                    _id: '$projectCategory', // 按照projectCategory分组
                    count: { $sum: 1 } // 统计每个分组的文档数量
                }
            },
            { $sort: { _id: 1 } } // 按照projectCategory升序排序
        ]);

        // 将统计结果转换为前端echarts饼图需要的格式
        const pieChartData = projectCategoryStats.map(item => ({
            name: item._id, // 项目类别名称
            value: item.count // 对应的个数
        }));
        return pieChartData
    }


    // 递归获取地区数组
    async getDistrictHierarchy(code) {
        const district = await this.ctx.model.District.findOne({
            area_code: code
        });

        if (!district) {
            throw new Error(`No district found for code: ${code}`);
        }

        // 如果没有父级地区码，或者父级地区码为0，直接返回当前地区的名称
        if (!district.parent_code || district.parent_code === '0') {
            return [district.name];
        }

        // 递归调用，获取父级地区的层级
        const parentHierarchy = await this.getDistrictHierarchy(district.parent_code);

        // 将当前地区的名称添加到父级层级数组的末尾
        return [...parentHierarchy, district.name];
    }

    // 获取地区的子集
    async getSubDistrictArr(query) {
        const { areaCode } = query
        const district = await this.ctx.model.District.find({
            parent_code: areaCode
        }).select('area_code name')
        return district
    }

    // 获取建设项目本辖区的建设项目验收单位
    async getBelongtoCompany(query) {
        try {
            const districtArr = await this.getDistrictHierarchy(query.areaCode)   // 获取当前账号的地区数组
            const match1 = {}
            const { startTime, endTime } = query
            if (startTime && endTime) {
                match1.createdAt = {
                    $gte: new Date(startTime), // 转换为Date对象
                    $lte: new Date(endTime)
                }
            }
            const result = await this.ctx.model.ContructEquipRecord.aggregate([
                {
                    $lookup: {
                        from: 'adminorgs', // adminorg表名
                        localField: 'cId',
                        foreignField: '_id',  // 修改为使用 _id 作为关联字段
                        as: 'adminorgData'
                    }
                },
                {
                    $unwind: '$adminorgData'
                },
                ...(Object.keys(match1).length > 0 ? [{ $match: match1 }] : []),
                {
                    $match: {
                        'adminorgData.districtRegAdd': {
                            $all: districtArr // 确保 districtRegAdd 包含 districtArr 中的所有元素
                        },
                    }
                },
                {
                    $count: 'totalCount'
                }
            ])
            return result.length > 0 ? result[0].totalCount : 0;
        } catch (error) {
            console.log(error)
        }
    }

    // 参与人员职责分工
    async checkPeopleAndFY(query) {
        const { ctx } = this;
        const { areaCode, startTime, endTime } = query;
        const filter = {};
        filter.districtCode = { $regex: `^${areaCode}` };
        if (startTime && endTime) {
            filter.createdAt = {
                $gte: new Date(startTime), // 转换为Date对象
                $lte: new Date(endTime)
            }
        }

        // 查询并统计各个projectCategory的个数
        const workTypeCount = await ctx.model.ContructEquipRecord.aggregate([
            { $match: filter }, // 应用过滤条件

            // 展开嵌套数组，以便对每个personType进行统计
            { $unwind: "$peopleData" },

            // 按personType字段分组并计数
            {
                $group: {
                    _id: "$peopleData.personType",
                    count: { $sum: 1 }
                }
            },

            // 可选：重命名_id字段为更友好的名称
            {
                $project: {
                    personType: "$_id",
                    count: 1,
                    _id: 0 // 排除默认的_id字段
                }
            },

            // 可选：按计数结果排序（降序）
            {
                $sort: { count: -1 }
            }
        ])
        // return workTypeCount
        const xAxisData = workTypeCount.map(item => item.personType);
        const seriesData = workTypeCount.map(item => item.count);
        return {
            xAxisData,
            seriesData
        }
    }

    // 各备案结果的建设项目数
    async recordTypeCount(query) {
        const { ctx } = this;
        const { areaCode, endTime, startTime } = query;
        const filter = {};
        filter.districtCode = { $regex: `^${areaCode}` };
        if (startTime && endTime) {
            filter.createdAt = {
                $gte: new Date(startTime), // 转换为Date对象
                $lte: new Date(endTime)
            }
        }

        const statusCount = await ctx.model.ContructEquipRecord.aggregate([
            { $match: filter }, // 应用过滤条件

            // 按status字段分组并计数
            {
                $group: {
                    _id: "$status",
                    count: { $sum: 1 }
                }
            },

            // 将status值映射为success/fail，并重新分组
            {
                $group: {
                    _id: null,
                    successCount: { $sum: { $cond: [{ $eq: ["$_id", 1] }, "$count", 0] } },
                    failCount: { $sum: { $cond: [{ $eq: ["$_id", 5] }, "$count", 0] } }
                }
            },

            // 移除默认的_id字段
            {
                $project: {
                    _id: 0
                }
            }
        ]);

        // 如果没有匹配的文档，返回默认值{successCount: 0, failCount: 0}
        return statusCount.length > 0 ? statusCount[0] : { successCount: 0, failCount: 0 };
    }

    // 建设项目落实整改情况
    async implementRectification(query) {
        const { ctx } = this;
        const { areaCode, startTime, endTime } = query;
        const filter = {};
        filter.districtCode = { $regex: `^${areaCode}` };
        if (startTime && endTime) {
            filter.createdAt = {
                // $gte:startTime,
                // $lte:endTime
                $gte: new Date(startTime), // 转换为Date对象
                $lte: new Date(endTime)
            }
        }

        const statusCount = await ctx.model.ControlProtectWorkProgressRecord.aggregate([
            { $match: filter }, // 应用过滤条件

            // 按status字段分组并计数
            {
                $group: {
                    _id: "$dealMethodDetail",
                    count: { $sum: 1 }
                }
            },

            // 将status值映射为success/fail，并重新分组
            {
                $group: {
                    _id: null,
                    hasDealCount: { $sum: { $cond: [{ $eq: ["$_id", 1] }, "$count", 0] } },
                    waitDealCount: { $sum: { $cond: [{ $eq: ["$_id", 2] }, "$count", 0] } }
                }
            },

            // 移除默认的_id字段
            {
                $project: {
                    _id: 0
                }
            }
        ]);

        // 如果没有匹配的文档，返回默认值{successCount: 0, failCount: 0}
        return statusCount.length > 0 ? statusCount[0] : { hasDealCount: 0, awaitDealCount: 0 };
    }

    // 专家评审意见
    async prevaluteCheckCount(query) {
        const { ctx } = this;
        const { areaCode, startTime, endTime } = query;
        const filter = {};
        filter.districtCode = { $regex: `^${areaCode}` };
        if (startTime && endTime) {
            filter.createdAt = {
                $gte: new Date(startTime), // 转换为Date对象
                $lte: new Date(endTime)
            }
        }

        const statusCount = await ctx.model.ContructEquipRecord.aggregate([
            { $match: filter }, // 应用过滤条件

            // 按status字段分组并计数
            {
                $group: {
                    _id: "$prevalueCheck",
                    count: { $sum: 1 }
                }
            },

            // 将status值映射为success/fail，并重新分组
            {
                $group: {
                    _id: null,
                    agreeCount: { $sum: { $cond: [{ $eq: ["$_id", 1] }, "$count", 0] } },
                    refuseCount: { $sum: { $cond: [{ $eq: ["$_id", 2] }, "$count", 0] } }
                }
            },

            // 移除默认的_id字段
            {
                $project: {
                    _id: 0
                }
            }
        ]);

        // 如果没有匹配的文档，返回默认值{successCount: 0, failCount: 0}
        return statusCount.length > 0 ? statusCount[0] : { agreeCount: 0, refuseCount: 0 };
    }


    // 获取汇报答疑数
    async answerAndReportCount(query) {
        const { ctx } = this;
        const { areaCode, startTime, endTime } = query;
        const filter = {};

        if (areaCode) filter.districtCode = { $regex: `^${areaCode}` };
        if (startTime && endTime) {
            filter.createdAt = {
                $gte: new Date(startTime),
                $lte: new Date(endTime)
            };
        }

        // 只查询存在 askReportCount 字段且类型为数字的文档
        filter.askReportCount = { $type: "number" };

        const result = await ctx.model.ContructEquipRecord.aggregate([
            { $match: filter },
            {
                $group: {
                    _id: null,
                    totalAskReportCount: { $sum: "$askReportCount" }
                }
            }
        ]);

        return result.length > 0 ? result[0].totalAskReportCount : 0;
    }
}

module.exports = contructProjectManageService;