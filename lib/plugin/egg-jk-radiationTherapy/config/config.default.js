/* eslint valid-jsdoc: "off" */
exports.jk_Radiation_Therapy = {
  alias: 'radiationTherapy', // 插件目录，必须为英文
  pkgName: 'egg-jk-radiationTherapy', // 插件包名
  enName: 'jk_Radiation_Therapy', // 插件名
  name: '放射诊疗管理', // 插件名称
  description: '放射诊疗管理', // 插件描述
  adminApi: [
    {
      url: 'radiationTherapy/getApproveApplyList',
      method: 'get',
      controllerName: 'getApproveApplyList',
      details: '放射诊疗许可证申请',
    },
    {
      url: 'radiationTherapy/getApplyDetailById',
      method: 'get',
      controllerName: 'getApplyDetailById',
      details: '放射诊疗许可证申请详情',
    },
    {
      url: 'radiationTherapy/agreeApplyApprove',
      method: 'post',
      controllerName: 'agreeApplyApprove',
      details: '通过许可证申请',
    },
    {
      url: 'radiationTherapy/refuseApplyApprove',
      method: 'post',
      controllerName: 'refuseApplyApprove',
      details: '驳回许可证申请',
    },
    {
      url: 'radiationTherapy/statisticApproveByArea',
      method: 'get',
      controllerName: 'statisticApproveByArea',
      details: '按所在地区统计放射诊疗许可证信息',
    },
    {
      url: 'radiationTherapy/statisticApproveZLType',
      method: 'get',
      controllerName: 'statisticApproveZLType',
      details: '按放射诊疗类别统计放射诊疗许可证信息',
    },
    {
      url: 'radiationTherapy/statisticApproveByDepartment',
      method: 'get',
      controllerName: 'statisticApproveByDepartment',
      details: '按发证部门统计放放射诊疗许可证信息',
    },
    {
      url: 'radiationTherapy/getApproveCertificateUrl',
      method: 'get',
      controllerName: 'getApproveCertificateUrl',
      details: '获取放射诊疗许可证下载',
    },
    {
      url: 'radiationTherapy/statisticApproveCertificateDataByArea',
      method: 'get',
      controllerName: 'statisticApproveCertificateDataByArea',
      details: '按地区统计放射诊疗许可证',
    },
    {
      url: 'radiationTherapy/statisticApproveCertificateDataByZLType',
      method: 'get',
      controllerName: 'statisticApproveCertificateDataByZLType',
      details: '按放射诊疗类别统计',
    },
      {
      url: 'radiationTherapy/statisticApproveCertificateDataByDepartment',
      method: 'get',
      controllerName: 'statisticApproveCertificateDataByDepartment',
      details: '按发证部门统计放射诊疗许可证',
    },

    {
      url: 'medJGPreEvaluate/getMedPreEvaluateProjectApplyList',
      method: 'get',
      controllerName: 'getMedPreEvaluateProjectApplyList',
      details: '获取预评价项目申请列表',
    },
    {
      url: 'medJGPreEvaluate/getMedPreEvaluateProjectApplyDetail',
      method: 'get',
      controllerName: 'getMedPreEvaluateProjectApplyDetail',
      details: '获取预评价项目申请详情数据',
    },
    {
      url: 'medJGPreEvaluate/mpStatisticByArea',
      method: 'get',
      controllerName: 'mpStatisticByArea',
      details: '按地区统计项目性质数据',
    },
    {
      url: 'medJGPreEvaluate/mpStatisticByZLType',
      method: 'get',
      controllerName: 'mpStatisticByZLType',
      details: '按项目诊疗类别统计项目性质数据',
    },
    {
      url: 'medJGPreEvaluate/mpStatisticByHazardType',
      method: 'get',
      controllerName: 'mpStatisticByHazardType',
      details: '按放射性危害类别统计项目性质数据',
    },


    {
      url: 'medJGProjectFinishAudit/getMedProjectFinishAuditList',
      method: 'get',
      controllerName: 'getMedProjectFinishAuditList',
      details: '获取医疗项目竣工审核申请列表',
    },
    {
      url: 'medJGProjectFinishAudit/getMedProjectFinishAuditApplyDetail',
      method: 'get',
      controllerName: 'getMedProjectFinishAuditApplyDetail',
      details: '获取医疗项目竣工审核详情数据',
    },
    {
      url: 'medJGProjectFinishAudit/mfStatisticByArea',
      method: 'get',
      controllerName: 'mfStatisticByArea',
      details: '按地区统计项目性质数据',
    },
    {
      url: 'medJGProjectFinishAudit/mfStatisticByZLType',
      method: 'get',
      controllerName: 'mfStatisticByZLType',
      details: '按项目诊疗类别统计项目性质数据',
    },
    {
      url: 'medJGProjectFinishAudit/mfStatisticByHazardType',
      method: 'get',
      controllerName: 'mfStatisticByHazardType',
      details: '按放射性危害类别统计项目性质数据',
    },


    {
      url: 'medJGPreEvaluate/agreePreEvaluateApply',
      method: 'post',
      controllerName: 'agreePreEvaluateApply',
      details: '通过医疗预评价审批',
    },
    {
      url: 'medJGPreEvaluate/refusePreEvaluateApply',
      method: 'post',
      controllerName: 'refusePreEvaluateApply',
      details: '驳回医疗预评价审批',
    },


    {
      url: 'medJGProjectFinishAudit/agreeFinishApply',
      method: 'post',
      controllerName: 'agreeFinishApply',
      details: '通过医疗竣工审批',
    },
    {
      url: 'medJGProjectFinishAudit/refuseFinishApply',
      method: 'post',
      controllerName: 'refuseFinishApply',
      details: '驳回医疗竣工审批',
    },
    {
      url: 'medJGPreEvaluate/getPreEvaluateDocUrl',
      method: 'get',
      controllerName: 'getPreEvaluateDocUrl',
      details: '获取预评价审批URL',
    },
    {
      url: 'medJGProjectFinishAudit/getFinishAuditDocUrl',
      method: 'get',
      controllerName: 'getFinishAuditDocUrl',
      details: '获取医疗审核审批URL',
    },
    {
      url: 'medJGProjectFinishAudit/getRefuseReason',
      method: 'get',
      controllerName: 'getRefuseReason',
      details: '获取医疗项目竣工驳回原因',
    },
    {
      url: 'medJGPreEvaluate/getRefuseReason',
      method: 'get',
      controllerName: 'getRefuseReason',
      details: '获取医疗预评价驳回原因',
    },
    // 建设项目防护设施预评价
    {
      url: 'jgContructProjectManage/getContructproEquRecordList',
      method: 'get',
      controllerName: 'getContructproEquRecordList',
      details: '获取建设项目防护设施备案列表',
    },
    {
      url: 'jgContructProjectManage/getContructproEquRecordDetail',
      method: 'get',
      controllerName: 'getContructproEquRecordDetail',
      details: '获取建设项目防护设施备案详情数据',
    },

    // 过程
    {
      url: 'jgContructProjectManage/getContructProgressRecordList',
      method: 'get',
      controllerName: 'getContructProgressRecordList',
      details: '获取过程备案列表',
    },
    {
      url: 'jgContructProjectManage/getContructProgressRecordDetail',
      method: 'get',
      controllerName: 'getContructProgressRecordDetail',
      details: '获取过程备案详情数据',
    },
    {
      url: 'jgContructProjectManage/getContructProEquStatisticData',
      method: 'get',
      controllerName: 'getContructProEquStatisticData',
      details: '获取建设项目防护设施备案统计',
    },
    {
      url: 'jgContructProjectManage/getContructControlStatisticData',
      method: 'get',
      controllerName: 'getContructControlStatisticData',
      details: '获取建设项目控制效果过程备案统计',
    },

    {
      url: 'jgContructProjectManage/getAreaRecordCountRank',
      method: 'get',
      controllerName: 'getAreaRecordCountRank',
      details: '获取兵团师市团镇建设项目的备案数并进行排名',
    },

    // 大数据展示-项目性质相关
    {
      url: 'jgContructProjectManage/getTypeRecordCountes',
      method: 'get',
      controllerName: 'getTypeRecordCountes',
      details: '获取项目性质项目个数和占比',
    },

    // 大数据展示-项目性质-地区分布情况
    {
      url: 'jgContructProjectManage/getTypeRecordAreaCount',
      method: 'get',
      controllerName: 'getTypeRecordAreaCount',
      details: '获取项目性质地区分布',
    },

    // 大数据展示-危害风险等级
    {
      url: 'jgContructProjectManage/getRiskRecordCount',
      method: 'get',
      controllerName: 'getRiskRecordCount',
      details: '获取项目风险等级项目个数和占比',
    },

    // 大数据展示-危害风险等级-地区分布情况
    {
      url: 'jgContructProjectManage/getRiskRecordAreaCount',
      method: 'get',
      controllerName: 'getRiskRecordAreaCount',
      details: '获取项目性质项目地区数目',
    },

    // 大数据展示-项目类型
    {
      url: 'jgContructProjectManage/getContructProjectCategory',
      method: 'get',
      controllerName: 'getContructProjectCategory',
      details: '获取项目类型个数和占比，饼图',
    },

    // 大数据展示- 验收单位地址分布
    {
      url: 'jgContructProjectManage/checkUnitDistribution',
      method: 'get',
      controllerName: 'checkUnitDistribution',
      details: '建设项目备案验收单位分布',
    },

    // 大数据展示- 参与验收人员/职责分工
    {
      url: 'jgContructProjectManage/checkPeopleAndFY',
      method: 'get',
      controllerName: 'checkPeopleAndFY',
      details: '建设项目参与验收人员/职责分工',
    },

     // 大数据展示 - 各职责分工人员详情
    {
      url: 'jgContructProjectManage/getPersonDetailUnderFY',
      method: 'get',
      controllerName: 'getPersonDetailUnderFY',
      details: '各职责分工人员详情',
    },

    // 大数据展示- 汇报答疑数等信息
    {
      url: 'jgContructProjectManage/answerAndReportCount',
      method: 'get',
      controllerName: 'answerAndReportCount',
      details: '汇报答疑数等信息',
    },
    
    // 大数据展示 - 各备案结果的项目数
    {
      url: 'jgContructProjectManage/recordTypeCount',
      method: 'get',
      controllerName: 'recordTypeCount',
      details: '各备案结果的建设项目数',
    },

    // 大数据展示 - 落实整改建设项目数
    {
      url: 'jgContructProjectManage/implementRectification',
      method: 'get',
      controllerName: 'implementRectification',
      details: '落实整改建设项目数',
    },

    // 大数据展示-各评审意见建设项目数
    {
      url: 'jgContructProjectManage/prevaluteCheckCount',
      method: 'get',
      controllerName: 'prevaluteCheckCount',
      details: '各评审意见建设项目数',
    },
  ],
  fontApi: [],

  initData: '', // 初始化数据脚本
  pluginsConfig: `
    exports.jk_Radiation_Therapy = {\n
        enable: true,\n        package: 'egg-jk-radiationTherapy',
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
    radiationTherapyRouter:{\n
      match: [ctx => ctx.path.startsWith('/manage/radiationTherapy'), ctx => ctx.path.startsWith('/api/radiationTherapy')],,\n
    },\n
    `,
  // 插入到 config.default.js 中的配置
  security: {
    csrf: {
      ignoreJSON: true,
    },
    // 白名单
    // domainWhiteList: ['*'],
  },
};

