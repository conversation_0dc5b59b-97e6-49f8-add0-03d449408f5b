const Service = require('egg').Service;
const path = require('path');
const fs = require('fs');
class KqyFollowUpService extends Service {

  async getoccupationalDiseaselist() {
    const { ctx } = this;
    try {
      const departments = await ctx.model.OccupationalDisease.find({});
      return departments;
    } catch (error) {
      ctx.logger.error('Failed to fetch OccupationalDisease:', error);
      throw new Error('Failed to fetch OccupationalDisease');
    }
  }
  async getPatientlist(params) {
    const { ctx } = this;
    try {
      console.log(params,'params');
      
      // 参数校验
      if (!params.PatientName) {
        return await ctx.model.User.find({}); // 若未传参，返回所有数据（或抛错）
      }
  
      // 模糊查询：不区分大小写
      const regex = new RegExp(params.PatientName, 'i');
      const patients = await ctx.model.User.find({
        name: { $regex: regex },
      });
      return patients;
    } catch (error) {
      ctx.logger.error('Failed to fetch patients:', error);
      throw new Error('Failed to fetch patients');
    }
  }
  async add(data) {
    const { ctx } = this;
    try {
      console.log(data,'data');
      data.area_code=ctx.session.superUserInfo.area_code
      const newData = new ctx.model.KqyFollowUp(data);
      const savedData = await newData.save();
      return savedData;
    } catch (error) {
      ctx.logger.error('Failed to add KqyFollowUp:', error);
      throw new Error('Failed to add KqyFollowUp');
    }
  }

  async getlist(params) {
    const { ctx } = this;
    try {
      const { pageNum = 1, pageSize = 10, ...rawQuery } = params;
      console.log(rawQuery,'rawQueryrawQuery');
      // 1. 动态构建查询条件
      const query = {};
      let code=await ctx.service.dashboard.getSubArea(ctx.session.superUserInfo.area_code)
      code= Array.isArray(code)? code.map(item=>{return item.area_code}) : code
      query.area_code = { $in: [
        ...(Array.isArray(ctx.session.superUserInfo.area_code)
            ? ctx.session.superUserInfo.area_code
            : ctx.session.superUserInfo.area_code
              ? [ctx.session.superUserInfo.area_code]
              : []),
        ...code
      ]};
      const allowedFields = ['state', 'PatientName', 'submitDepartment','AcceptingDepartment']; // 允许查询的字段
      Object.keys(rawQuery).forEach(key => {
        if (allowedFields.includes(key) && rawQuery[key] !== undefined && rawQuery[key] !== '') {
          // 根据字段类型处理查询条件
          if (key === 'PatientName') {
            query[key] = { $regex: rawQuery[key], $options: 'i' }; // 文本字段模糊匹配
          } else if(key==='state'){
            query[key] = parseInt(rawQuery[key]); // 确保 state 是数字
          }else{
            query[key] = rawQuery[key];
          }
        }
      });

      // 2. 分页查询
      const skip = (pageNum - 1) * pageSize;
      const list = await ctx.model.KqyFollowUp.find(query)
        .skip(skip)
        .limit(parseInt(pageSize))
        .populate('AcceptingDepartment', 'cname')
        .populate('submitDepartment', 'cname')
        .populate('Patient', 'name idNo gender occupationalDisease')
        .lean();

      // 3. 总数统计
      const total = await ctx.model.KqyFollowUp.countDocuments(query);
      const filePath = path.join('/static', this.config.upload_http_path, 'kqyFollowUp');

      const resList=list.map(item=>{
        // 创建对象副本避免修改原文档
        const newItem = item.toObject ? item.toObject() : { ...item };

        // 安全处理 material 数组
        if (Array.isArray(newItem.material) && newItem.material.length > 0) {
          newItem.material = newItem.material.map(file => 
            file ? path.join(filePath, file) : ''
          ).filter(Boolean); // 过滤空路径
        } else {
          newItem.material = []; // 统一返回数组
        }

        return newItem;
      })
      return { resList, total };
    } catch (error) {
      ctx.logger.error('查询联合执法记录失败:', error);
      throw new Error('查询失败');
    }
  }

  async updateById(_id, data) {
    const { ctx } = this;
    
    // 1. 提取允许更新的字段
    const updateFields = {};
    if (data.PatientName !== undefined) {
      updateFields.PatientName = data.PatientName;
    }
    if (data.occupationalDisease !== undefined) {
      updateFields.occupationalDisease = data.occupationalDisease;
    }
    if (data.AcceptingDepartment !== undefined) {
      updateFields.AcceptingDepartment = data.AcceptingDepartment;
    }
  
    // 2. 执行更新
    return ctx.model.KqyFollowUp.findByIdAndUpdate(
      _id,
      { $set: updateFields },
      { new: true } // 返回更新后的文档
    );
  }
  async deleteById(_id) {
    const { ctx } = this;
    const item = await ctx.model.KqyFollowUp.findOne({ _id });
    const res = await ctx.model.KqyFollowUp.deleteOne({ _id });
    if (res.deletedCount > 0 && item.material && item.material.length > 0) {
      for (const file of item.material) {
        const filePath = path.join(this.app.config.upload_path, 'KqyFollowUp', file);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      }
    }
    return res;
  }
  // 处理反馈逻辑
  async feedbackwithfile(data) {
    // 1. 数据预处理
    const updateFields = {
      ...data,
      feedbackTime: new Date(),
      state: 1
    };
    // 2. 数据库操作
    const result = await this.ctx.model.KqyFollowUp.findByIdAndUpdate(
      updateFields._id,
      { $set: updateFields },
      { new: true}
    );

    if (!result) {
      throw new Error('未找到对应记录');
    }
    return result;
  }
  // 处理反馈逻辑
  async feedback(_id,feedbackData) {
    // 1. 数据预处理
    const updateFields = {
      ...feedbackData,
      feedbackTime: new Date(),
      state: 1
    };
    // 2. 数据库操作
    const result = await this.ctx.model.KqyFollowUp.findByIdAndUpdate(
      _id,
      { $set: updateFields },
      { new: true}
    );

    if (!result) {
      throw new Error('未找到对应记录');
    }
    return result;
  }
}

module.exports = KqyFollowUpService;
