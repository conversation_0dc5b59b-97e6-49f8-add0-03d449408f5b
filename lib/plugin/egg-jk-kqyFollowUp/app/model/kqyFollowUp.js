module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const kqyFollowUpSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    Patient:{ //患者id
      type: String,
      ref:'User'
    },
    PatientName:String,//患者名称
    occupationalDisease:String,//职业病
    AcceptingDepartment:{
      type: String,
      ref:'SuperUser'
    },//接受部门
    state:Number, //案件状态
    submitBy:String, //报送人
    followUpUnit:String, //随访单位
    followUpUser:String, //随访人员
    followUpDate:String, //随访日期
    followUpContent:String, //随访内容
    followUpResults:String, //随访结果
    material:[String], //材料
    feedbackBy:String, //反馈人
    feedbackTime:String, //反馈时间
    area_code:[String],//发起方机构区域编码
    submitDepartment:{
      type: String,
      ref:'SuperUser'
    } //报送部门
  }, { timestamps: true });

  return mongoose.model('kqyFollowUp', kqyFollowUpSchema, 'kqyFollowUp');
};
