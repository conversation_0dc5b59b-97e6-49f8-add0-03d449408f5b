module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const kqyInvestigationSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    Employer:{ //用人单位
      type: String,
    },
    AcceptingDepartment:{
      type: String,
      ref:'SuperUser'
    },//接受部门
    state:Number, //案件状态
    submitBy:String, //报送人
    InvestigationOrg:String, //调查机构
    InvestigationTime:String, //调查时间
    InvestigationResult:String, //调查结果
    feedbackBy:String, //反馈人
    feedbackTime:String, //反馈时间
    area_code:[String],//发起方机构区域编码
    submitDepartment:{
      type: String,
      ref:'SuperUser'
    } //报送部门
  }, { timestamps: true });

  return mongoose.model('kqyInvestigation', kqyInvestigationSchema, 'kqyInvestigation');
};
