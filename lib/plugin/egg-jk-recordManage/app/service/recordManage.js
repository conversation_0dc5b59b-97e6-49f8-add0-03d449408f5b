const Service = require('egg').Service;

class RecordManageService extends Service {
  async checkupRecord(params) {
    try {
      const { ctx } = this;
      const { regAdd } = ctx.session.superUserInfo;
      const query = {
        $match: {
          submit: true,
          org_regAdd: { $all: regAdd },
        },
      };
      const query2 = {
        submit: true,
        org_regAdd: { $all: regAdd },
      }
      if (params.type) {
        if (params.type === '1') {
          query.$match.recordType = '首次备案';
          query2.recordType = '首次备案';
        }

        if (params.type === '2') {
          query.$match.recordType = '变更备案';
          query2.recordType = '变更备案';
        }
      }
      if (params.unitNature) {
        query.$match.unitNature = {
          $regex: params.unitNature,
        };
        query2.unitNature = {
          $regex: params.unitNature,
        };
      }
      if (params.institution) {
        query.$match.institution = {
          $regex: params.institution,
        };
        query2.institution = {
          $regex: params.institution,
        };
      }

      const res = await ctx.model.CheckRecord.aggregate([
        query,
        {
          $skip: (Number(params.curPage) - 1) * Number(params.limit),
        },
        {
          $limit: Number(params.limit),
        },
        {
          $lookup: {
            from: 'physicalExamOrgs',
            localField: 'org_id',
            foreignField: '_id',
            as: 'physicalExamOrgs',
          },
        },
        { $unwind: '$physicalExamOrgs' },
        { $addFields: { regNo: '$physicalExamOrgs.regNo' } },
        {
          $project: {
            physicalExamOrgs: 0,
          },
        },
      ]);

      const total = await ctx.model.CheckRecord.countDocuments(query2);

      return {
        res,
        total,
      };
    } catch (error) {
      throw new Error(error);
    }
  }
  processFileUrls(record, fieldName) {
    const { upload_http_record_path, domainNames } = this.app.config;
    const staticBase = `${domainNames.tj}/static${upload_http_record_path}`;

    if (
      record &&
          record[fieldName] &&
          Array.isArray(record[fieldName]) &&
          record[fieldName].length > 0
    ) {
      record[fieldName].forEach(item => {
        if (item.url && item.url.indexOf(staticBase) === -1) {
          item.url = `${staticBase}/${item.url}`;
        }
      });
    }
  }

  // getRecordDetail
  async getRecordDetail(params) {
    try {
      const { ctx } = this;
      // const record = await ctx.model.CheckRecord.findOne({ _id: params.id })
      const records = await ctx.model.CheckRecord.aggregate([
        {
          $match: {
            _id: params.id,
          },
        },
        {
          $lookup: {
            from: 'physicalExamOrgs',
            localField: 'org_id',
            foreignField: '_id',
            as: 'physicalExamOrgs',
          },
        },
        { $unwind: '$physicalExamOrgs' },
        { $addFields: { regNo: '$physicalExamOrgs.regNo' } },
        {
          $project: {
            physicalExamOrgs: 0,
          },
        },
      ]);
      const record = records[0];

      const persons = await ctx.model.Technician.find({
        checkRecordId: params.id,
      });
      // 使用公共方法处理所有文件字段
      const fileFields = [
        'businessLicense',
        'practicingLicense',
        'diagnosisLicense',
        'qualityControl',
        'checkReport',
        'recordTable'
      ];
      
      fileFields.forEach(field => {
        this.processFileUrls(record, field);
      });

      const tools = await ctx.model.InspectionInstrument.find({
        checkRecordId: params.id,
      });
      return {
        record,
        persons,
        tools,
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async checkupPass(params) {
    try {
      const { ctx } = this;
      const { physicalExamGroupID } = ctx.app.config.groupID;
      const info = await ctx.model.CheckRecord.findOne({ _id: params.id });
      if (!info) { throw new Error('未找到对应的备案记录'); }

      // 更新备案状态
      await ctx.model.CheckRecord.updateOne(
        { _id: params.id },
        { $set: { recordStatus: '审核通过' } }
      );

      const updateData = {
        recordReviewStatus: true,
      };

      const physicalExamOrg = await ctx.model.PhysicalExamOrg.findOne({ _id: info.org_id });
      if (!physicalExamOrg) { throw new Error('未找到对应的体检机构'); }

      // 如果没有备案号则生成备案号
      if (!physicalExamOrg.regNo) {
        const curYear = new Date().getFullYear();
        // 正确构建正则表达式
        const regNoPattern = new RegExp(`^兵卫职检备字【${curYear}】第\\d+号$`);
        const regNoList = await ctx.model.PhysicalExamOrg.find({
          regNo: regNoPattern,
        }).sort({ regNo: 1 });
        let num = regNoList.length + 1 + '';
        while (num.length < 3) {
          num = '0' + num;
        }
        updateData.regNo = `兵卫职检备字【${curYear}】第${num}号`;
      }
      await ctx.model.PhysicalExamUser.updateMany({
        org_id: info.org_id,
      }, {
        $set: {
          group: physicalExamGroupID,
        },
      });

      const res = await ctx.model.PhysicalExamOrg.updateOne(
        { _id: info.org_id },
        { $set: updateData }
      );
      return res;
    } catch (error) {
      throw new Error(error);
    }
  }

  async checkupBack(params) {
    try {
      const { ctx } = this;
      const res = await ctx.model.CheckRecord.updateOne(
        { _id: params.id },
        { $set: { recordStatus: '不予备案', reviewComments: params.reason } }
      );
      return res;
    } catch (error) {
      throw new Error(error);
    }
  }

  // checkupFix
  async checkupFix(params) {
    try {
      const { ctx } = this;
      const res = await ctx.model.CheckRecord.updateOne(
        { _id: params.id },
        {
          $set: {
            recordStatus: '补正备案材料',
            reviewComments: params.content,
          },
        }
      );
      return res;
    } catch (error) {
      throw new Error(error);
    }
  }

  async checkupLogoff(params) {
    try {
      const { ctx } = this;
      const res = await ctx.model.CheckRecord.updateOne(
        { _id: params.id },
        {
          $set: {
            isLog: true,
            logReason: params.reason,
          },
        }
      );
      return res;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 统计
  async checkOrgList(query) {

    try {
      const { regAdd } = this.ctx.session.superUserInfo;
      const pipe = {
        $match: {
          recordReviewStatus: true,
          regAddr: { $all: regAdd },
        },
      };
      if (query.cname) {
        pipe.$match.name = {
          $regex: query.cname,
        };
      }
      const res = await this.ctx.model.PhysicalExamOrg.aggregate([pipe]);

      return res;
    } catch (error) {
      throw new Error(error.message);
    }
  }
}

module.exports = RecordManageService;
