exports.jk_checkLawEnforcementData = {
  alias: 'checkLawEnforcementData', // 插件目录
  pkgName: 'egg-jk-checkLawEnforcementData', // 插件包名
  enName: 'jk_checkLawEnforcementData', // 插件名
  name: '职业健康大数据深度开发及展示利用-监督检查、行政执法数据展示 模块', // 插件名称
  description: '职业健康大数据深度开发及展示利用-监督检查、行政执法数据展示后端', // 插件描述
  adminApi: [
  { url: 'checkLawEnforcementData/getRegionList', method: 'get', controllerName: 'getRegionList', details: '获取区域列表' },
  { url: 'checkLawEnforcementData/getCarryOut',   method: 'get', controllerName: 'getCarryOut',   details: '监督执法开展情况' },
  { url: 'checkLawEnforcementData/getLocation',   method: 'get', controllerName: 'getLocation',   details: '执法地点分布情况' },
  { url: 'checkLawEnforcementData/getProject',    method: 'get', controllerName: 'getProject',    details: '监督检查项目情况' },
  { url: 'checkLawEnforcementData/getResults',    method: 'get', controllerName: 'getResults',    details: '行政执法结果情况' },
  ],
  pluginsConfig: ` 
    exports.jk_checkLawEnforcementData = {\n
        enable: true,\n        package: 'egg-jk-checkLawEnforcementData',
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
    checkLawEnforcementDataRouter:{\n
        match: [ctx => ctx.path.startsWith('/manage/checkLawEnforcementData')],\n
    },\n
    `, // 插入到 config.default.js 中的配置
};