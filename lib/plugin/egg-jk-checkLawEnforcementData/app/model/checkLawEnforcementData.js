/**
 * @file CheckLawEnforcementData 模型
 * @description 职业健康大数据深度开发及展示利用-监督检查、行政执法数据展示实体
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const nanoid = require('nanoid');
  
  const CheckLawEnforcementDataSchema = new Schema({
    _id: {
      type: String,
      required: [true, '_id不能为空'],
      default: () => nanoid(8),
    },
    name: {
      type: String,
      required: [true, 'name不能为空'],
    },

    state: {
      type: String,
      default: '1', // 1正常，0删除
      enum: ['0', '1'],
    },
  }, {
    timestamps: true,
    versionKey: false,
    collection: 'checkLawEnforcementData',
    minimize: false,
    strict: true
  });
  

  // 创建索引
  CheckLawEnforcementDataSchema.index({ name: 1 });

  
  // 查询中间件 - 只查询正常数据
  CheckLawEnforcementDataSchema.pre('find', function() {
    this.where({ state: '1' });
  });
  
  CheckLawEnforcementDataSchema.pre('findOne', function() {
    this.where({ state: '1' });
  });
  
  return mongoose.model('CheckLawEnforcementData', CheckLawEnforcementDataSchema);
}; 