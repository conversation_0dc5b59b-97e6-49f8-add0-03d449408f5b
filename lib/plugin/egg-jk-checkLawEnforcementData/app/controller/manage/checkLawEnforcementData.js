/**
 * @controller CheckLawEnforcementData 管理
 */

const CheckLawEnforcementDataController = {
  // 区域列表
  async getRegionList(ctx) {
    try {
      console.log(ctx.session,'ctx.session');
      const params = ctx.query;
      params.area_code=ctx.session.superUserInfo.area_code;
      const res = await ctx.service.checkLawEnforcementData.getRegionList(params);
      ctx.helper.renderSuccess(ctx, { data: res, status: 200 });
    } catch (err) {
      ctx.helper.renderFail(ctx, { message: err });
    }
  },

  // 监督执法开展情况
  async getCarryOut(ctx) {
    try {
      const params = ctx.query;
      const res = await ctx.service.checkLawEnforcementData.getCarryOut(params);
      ctx.helper.renderSuccess(ctx, { data: res, status: 200, message: 'success' });
    } catch (err) {
      ctx.helper.renderFail(ctx, { message: err });
    }
  },

  // 执法地点分布情况
  async getLocation(ctx) {
    try {
      const params = ctx.query;
      const res = await ctx.service.checkLawEnforcementData.getLocation(params);
      ctx.helper.renderSuccess(ctx, { data: res, status: 200, message: 'success' });
    } catch (err) {
      ctx.helper.renderFail(ctx, { message: err });
    }
  },

  // 监督检查项目情况
  async getProject(ctx) {
    try {
      const params = ctx.query;
      const res = await ctx.service.checkLawEnforcementData.getProject(params);
      ctx.helper.renderSuccess(ctx, { data: res, status: 200, message: 'success' });
    } catch (err) {
      ctx.helper.renderFail(ctx, { message: err });
    }
  },

  // 行政执法结果情况
  async getResults(ctx) {
    try {
      const params = ctx.query;
      const res = await ctx.service.checkLawEnforcementData.getResults(params);
      ctx.helper.renderSuccess(ctx, { data: res, status: 200, message: 'success' });
    } catch (err) {
      ctx.helper.renderFail(ctx, { message: err });
    }
  },

  // 示例: 列表
  async getlist(ctx) {
    try {
      const params = ctx.query;
      const res = await ctx.service.checkLawEnforcementData.getlist(params);
      ctx.helper.renderSuccess(ctx, { data: res, status: 200, message: 'success' });
    } catch (err) {
      ctx.helper.renderFail(ctx, { message: err });
    }
  },
};

module.exports = CheckLawEnforcementDataController;