/**
 * @service CheckLawEnforcementData 服务
 */
const Service = require('egg').Service;

class CheckLawEnforcementDataService extends Service {
  // 以下为按“示例”方式实现的 5 个转发方法
  async getRegionList(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `https://xjbtportal.jkqy.cn/api/dictManage/getValueChain?key=district_code&value=${params.area_code}`,
        { method: 'get', dataType: 'json'}
      );      
      return data.data;
    } catch (error) {
      throw new Error('查询失败');
    }
  }

  async getCarryOut(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.xjbtjdglHost}/check/getCarryOut`,
        { method: 'POST', dataType: 'json', data: params }
      );
      console.log(data,'data');
      
      if (data.code !== 0) throw new Error(data.message);
      return data.data;
    } catch (error) {
      throw new Error('查询失败');
    }
  }

  async getLocation(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.xjbtjdglHost}/check/getLocation`,
        { method: 'POST', dataType: 'json', data: params }
      );
      if (data.code !== 0) throw new Error(data.message);
      return data.data;
    } catch (error) {
      throw new Error('查询失败');
    }
  }

  async getProject(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.xjbtjdglHost}/check/getProject`,
        { method: 'POST', dataType: 'json', data: params }
      );
      if (data.code !== 0) throw new Error(data.message);
      return data.data;
    } catch (error) {
      throw new Error('查询失败');
    }
  }

  async getResults(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.xjbtjdglHost}/check/getResults`,
        { method: 'POST', dataType: 'json', data: params }
      );
      if (data.code !== 0) throw new Error(data.message);
      return data.data;
    } catch (error) {
      throw new Error('查询失败');
    }
  }
}

module.exports = CheckLawEnforcementDataService;