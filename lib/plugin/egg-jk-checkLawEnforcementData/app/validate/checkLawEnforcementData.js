/**
 * @file CheckLawEnforcementData 验证规则
 * @description 定义职业健康大数据深度开发及展示利用-监督检查、行政执法数据展示相关接口的参数验证规则
 */
module.exports = app => {
  const { validator } = app;

  // 创建职业健康大数据深度开发及展示利用-监督检查、行政执法数据展示
  validator.addRule('createCheckLawEnforcementData', {
    name: {
      type: 'string',
      required: true,
      message: '名称不能为空',
    },
  });

  // 更新职业健康大数据深度开发及展示利用-监督检查、行政执法数据展示
  validator.addRule('updateCheckLawEnforcementData', {
    _id: {
      type: 'string',
      required: true,
      message: 'ID不能为空',
    },
  });

  // 删除职业健康大数据深度开发及展示利用-监督检查、行政执法数据展示
  validator.addRule('deleteCheckLawEnforcementData', {
    ids: {
      type: 'array',
      required: true,
      message: '请选择要删除的记录',
    },
  });

  // 获取详情
  validator.addRule('getCheckLawEnforcementDataDetail', {
    id: {
      type: 'string',
      required: true,
      message: 'ID不能为空',
    },
  });

  // 获取列表
  validator.addRule('getCheckLawEnforcementDataList', {
    currentPage: {
      type: 'int',
      convertType: 'int',
      required: false,
      default: 1,
    },
    pageSize: {
      type: 'int',
      convertType: 'int',
      required: false,
      default: 10,
    },
    keyWords: {
      type: 'string',
      required: false,
    },
  });
}; 