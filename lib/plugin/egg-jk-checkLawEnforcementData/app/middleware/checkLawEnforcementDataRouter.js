/**
 * @file 职业健康大数据深度开发及展示利用-监督检查、行政执法数据展示模块中间件
 * @description 处理职业健康大数据深度开发及展示利用-监督检查、行政执法数据展示模块相关路由请求
 */

const checkLawEnforcementDataController = require('../controller/manage/checkLawEnforcementData');

module.exports = (options, app) => {
  return async function checkLawEnforcementDataRouter(ctx, next) {
    const pluginConfig = app.config.jk_checkLawEnforcementData;
    
    if (ctx.request.url.startsWith('/manage/checkLawEnforcementData')) {
      await app.initPluginRouter(ctx, pluginConfig, checkLawEnforcementDataController);
    }
    await next();
  };
};
