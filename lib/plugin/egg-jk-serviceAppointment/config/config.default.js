exports.jk_serviceAppointment = {
  alias: 'serviceAppointment', // 插件目录
  pkgName: 'egg-jk-serviceAppointment', // 插件包名
  enName: 'jk_serviceAppointment', // 插件名
  name: '职业卫生检测线上预约 模块', // 插件名称
  description: '各级管理机构可对所管辖的企业线上预约职业卫生检测的情况进行查询，一方面了解企业进行职业卫生检测的情况以及便利性，另一方面可以了解职业卫生检测机构的业务情况。', // 插件描述
  adminApi: [{
    url: 'serviceAppointment/getList',
    method: 'get',
    controllerName: 'getList',
    details: '获取职业卫生检测线上预约列表',
  }],
  pluginsConfig: ` 
    exports.jk_serviceAppointment = {\n
        enable: true,\n        package: 'egg-jk-serviceAppointment',
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
    serviceAppointmentRouter:{\n
        match: [ctx => ctx.path.startsWith('/manage/serviceAppointment')],\n
    },\n
    `, // 插入到 config.default.js 中的配置
};
