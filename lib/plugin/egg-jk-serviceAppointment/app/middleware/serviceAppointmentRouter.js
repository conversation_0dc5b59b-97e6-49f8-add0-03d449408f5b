/**
 * @file 职业卫生检测线上预约模块中间件
 * @description 处理职业卫生检测线上预约模块相关路由请求
 */

const serviceAppointmentController = require('../controller/manage/serviceAppointment');

module.exports = (options, app) => {
  return async function serviceAppointmentRouter(ctx, next) {
    const pluginConfig = app.config.jk_serviceAppointment;
    
    if (ctx.request.url.startsWith('/manage/serviceAppointment')) {
      await app.initPluginRouter(ctx, pluginConfig, serviceAppointmentController);
    }
    await next();
  };
};
