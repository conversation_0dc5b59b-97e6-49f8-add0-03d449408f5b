/**
 * @file ServiceAppointment 验证规则
 * @description 定义职业卫生检测线上预约相关接口的参数验证规则
 */
module.exports = app => {
  const { validator } = app;

  // 创建职业卫生检测线上预约
  validator.addRule('createServiceAppointment', {
    name: {
      type: 'string',
      required: true,
      message: '名称不能为空',
    },
  });

  // 更新职业卫生检测线上预约
  validator.addRule('updateServiceAppointment', {
    _id: {
      type: 'string',
      required: true,
      message: 'ID不能为空',
    },
  });

  // 删除职业卫生检测线上预约
  validator.addRule('deleteServiceAppointment', {
    ids: {
      type: 'array',
      required: true,
      message: '请选择要删除的记录',
    },
  });

  // 获取详情
  validator.addRule('getServiceAppointmentDetail', {
    id: {
      type: 'string',
      required: true,
      message: 'ID不能为空',
    },
  });

  // 获取列表
  validator.addRule('getServiceAppointmentList', {
    currentPage: {
      type: 'int',
      convertType: 'int',
      required: false,
      default: 1,
    },
    pageSize: {
      type: 'int',
      convertType: 'int',
      required: false,
      default: 10,
    },
    keyWords: {
      type: 'string',
      required: false,
    },
  });
}; 