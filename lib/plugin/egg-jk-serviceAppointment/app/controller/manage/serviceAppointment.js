/**
 * @controller ServiceAppointment 管理
 */
const moment = require('moment');

const ServiceAppointmentController = {
  /**
   * @summary 获取职业卫生检测线上预约列表
   * @description 获取职业卫生检测线上预约列表，支持分页和搜索
   * @return {Promise<void>} 无返回值
   */
  async getList(ctx) {
    try {
      const { current = 1, pageSize = 10, searchkey, isPaging, skip, projectName, appointmentDate } = ctx.query;
      const query = {};
      const { regAddr } = ctx.queries;
      // const { EnterpriseID } = ctx.session.adminUserInfo;
      // query.EnterpriseID = EnterpriseID;
      
      // 处理申请日期筛选
      // if (applyTime) {
      //   const startOfDay = moment(applyTime).startOf('day').toDate();
      //   const endOfDay = moment(applyTime).endOf('day').toDate();
      //   query.applyTime = {
      //     $gte: new Date(startOfDay),
      //     $lte: new Date(endOfDay),
      //   };
      // }
      
      // 处理预约日期筛选
      if (appointmentDate) {
        const startOfDay = moment(appointmentDate).startOf('day').toDate();
        const endOfDay = moment(appointmentDate).endOf('day').toDate();
        query.appointmentDate = {
          $gte: new Date(startOfDay),
          $lte: new Date(endOfDay),
        };
      }
      
      // 处理项目名称筛选
      if (projectName) {
        query.projectName = { $regex: projectName, $options: 'i' };
      }
      
      // 处理机构服务地区筛选
      let regAddrList = [];
      if (regAddr && regAddr.length) {
        regAddrList = await ctx.service.talentPool.getAreaNames(regAddr[0]);
      } else {
        const { regAdd } = ctx.session.superUserInfo;
        regAddrList = regAdd;
      }
      const enterpriseId = await ctx.model.Adminorg.aggregate([
        { $match: { districtRegAdd: { $all: regAddrList } } },
        { $project: { _id: 1 } },
      ]);
      query.EnterpriseID = { $in: enterpriseId.map(item => item._id) };

      const payload = {
        current,
        pageSize,
        searchkey,
        isPaging,
        skip,
      };
      const result = await ctx.service.serviceAppointment.getAppointmentList(payload, {
        query,
        sort: {
          updatedAt: -1,
        },
        populate: [
          {
            path: 'initiator',
            select: 'name phoneNum',
          },
          {
            path: 'EnterpriseID',
            select: 'cname contract phoneNum districtRegAdd',
          },
          {
            path: 'serviceOrgId',
            select: 'name organization',
          },
          {
            path: 'auditId',
            select: 'action submitorId remark operatorId',
            populate: [
              {
                path: 'submitorId',
                select: 'name phoneNum',
              },
              {
                path: 'operatorId',
                select: 'name phoneNum',
              },
            ],
          },
        ],
      });
      ctx.helper.renderSuccess(ctx, { data: result, message: '获取成功' });
    } catch (err) {
      ctx.helper.renderFail(ctx, { message: err.message });
    }
  },
};

module.exports = ServiceAppointmentController; 