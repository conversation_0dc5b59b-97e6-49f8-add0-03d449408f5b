/**
 * 检测预约
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const moment = require('moment');

  const ServiceAppointmentSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    // 项目名称
    projectName: {
      type: String,
      required: true,
    },
    // 用人单位
    EnterpriseID: {
      type: String,
      ref: 'Adminorg', // 用人单位
      required: true,
    },
    serviceOrgId: {
      type: String,
      ref: 'ServiceOrg', // 检测机构
      required: true,
    },
    // 单位联系人
    contact: {
      type: String,
      required: true,
    },
    // 单位联系人电话
    contactPhone: {
      type: String,
      required: true,
    },
    // 机构联系人
    serviceOrgContact: {
      type: String,
      required: true,
    },
    // 机构联系人电话
    serviceOrgPhone: {
      type: String,
      required: true,
    },
    // 申请日期
    applyTime: {
      type: Date,
      default: Date.now,
    },
    // 状态
    status: {
      type: String,
      enum: [ 'pending', 'accepted', 'rejected', 'canceled' ],
      default: 'pending',
    },
    // 预约日期
    appointmentDate: {
      type: Date,
      required: true,
    },
    reason: {
      type: String, // 用人单位填写预约备注
    },
    contractId: {
      type: String,
      ref: 'EntrustClient', // 签订的合同
    },
    // 发起人
    initiator: {
      type: String,
      ref: 'AdminUser',
    },
    // 操作记录
    auditId: {
      type: String,
      ref: 'ServiceAppointmentAudit',
    },
  }, {
    timestamps: true,
    toJSON: { getters: true },
    toObject: { getters: true },
  });

  ServiceAppointmentSchema.path('updatedAt').get(function(v) {
    return moment(v).format('YYYY-MM-DD HH:mm:ss');
  });

  ServiceAppointmentSchema.path('createdAt').get(function(v) {
    return moment(v).format('YYYY-MM-DD HH:mm:ss');
  });

  ServiceAppointmentSchema.path('appointmentDate').get(function(v) {
    return moment(v).format('YYYY-MM-DD');
  });

  ServiceAppointmentSchema.path('applyTime').get(function(v) {
    return moment(v).format('YYYY-MM-DD');
  });

  return mongoose.model('ServiceAppointment', ServiceAppointmentSchema);

};

