/**
 * 检测预约操作记录
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const moment = require('moment');

  const ServiceAppointmentAuditSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    appointmentId: {
      type: String,
      ref: 'ServiceAppointment',
      required: true,
    },
    action: {
      type: String,
      enum: [ 'accept', 'reject', 'cancel', 'update', 'create' ],
      required: true,
    },
    // 审核人
    operatorId: {
      type: String,
      ref: 'JcUser',
    },
    // 提交人
    submitorId: {
      type: String,
      ref: 'AdminUser',
    },
    remark: {
      type: String,
    },
  }, {
    timestamps: true,
    toJSON: { getters: true },
    toObject: { getters: true },
  });

  ServiceAppointmentAuditSchema.path('updatedAt').get(function(v) {
    return moment(v).format('YYYY-MM-DD HH:mm:ss');
  });

  ServiceAppointmentAuditSchema.path('createdAt').get(function(v) {
    return moment(v).format('YYYY-MM-DD HH:mm:ss');
  });

  return mongoose.model('ServiceAppointmentAudit', ServiceAppointmentAuditSchema);
};
