/**
 * @service ServiceAppointment 服务
 */
const Service = require('egg').Service;
const path = require('path');
const { _list } = require(path.join(process.cwd(), 'app/service/general'));

class ServiceAppointmentService extends Service {
  /**
   * @summary 获取职业卫生检测线上预约列表
   * @description 根据筛选条件获取职业卫生检测线上预约列表
   * @return {Promise<Object>} 查询结果
   */

  async getAppointmentList(
    payload,
    {
      query = {},
      searchKeys = [],
      populate = [],
      files = null,
      sort = {
        updatedAt: -1,
      },
    } = {}
  ) {
    const listdata = _list(this.ctx.model.ServiceAppointment, payload, {
      query,
      searchKeys,
      populate,
      files,
      sort,
    });
    return listdata;
  }
}

module.exports = ServiceAppointmentService; 