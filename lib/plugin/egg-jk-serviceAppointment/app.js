/**
 * @file 实例管理插件入口文件
 * @description 插件初始化加载
 */
const path = require('path');

class AppBootHook {
  constructor(app) {
    this.app = app;
  }

  configWillLoad() {
    // 插件配置加载前执行
    const { app } = this;
    // 将中间件添加到应用中 - 修改为middleware而不是appMiddleware
    app.config.middleware.push('serviceAppointmentRouter');
  }

  async didLoad() {
    // 数据模型初始化 - 修正路径
    const modelsPath = path.join(__dirname, 'app/model');
    this.app.initExtendModel(modelsPath);
  }

  async willReady() {
    // 确保所有的插件都已启动
    const { app } = this;
    // 可以在这里添加数据库等初始化操作
    app.logger.info('[egg-jk-serviceAppointment] 插件初始化完成');
  }
}

module.exports = AppBootHook;