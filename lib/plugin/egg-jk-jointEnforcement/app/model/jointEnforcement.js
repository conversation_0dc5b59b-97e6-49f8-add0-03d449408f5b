module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 法律法规
  const jointEnforcementSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    caseInformation:{ //案件信息
      type: String,
    },
    AcceptingDepartment:{
      type: String,
      ref:'SuperUser'
    },//接受部门
    state:Number, //案件状态
    submitBy:String, //报送人
    enforcementOrg:String, //执法机构
    enforcementTime:String, //执法时间
    enforcementResult:String, //执法结果
    punish:String, //处罚
    material:[String], //材料
    feedbackBy:String, //反馈人
    feedbackTime:String, //反馈时间
    area_code:[String],//发起方机构区域编码
    submitDepartment:{
      type: String,
      ref:'SuperUser'
    } //报送部门
  }, { timestamps: true });

  return mongoose.model('jointEnforcement', jointEnforcementSchema, 'jointEnforcement');
};
