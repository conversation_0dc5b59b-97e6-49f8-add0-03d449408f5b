const crossRegionLawEnforcementShareController = {

  async getlist(ctx) {
    try {
      const params = ctx.query;
      let code = await ctx.service.dashboard.getSubArea(ctx.session.superUserInfo.area_code);
      code = Array.isArray(code) ? code.map(item => { return item.area_code; }) : code;
      params.zonecode = [
        ...(Array.isArray(ctx.session.superUserInfo.area_code)
          ? ctx.session.superUserInfo.area_code
          : ctx.session.superUserInfo.area_code
            ? [ ctx.session.superUserInfo.area_code ]
            : []),
        ...code,
      ];
      const res = await ctx.service.crossRegionLawEnforcementShare.getlist(
        params
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: 'success',
      });

    } catch (err) {
      // console.log(err,'err');
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },
};

module.exports = crossRegionLawEnforcementShareController;
