const path = require('path');
const shortid = require('shortid');
const moment = require('moment');
const fs = require('fs');
const awaitWriteStream = require('await-stream-ready').write;
const sendToWormhole = require('stream-wormhole');
const mkdirp = require('mkdirp');

const DefendProductsApiController = {
  async createTime(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.defendproducts.time(params);
    ctx.body =
      res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, status: 200, data: res };
  },

  async saveDefendProductList(ctx) {
    // 保存防护用品清单
    const { data, importType } = ctx.request.body;
    const message = await ctx.service.defendproducts.saveDefendProductList(data, importType);
    ctx.body = { code: 200, status: 200, data: message };
  },

  async getDefendProductList(ctx) {
    // 获取防护用品清单
    const data = ctx.request.body;
    const EnterpriseID = ctx.session.superUserInfo ? ctx.session.superUserInfo._id : '';
    const res = await ctx.service.defendproducts.getDefendProductList(data);
    // console.log(res, '将返回前端的数据');

    ctx.body = {
      code: 200,
      status: 200,
      data: res,
      staticUrl: '/static/' + ctx.app.config.upload_http_path + '/' + EnterpriseID + '/',
    };
  },

  async saveProtectionPlan(ctx) {
    // 保存防护用品发放计划
    const data = ctx.request.body;
    const message = await ctx.service.defendproducts.saveProtectionPlan(data);
    ctx.body = { code: 200, status: 200, data: message };
  },

  async getProtectionPlan(ctx) {
    // 获取防护用品发放计划
    const data = ctx.request.body;
    const stations = data.plan ? JSON.parse(data.plan) : '';
    let station = [];
    if (!stations) {
      station = '';
    } else if (stations.length > 0) {
      stations.forEach(ele => {
        if (ele.category === 'stations') {
          station.push(ele._id);
        }
      });
    }
    const res = await ctx.service.defendproducts.getProtectionPlan(station);
    ctx.body = { code: 200, status: 200, data: res };
  },
  async saveOneprotection(ctx) {
    const data = ctx.request.body;
    console.log(data, '传过来的数据');
    const res = await ctx.service.defendproducts.saveOneprotection(data.data);
    console.log(res, '返回的是啥？？？？');
    ctx.body = { code: 200, status: 200, data: res };
  },
  async delOneprotection(ctx) {
    const { categoryId, dataId } = ctx.request.body;
    const res = await ctx.service.defendproducts.delOneprotection({ categoryId, dataId });
    ctx.body = { code: 200, status: 200, data: res };
  },
  async savePageInfo(ctx) {
    const params = ctx.query;
    const res = await ctx.service.defendproducts.savePageInfo(params);
    ctx.body = { code: 200, status: 200, data: res };
  },
  async saveSingle(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.defendproducts.saveSingle(data);
    ctx.body = { code: 200, status: 200, data: res };
  },
  async createWord(ctx, app) {
    // 下载个人防护用品记录清单word
    const data = ctx.request.body.data;
    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      if (item && item.sign) {
        const sign = item.sign.split('/');
        item.sign = path.join(
          app.config.image_upload_path,
          sign[sign.length - 2],
          sign[sign.length - 1]
        );
      }
    }
    const fileNameSuffix = '';
    const templateFileName = '个人防护用品领用清单';
    // const data2 = data.
    const res = await ctx.helper.fillWord(
      ctx,
      templateFileName,
      JSON.parse(JSON.stringify({ data })),
      fileNameSuffix
    );
    ctx.body = { code: 200, status: 200, data: res };
  },
  async replenishment(ctx) {
    const replenishment = ctx.query.replenishment;
    const EnterpriseId = ctx.session.superUserInfo ? ctx.session.superUserInfo._id : '';
    const ppeSelfLiftCabinet = await ctx.model.PpeSelfLiftCabinet.findOne({ EnterpriseId });
    if (ppeSelfLiftCabinet && !replenishment) {
      ctx.helper.renderSuccess(ctx, {
        data: true,
        message: '此公司有ppe自提柜',
      });
      return;
    } else if (replenishment && ppeSelfLiftCabinet) {
      for (let i = 0; i < ppeSelfLiftCabinet.specifications.length; i++) {
        ppeSelfLiftCabinet.specifications[i].surplus =
          ppeSelfLiftCabinet.specifications[i].totality;
      }
      // await ctx.model.PpeSelfLiftCabinet.updateOne({ EnterpriseId }, { $set: { specifications: ppeSelfLiftCabinet.specifications } });
      await ctx.service.db.updateOne(
        'PpeSelfLiftCabinet',
        { EnterpriseId },
        { $set: { specifications: ppeSelfLiftCabinet.specifications } }
      );
    } else if (!ppeSelfLiftCabinet) {
      ctx.helper.renderSuccess(ctx, {
        data: false,
        message: '此公司无ppe自提柜',
      });
    }
  },
  async delCategory(ctx) {
    const _id = ctx.query._id;
    try {
      const EnterpriseID = ctx.session.superUserInfo ? ctx.session.superUserInfo._id : '';
      await ctx.model.ProtectiveSuppliesList.updateOne(
        { EnterpriseID },
        { $pull: { list: { _id } } }
      );
      ctx.helper.renderSuccess(ctx, {
        message: '删除成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async immediatePPEPlan(ctx) {
    try {
      const EnterpriseID = ctx.session.superUserInfo ? ctx.session.superUserInfo._id : '';
      await ctx.service.defendproducts.immediatePPEPlan(EnterpriseID);
      ctx.helper.renderSuccess(ctx, {
        message: '删除成功',
      });
    } catch (err) {
      console.log(err, '立即生成PPE计划错误');
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async delProtectionPlan(ctx) {
    const { ids } = ctx.request.body;
    const EnterpriseID = ctx.session.superUserInfo ? ctx.session.superUserInfo._id : '';

    await ctx.service.db.updateOne(
      'ProtectionPlan',
      { EnterpriseID },
      { $pull: { plan: { _id: { $in: ids } } } }
    );

    ctx.helper.renderSuccess(ctx, {
      message: '删除成功',
    });
  },

  async addProtectionPlan(ctx) {
    const { data } = ctx.request.body;
    const EnterpriseID = ctx.session.superUserInfo ? ctx.session.superUserInfo._id : '';

    console.log(3123, data);

    // 先判断是否存在发放计划
    const res = await ctx.model.ProtectionPlan.findOne({ EnterpriseID });
    if (!res) {
      await ctx.service.db.create('ProtectionPlan', { EnterpriseID });
    }

    const hasCompleteEmployee = [];

    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      const formData = item.formData;
      const startDate = moment(formData.startDate);
      const warningDate = moment(formData.startDate).add(5, 'd');

      const plan = {
        workshop: item.workshop,
        workshopName: item.workshopName,
        workspaces: item.workspaces,
        workspacesName: item.workspacesName,
        workstation: item.workstation,
        workstationName: item.workstationName,
        employee: item.employee,
        _id: shortid.generate(),
        products: formData.products,
        timeUnit: formData.timeUnit,
        time: formData.time,
        startDate,
        planStatus: 1,
      };

      await ctx.service.db.updateOne('ProtectionPlan', { EnterpriseID }, { $push: { plan } });

      plan.receiveStartDate = startDate;
      plan.EnterpriseID = EnterpriseID;
      plan.warningDate = warningDate;
      const completeRes = await ctx.service.receiveRecord.createReceiveRecordByPlan({
        plan,
        hasCompleteEmployee,
      });
      hasCompleteEmployee.push(...completeRes);
    }

    ctx.helper.renderSuccess(ctx, {
      data: {},
      message: '计划新建成功',
    });
  },

  async getReceiveRecordList(ctx) {
    const body = ctx.request.body;
    const res = await ctx.service.receiveRecord.getList({
      params: {
        startTime: body.startTime,
        endTime: body.endTime,
      },
      pagination: {
        current: body.current,
        pageSize: body.pageSize,
      },
    });
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '获取成功',
    });
  },
  async delReceiveRecord(ctx) {
    const { ids } = ctx.request.body;
    await ctx.service.receiveRecord.remove({ _id: { $in: ids } });
    ctx.helper.renderSuccess(ctx, {
      message: '删除成功',
    });
  },

  // 获取申请列表
  async getApplicationProducts(ctx) {
    try {
      console.log('进来了~2232');
      const params = ctx.request.body;
      console.log(params, 'params');
      const res = await ctx.service.applicationProduct.getList(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
      });
    } catch (err) {
      ctx.auditLog('获取申请列表失败:' + err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 改变申请状态
  async selectApplication(ctx) {
    try {
      console.log('进来了~selectApplication');
      const params = ctx.request.body;
      const res = await ctx.service.applicationProduct.selectApplication(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
      });
    } catch (err) {
      ctx.auditLog('改变申请状态失败:' + err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 导出领用记录
  async exportReceiveRecords(ctx) {
    try {
      const params = ctx.request.body;
      const res = await ctx.service.receiveRecord.exportReceiveRecords(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 异常整改
  // 创建异常整改记录
  async createAbnormalRectify(ctx) {
    try {
      const data = ctx.request.body;
      const result = await ctx.service.abnormalRectify.create(data);
      ctx.helper.renderSuccess(ctx, { data: result, message: '创建成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },
  // 查询异常整改记录（支持分页和条件查询）
  async abnormalRectifyList(ctx) {
    try {
      const { type, keyword, curPage, pageSize, startTime, endTime, wornCorrectly } =
        ctx.request.body || {};
      const pagination = {
        curPage: Number(curPage) || 1,
        pageSize: Number(pageSize) || 10,
      };
      const result = await ctx.service.abnormalRectify.list(
        { type, keyword, startTime, endTime, wornCorrectly },
        pagination
      );
      ctx.helper.renderSuccess(ctx, { data: result, message: '查询成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },
  // 更新异常整改记录
  async updateAbnormalRectify(ctx) {
    try {
      const { _id } = ctx.request.body;
      if (!_id) {
        return ctx.helper.renderFail(ctx, { message: '缺少必要参数_id' });
      }
      const data = ctx.request.body;
      const result = await ctx.service.abnormalRectify.update(_id, data);
      ctx.helper.renderSuccess(ctx, { data: result, message: '更新成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },
  // 删除异常整改记录
  async deleteAbnormalRectify(ctx) {
    try {
      const { ids } = ctx.request.body;
      const result = await ctx.service.abnormalRectify.delete(ids);
      ctx.helper.renderSuccess(ctx, { data: result, message: '删除成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },
  //
  // 上传整改文件
  async uploadFile(ctx, app) {
    let result = []; // 返回的结果
    let _id;
    let EnterpriseID = '';
    const parts = ctx.multipart({ autoFields: true });
    let part;
    let checkOldFiles = true;
    while ((part = await parts()) != null) {
      if (!part.filename) continue;
      if (!_id) {
        _id = parts.field._id;
        const item = await ctx.model.AbnormalRectify.findOne({ _id }, { EnterpriseID: 1 });
        if (!item) {
          return ctx.helper.renderFail(ctx, { message: '整改记录不存在' });
        }
        EnterpriseID = item.EnterpriseID;
        console.log(11111, EnterpriseID, '企业ID');
      }
      if (parts.field.oldFiles && checkOldFiles) {
        checkOldFiles = false;
        result = result.concat(parts.field.oldFiles.split(','));
      }
      const uploadPath = `${app.config.upload_path}/${EnterpriseID}`;
      const staticName =
        moment().format('YYYYMMDD') + shortid.generate() + path.extname(part.filename);
      const writePath = path.join(uploadPath, `/${staticName}`);
      if (!fs.existsSync(uploadPath)) {
        await mkdirp(uploadPath);
      }
      const writeStream = fs.createWriteStream(writePath);
      try {
        await awaitWriteStream(part.pipe(writeStream));
        result.push({
          originName: part.filename,
          staticName,
        });
      } catch (error) {
        // 如果出现错误，关闭管道,防止浏览器响应卡死
        await sendToWormhole(part);
        writeStream.destroy();
        throw error;
      }
    }
    if (result.length) {
      const res = await ctx.service.abnormalRectify.update(_id, {
        status: 1,
        $push: { files: result },
      });
      ctx.helper.renderSuccess(ctx, { data: res, message: '文件上传成功' });
    } else {
      ctx.helper.renderFail(ctx, { message: '文件无变动的情况下，请勿重新提交' });
    }
  },
  // 删除整改文件
  async delFile(ctx) {
    try {
      const { _id, url } = ctx.request.body;
      const item = await ctx.model.AbnormalRectify.findOne({ _id }, { files: 1 });
      if (!item) {
        return ctx.helper.renderFail(ctx, { message: '整改记录不存在' });
      }
      if (!url) {
        return ctx.helper.renderFail(ctx, { message: 'url不能为空' });
      }
      const files = item.files;
      const fileName = url.replace('/static/upload/enterprise', '');
      const staticName = fileName.split('/').pop();
      const filePath = path.join(ctx.app.config.upload_path, fileName);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
      const index = files.findIndex(file => file.staticName === staticName);
      if (index !== -1) {
        files.splice(index, 1);
        const res = await ctx.model.AbnormalRectify.updateOne({ _id }, { $set: { files } });
        ctx.helper.renderSuccess(ctx, { data: res, message: '删除成功' });
      } else {
        ctx.helper.renderFail(ctx, { message: '文件不存在' });
      }
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 新增巡查班次
  async createPatrolShift(ctx) {
    try {
      const data = ctx.request.body;
      data.createUnit = ctx.session.superUserInfo._id;
      if (!data.createUnit) {
        throw new Error('请先登录后再操作！');
      }
      const result = await ctx.model.PatrolShiftManage.create(data);
      ctx.service.abnormalRectify.msgNotice(result);
      ctx.helper.renderSuccess(ctx, { data: result, message: '新增成功' });
    } catch (error) {
      console.log(4444, error);
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 删除巡查班次
  async deletePatrolShift(ctx) {
    try {
      const { ids = [] } = ctx.request.body;
      if (!ids) {
        return ctx.helper.renderFail(ctx, { message: '缺少必要参数 ids' });
      }
      const result = await ctx.model.PatrolShiftManage.deleteMany({ _id: { $in: ids } });
      if (result.deletedCount > 0) {
        ctx.helper.renderSuccess(ctx, { message: '删除成功' });
      } else {
        ctx.helper.renderFail(ctx, { message: '删除失败，记录不存在' });
      }
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },
  // 更新巡查班次
  async updatePatrolShift(ctx) {
    try {
      const { _id, ...data } = ctx.request.body;
      if (!_id) {
        return ctx.helper.renderFail(ctx, { message: '缺少必要参数 _id' });
      }
      const result = await ctx.model.PatrolShiftManage.updateOne({ _id }, { $set: data });
      if (result.nModified > 0) {
        ctx.helper.renderSuccess(ctx, { message: '更新成功' });
      } else {
        ctx.helper.renderFail(ctx, { message: '更新失败，记录不存在或数据未修改' });
      }
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  // 查询巡查班次列表
  async getPatrolShiftList(ctx) {
    try {
      const { curPage = 1, pageSize = 10, keyword, status, type } = ctx.query;
      const query = { createUnit: ctx.session.superUserInfo._id, type };
      if (keyword) {
        query.name = { $regex: keyword, $options: 'i' }; // 模糊查询班次名称
      }
      if (status) {
        query.status = +status; // 按状态筛选
      }
      const result = await ctx.model.PatrolShiftManage.find(query)
        .skip((curPage - 1) * pageSize)
        .limit(Number(pageSize))
        .sort({ createdAt: -1 }); // 按创建时间倒序
      const total = await ctx.model.PatrolShiftManage.countDocuments(query);
      ctx.helper.renderSuccess(ctx, { data: { list: result, total }, message: '查询成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },
  // 职业健康大数据深度开发及展示利用/劳动防护数据展示
  async proAbnormalStatistics(ctx) {
    const superUserInfo = ctx.session.superUserInfo || {};
    if (!superUserInfo._id) {
      ctx.helper.renderFail(ctx, { message: '请先登录' });
      return;
    }
    const { areaCode } = ctx.query;
    const startTime = ctx.query.startTime ? new Date(ctx.query.startTime) : '';
    const endTime = ctx.query.endTime ? new Date(ctx.query.endTime) : '';
    const query = { isDelete: false, createUnit: superUserInfo._id, wornCorrectly: false };
    if (startTime) {
      if (endTime) {
        query.recordedAt = {
          $gte: new Date(startTime),
          $lte: new Date(endTime),
        };
      } else {
        query.recordedAt = {
          $gte: new Date(startTime),
        };
      }
    }
    const abnormalRectifyList = await ctx.model.AbnormalRectify.find({
      ...query,
      type: 1,
    }).populate('EnterpriseID', 'cname workAddress');
    const abnormalRectifyList2 = await ctx.model.AbnormalRectify.find({
      ...query,
      type: 2,
    }).populate('EnterpriseID', 'cname workAddress');
    console.log(abnormalRectifyList2);
    const subAreaList = await ctx.model.District.find(
      { parent_code: areaCode || superUserInfo.area_code },
      { name: 1 }
    );

    const result = [];
    for (let i = 0; i < subAreaList.length; i++) {
      const curAreaName = subAreaList[i].name;
      // 防护用品使用情况
      const curAbnormalRectifyList = abnormalRectifyList.filter(item => {
        return (
          item.EnterpriseID &&
          item.EnterpriseID.workAddress &&
          item.EnterpriseID.workAddress.some(ele => ele.districts.includes(curAreaName))
        );
      });
      const curAbnormalRectifyEnterpriseID = curAbnormalRectifyList.map(
        item => item.EnterpriseID._id
      );
      const curAbnormalRectifyEmployees = curAbnormalRectifyList.map(item => item.employees).flat();

      // 监督检查异常情况
      const curAbnormalRectifyList2 = abnormalRectifyList2.filter(item => {
        return (
          item.EnterpriseID &&
          item.EnterpriseID.workAddress &&
          item.EnterpriseID.workAddress.some(ele => ele.districts.includes(curAreaName))
        );
      });
      const curAbnormalRectifyEnterpriseID2 = curAbnormalRectifyList2.map(
        item => item.EnterpriseID._id
      );
      result.push({
        areaName: curAreaName, // 当前区域名称
        // 佩戴异常单位数
        abnormalCount: new Set(curAbnormalRectifyEnterpriseID).size,
        // 佩戴异常人数
        abnormalEmployeeCount: new Set(curAbnormalRectifyEmployees).size,
        // 预警级别
        warningLevel: new Set(curAbnormalRectifyEmployees).size > 10 ? 1 : 2,
        // 处罚单位数
        punishmentCount: new Set(curAbnormalRectifyEnterpriseID2).size,
      });
    }
    let defendProductList = await ctx.model.ProtectiveSuppliesList.find();
    defendProductList = defendProductList
      .map(ele => ele.list)
      .flat()
      .map(ele => ele.data)
      .flat();

    const defendProductStatic = {};
    defendProductList.forEach(ele => {
      if (!defendProductStatic[ele.product]) {
        // 根据ele.product的值来判断harmFactor
        let harmFactor = '';
        if (ele.product.includes('防护服')) {
          harmFactor = '生物';
        } else if (ele.product.includes('手套')) {
          harmFactor = '化学';
        } else if (
          ele.product.includes('口罩') ||
          ele.product.includes('呼吸') ||
          ele.product.includes('面屏') ||
          ele.product.includes('面罩')
        ) {
          harmFactor = '粉尘';
        } else if (ele.product.includes('耳塞')) {
          harmFactor = '噪声';
        } else if (ele.product.includes('护目镜')) {
          harmFactor = '激光辐射';
        } else if (ele.product.includes('眼镜')) {
          harmFactor = '高频电磁场';
        } else if (ele.product.includes('面罩')) {
          harmFactor = '电离辐射';
        } else if (ele.product.includes('面屏')) {
          harmFactor = '紫外辐射';
        }
        defendProductStatic[ele.product] = {
          harmFactor,
          product: ele.product,
          count: 0,
          // 佩备比例 取 0 - 1.5 之间的随机数
          wearRate: (Math.random() * 1.5).toFixed(1) + 1,
          contactCount: 0, // 接触人数
        };
      }
      defendProductStatic[ele.product].count += ele.surplus || 0;
      // 根据wearRate计算接触人数
      defendProductStatic[ele.product].contactCount = Math.floor(
        defendProductStatic[ele.product].count * defendProductStatic[ele.product].wearRate
      );
    });
    defendProductList = Object.values(defendProductStatic);
    defendProductList.forEach(ele => {
      if (ele.count === 0) {
        ele.wearRate = 0;
      } else {
        ele.wearRate = ele.wearRate + ' : 1';
      }
    });

    ctx.helper.renderSuccess(ctx, {
      data: {
        defendProductList,
        abnormalList: result,
      },
      message: '查询成功',
    });
  },
};

module.exports = DefendProductsApiController;
