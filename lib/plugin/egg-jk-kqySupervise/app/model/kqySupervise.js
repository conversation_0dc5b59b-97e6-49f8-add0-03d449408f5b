module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 法律法规
  const kqySuperviseSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    InstitutionName:{ //机构名称
      type: String,
    },
    InstitutionType:String,//机构类型
    AcceptingDepartment:{
      type: String,
      ref:'SuperUser'
    },//接受部门
    state:Number, //案件状态
    submitBy:String, //报送人
    SuperviseInstitution:String, //监管机构
    SuperviseTime:String, //监管时间
    SuperviseResult:String, //监管结果
    material:[String], //材料
    feedbackBy:String, //反馈人
    feedbackTime:String, //反馈时间
    area_code:[String],//发起方机构区域编码
    submitDepartment:{
      type: String,
      ref:'SuperUser'
    } //报送部门
  }, { timestamps: true });

  return mongoose.model('kqySupervise', kqySuperviseSchema, 'kqySupervise');
};
