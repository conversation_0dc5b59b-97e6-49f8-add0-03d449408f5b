const Service = require('egg').Service;
const path = require('path');
const fs = require('fs');
class KqySuperviseService extends Service {
  async listByKey(params) {
    try {
      const { ctx } = this;
      const targetUrl = `${this.config.domainNames.portal}/api/dictManage/listByKey?key=${params}`; // 目标接口 URL
      console.log(targetUrl, '请求 URL');
      const { data } = await ctx.curl(targetUrl, {
        method: 'GET', // 请求方法
        dataType: 'json', // 响应数据类型
      });
      if(data.status===200){
        return data.data;
      }
      return data; // 返回目标接口的响应
    } catch (error) {
      return error
    }
  }
  async getInstitutionlist(params) {
    try {
      const { ctx } = this;
      const targetUrl = `${this.config.iService2Host}/organizations`;
      // 拼接查询参数到 URL
      const queryParams = new URLSearchParams();
      if (params.InstitutionType) {
        queryParams.append('orgTypes', params.InstitutionType);
      }
      if (params.InstitutionName) {
        queryParams.append('name', params.InstitutionName);
      }
      queryParams.append('zonecode', ctx.session.superUserInfo.area_code);

      console.log(queryParams,'queryParams');
      
      const fullUrl = `${targetUrl}?${queryParams.toString()}`;
      console.log(fullUrl, '完整请求 URL');
  
      const { data } = await ctx.curl(fullUrl, {
        method: 'GET',
        dataType: 'json',
      });
  
      if (data.code === 200) {
        return data.data;
      }
      return data;
    } catch (error) {
      console.error(error);
      return error;
    }
  }
  async add(data) {
    const { ctx } = this;
    try {
      data.area_code=ctx.session.superUserInfo.area_code
      const newData = new ctx.model.KqySupervise(data);
      const savedData = await newData.save();
      return savedData;
    } catch (error) {
      ctx.logger.error('Failed to add KqySupervise:', error);
      throw new Error('Failed to add KqySupervise');
    }
  }

  async getlist(params) {
    const { ctx } = this;
    try {
      const { pageNum = 1, pageSize = 10, ...rawQuery } = params;
      console.log(rawQuery,'rawQueryrawQuery');
      // 1. 动态构建查询条件
      const query = {};
      let code=await ctx.service.dashboard.getSubArea(ctx.session.superUserInfo.area_code)
      code= Array.isArray(code)? code.map(item=>{return item.area_code}) : code
      query.area_code = { $in: [
        ...(Array.isArray(ctx.session.superUserInfo.area_code)
            ? ctx.session.superUserInfo.area_code
            : ctx.session.superUserInfo.area_code
              ? [ctx.session.superUserInfo.area_code]
              : []),
        ...code
      ]};
      const allowedFields = ['state', 'InstitutionName', 'InstitutionType','submitDepartment','AcceptingDepartment']; // 允许查询的字段
      Object.keys(rawQuery).forEach(key => {
        if (allowedFields.includes(key) && rawQuery[key] !== undefined && rawQuery[key] !== '') {
          // 根据字段类型处理查询条件
          if (key === 'InstitutionName') {
            query[key] = { $regex: rawQuery[key], $options: 'i' }; // 文本字段模糊匹配
          } else if(key==='state'){
            query[key] = parseInt(rawQuery[key]); // 确保 state 是数字
          }else{
            query[key] = rawQuery[key];
          }
        }
      });

      // 2. 分页查询
      const skip = (pageNum - 1) * pageSize;
      const list = await ctx.model.KqySupervise.find(query)
        .skip(skip)
        .limit(parseInt(pageSize))
        .populate('AcceptingDepartment', 'cname')
        .populate('submitDepartment', 'cname')
        .lean();

      // 3. 总数统计
      const total = await ctx.model.KqySupervise.countDocuments(query);
      const filePath = path.join('/static', this.config.upload_http_path, 'kqySupervise');

      const resList=list.map(item=>{
        // 创建对象副本避免修改原文档
        const newItem = item.toObject ? item.toObject() : { ...item };

        // 安全处理 material 数组
        if (Array.isArray(newItem.material) && newItem.material.length > 0) {
          newItem.material = newItem.material.map(file => 
            file ? path.join(filePath, file) : ''
          ).filter(Boolean); // 过滤空路径
        } else {
          newItem.material = []; // 统一返回数组
        }

        return newItem;
      })
      return { resList, total };
    } catch (error) {
      ctx.logger.error('查询联合执法记录失败:', error);
      throw new Error('查询失败');
    }
  }

  async updateById(_id, data) {
    const { ctx } = this;
    
    // 1. 提取允许更新的字段
    const updateFields = {};
    if (data.InstitutionName !== undefined) {
      updateFields.InstitutionName = data.InstitutionName;
    }
    if (data.InstitutionType !== undefined) {
      updateFields.InstitutionType = data.InstitutionType;
    }
    if (data.AcceptingDepartment !== undefined) {
      updateFields.AcceptingDepartment = data.AcceptingDepartment;
    }
  
    // 2. 执行更新
    return ctx.model.KqySupervise.findByIdAndUpdate(
      _id,
      { $set: updateFields },
      { new: true } // 返回更新后的文档
    );
  }
  async deleteById(_id) {
    const { ctx } = this;
    const item = await ctx.model.KqySupervise.findOne({ _id });
    const res = await ctx.model.KqySupervise.deleteOne({ _id });
    if (res.deletedCount > 0 && item.material && item.material.length > 0) {
      for (const file of item.material) {
        const filePath = path.join(this.app.config.upload_path, 'KqySupervise', file);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      }
    }
    return res;
  }
  // 处理反馈逻辑
  async feedbackwithfile(data) {
    // 1. 数据预处理
    const updateFields = {
      ...data,
      feedbackTime: new Date(),
      state: 1
    };
    // 2. 数据库操作
    const result = await this.ctx.model.KqySupervise.findByIdAndUpdate(
      updateFields._id,
      { $set: updateFields },
      { new: true}
    );

    if (!result) {
      throw new Error('未找到对应记录');
    }
    return result;
  }
  // 处理反馈逻辑
  async feedback(_id,feedbackData) {
    // 1. 数据预处理
    const updateFields = {
      ...feedbackData,
      feedbackTime: new Date(),
      state: 1
    };
    // 2. 数据库操作
    const result = await this.ctx.model.KqySupervise.findByIdAndUpdate(
      _id,
      { $set: updateFields },
      { new: true}
    );

    if (!result) {
      throw new Error('未找到对应记录');
    }
    return result;
  }
}

module.exports = KqySuperviseService;
