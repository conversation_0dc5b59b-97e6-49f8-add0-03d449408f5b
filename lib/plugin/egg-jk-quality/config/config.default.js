const pkgInfo = require("../package.json");
exports.jk_quality = {
  alias: "jk_quality", // 插件目录，必须为英文
  pkgName: "egg-jk-quality", // 插件包名
  enName: "jk_quality", // 插件名
  name: "", // 插件名称
  description: "质控管理", // 插件描述
  isadm: 1, // 是否有后台管理，1：有，0：没有，入口地址:'/ext/devteam/admin/index'
  isindex: 0, // 是否需要前台访问，1：需要，0：不需要,入口地址:'/ext/devteam/index/index'
  version: pkgInfo.version, // 版本号
  iconName: "icon_service", // 主菜单图标名称
  adminUrl: "/serviceOrg/js/app.js",
  adminApi: [
    {
      url: "quality/addTarget",
      method: "post",
      controllerName: "addTarget",
      details: "新增指标",
    },
    {
      url: "quality/getTargets",
      method: "get",
      controllerName: "getTargets",
      details: "获取指标列表",
    },
    {
      url: "quality/removeTarget",
      method: "post",
      controllerName: "removeTarget",
      details: "删除指标",
    },
    {
      url: "quality/addTargetContent",
      method: "post",
      controllerName: "addTargetContent",
      details: "添加指标内容",
    },
    {
      url: "quality/getTargetContents",
      method: "get",
      controllerName: "getTargetContents",
      details: "获取指标内容",
    },
    {
      url: "quality/removeContent",
      method: "post",
      controllerName: "removeContent",
      details: "删除指标内容",
    },
    {
      url: "quality/getHCReportList",
      method: "post",
      controllerName: "getHCReportList",
      details: "获取体检报告列表",
    },
    {
      url: "quality/getContractTableList",
      method: "get",
      controllerName: "getContractTableList",
      details: "获取体检合同列表",
    },
    {
      url: "quality/getDiagnosisReportList",
      method: "get",
      controllerName: "getDiagnosisReportList",
      details: "获取诊断报告列表",
    },
    {
      url: "quality/checkIndicatorClassifySameName",
      method: "post",
      controllerName: "checkIndicatorClassifySameName",
      details: "检查指标是否重名",
    },
    {
      url: "quality/checkIndicatorSameName",
      method: "post",
      controllerName: "checkIndicatorSameName",
      details: "检查考核内容是否重名",
    },
    {
      url: "quality/getCheckList",
      method: "get",
      controllerName: "getCheckList",
      details: "获取检查库列表",
    },
    {
      url: "quality/getCheck",
      method: "get",
      controllerName: "getCheck",
      details: "获取检查库详情",
    },
    {
      url: "quality/saveUpdateCheck",
      method: "post",
      controllerName: "saveUpdateCheck",
      details: "新增编辑检查表",
    },
    {
      url: "quality/deleteCheck",
      method: "delete",
      controllerName: "deleteCheck",
      details: "删除检查表数据",
    },
    {
      url: "quality/enableCheck",
      method: "put",
      controllerName: "enableCheck",
      details: "启用禁用检查表",
    },
    {
      url: "quality/getEvaluateList",
      method: "get",
      controllerName: "getEvaluateList",
      details: "获取评价列表",
    },
    {
      url: "quality/saveUpdateEvaluate",
      method: "post",
      controllerName: "saveUpdateEvaluate",
      details: "新增编辑评价",
    },
    {
      url: "quality/deleteEvaluate",
      method: "delete",
      controllerName: "deleteEvaluate",
      details: "删除评价",
    },
    {
      url: "quality/getEvaluateResult",
      method: "get",
      controllerName: "getEvaluateResult",
      details: "获取评价结果",
    },
    {
      url: "quality/getCheckDict",
      method: "get",
      controllerName: "getCheckDict",
      details: "获取检查表字典",
    },
    {
      url: "quality/getOrgDict",
      method: "get",
      controllerName: "getOrgDict",
      details: "获取机构字典",
    },
    {
      url: "quality/getTestContract",
      method: "post",
      controllerName: "getTestContract",
      details: "获取检测合同列表",
    },
    {
      url: "quality/getExpertEvaluate",
      method: "get",
      controllerName: "getExpertEvaluate",
      details: "获取专家评价",
    },
    {
      url: "quality/adjustEvaluateResult",
      method: "post",
      controllerName: "adjustEvaluateResult",
      details: "微调评价结果",
    },
    {
      url: "quality/getAdjustEvaluateResultList",
      method: "get",
      controllerName: "getAdjustEvaluateResultList",
      details: "获取微调评价结果列表",
    },
    {
      url: "quality/getReportList",
      method: "get",
      controllerName: "getReportList",
      details: "获取上报列表",
    },
    {
      url: "quality/getUnReportList",
      method: "get",
      controllerName: "getUnReportList",
      details: "获取未上报列表",
    },
    {
      url: "quality/exceptionReport",
      method: "post",
      controllerName: "exceptionReport",
      details: "异常上报",
    },
    {
      url: "quality/getReportLookList",
      method: "get",
      controllerName: "getReportLookList",
      details: "获取上报列表",
    },
    {
      url: "quality/getExpertEvaluate",
      method: "get",
      controllerName: "getExpertEvaluate",
      details: "获取专家评价",
    },
    {
      url: "quality/adjustEvaluateResult",
      method: "post",
      controllerName: "adjustEvaluateResult",
      details: "微调评价结果",
    },
    {
      url: "quality/getAdjustEvaluateResultList",
      method: "get",
      controllerName: "getAdjustEvaluateResultList",
      details: "获取微调评价结果列表",
    },
    {
      url: "quality/physicalExamOrgs",
      method: "get",
      controllerName: "physicalExamOrgs",
      details: "获取检查机构列表",
    },
    {
      url: "quality/getAssessmentList",
      method: "get",
      controllerName: "getAssessmentList",
      details: "督导意见管理列表",
    },
    {
      url: "quality/getFeedBack",
      method: "get",
      controllerName: "getFeedBack",
      details: "督导反馈意见详情",
    },
    {
      url: "quality/getOrgDetail",
      method: "get",
      controllerName: "getOrgDetail",
      details: "机构详情",
    },
    {
      url: "quality/getAreaDict",
      method: "get",
      controllerName: "getAreaDict",
      details: "获取管辖区域字典",
    },
    {
      url: "quality/getHealthCheckStatistics",
      method: "post",
      controllerName: "getHealthCheckStatistics",
      details: "获取健康检查统计数据",
    },
    {
      url: "quality/getDiagnosticStatistics",
      method: "post",
      controllerName: "getDiagnosticStatistics",
      details: "获取职业病诊断统计数据",
    },
    {
      url: "quality/getRadiateStatistics",
      method: "post",
      controllerName: "getRadiateStatistics",
      details: "获取检测机构统计数据",
    },
    {
      url: "quality/getSupervisorOpinionStatistics",
      method: "post",
      controllerName: "getSupervisorOpinionStatistics",
      details: "获取督导意见统计数据",
    },
    {
      url: 'quality/getAreaDict',
      method: 'get',
      controllerName: 'getAreaDict',
      details: '获取管辖区域字典',
    },
    {
      url: 'quality/getHealthCheckStatistics',
      method: 'post',
      controllerName: 'getHealthCheckStatistics',
      details: '获取健康检查统计数据',
    },
    {
      url: 'quality/getDiagnosticStatistics',
      method: 'post',
      controllerName: 'getDiagnosticStatistics',
      details: '获取职业病诊断统计数据',
    },
    {
      url: "quality/getQualityRanking",
      method: "get",
      controllerName: "getQualityRanking",
      details: "获取质控排名",
    },
  ],
  fontApi: [],
  initData: "", // 初始化数据脚本
  pluginsConfig: ` 
    exports.jk_quality = {\n
        enable: true,\n
         \n
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
    qualityRouter:{\n
        match: [ctx => ctx.path.startsWith('/manage/quality'), ctx => ctx.path.startsWith('/api/quality'),ctx => ctx.path.startsWith('/manage/quality'), ctx => ctx.path.startsWith('/api/quality'), ctx => ctx.path.startsWith('/manage/quality')],\n
    },\n
    `, // 插入到 config.default.js 中的配置
};
