const qualityController = {
  async addTarget(ctx) {
    try {
      const res = await ctx.service.quality.addTarget(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },
  // getTargets
  async getTargets(ctx) {
    try {
      const res = await ctx.service.quality.getTargets(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // removeTarget
  async removeTarget(ctx) {
    try {
      const res = await ctx.service.quality.removeTarget(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // addTargetContent
  async addTargetContent(ctx) {
    try {
      const res = await ctx.service.quality.addTargetContent(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // getTargetContents
  async getTargetContents(ctx) {
    try {
      const res = await ctx.service.quality.getTargetContents(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // removeContent
  async removeContent(ctx) {
    try {
      const res = await ctx.service.quality.removeContent(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 检查指标是否重名
  async checkIndicatorClassifySameName(ctx) {
    try {
      const res = await ctx.service.quality.checkIndicatorClassifySameName(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 检查考核内容是否重名
  async checkIndicatorSameName(ctx) {
    try {
      const res = await ctx.service.quality.checkIndicatorSameName(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取检查库列表
  async getCheckList(ctx) {
    try {
      const res = await ctx.service.quality.getCheckList(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取去检查表字典数据
  async getCheckDict(ctx) {
    try {
      const res = await ctx.service.quality.getCheckDict(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取检查表详情
  async getCheck(ctx) {
    try {
      const res = await ctx.service.quality.getCheck(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 新增编辑检查表
  async saveUpdateCheck(ctx) {
    try {
      const res = await ctx.service.quality.saveUpdateCheck(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 删除检查表数据
  async deleteCheck(ctx) {
    try {
      const res = await ctx.service.quality.deleteCheck(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 启用禁用检查表
  async enableCheck(ctx) {
    try {
      const res = await ctx.service.quality.enableCheck(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取评价列表
  async getEvaluateList(ctx) {
    try {
      const res = await ctx.service.quality.getEvaluateList(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 新增编辑评价
  async saveUpdateEvaluate(ctx) {
    try {
      const res = await ctx.service.quality.saveUpdateEvaluate(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 删除评价
  async deleteEvaluate(ctx) {
    try {
      const res = await ctx.service.quality.deleteEvaluate(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取评价结果
  async getEvaluateResult(ctx) {
    try {
      const res = await ctx.service.quality.getEvaluateResult(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取机构字典
  async getOrgDict(ctx) {
    try {
      const res = await ctx.service.quality.getOrgDict(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取专家评价
  async getExpertEvaluate(ctx) {
    try {
      const res = await ctx.service.quality.getExpertEvaluate(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 微调评价结果
  async adjustEvaluateResult(ctx) {
    try {
      const res = await ctx.service.quality.adjustEvaluateResult(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取微调评价结果列表
  async getAdjustEvaluateResultList(ctx) {
    try {
      const res = await ctx.service.quality.getAdjustEvaluateResultList(
        ctx.query
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取上报列表
  async getReportList(ctx) {
    try {
      const res = await ctx.service.quality.getReportList(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async getReportLookList(ctx) {
    try {
      const res = await ctx.service.quality.getReportLookList(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取未上报列表
  async getUnReportList(ctx) {
    try {
      const res = await ctx.service.quality.getUnReportList(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 异常上报
  async exceptionReport(ctx) {
    try {
      const res = await ctx.service.quality.exceptionReport(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取管辖区域字典
  async getAreaDict(ctx) {
    try {
      const res = await ctx.service.quality.getAreaDict(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 健康检查统计
  async getHealthCheckStatistics(ctx) {
    try {
      const res = await ctx.service.quality.getHealthCheckStatistics(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 职业病诊断统计
  async getDiagnosticStatistics(ctx) {
    try {
      const res = await ctx.service.quality.getDiagnosticStatistics(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 检测机构统计
  async getRadiateStatistics(ctx) {
    try {
      const res = await ctx.service.quality.getRadiateStatistics(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 督导意见统计
  async getSupervisorOpinionStatistics(ctx) {
    try {
      const res = await ctx.service.quality.getSupervisorOpinionStatistics(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取体检报告列表
  async getHCReportList(ctx) {
    try {
      const body = ctx.request.body;
      const result = await ctx.service.quality.getHCReportList(body);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: "获取成功",
        status: 200,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error });
    }
  },

  // 获取体检合同列表
  async getContractTableList(ctx) {
    try {
      const params = ctx.query;
      const res = await ctx.service.quality.getContractTableList(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "数据获取成功",
        status: 200,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error });
    }
  },

  // 获取诊断机构报告列表
  async getDiagnosisReportList(ctx) {
    try {
      const params = ctx.query;
      const res = await ctx.service.quality.getDiagnosisReportList(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "数据获取成功",
        status: 200,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error });
    }
  },

  // 获取检测合同列表
  async getTestContract(ctx) {
    try {
      const params = ctx.request.body;
      console.log(params, "params---->");

      const res = await ctx.service.quality.getTestContract(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "数据获取成功",
        status: 200,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error });
    }
  },

  // 获取健康检查机构列表
  async physicalExamOrgs(ctx) {
    try {
      const params = ctx.query;
      console.log(params, "params---->");
      const res = await ctx.service.quality.physicalExamOrgs(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: "数据获取成功",
        status: 200,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error });
    }
  },

  // 督导意见管理列表
  async getAssessmentList(ctx) {
    try {
      const res = await ctx.service.quality.getAssessmentList(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 督导反馈意见详情
  async getFeedBack(ctx) {
    try {
      const res = await ctx.service.quality.getFeedBack(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取机构详情
  async getOrgDetail(ctx) {
    try {
      const res = await ctx.service.quality.getOrgDetail(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取质控排名
  async getQualityRanking(ctx) {
    try {
      const res = await ctx.service.quality.getQualityRanking(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },
};

module.exports = qualityController;
