const Service = require('egg').Service;

class EvaluationSystemService extends Service {
  async addTarget(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/health-check/addOrEditIndicatorClassify`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: params,
          contentType: 'json',
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // getTargets
  async getTargets(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/health-check/getIndicatorClassifyTreeList`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // removeTarget
  async removeTarget(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/health-check/deleteIndicatorClassify?id=${params.id}`,
        {
          method: 'DELETE',
          dataType: 'json', // 返回的数据类型
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // addTargetContent
  async addTargetContent(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/health-check/addOrEditIndicator`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // getTargetContents
  async getTargetContents(params) {
    try {
      const { ctx } = this;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/health-check/getIndicatorTreeList`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // removeContent
  async removeContent(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/health-check/deleteIndicator?id=${params.id}`,
        {
          method: 'DELETE',
          dataType: 'json', // 返回的数据类型
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 检查指标是否重名
  async checkIndicatorClassifySameName(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/health-check/checkIndicatorClassifySameName`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: params,
          contentType: 'json',
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 检查考核内容是否重名
  async checkIndicatorSameName(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/health-check/checkIndicatorSameName`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: params,
          contentType: 'json',
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 获取检查库列表
  async getCheckList(params) {
    try {
      const { ctx } = this;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getCheckList`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 获取去检查表字典数据
  async getCheckDict(params) {
    try {
      const { ctx } = this;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getCheckDict`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 获取检查表详情
  async getCheck(params) {
    try {
      const { ctx } = this;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getCheck`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 新增编辑检查表
  async saveUpdateCheck(params) {
    try {
      const { ctx } = this;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/saveUpdateCheck`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: params,
          contentType: 'json',
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 删除检查表数据
  async deleteCheck(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/deleteCheck?id=${params.id}`,
        {
          method: 'DELETE',
          dataType: 'json', // 返回的数据类型
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 禁用启用检查表
  async enableCheck(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/enableCheck`,
        {
          method: 'PUT',
          dataType: 'json', // 返回的数据类型
          data: params,
          contentType: 'json',
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 获取评价列表
  async getEvaluateList(params) {
    try {
      const { ctx } = this;

      const { orgCode } = ctx.session.superUserInfo;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getEvaluateList`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: {
            ...params,
            org_code: orgCode,
          },
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 新增编辑评价
  async saveUpdateEvaluate(params) {
    try {
      const { ctx } = this;

      const { orgCode, area_code } = ctx.session.superUserInfo;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/saveUpdateEvaluate`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: {
            ...params,
            org_code: orgCode,
            area_code,
          },
          contentType: 'json',
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 删除评价
  async deleteEvaluate(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/deleteEvaluate?id=${params.id}`,
        {
          method: 'DELETE',
          dataType: 'json', // 返回的数据类型
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 获取评价列表
  async getEvaluateResult(params) {
    try {
      const { ctx } = this;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getEvaluateResult`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 获取机构字典
  async getOrgDict(params) {
    try {
      const { ctx } = this;

      const { area_code } = ctx.session.superUserInfo;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/organizations`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: {
            ...params,
            zonecode: area_code,
          },
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 获取专家评价
  async getExpertEvaluate(params) {
    try {
      const { ctx } = this;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getExpertEvaluate`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 微调评价结果
  async adjustEvaluateResult(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/adjustEvaluateResult`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: params,
          contentType: 'json',
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 获取微调评价结果列表
  async getAdjustEvaluateResultList(params) {
    try {
      const { ctx } = this;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getAdjustEvaluateResultList`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 获取上报列表
  async getReportList(params) {
    try {
      const { ctx } = this;

      const { orgCode } = ctx.session.superUserInfo;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getReportList`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: {
            ...params,
            org_code: orgCode,
          },
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      console.log(error);
      throw new Error(error);
    }
  }

  async getReportLookList(params) {
    try {
      const { ctx } = this;

      const { orgCode } = ctx.session.superUserInfo;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getReportList`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: {
            ...params,
            report_org_code: orgCode,
          },
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      console.log(error);
      throw new Error(error);
    }
  }

  // 获取未上报列表
  async getUnReportList(params) {
    try {
      const { ctx } = this;

      const { orgCode } = ctx.session.superUserInfo;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getUnReportList`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: {
            ...params,
            org_code: orgCode,
          },
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 异常上报
  async exceptionReport(params) {
    try {
      const { ctx } = this;

      // 获取上一级机构 (卫健委)
      const { regAdd } = ctx.session.superUserInfo;
      let report_org_code = null;
      let report_org_name = null;
      if (regAdd.length > 1) {
        console.log(regAdd[regAdd.length - 2]);
        const superUser = await ctx.model.SuperUser.find({
          type: 1,
          regAdd: { $ne: [] }, // 可选：排除空数组
          $expr: {
            $eq: [{ $arrayElemAt: [ '$regAdd', -1 ] }, regAdd[regAdd.length - 2] ],
          },
        });
        report_org_code = superUser[0].orgCode;
        report_org_name = superUser[0].cname;
      }

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/exceptionReport`,
        {
          method: 'POST',
          dataType: 'json', // 返回的数据类型
          data: {
            ...params,
            report_org_code,
            report_org_name,
          },
          contentType: 'json',
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 获取管辖区域字典
  async getAreaDict() {
    try {
      const { ctx } = this;

      // console.log(ctx.session.superUserInfo);

      // 获取上一级机构 (卫健委)
      // const { regAdd } = ctx.session.superUserInfo;
      // let area_dict = [];
      // if (regAdd.length > 0) {
      //   area_dict = await ctx.model.SuperUser.find({
      //     type: 1,
      //     regAdd: { $ne: [] }, // 可选：排除空数组
      //     $expr: {
      //       $eq: [
      //         { $arrayElemAt: ["$regAdd", regAdd.length - 1] },
      //         regAdd[regAdd.length - 1],
      //       ],
      //     },
      //   });
      // }
      // area_dict.unshift(ctx.session.superUserInfo);
      // return area_dict;
      const { area_code } = ctx.session.superUserInfo;
      // 1. 查询目标区域
      const parentArea = await ctx.model.District.findOne({
        area_code,
      }).lean();

      if (!parentArea) return []; // 未找到返回空数组

      // 2. 查询直接子区域 (parent_code 指向父级 id)
      const children = await ctx.model.District.find({
        parent_code: parentArea.id, // 使用父级的 ObjectId 关联
      }).lean();

      const res = [ parentArea, ...children ];

      // 3. 合并结果
      return res;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 健康检查统计
  async getHealthCheckStatistics(params) {
    try {
      const { ctx } = this;

      const { area_code } = params;
      // 1. 查询目标区域
      const parentArea = await ctx.model.District.findOne({
        area_code,
      }).lean();

      console.log('123456', parentArea);

      if (!parentArea) return []; // 未找到返回空数组

      // 2. 查询直接子区域 (parent_code 指向父级 id)
      const children = await ctx.model.District.find({
        parent_code: parentArea.id, // 使用父级的 ObjectId 关联
      }).lean();

      const res = [ parentArea, ...children ];

      const area_codes = res.map(item => item.area_code);

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getHealthCheckStatistics`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: {
            ...params,
            area_codes,
          },
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 职业病诊断统计
  async getDiagnosticStatistics(params) {
    try {
      const { ctx } = this;

      const { area_code } = params;
      // 1. 查询目标区域
      const parentArea = await ctx.model.District.findOne({
        area_code,
      }).lean();

      console.log('123456', parentArea);

      if (!parentArea) return []; // 未找到返回空数组

      // 2. 查询直接子区域 (parent_code 指向父级 id)
      const children = await ctx.model.District.find({
        parent_code: parentArea.id, // 使用父级的 ObjectId 关联
      }).lean();

      const res = [ parentArea, ...children ];

      const area_codes = res.map(item => item.area_code);

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getDiagnosticStatistics`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: {
            ...params,
            area_codes,
          },
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 检测机构统计
  async getRadiateStatistics(params) {
    try {
      const { ctx } = this;

      const { area_code } = params;
      // 1. 查询目标区域
      const parentArea = await ctx.model.District.findOne({
        area_code,
      }).lean();

      console.log('123456', parentArea);

      if (!parentArea) return []; // 未找到返回空数组

      // 2. 查询直接子区域 (parent_code 指向父级 id)
      const children = await ctx.model.District.find({
        parent_code: parentArea.id, // 使用父级的 ObjectId 关联
      }).lean();

      const res = [ parentArea, ...children ];

      const area_codes = res.map(item => item.area_code);

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getRadiateStatistics`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: {
            ...params,
            area_codes,
          },
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 督导意见统计
  async getSupervisorOpinionStatistics(params) {
    try {
      const { ctx } = this;

      // console.log("aaaa")

      const { area_code } = params;
      // 1. 查询目标区域
      const parentArea = await ctx.model.District.findOne({
        area_code,
      }).lean();

      console.log('123456', parentArea);

      if (!parentArea) return []; // 未找到返回空数组

      // 2. 查询直接子区域 (parent_code 指向父级 id)
      const children = await ctx.model.District.find({
        parent_code: parentArea.id, // 使用父级的 ObjectId 关联
      }).lean();

      const res = [ parentArea, ...children ];

      const area_codes = res.map(item => item.area_code);

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getSupervisorOpinionStatistics`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: {
            ...params,
            area_codes,
          },
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 获取体检报告列表
  async getHCReportList(params = {}) {
    const { ctx } = this;
    const {
      physicalOrgID,
      pageNum = 1,
      pageSize = 10,
      name, // 姓名
      idNumber, // 身份证号
      phone, // 手机号
      examType, // 体检类别
    } = params;

    const filter = {
      physicalOrgID,
      status: 3, // 审核通过
    };

    // 条件过滤
    if (name) filter.name = new RegExp(name, 'i');
    if (phone) filter.phone = new RegExp(phone, 'i');
    if (idNumber) filter.idNumber = new RegExp(idNumber, 'i');
    if (examType) filter.examType = examType;
    const list = await ctx.model.HealthCheckRegister.aggregate([
      { $match: { ...filter } },
      { $sort: { registerTime: -1 } },
      {
        $lookup: {
          from: 'adminorgs',
          localField: 'EnterpriseID',
          foreignField: '_id',
          as: 'Enterprise',
        },
      },
      {
        $lookup: {
          from: 'physicalExamOrgs',
          localField: 'physicalOrgID',
          foreignField: '_id',
          as: 'physicalOrg',
        },
      },
      { $skip: (pageNum - 1) * pageSize },
      { $limit: pageSize },
      {
        $project: {
          total: 1,
          name: 1,
          phone: 1,
          idNumber: 1,
          examType: 1,
          registerTime: 1,
          physicalOrgName: '$physicalOrg.name',
          checkHazardFactors: 1,
          id: '$_id',
          status: 1,
          jobConclusion: 1,
          cname: '$Enterprise.cname',
        },
      },
    ]);

    const total = await ctx.model.HealthCheckRegister.countDocuments(filter);
    return {
      list,
      total,
    };
  }

  // 获取体检合同列表
  async getContractTableList(query) {
    const pageNum = Number(query.pageNum);
    const pageSize = Number(query.pageSize);
    const { ctx } = this;

    // 构建查询条件
    const filter = {};
    const match = {};
    if (query.cName) {
      match.cname = { $regex: query.cName, $options: 'i' };
    }
    if (query.auditStatu) {
      filter.auditStatu = query.auditStatu;
    }
    if (query.contractType) {
      filter.contractType = query.contractType;
    }
    if (query.contractPeopleNum) {
      filter.contractPeopleNum = query.contractPeopleNum;
    }
    if (query.contractAmountMin && !query.contractAmountMax) {
      filter.contractAmount = { $gte: Number(query.contractAmountMin) };
    }
    if (!query.contractAmountMin && query.contractAmountMax) {
      filter.contractAmount = { $lte: Number(query.contractAmountMin) };
    }
    if (query.contractAmountMin && query.contractAmountMax) {
      filter.contractAmount = {
        $gte: Number(query.contractAmountMin),
        $lte: Number(query.contractAmountMax),
      };
    }
    if (query.physicalOrgID) {
      filter.physicalOrgId = query.physicalOrgID;
    }
    const res = await ctx.model.MedicalExamContract.find(filter)
      .populate({
        path: 'cId',
        select: 'cname code',
        match,
      })
      .populate({
        path: 'physicalOrgId',
        select: 'name organization',
      })
      .select(
        'companyId agencyContact agencyPhone companyPhone companyContact contractAmount contractPeopleNum contractType contractSignDate contractDraftDate auditStatu examStartDate examEndDate contractFile receivedAmount'
      )
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .sort({ createDate: -1 });
    // 过滤掉未匹配联查条件的主表数据
    let result = res.filter(item => item.cId !== null);

    result = result.map(item => {
      const { cId, ...rest } = item.toObject(); // 拆分数据，将 cId 提取出来
      const { ...cIdFields } = cId; // 从 cId 中移除 _id 和 id 字段
      return {
        ...rest,
        ...cIdFields, // 平级合并剩余字段
      };
    });
    // 统计总数
    const sum = await ctx.model.MedicalExamContract.find(filter).populate({
      path: 'cId',
      select: 'cname code contract phoneNum',
      match,
    });
    const total = sum.filter(item => item.cId !== null).length;

    return {
      list: result,
      pageNum,
      pageSize,
      total,
    };
  }

  // 获取诊断机构报告列表
  async getDiagnosisReportList(query) {
    const { ctx } = this;
    const { pageNum, pageSize } = query;
    const filter = {};
    if (query.diagnosisInstitutionCode) {
      filter.diagnosisInstitutionCode = query.diagnosisInstitutionCode;
    }

    if (query.employerName) {
      filter.employerName = new RegExp(query.employerName, 'i'); // 使用正则表达式进行模糊匹配
    }

    // 直接检查 query.hasOccupationalDisease 是否为布尔值
    if (query.hasOccupationalDisease) {
      filter.hasOccupationalDisease = query.hasOccupationalDisease;
    }

    if (query.startDate || query.endDate) {
      filter.diagnosisDate = {};
      if (query.startDate) {
        filter.diagnosisDate.$gte = new Date(query.startDate).getTime();
      }
      if (query.endDate) {
        const endDate = new Date(query.endDate);
        endDate.setHours(23, 59, 59, 999);
        filter.diagnosisDate.$lte = endDate.getTime();
      }
    }

    // 计算分页偏移量
    const offset = (pageNum - 1) * pageSize;
    // 查询总记录数
    const total = await ctx.model.DiagnosticRecord.countDocuments(filter);
    const list = await ctx.model.DiagnosticRecord.find(filter)
      .sort({ diagnosisDate: -1 }) // 默认按诊断日期降序排列
      .skip(offset)
      .limit(parseInt(pageSize))
      .exec();
    return {
      list,
      pageNum,
      pageSize,
      total,
    };
  }

  // 获取检测合同列表
  async getTestContract({ curPage = 1, limit = 10, regAddr, keyWords }) {
    const { ctx } = this;

    // 定义查询条件
    const matchStage = {};

    if (regAddr && regAddr.length > 0) {
      // districtRegAdd
      const regAddrArr = regAddr[regAddr.length - 1]; // 按照注册地址过滤
      const regAddrName = regAddrArr.merger_name
        .split(',')
        .map(item => item.trim());
      const serviceSearch = await ctx.model.ServiceOrg.find(
        { regAddr: { $all: regAddrName } },
        { _id: 1 }
      );
      matchStage.serviceOrgId = { $in: serviceSearch.map(item => item._id) };
    } else {
      const { regAdd } = ctx.session.superUserInfo;
      const serviceSearch = await ctx.model.ServiceOrg.find(
        { regAddr: { $all: regAdd } },
        { _id: 1 }
      );
      matchStage.serviceOrgId = { $in: serviceSearch.map(item => item._id) };
    }

    if (keyWords) {
      const serviceSearch = await ctx.model.ServiceOrg.find(
        { name: { $regex: keyWords, $options: 'i' } },
        { _id: 1 }
      );
      matchStage.$or = [
        { cname: { $regex: keyWords, $options: 'i' } }, // 模糊搜索字段1
        { 'inspectionProject.cname': { $regex: keyWords, $options: 'i' } }, // 模糊搜索字段2
        { serviceOrgId: { $in: serviceSearch.map(item => item._id) } },
      ];
    }

    // 聚合管道
    const aggregatePipeline = [
      { $match: matchStage }, // 查询过滤条件
      { $skip: (Number(curPage) - 1) * limit }, // 跳过前面的数据
      { $limit: Number(limit) }, // 限制返回的数量
      {
        $lookup: {
          from: 'serviceOrg', // 关联的表名
          localField: 'serviceOrgId', // EntrustClient 中关联的字段
          foreignField: '_id', // serviceOrg 表中用于关联的字段
          as: 'serviceOrgInfo', // 将 serviceOrg 信息添加到 serviceOrgInfo 字段
        },
      },
      {
        $addFields: {
          serviceOrgInfo: {
            $cond: {
              if: { $gt: [{ $size: '$serviceOrgInfo' }, 0 ] }, // 判断 serviceOrgInfo 数组是否有内容
              then: { $arrayElemAt: [ '$serviceOrgInfo', 0 ] }, // 如果有，取第一个元素
              else: {}, // 如果没有，替换为空对象
            },
          },
        },
      },
    ];

    // 查询数据并返回分页结果
    const [ count, list ] = await Promise.all([
      ctx.model.EntrustClient.countDocuments(matchStage), // 获取文档总数
      ctx.model.EntrustClient.aggregate(aggregatePipeline), // 执行聚合查询
    ]);

    // 返回分页数据和总数
    return {
      count, // 总记录数
      list, // 当前页数据
      curPage, // 当前页数
      limit, // 每页限制条数
    };
  }

  // 获取健康检查机构列表
  async physicalExamOrgs(params) {
    try {
      const { ctx } = this;
      const { regAdd } = ctx.session.superUserInfo;

      // 1. 构建统一查询条件
      const baseMatch = {
        submit: true,
        org_regAdd: { $all: [].concat(regAdd) },
        recordStatus: '审核通过',
      };

      // 2. 处理机构名称查询
      if (params.institution) {
        baseMatch.institution = {
          $regex: params.institution,
          $options: 'i',
        };
      }

      // 3. 安全处理分页参数
      const curPage = Math.max(1, parseInt(params.curPage) || 1);
      const limit = Math.min(100, Math.max(1, parseInt(params.limit) || 10));
      const skip = (curPage - 1) * limit;

      // 4. 使用聚合查询获取机构总数和分页数据
      const [ countResult, results ] = await Promise.all([
        // 获取去重机构总数
        ctx.model.CheckRecord.aggregate([
          { $match: baseMatch },
          { $group: { _id: '$org_id' } }, // 只按机构分组
          { $count: 'totalOrgs' },
        ]),

        // 获取分页数据
        ctx.model.CheckRecord.aggregate([
          { $match: baseMatch },
          { $sort: { updatedAt: -1 } }, // 使用 Mongoose 自动管理的更新时间
          {
            $group: {
              _id: '$org_id',
              latestRecord: { $first: '$$ROOT' }, // 获取每组的最新记录
            },
          },
          { $replaceRoot: { newRoot: '$latestRecord' } }, // 将最新记录提升为根文档
          { $sort: { updatedAt: -1 } }, // 关键：对最终结果集再次排序
          { $skip: skip },
          { $limit: limit },
          // 可选：添加额外字段处理
          {
            $addFields: {
              // 将数组字段转换为单个值（示例）
              businessLicense: { $arrayElemAt: [ '$businessLicense', 0 ] },
              practicingLicense: { $arrayElemAt: [ '$practicingLicense', 0 ] },
            },
          },
        ]),
      ]);

      // 5. 处理总数结果
      const total = (countResult[0] && countResult[0].totalOrgs) || 0;

      return {
        list: results,
        total,
      };
    } catch (error) {
      throw new Error('查询失败，请稍后重试');
    }
  }

  // 督导意见管理列表
  async getAssessmentList(params) {
    try {
      const { ctx } = this;
      const { orgCode } = ctx.session.superUserInfo;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getAssessmentList`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: {
            ...params,
            org_code: orgCode,
          },
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 督导反馈意见详情
  async getFeedBack(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getFeedBack`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 获取机构详情
  async getOrgDetail(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/organizations/${params.id}`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 获取质控排名
  async getQualityRanking(params) {
    try {
      const { ctx } = this;

      const { orgCode } = ctx.session.superUserInfo;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/quality/getQualityRanking`,
        {
          method: 'GET',
          dataType: 'json', // 返回的数据类型
          data: {
            ...params,
            org_code: orgCode,
          },
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }
}

module.exports = EvaluationSystemService;
