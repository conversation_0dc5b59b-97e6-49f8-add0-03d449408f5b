// const await = require('await-stream-ready/lib/await');
// const moment = require('moment');
const path = require('path');
const fs = require('fs');

const Service = require('egg').Service;

class QuestionBankService extends Service {
  // 获取 题库
  async getQB(data) {
    const query = {
      source: 'super',
      authorID: this.ctx.session.superUserInfo._id,
    };
    if (data.keyWord) {
      query.name = { $regex: data.keyWord };
    }
    const res = await this.ctx.model.QuestionBank.find(query)
      .sort({ createdAt: -1 })
      .skip((data.pageCurrent - 1) * data.size)
      .limit(data.size);

    const pageInfo = await this.getPageInfo('QuestionBank', data.size, data.pageCurrent, query);
    return {
      res,
      pageInfo,
    };
  }
  async getPageInfo(collection, size, pageCurrent, query = {}) {
    const total = await this.ctx.model[collection].find(query).count();
    const pageInfo = {
      total,
      size,
      pageCurrent,
    };
    return pageInfo;
  }
  // data 存在_id 就更新，不存在就 新建
  async createQB(data) {
    const { ctx } = this;
    const newData = {
      name: data.questionBankName,
      authorID: ctx.session.superUserInfo._id,
      createRange: ctx.session.superUserInfo.regAdd, // 可能有用
    };
    const doc = await ctx.model.QuestionBank.findOne(newData);
    if (!doc) {
      let res;
      if (!data._id) {
        // res = await new ctx.model.QuestionBank(newData).save();
        res = await ctx.service.db.create('QuestionBank', newData);
      } else {
        // res = await ctx.model.QuestionBank.findByIdAndUpdate(data._id, { name: data.questionBankName });
        res = await ctx.service.db.updateOne('QuestionBank', { _id: data._id }, { name: data.questionBankName });
      }
      if (res) {
        const newDoc = await ctx.model.QuestionBank.find({ authorID: ctx.session.superUserInfo._id }).sort({ createdAt: -1 }).limit(10);
        const pageInfo = await this.getPageInfo('QuestionBank', data.size, 1);
        return {
          data: newDoc,
          status: 200,
          message: 'success',
          pageInfo,
        };
      }
    } else {
      return {
        status: 400,
        message: '题库名称已存在',
      };
    }
  }
  async delQB(_id) {
    const { ctx } = this;
    const docCount = await ctx.model.Topic.find({ questionBankID: _id }).count();
    console.log(222, docCount);
    if (docCount > 0) {
      return {
        message: `题库内存在${docCount}道题目，请先删除题目`,
      };
    }
    // const res = await ctx.model.QuestionBank.findByIdAndRemove(_id);
    const res = await ctx.service.db.remove('QuestionBank', { _id });
    if (res) {
      return {
        message: 'success',
      };
    }
  }
  // 创建题目
  async createTopic(newData) {
    const { ctx } = this;
    // const doc = await new ctx.model.Topic(newData).save();
    if ([ 4, 5 ].includes(+newData.topicType)) {
      newData.options = [];
    }
    const doc = await ctx.service.db.create('Topic', newData);
    if (doc) {
      await this.updateQB2(doc.questionBankID, doc.topicType, 1);
    }
    return doc;
  }
  // 更新题库的 数量
  async updateQB(ID, topicType, count) {
    const { ctx } = this;
    const query = { $inc: { count } };
    switch (topicType) {
      case 1:
        query.$inc.singleTopic = count;
        break;
      case 2:
        query.$inc.multipleTopic = count;
        break;
      case 3:
        query.$inc.judgeTopic = count;
        break;
      default:
        break;
    }
    // await ctx.model.QuestionBank.findByIdAndUpdate(ID, query);
    await ctx.service.db.updateOne('QuestionBank', { _id: ID }, query);
  }
  // 更新题库的 数量 xxn add
  async updateQB2(_id) {
    const { ctx } = this;
    const queryConditions = [
      { questionBankID: _id },
      { questionBankID: _id, topicType: 1 },
      { questionBankID: _id, topicType: 2 },
      { questionBankID: _id, topicType: 3 },
    ];
    const [ count, singleTopic, multipleTopic, judgeTopic ] = await Promise.all(
      queryConditions.map(condition => ctx.model.Topic.count(condition))
    );
    const res = await ctx.service.db.updateOne('QuestionBank', { _id }, { count, singleTopic, multipleTopic, judgeTopic });
    ctx.auditLog('更新题库数量: ' + _id, res, 'info');
  }
  // 批量导入试题
  async addSomeTopic(data) {
    const { ctx } = this;
    const promises = [];
    const errorData = [];
    const successData = [];
    for (let i = 0; i < data.length; i++) {
      const formatLabel = [];
      const formOutline = [];
      if (data[i].labels) {
        const labelArr = data[i].labels.split('|');
        for (let i = 0; i < labelArr.length; i++) {
          const doc = await ctx.model.TopicLabel.findOne({ name: labelArr[i] });
          if (!doc) {
            // const res = await new ctx.model.TopicLabel({ name: labelArr[i] }).save();
            const res = await ctx.service.db.create('TopicLabel', { name: labelArr[i] });
            formatLabel.push(res._id);
          } else {
            formatLabel.push(doc._id);
          }
        }
      }
      if (data[i].outline) {
        const outlineArr = data[i].outline.split('/');
        const doc = await ctx.model.Outline.findOne({ name: outlineArr[0] });
        if (doc) {
          const filterArr = doc.children.filter(item => {
            return item.name === outlineArr[1];
          });
          if (filterArr.length === 0) {
            // const res = await ctx.model.Outline.findByIdAndUpdate(doc._id, { $push: { children: { name: outlineArr[1] } } }, { new: true });
            const res = await ctx.service.db.updateOne('Outline', { _id: doc._id }, { $push: { children: { name: outlineArr[1] } } }, { new: true });
            const children = res.children.filter(item => {
              return item.name === outlineArr[1];
            });
            formOutline.push(...[ res._id, children[0]._id ]);
          } else {
            const children = doc.children.filter(item => {
              return item.name === outlineArr[1];
            });
            formOutline.push(...[ doc._id, children[0]._id ]);
          }
        } else {
          const data = outlineArr[1] ? { name: outlineArr[0], children: [{ name: outlineArr[1] }] } : { name: outlineArr[0] };
          // const newdoc = await new ctx.model.Outline(data).save();
          const newdoc = await ctx.service.db.create('Outline', data);
          formOutline.push(newdoc._id);
          newdoc.children.length > 0 && formOutline.push(newdoc.children[0]._id);
        }
      }
      data[i].labels = formatLabel;
      data[i].outline = formOutline;
      // const res = new ctx.model.Topic(data[i]).save();
      const res = await ctx.service.db.create('Topic', data[i]);
      promises.push(res);
    }
    await Promise.allSettled(promises).then(async result => {
      for (let i = 0; i < result.length; i++) {
        if (result[i].status === 'rejected') {
          errorData.push({
            data: data[i],
            reason: result[i].reason.message,
            index: i,
          });
        } else {
          await this.updateQB(result[i].value.questionBankID, result[i].value.topicType, 1);
          successData.push(result[i].value);
        }
      }
    });
    return {
      data: errorData,
      message: `导入成功${data.length - errorData.length}条试题`,
      status: 200,
      successData,
    };
  }

  // 更新题目
  async updateTopic(newData) {
    const ctx = this.ctx;
    // const updateRes = await ctx.model.Topic.findByIdAndUpdate(newData._id, newData);
    const updateRes = await ctx.service.db.updateOne('Topic', { _id: newData._id }, newData);
    return await ctx.model.Topic.findById(updateRes._id || newData._id);
  }
  // 获取题目
  async getTopic(data) {
    const { ctx } = this;
    const query = {
      questionBankID: data.questionBankID,
    };
    if (data.query.keyWords) {
      query.steam = { $regex: data.query.keyWords };
    }
    if (data.query.topicType) {
      query.topicType = parseInt(data.query.topicType);
    }
    if (data.query.label) {
      query.labels = data.query.label;
    }
    const doc = await ctx.model.Topic.find(query)
      .sort({ createdAt: -1 })
      .skip((data.pageCurrent - 1) * data.size)
      .limit(data.size);
    const pageInfo = await this.getPageInfo('Topic', data.size, data.pageCurrent, query);
    const topicLable = await this.findLabel();
    return {
      doc,
      pageInfo,
      topicLable,
    };
  }
  // 删除题目
  async delTopic(ids) {
    try {
      const { ctx, app } = this;
      const EnterpriseID = ctx.session.operateUserInfo ? ctx.session.operateUserInfo._id : '';
      // const doc = await ctx.model.Topic.find({ _id: { $in: ids } });
      // const res = await ctx.model.Topic.findByIdAndDelete({ _id: { $in: ids } });
      const doc = [];
      for (let i = 0; i < ids.length; i++) {
        // const res = await ctx.model.Topic.findByIdAndDelete({ _id: ids[i] });
        const res = await ctx.service.db.deleteOne('Topic', { _id: ids[i] });
        doc.push(res);
      }
      if (doc.length > 0) {
        const questionBankID = doc[0].questionBankID;
        await this.updateQB2(questionBankID);
      }
      // for (let i = 0; i < doc.length; i++) {
      //   await this.updateQB(doc[i].questionBankID, doc[i].topicType, -1);
      // }
      const fields = [ 'steamPic', 'answerAnalysisPic' ];
      doc.forEach(item => {
        fields.forEach(item2 => {
          if (item[item2].length) {
            item[item2].forEach(val => {
              fs.unlinkSync(path.resolve(app.config.upload_path, EnterpriseID, val.staticName));
            });
          }
        });
      });
      return {
        message: 'success delete',
        status: 200,
      };
    } catch (error) {
      console.log(error);
      return {
        message: 'error delete',
        status: 400,
      };
    }
  }
  // 查找标签
  async findLabel(data) {
    let doc;
    let pageInfo = {};
    if (data) {
      const query = {};
      if (data.keyWord) query.name = { $regex: data.keyWord };
      doc = await this.ctx.model.TopicLabel.find(query, { name: 1, _id: 1 })
        .sort({ createdAt: -1 })
        .skip((data.pageCurrent - 1) * data.size)
        .limit(data.size);
      pageInfo = await this.getPageInfo('TopicLabel', data.size, data.pageCurrent, query);
    } else {
      doc = await this.ctx.model.TopicLabel.find({}, { name: 1, _id: 1 });
    }
    const newData = doc.map(item => {
      return {
        value: item._id,
        label: item.name,
      };
    });
    return {
      doc: newData,
      pageInfo,
    };
  }
  // 查找
  async findOutline() {
    const doc = await this.ctx.model.Outline.find({}, { name: 1, _id: 1, children: 1 });
    const newData = doc.map(item => {
      if (item.children.length === 0) {
        return {
          name: item.name,
          _id: item._id,
        };
      }
      return item;
    });
    return newData;
  }
  // 新增标签
  async addLabel(data) {
    const { ctx } = this;
    const doc = await ctx.model.TopicLabel.findOne(data);
    if (doc) {
      return {
        status: 400,
        message: '标签已存在',
      };
    }
    // const res = await new this.ctx.model.TopicLabel(data).save();
    const res = await ctx.service.db.create('TopicLabel', data);
    if (res) {
      const data = await this.findLabel();
      return {
        data,
        status: 200,
        message: 'success',
      };
    }
  }
  // 新增大纲内容
  async addOutline(data) {
    const { ctx } = this;
    if (!data.parentId) {
      // const res = await new ctx.model.Outline(data).save();
      const res = await ctx.service.db.create('Outline', data);
      if (res) {
        const data = await this.findOutline();
        return {
          data,
          status: 200,
          message: 'success',
        };
      }
    } else {
      // const res = await ctx.model.Outline.updateOne({ _id: data.parentId }, { $addToSet: { children: { name: data.name } } });
      const res = await ctx.service.db.updateOne('Outline', { _id: data.parentId }, { $addToSet: { children: { name: data.name } } });
      if (res.nModified === 1) {
        const data = await this.findOutline();
        return {
          data,
          status: 200,
          message: 'success',
        };
      }
      return {
        data,
        status: 200,
        message: 'error',
      };
    }
  }
  // 删除标签
  async delLabel(data) {
    // const res = await this.ctx.model.TopicLabel.findByIdAndRemove(data.value);
    const res = await this.ctx.service.db.deleteOne('TopicLabel', { _id: data.value });
    // await this.ctx.model.Topic.updateMany({ labels: res._id }, { $pull: { labels: res._id } });
    await this.ctx.service.db.updateMany('Topic', { labels: res._id }, { $pull: { labels: res._id } });
    return res;
  }
  // 删除大纲
  async delOutline(data) {
    console.log(5555555, data);
    const { ctx } = this;
    let res;
    if (data.parentId) {
      // res = await ctx.model.Outline.updateOne({ _id: data.parentId }, { $pull: { children: { _id: data._id } } });
      res = await ctx.service.db.updateOne('Outline', { _id: data.parentId }, { $pull: { children: { _id: data._id } } });
    } else {
      // res = await ctx.model.Outline.deleteOne({ _id: data._id });
      res = await ctx.service.db.deleteOne('Outline', { _id: data._id });
    }
    return res;
  }
  // 更新标签
  async updateLabel(data) {
    const doc = await this.ctx.model.TopicLabel.findOne({ name: data.label });
    if (doc) {
      return {
        status: 400,
        message: '标签已存在',
      };
    }
    const doc2 = await this.ctx.service.db.updateOne('TopicLabel', { _id: data.value }, { $set: { name: data.label } });
    if (doc2) {
      return {
        status: 200,
        message: 'success',
      };
    }
  }
  // 导出数据
  async exportTopics(questBankId) {
    const { ctx } = this;
    const doc = await ctx.model.Topic.aggregate([
      {
        $match: { questionBankID: questBankId },
      },
      {
        $lookup: {
          from: 'topicLabel',
          foreignField: '_id',
          localField: 'labels',
          as: 'labels2',
        },
      },
      {
        $lookup: {
          from: 'outline',
          foreignField: '_id',
          localField: 'outline',
          as: 'outline2',
        },
      },
    ]);
    const tableField = {
      outline: '大纲内容',
      topicType: '题型（必填）',
      steam: '题目（必填）',
      answerAnalysis: '答案解析（选填）',
      labels: '试题来源（教材名称及页码、法规标准名称或其他参考，如有多个，请用|分割）',
      difficultyLevel: '难度（易、中、难）',
      knowledge: '知识点',
      knowledgeLevel: '知识点要求',
      answer: '答案（必填）',
      options: '选项',
    };
    const newArr = [];
    const handleType = function(type) {
      let topicTypeText = '';
      switch (type) {
        case 1:
          topicTypeText = '单选题';
          break;
        case 2:
          topicTypeText = '多选题';
          break;
        case 3:
          topicTypeText = '判断题';
          break;
        default:
          break;
      }
      return topicTypeText;
    };
    const A_J = [ 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J' ];
    for (let i = 0; i < doc.length; i++) {
      doc[i].answer = (doc[i].answer.map(item => {
        return A_J[item];
      })).join('');
      doc[i].labels = (doc[i].labels2.map(item => {
        return item.name;
      }).join('|'));
      const newOutline = [];
      if (doc[i].outline2.length > 0) {
        newOutline.push(doc[i].outline2[0].name);
        if (doc[i].outline[1]) {
          const res = doc[i].outline2[0].children.filter(item => {
            return item._id === doc[i].outline[1];
          });
          newOutline.push(res[0].name);
        }
      }
      doc[i].outline = newOutline.join('/');
      doc[i].topicType = handleType(doc[i].topicType);
      // doc[i].options.forEach((option, index) => {
      //   doc[i]['选项' + A_J[index]] = option.optionText;
      // });
      const newData = {};
      Object.keys(tableField).forEach(key => {
        newData[tableField[key]] = doc[i][key];
      });
      newData['选项'].forEach((option, index) => {
        newData['选项' + A_J[index]] = option.optionText;
      });
      delete newData['选项'];
      newArr.push(newData);
    }
    return newArr;
  }
}

module.exports = QuestionBankService;
