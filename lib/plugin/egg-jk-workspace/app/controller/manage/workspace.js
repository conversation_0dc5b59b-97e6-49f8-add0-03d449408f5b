// const axios = require('axios');

// 场所检测
const workspaceController = {
  // 获取当前单位的监测任务列表
  async getTaskList(ctx) {
    try {
      const params = ctx.request.query;
      const res = await ctx.service.workspace.getTaskList(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '获取监测任务列表成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 获取监测任务详情
  async getTaskDetail(ctx) {
    try {
      const params = ctx.request.query;
      const res = await ctx.service.workspace.getTaskDetail(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '获取监测任务详情成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 省级 - 获取可选的自定义危害因素列表
  async getFactorList(ctx) {
    try {
      const taskId = ctx.request.query.taskId;
      const res = await ctx.service.workspace.getFactorList(taskId);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '获取可选的自定义危害因素列表',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 国家 自定义危害因素列表（添加cnCsjcCode  国家-场所监测危害因素编码）
  async getFactorListNation(ctx) {
    try {
      const res = await ctx.service.workspace.getFactorListNation();
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '获取自定义危害因素列表',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 获取当前单位辖下区域
  async getJurisdiction(ctx) {
    try {
      const { area_code } = ctx.session.superUserInfo;
      const res = await ctx.service.district.getJurisdiction(area_code);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '获取辖下区域',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 省级单位发布任务和添加自定义危害因素
  async releaseTasks(ctx) {
    const data = ctx.request.body;
    try {
      await ctx.service.workspace.releaseTasks(data);
      ctx.helper.renderSuccess(ctx, {
        message: '操作成功',
      });
    } catch (err) {
      console.log(4444, err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 取可选委托单位/检测机构/用人单位
  async getOrgList(ctx) {
    const query = ctx.query;
    try {
      const data = await ctx.service.workspace.getOrgList(query);
      ctx.helper.renderSuccess(ctx, {
        message: '操作成功',
        data,
      });
    } catch (err) {
      console.log(4444, err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 市级单位选择企业名单（发布任务）
  async releaseTasks2(ctx) {
    const data = ctx.request.body;
    try {
      await ctx.service.workspace.addEnterpriseList(data);
      ctx.helper.renderSuccess(ctx, {
        message: '操作成功',
      });
    } catch (err) {
      console.log(4444, err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 创建监测记录
  async addMonitoringRecord(ctx) {
    try {
      const data = await ctx.service.workspace.addMonitoringRecord(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        message: '新建操作成功',
        data,
      });
    } catch (err) {
      console.log(4444, err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 更新监测记录
  async updateMonitoringRecord(ctx) {
    try {
      const data = await ctx.service.workspace.updateMonitoringRecord(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        message: '更新操作成功',
        data,
      });
    } catch (err) {
      console.log(4444, err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 删除监测记录
  async deleteMonitoringRecord(ctx) {
    try {
      const data = await ctx.service.workspace.deleteMonitoringRecord(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        message: '删除操作成功',
        data,
      });
    } catch (err) {
      console.log(4444, err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 获取监测记录列表
  async monitoringRecordList(ctx) {
    try {
      const params = ctx.request.query;
      const res = await ctx.service.workspace.monitoringRecordList(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '获取监测记录列表成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 提交审核
  async addAuditRecord(ctx) {
    try {
      const data = await ctx.service.auditRecord.create(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        message: '操作成功',
        data,
      });
    } catch (err) {
      console.log(4444, err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 删除审核
  async deleteAuditRecord(ctx) {
    try {
      const data = await ctx.service.auditRecord.delete(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        message: '操作成功',
        data,
      });
    } catch (err) {
      console.log(4444, err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 调查表  -创建/修改基本信息
  async baseInfo(ctx) {
    const data = ctx.request.body;
    try {
      await ctx.service.dcTables.baseInfo(data);
      ctx.helper.renderSuccess(ctx, {
        message: '基本信息保存成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 获取调查表详情
  async getBaseInfo(ctx) {
    const { workspaceMonitoringRecordId } = ctx.query;
    try {
      const result = await ctx.service.dcTables.getBaseInfo(
        workspaceMonitoringRecordId
      );
      ctx.helper.renderSuccess(ctx, {
        message: result.message,
        data: result.data,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 更新审核
  async updateAuditRecord(ctx) {
    try {
      await ctx.service.auditRecord.update(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        message: '审核成功',
      });
    } catch (err) {
      console.log(4444, err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 创建经济类型
  // async economicType(ctx) {
  //   const data = ctx.request.body;
  //   console.log(data,'0000data')
  //    await ctx.service.dcTables.createEconomicCategoryData(data);

  // },
  // 获取经济类型列表
  async getEconomicCategory(ctx) {
    try {
      const params = ctx.request.query;
      const res = await ctx.service.dcTables.getEconomicCategory(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '获取经济类型列表成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 获取审核记录列表
  async auditRecordList(ctx) {
    try {
      const params = ctx.request.query;
      const res = await ctx.service.auditRecord.list(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '获取审核记录列表成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 获取岗位编码列表
  async getJobList(ctx) {
    try {
      const params = ctx.request.query;
      const res = await ctx.service.dcTables.getJobList(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '获取岗位编码列表成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 新增/编辑监测情况+定性分析
  async monitoringAnalyse(ctx) {
    const data = ctx.request.body;
    try {
      await ctx.service.dcTables.monitoringAnalyse(data);
      ctx.helper.renderSuccess(ctx, {
        message: '保存成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 获取监测情况+定性分析详情
  async getMonitoringAnalyseDetail(ctx) {
    const { workspaceMonitoringRecordId } = ctx.query;

    try {
      const result = await ctx.service.dcTables.getMonitoringAnalyseDetail({
        workspaceMonitoringRecordId,
      });
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取详情成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 新增/编辑检查情况
  async checkSituation(ctx) {
    const data = ctx.request.body;
    try {
      await ctx.service.dcTables.checkSituation(data);
      ctx.helper.renderSuccess(ctx, {
        message: '保存成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 检查情况详情
  async getCheckSituationDetail(ctx) {
    const { workspaceMonitoringRecordId } = ctx.query;

    try {
      const result = await ctx.service.dcTables.getCheckSituationDetail({
        workspaceMonitoringRecordId,
      });
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取详情成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 新增/编辑防护设施&用品
  async safeguard(ctx) {
    const data = ctx.request.body;
    try {
      await ctx.service.dcTables.safeguard(data);
      ctx.helper.renderSuccess(ctx, {
        message: '防护设施&用品保存成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 防护设施&用品详情
  async getSafeguardDetail(ctx) {
    const { workspaceMonitoringRecordId } = ctx.query;

    try {
      const result = await ctx.service.dcTables.getSafeguardDetail({
        workspaceMonitoringRecordId,
      });
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取防护设施&用品详情成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 新增/编辑劳动者调查表
  async workerForm(ctx) {
    const data = ctx.request.body;
    try {
      await ctx.service.dcTables.workerForm(data);
      ctx.helper.renderSuccess(ctx, {
        message: '劳动者调查表保存成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 劳动者调查表详情
  async getWorkerFormDetail(ctx) {
    const { workspaceMonitoringRecordId } = ctx.query;

    try {
      const result = await ctx.service.dcTables.getWorkerFormDetail({
        workspaceMonitoringRecordId,
      });
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取劳动者调查表详情成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 监测进度查询
  async monitorProgress(ctx) {
    try {
      const params = ctx.request.query;
      const res = await ctx.service.workspace.monitorProgress(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '获取监测进度列表成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 监测记录查询
  async monitorRecordStatistics(ctx) {
    try {
      const params = ctx.request.query;
      const res = await ctx.service.workspace.monitorRecordStatistics(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '获取监测记录查询成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 获取劳动者调查表预填写信息
  async previewWorker(ctx) {
    try {
      const params = ctx.request.query;
      const res = await ctx.service.workspace.previewWorker(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '获取劳动者调查表预填写信息成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 体检因素预填写
  async previewPhysicalHazard(ctx) {
    try {
      const params = ctx.request.query;
      const res = await ctx.service.workspace.previewPhysicalHazard(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '获取体检因素成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 体检总结报告预填写
  async previewCheckSum(ctx) {
    try {
      const params = ctx.request.query;
      const res = await ctx.service.workspace.previewCheckSum(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '获取体检总结报告成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
  // 检测结果
  async monitoringResult(ctx, app) {

    const params = ctx.request.query;
    try {
      const { data } = await ctx.curl(
        `${app.config.iService2Host}/monitoring/result`,
        {
          method: 'get',
          dataType: 'json',
          params,
        }
      );
      if (data.message && data.message.includes('没有找到')) {
        ctx.body = {
          status: 400,
          data,
          message: data.message || '获取失败',
        };
      } else {
        // 有业务数据则判定为成功
        ctx.body = {
          status: 200,
          data,
          message: '获取成功',
        };
      }

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取失败',
        data: err,
      });
    }
  },

};
module.exports = workspaceController;
