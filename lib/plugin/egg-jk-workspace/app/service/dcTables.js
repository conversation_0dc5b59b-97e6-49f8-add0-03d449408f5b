const Service = require('egg').Service;
const shortid = require('shortid');
// const moment = require('moment');
// 调查表
class DcTablesService extends Service {
  // 创建/修改基本信息
  async baseInfo(data) {
    const { ctx } = this;
    // eslint-disable-next-line no-unused-vars
    const { workspaceMonitoringRecordId, _id, ...restData } = data; // 解构时排除_id

    // 1. 参数校验
    if (!workspaceMonitoringRecordId) {
      throw new Error('缺少必要参数: workspaceMonitoringRecordId');
    }

    // 2. 检查监测记录是否存在
    const taskExists = await ctx.model.WorkspaceMonitoringRecord.exists({
      _id: workspaceMonitoringRecordId,
    });
    if (!taskExists) {
      throw new Error(`未找到监测记录: ${workspaceMonitoringRecordId}`);
    }

    // 3. 权限检查（仅允许市/区级单位操作）
    const { regAdd } = ctx.session.superUserInfo || {};
    if (!regAdd || (regAdd.length !== 2 && regAdd.length !== 3)) {
      throw new Error('仅市/区级单位有操作权限');
    }

    // 4. 准备数据结构（安全处理嵌套对象和数组）
    const prepareData = obj => {
      if (!obj || typeof obj !== 'object') return obj;
      const newObj = { ...obj };
      delete newObj._id; // 移除_id字段
      delete newObj.__v; // 移除版本字段
      return newObj;
    };

    const basicInfoData = {
      ...prepareData(restData),
      crowd: prepareData(data.crowd) || {
        contactTotalPeoples: 0,
        ifhfDust: false,
        hfDustPeoples: 0,
        ifhfChemistry: false,
        hfChemistryPeoples: 0,
        ifhfPhysics: false,
        hfPhysicsPeoples: 0,
      },
      industryCode: Array.isArray(data.industryCode)
        ? [ ...data.industryCode ]
        : [],
      workNode: Array.isArray(data.workNode) ? [ ...data.workNode ] : [],
      keyJobInvestigationSituationList: Array.isArray(
        data.keyJobInvestigationSituationList
      )
        ? data.keyJobInvestigationSituationList.map(prepareData)
        : [],
      factorCrowdItemList: Array.isArray(data.factorCrowdItemList)
        ? data.factorCrowdItemList.map(item => {
          const factorId = item.factorId
            ? typeof item.factorId === 'object'
              ? item.factorId._id
              : item.factorId
            : undefined;
          return {
            ...prepareData(item),
            factorId,
          };
        })
        : [],
    };

    // 5. 创建或更新记录
    try {
      const result = await ctx.model.WorkspaceDcBasicInfo.findOneAndUpdate(
        { workspaceMonitoringRecordId },
        {
          $set: basicInfoData,
          $setOnInsert: {
            _id: shortid.generate(),
            createdAt: new Date(),
          },
        },
        {
          upsert: true,
          new: true,
          runValidators: true,
          setDefaultsOnInsert: true,
        }
      );

      return {
        success: true,
        message: '基础信息保存成功',
        data: result,
      };
    } catch (error) {
      ctx.logger.error('保存工作场所基础信息失败:', {
        error: error.message,
        stack: error.stack,
        data: basicInfoData, // 记录引发错误的数据
      });
      throw new Error(`操作失败: ${error.message}`);
    }
  }
  // 获取基本信息详情
  async getBaseInfo(workspaceMonitoringRecordId) {
    const { ctx } = this;

    // 1. 参数校验
    if (!workspaceMonitoringRecordId) {
      throw new Error('缺少必要参数: workspaceMonitoringRecordId');
    }

    // 2. 权限检查（仅允许市/区级单位操作）
    const { regAdd } = ctx.session.superUserInfo || {};
    if (!regAdd || (regAdd.length !== 2 && regAdd.length !== 3)) {
      throw new Error('仅市/区级单位有操作权限');
    }

    // 3. 查询数据
    try {
      const result = await ctx.model.WorkspaceDcBasicInfo.findOne({
        workspaceMonitoringRecordId,
      })
        .populate('EnterpriseID', 'cname code regAdd') // 关联用人单位信息
        .populate('factorCrowdItemList.factorId', 'name category limitValue'); // 关联危害因素信息

      if (!result) {
        throw new Error('未找到该监测任务的基础信息');
      }

      return {
        success: true,
        message: '获取基础信息成功',
        data: result,
      };
    } catch (error) {
      ctx.logger.error('获取工作场所基础信息失败:', error);
      throw new Error(`获取失败: ${error.message}`);
    }
  }
  // 创建/修改监测情况+定性情况分析
  async monitoringAnalyse(data) {
    const { ctx } = this;
    const { workspaceMonitoringRecordId, EnterpriseID } = data;

    // 1. 参数校验
    if (!workspaceMonitoringRecordId) {
      throw new Error('缺少必要参数: workspaceMonitoringRecordId');
    }
    if (!EnterpriseID) {
      throw new Error('缺少必要参数: EnterpriseID');
    }

    // 2. 检查监测记录是否存在
    const taskExists = await ctx.model.WorkspaceMonitoringRecord.exists({
      _id: workspaceMonitoringRecordId,
    });
    if (!taskExists) {
      throw new Error(`未找到监测记录: ${workspaceMonitoringRecordId}`);
    }

    // 3. 权限检查（仅允许市/区级单位操作）
    const { regAdd } = ctx.session.superUserInfo || {};
    if (!regAdd || (regAdd.length !== 2 && regAdd.length !== 3)) {
      throw new Error('仅市/区级单位有操作权限');
    }

    // 4. 检查是否已存在记录
    const existingRecord = await ctx.model.WorkspaceDcJcSituation.findOne({
      workspaceMonitoringRecordId,
    });

    // 5. 准备更新/创建数据
    const now = new Date();
    const recordData = {
      ...data,
      updatedAt: now,
    };

    if (existingRecord) {
      // 更新现有记录
      await ctx.model.WorkspaceDcJcSituation.updateOne(
        { _id: existingRecord._id },
        { $set: recordData }
      );
    } else {
      // 创建新记录
      await ctx.model.WorkspaceDcJcSituation.create({
        ...recordData,
        createdAt: now,
      });
    }

    // 6. 返回处理结果
    return {
      success: true,
      message: existingRecord ? '更新成功' : '创建成功',
    };
  }
  // 获取监测情况+定性分析详情
  async getMonitoringAnalyseDetail(params) {
    const { ctx } = this;
    const { workspaceMonitoringRecordId } = params;

    // 1. 简单参数校验
    if (!workspaceMonitoringRecordId) {
      throw new Error('缺少必要参数: workspaceMonitoringRecordId');
    }

    // 2. 直接查询记录
    const record = await ctx.model.WorkspaceDcJcSituation.findOne({
      workspaceMonitoringRecordId,
    }).lean();

    if (!record) {
      throw new Error('未找到详情记录');
    }

    // 3. 直接返回原始数据
    return record;
  }
  //  创建 经济类型
  //   async createEconomicCategoryData(categories) {
  //     const { ctx } = this;
  //     // const categories = ctx.request.body;
  //     console.log(categories, 'categories9---');

  //     try {
  //       // 验证数据格式
  //       if (!Array.isArray(categories)) {
  //         ctx.throw(400, '请求数据必须是数组格式');
  //       }

  //       // 检查编码唯一性
  //       const codes = categories.map((item) => item.code);
  //       const exists = await ctx.model.EconomicCategory.find({
  //         code: { $in: codes },
  //       });
  //       if (exists.length > 0) {
  //         ctx.throw(
  //           400,
  //           `以下编码已存在: ${exists.map((e) => e.code).join(', ')}`
  //         );
  //       }

  //       // 批量创建
  //       const result = await ctx.model.EconomicCategory.insertMany(categories);
  //       console.log(result,'result----')
  //       // 返回标准格式响应
  //       ctx.body = {
  //         success: true,
  //         message: '创建成功',
  //         data: result,
  //         count: result.length,
  //       };
  //     } catch (error) {
  //       ctx.status = error.status || 500;
  //       ctx.body = {
  //         success: false,
  //         message: error.message,
  //         data: null,
  //       };
  //     }
  //   }
  //   获取经济类型列表
  async getEconomicCategory() {
    const { ctx } = this;
    try {
      const list = await ctx.model.EconomicCategory.find();
      return { list };
    } catch (error) {
      ctx.status = 500;
      ctx.body = { success: false, message: error.message };
    }
  }
  // 获取岗位编码列表
  async getJobList(params) {
    const { ctx } = this;
    try {
      const { keyword = '' } = params;

      // 构建查询条件
      const query = {};
      if (keyword) {
        const regex = new RegExp(keyword, 'i');
        query.$or = [
          { jobName: { $regex: regex } },
          { cnCsjcNo: { $regex: regex } },
        ];
      }

      const list = await ctx.model.JobList.find(query, {
        jobName: 1,
        cnCsjcNo: 1,
      });

      return { list };
    } catch (error) {
      ctx.status = 500;
      throw error;
    }
  }
  // 创建/修改检查情况
  async checkSituation(data) {
    const { ctx } = this;
    const { workspaceMonitoringRecordId, EnterpriseID, ifhea } = data;

    // 1. 参数校验
    if (!workspaceMonitoringRecordId) {
      throw new Error('缺少必要参数: workspaceMonitoringRecordId');
    }
    if (!EnterpriseID) {
      throw new Error('缺少必要参数: EnterpriseID');
    }
    if (ifhea === undefined) {
      throw new Error('缺少必要参数: ifhea (是否开展职业健康检查)');
    }

    // 2. 检查监测记录是否存在
    const taskExists = await ctx.model.WorkspaceMonitoringRecord.exists({
      _id: workspaceMonitoringRecordId,
    });
    if (!taskExists) {
      throw new Error(`未找到监测记录: ${workspaceMonitoringRecordId}`);
    }

    // 3. 权限检查（仅允许市/区级单位操作）
    const { regAdd } = ctx.session.superUserInfo || {};
    if (!regAdd || (regAdd.length !== 2 && regAdd.length !== 3)) {
      throw new Error('仅市/区级单位有操作权限');
    }

    // 4. 检查是否已存在记录
    const existingRecord = await ctx.model.WorkspaceDcTjSituation.findOne({
      workspaceMonitoringRecordId,
    });

    // 5. 准备更新/创建数据
    const now = new Date();
    const recordData = {
      ...data,
      updatedAt: now,
    };

    if (existingRecord) {
      // 更新现有记录
      await ctx.model.WorkspaceDcTjSituation.updateOne(
        { _id: existingRecord._id },
        { $set: recordData }
      );
    } else {
      // 创建新记录
      await ctx.model.WorkspaceDcTjSituation.create({
        ...recordData,
        createdAt: now,
      });
    }

    // 6. 返回处理结果
    return {
      success: true,
      message: existingRecord ? '更新成功' : '创建成功',
    };
  }
  // 获取检查情况详情
  async getCheckSituationDetail(params) {
    const { ctx } = this;
    const { workspaceMonitoringRecordId } = params;

    // 1. 简单参数校验
    if (!workspaceMonitoringRecordId) {
      throw new Error('缺少必要参数: workspaceMonitoringRecordId');
    }

    // 2. 直接查询记录
    const record = await ctx.model.WorkspaceDcTjSituation.findOne({
      workspaceMonitoringRecordId,
    }).lean();

    if (!record) {
      throw new Error('未找到检查情况');
    }

    // 3. 直接返回原始数据
    return record;
  }
  // 创建/修改防护设施&用品
  async safeguard(data) {
    const { ctx } = this;
    const { workspaceMonitoringRecordId, EnterpriseID } = data;

    // 1. 参数校验
    if (!workspaceMonitoringRecordId) {
      throw new Error('缺少必要参数: workspaceMonitoringRecordId');
    }
    if (!EnterpriseID) {
      throw new Error('缺少必要参数: EnterpriseID');
    }

    // 2. 检查监测记录是否存在
    const taskExists = await ctx.model.WorkspaceMonitoringRecord.exists({
      _id: workspaceMonitoringRecordId,
    });
    if (!taskExists) {
      throw new Error(`未找到监测记录: ${workspaceMonitoringRecordId}`);
    }

    // 3. 权限检查（仅允许市/区级单位操作）
    const { regAdd } = ctx.session.superUserInfo || {};
    if (!regAdd || (regAdd.length !== 2 && regAdd.length !== 3)) {
      throw new Error('仅市/区级单位有操作权限');
    }

    // 4. 检查是否已存在记录
    const existingRecord = await ctx.model.WorkspaceDcProtection.findOne({
      workspaceMonitoringRecordId,
    });

    // 清理无效的防护效果
    if (data.fcssSituation === 3) data.fcssEffect = undefined;
    if (data.fdssSituation === 3) data.fdssEffect = undefined;
    if (data.fzsssSituation === 3) data.fzsssEffect = undefined;
    if (data.fckzDistributionSituation !== 1) data.fckzWearSituation = undefined;
    if (data.fdkzDistributionSituation !== 1) data.fdkzWearSituation = undefined;
    if (data.fzyesDistributionSituation !== 1) data.fzyesWearSituation = undefined;

    // 5. 准备更新/创建数据
    const now = new Date();
    const recordData = {
      ...data,
      updatedAt: now,
    };

    if (existingRecord) {
      // 更新现有记录
      await ctx.model.WorkspaceDcProtection.updateOne(
        { _id: existingRecord._id },
        { $set: recordData }
      );
    } else {
      // 创建新记录
      await ctx.model.WorkspaceDcProtection.create({
        ...recordData,
        createdAt: now,
      });
    }

    // 6. 返回处理结果
    return {
      success: true,
      message: existingRecord ? '更新成功' : '创建成功',
    };
  }
  // 获取防护设施&用品详情
  async getSafeguardDetail(params) {
    const { ctx } = this;
    const { workspaceMonitoringRecordId } = params;

    // 1. 简单参数校验
    if (!workspaceMonitoringRecordId) {
      throw new Error('缺少必要参数: workspaceMonitoringRecordId');
    }

    // 2. 直接查询记录
    const record = await ctx.model.WorkspaceDcProtection.findOne({
      workspaceMonitoringRecordId,
    }).lean();

    if (!record) {
      throw new Error('未找到防护设施&用品');
    }

    // 3. 直接返回原始数据
    return record;
  }
  // 新增/编辑劳动者调查表
  async workerForm(data) {
    const { ctx } = this;
    const { workspaceMonitoringRecordId, EnterpriseID } = data;

    // 1. 参数校验
    if (!workspaceMonitoringRecordId) {
      throw new Error('缺少必要参数: workspaceMonitoringRecordId');
    }
    if (!EnterpriseID) {
      throw new Error('缺少必要参数: EnterpriseID');
    }

    // 2. 检查监测记录是否存在
    const taskExists = await ctx.model.WorkspaceMonitoringRecord.exists({
      _id: workspaceMonitoringRecordId,
    });
    if (!taskExists) {
      throw new Error(`未找到监测记录: ${workspaceMonitoringRecordId}`);
    }

    // 3. 权限检查（仅允许市/区级单位操作）
    const { regAdd } = ctx.session.superUserInfo || {};
    if (!regAdd || (regAdd.length !== 2 && regAdd.length !== 3)) {
      throw new Error('仅市/区级单位有操作权限');
    }

    // 4. 检查是否已存在记录
    const existingRecord = await ctx.model.WorkspaceDcWorker.findOne({
      workspaceMonitoringRecordId,
    });

    // 5. 准备更新/创建数据
    const now = new Date();
    const recordData = {
      ...data,
      updatedAt: now,
    };

    if (existingRecord) {
      // 更新现有记录
      await ctx.model.WorkspaceDcWorker.updateOne(
        { _id: existingRecord._id },
        { $set: recordData }
      );
    } else {
      // 创建新记录
      await ctx.model.WorkspaceDcWorker.create({
        ...recordData,
        createdAt: now,
      });
    }

    // 6. 返回处理结果
    return {
      success: true,
      message: existingRecord ? '更新成功' : '创建成功',
    };
  }
  // 劳动者调查表详情
  async getWorkerFormDetail(params) {
    const { ctx } = this;
    const { workspaceMonitoringRecordId } = params;

    // 1. 简单参数校验
    if (!workspaceMonitoringRecordId) {
      throw new Error('缺少必要参数: workspaceMonitoringRecordId');
    }

    // 2. 直接查询记录
    const record = await ctx.model.WorkspaceDcWorker.findOne({
      workspaceMonitoringRecordId,
    }).lean();

    if (!record) {
      throw new Error('未找到劳动者调查表');
    }

    // 3. 直接返回原始数据
    return record;
  }
}

module.exports = DcTablesService;
