const Service = require('egg').Service;
// const moment = require('moment');
class AuditRecordService extends Service {
  async list(params) {
    const { ctx } = this;
    const pageNum = Number(params.pageNum) || 1;
    const pageSize = Number(params.pageSize) || 10;
    const auditStatus = params.auditStatus ? Number(params.auditStatus) : null;
    const recordId = params.recordId;
    const query = {};
    if (auditStatus) {
      if (![0, 1, 2].includes(auditStatus)) {
        throw new Error('auditStatus参数错误。');
      }
      query.auditStatus = auditStatus;
    }
    if (recordId) {
      query.recordId = recordId;
    }

    const { regAdd, _id: curOrgId } = ctx.session.superUserInfo;
    if (regAdd.length === 1) {
      query.auditLevel = 2;
    } else {
      query.$or = [{ auditOrgId: curOrgId }, { submitOrgId: curOrgId }];
    }
    const list = await ctx.model.AuditRecord.find(query)
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .lean()
      .populate([
        {
          path: 'auditOrgId',
          select: 'cname',
        },
        {
          path: 'submitOrgId',
          select: 'cname',
        },
        {
          path: 'recordId',
          select: 'EnterpriseID',
          populate: {
            path: 'EnterpriseID',
            select: 'cname',
          },
        },
      ]);
    const total = await ctx.model.AuditRecord.countDocuments(query);
    return { total, list };
  }
  async create(data) {
    const { ctx } = this;
    const { recordId } = data;
    if (!recordId) throw new Error('recordId不能为空');
    const { regAdd, _id, name, userName } = ctx.session.superUserInfo;
    if (regAdd.length === 1) throw new Error('省级单位无此操作权限');
    const record = await ctx.model.WorkspaceMonitoringRecord.findOne(
      { _id: recordId, isDeleted: false },
      { taskStatus: 1 }
    ).lean();
    if (!record) throw new Error(recordId + '监测记录不存在');
    if (record.taskStatus !== 0) {
      throw new Error(
        `${recordId}监测记录状态为${record.taskStatus}, 请勿重复提交审核。`
      );
    }
    // 判断前六步内容是否填写完毕
    // 1. 校验基本信息表
    const basicInfo = await ctx.model.WorkspaceDcBasicInfo.findOne({
      workspaceMonitoringRecordId: recordId,
    }).lean();
    // 调试信息 - 生产环境可删除
    ctx.logger.info(`基本信息查询: recordId=${recordId}, 结果: ${basicInfo ? '存在' : '不存在'}`);
    if (basicInfo) {
      ctx.logger.info(`查询到的workspaceMonitoringRecordId: ${basicInfo.workspaceMonitoringRecordId}`);
    }
    if (!basicInfo) {
      throw new Error('请检查基本信息是否填写完整，请点击“下一步”或者“暂存”保存数据');
    }

    // 2. 校验上一年度监测情况及定性分析表（两个步骤）
    const jcSituation = await ctx.model.WorkspaceDcJcSituation.findOne({
      workspaceMonitoringRecordId: recordId,
    }).lean();

    if (!jcSituation) {
      // 先判断记录是否存在
      throw new Error('请检查上一年度监测情况及定性分析是否填写完整，请点击“下一步”或者“暂存”保存数据');
    } else {
      // 检查上一年度监测情况步骤（ifat字段是否存在）
      if (jcSituation.ifat === undefined) {
        throw new Error('请检查上一年度监测情况是否填写完整，请点击“下一步”或者“暂存”保存数据');
      }

      // 检查定性分析步骤（ifExistAnalysis字段是否存在）
      if (jcSituation.ifExistAnalysis === undefined) {
        throw new Error('请检查定性分析是否填写完整，请点击“下一步”或者“暂存”保存数据');
      }
    }

    // 3. 校验防护设施&用品表
    const protection = await ctx.model.WorkspaceDcProtection.findOne({
      workspaceMonitoringRecordId: recordId,
    }).lean();
    if (!protection) {
      throw new Error('请检查防护设施&用品是否填写完整，请点击“下一步”或者“暂存”保存数据');
    }

    // 4. 校验体检情况表
    const tjSituation = await ctx.model.WorkspaceDcTjSituation.findOne({
      workspaceMonitoringRecordId: recordId,
    }).lean();
    if (!tjSituation) {
      throw new Error('请检查检查情况是否填写完整，请点击“下一步”或者“暂存”保存数据');
    }

    // 5. 校验劳动者调查表
    const workerInfo = await ctx.model.WorkspaceDcWorker.findOne({
      workspaceMonitoringRecordId: recordId,
    }).lean();
    if (!workerInfo) {
      throw new Error('请检查劳动者调查表是否填写完整，请点击“下一步”或者“暂存”保存数据');
    }

    const auditRecord = await ctx.model.AuditRecord.create({
      recordId,
      auditOrgId: _id,
      submitter: name || userName,
      submitOrgId: _id,
    });
    await ctx.model.WorkspaceMonitoringRecord.updateOne(
      { _id: recordId },
      { taskStatus: 1 }
    );
    return auditRecord;
  }

  // 删除
  async delete(data) {
    const { ctx } = this;
    const { _id } = data;
    const auditRecord = await ctx.model.AuditRecord.findOne({ _id });
    if (!auditRecord) throw new Error('审核记录不存在');
    if (auditRecord.auditStatus !== 0)
      throw new Error('该记录已审核，不可删除。');
    const res = await ctx.model.AuditRecord.deleteOne({ _id });
    if (res.deletedCount) {
      await ctx.model.WorkspaceMonitoringRecord.updateOne(
        { _id: auditRecord.recordId },
        { taskStatus: 0 }
      );
      return '删除成功';
    }
    throw new Error('删除失败');
  }
  // 审核
  async update(data) {
    const { ctx } = this;
    const { _id, auditOpinion = '' } = data;
    const auditStatus = +data.auditStatus;
    if (!_id || auditStatus === undefined || ![1, 2].includes(auditStatus)) {
      throw new Error('参数不完整');
    }
    const auditRecord = await ctx.model.AuditRecord.findOne({ _id });
    if (!auditRecord) throw new Error('审核记录不存在');
    const {
      auditStatus: oldStatus,
      auditLevel,
      auditOrgId,
      recordId,
    } = auditRecord;
    if (oldStatus !== 0) throw new Error('该记录已审核，不可重复提交。');
    const taskRecord = await ctx.model.WorkspaceMonitoringRecord.findOne(
      { _id: recordId, isDeleted: false },
      { taskStatus: 1 }
    ).lean();
    if (!taskRecord) throw new Error('监测记录: ' + recordId + '不存在');
    const { regAdd, _id: curOrgId, name, userName } = ctx.session.superUserInfo;
    const auditor = name || userName;
    if (auditLevel === 1) {
      // 市级
      if (auditOrgId !== curOrgId) throw new Error('贵单位无此操作权限。');
      const res = await ctx.model.AuditRecord.updateOne(
        { _id },
        { auditStatus, auditOpinion, auditor }
      );
      if (res && res.nModified) {
        await ctx.model.WorkspaceMonitoringRecord.updateOne(
          { _id: recordId },
          { taskStatus: auditStatus === 1 ? 2 : 4 }
        );
        const newAuditRecord = await ctx.model.AuditRecord.create({
          // 重新创建一条省级审核记录
          recordId,
          auditLevel: 2,
          submitter: auditor,
          submitOrgId: curOrgId,
        });
        console.log(66666, newAuditRecord);
      }
    } else if (auditLevel === 2) {
      // 省级
      if (regAdd.length !== 1) throw new Error('只有省级单位有此操作权限');
      const res = await ctx.model.AuditRecord.updateOne(
        { _id },
        { auditStatus, auditOpinion, auditor }
      );
      if (res && res.nModified) {
        await ctx.model.WorkspaceMonitoringRecord.updateOne(
          { _id: recordId },
          { taskStatus: auditStatus === 1 ? 3 : 4 }
        );
      }
    }
  }
}

module.exports = AuditRecordService;
