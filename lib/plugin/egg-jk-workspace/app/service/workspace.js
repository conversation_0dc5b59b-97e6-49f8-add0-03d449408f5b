const Service = require('egg').Service;
const moment = require('moment');
class WorkspaceService extends Service {
  // 获取当前单位的监测任务列表
  async getTaskList(params) {
    const { ctx } = this;
    const { year, keyword, taskStatus } = params;
    const pageNum = Number(params.pageNum) || 1;
    const pageSize = Number(params.pageSize) || 10;
    const query = {};
    if (year) query.year = Number(year);
    if (taskStatus && [ '0', '1' ]) query.taskStatus = +taskStatus;

    const { regAdd, cname, name } = ctx.session.superUserInfo;
    const curUserInfo = { regAdd, cname, name };

    let model;
    const listFields = [
      'year',
      'startTime',
      'endTime',
      'taskNum',
      'taskStatus',
    ];

    if (regAdd.length === 1) {
      // 省级
      model = ctx.model.ProvincialWorkspaceMonitoring;
      listFields.push('title');
      if (keyword) {
        query.title = { $regex: keyword, $options: 'i' };
      }
    } else if (regAdd.length === 2 || regAdd.length === 3) {
      // 市级
      model = ctx.model.WorkspaceMonitoringTask;
      query.areaName = regAdd;
      query.isDeleted = false;
    } else {
      throw new Error('当前用户无权限查看该页面');
    }

    const _list = await model
      .find(query, listFields)
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .populate('provincialWorkspaceMonitoringId', 'title')
      .lean()
      .exec();

    const list = _list.map(item => ({
      ...item,
      startTime: moment(item.startTime).format('YYYY-MM-DD'),
      endTime: moment(item.endTime).format('YYYY-MM-DD'),
      title: item.title || item.provincialWorkspaceMonitoringId.title,
    }));

    const total = await model.countDocuments(query);

    return {
      curUserInfo,
      pageInfo: {
        total,
        pageNum,
        pageSize,
      },
      list,
    };
  }

  // 获取监测任务详情
  async getTaskDetail(params) {
    const { ctx } = this;
    const _id = params.id || params._id;
    if (!_id) throw new Error('getTaskDetail缺少参数_id');
    let detail = await this.getProvincialTaskDetail(_id); // 省级任务详情
    let subTaskList = [];
    if (!detail) {
      // 市级任务详情
      detail = await ctx.model.WorkspaceMonitoringTask.findOne({ _id })
        .populate('jcOrgId', 'cname')
        .populate('EnterpriseList', 'cname')
        .lean()
        .exec();
      if (!detail) return null;
      if (detail.isDeleted) throw new Error('该监测任务已被删除');
      const provincialWorkspaceMonitoringId =
        detail.provincialWorkspaceMonitoringId;
      const provincialTaskDetail = await this.getProvincialTaskDetail(
        provincialWorkspaceMonitoringId
      );
      if (!provincialTaskDetail) return null;
      detail = Object.assign({}, detail);
      detail.industryJobList = provincialTaskDetail.industryJobList;
      detail.industryCheckFactorList =
        provincialTaskDetail.industryCheckFactorList;
      detail.customFactorList = provincialTaskDetail.customFactorList;
    } else {
      // 获取市级任务
      subTaskList = await ctx.model.WorkspaceMonitoringTask.find(
        { provincialWorkspaceMonitoringId: detail._id, isDeleted: false },
        { areaName: 1, taskNum: 1 }
      )
        .lean()
        .exec();
      if (subTaskList.length > 0) {
        subTaskList = subTaskList.map(item => ({
          areaName: item.areaName.pop(),
          taskNum: item.taskNum,
        }));
      }
    }

    return {
      ...detail,
      startTime: moment(detail.startTime).format('YYYY-MM-DD'),
      endTime: moment(detail.endTime).format('YYYY-MM-DD'),
      createdAt: moment(detail.createdAt).format('YYYY-MM-DD'),
      updatedAt: moment(detail.updatedAt).format('YYYY-MM-DD'),
      subTaskList,
    };
  }

  // 获取省级任务详情
  async getProvincialTaskDetail(_id) {
    const { ctx } = this;
    if (!_id) throw new Error('getProvincialTaskDetail缺少参数_id');
    const detail = await ctx.model.ProvincialWorkspaceMonitoring.findOne(
      { _id },
      { uuid: 0 }
    )
      .populate(
        'industryCheckFactorList.checkFactorList.factorId',
        'chineseName catetory cnCsjcCode'
      )
      .populate('customFactorList', 'chineseName catetory cnCsjcCode')
      .lean()
      .exec();
    if (!detail) return null;

    const industryMap = await ctx.service.industryCategory.getFlatMap(); // industryMap 结构如：{ 'C': '制造业', '13': '食品', ... }
    const jobListMap = await this.jobListMap();

    detail.industryJobList = detail.industryJobList.map(item => ({
      industry: item.industryNo.map(industryNo => industryMap[industryNo]),
      jobList: item.jobList.map(jobNo => jobListMap[ jobNo ]),
    }));
    detail.industryCheckFactorList = detail.industryCheckFactorList.map(
      item => ({
        industry: item.industryNo.map(industryNo => industryMap[ industryNo ]),
        checkFactorList: item.checkFactorList.map(factor => ({
          factor: factor.factorId,
          selectType: factor.selectType === 1 ? '必检' : '选检',
        })),
      })
    );
    detail.areaName = [ ctx.session.superUserInfo.regAdd[ 0 ] ];
    return detail;
  }
  // 获取岗位编码表，key为岗位编码，value为岗位名称
  async jobListMap() {
    const { ctx } = this;
    try {
      const list = await ctx.model.JobList.find();
      return list.reduce((acc, { cnCsjcNo, jobName }) => {
        acc[ cnCsjcNo ] = jobName;
        return acc;
      }, {});
    } catch (error) {
      // console.log(44444, error);
      ctx.auditLog('获取岗位编码错误', error.message, 'error');
      return {};
    }
  }

  // 省级 - 获取可选的自定义危害因素列表
  async getFactorList(taskId) {
    const { ctx } = this;
    const taskDetail = await ctx.model.ProvincialWorkspaceMonitoring.findOne(
      { _id: taskId },
      { industryCheckFactorList: 1, customFactorList: 1 }
    );
    if (!taskDetail) throw new Error('未找到该监测任务:taskId');
    const hasFactor = taskDetail.customFactorList;
    taskDetail.industryCheckFactorList.forEach(ele => {
      ele.checkFactorList.forEach(factor => {
        hasFactor.push(factor.factorId);
      });
    });
    const result = await ctx.model.OccupationalexposureLimits.aggregate([
      {
        $match: {
          _id: { $nin: hasFactor },
          // cnCsjcCode: { $exists: true },
        },
      },
      {
        $project: {
          chineseName: 1,
          catetory: 1,
          _id: 1,
        },
      },
      {
        $group: {
          _id: '$catetory', // 按 catetory 分组
          children: {
            $push: {
              _id: '$_id', //
              chineseName: '$chineseName',
            },
          },
        },
      },
    ]);
    return result;
  }
  // 国家 自定义危害因素列表（添加cnCsjcCode  国家-场所监测危害因素编码）
  async getFactorListNation() {
    const { ctx } = this;
    const result = await ctx.model.OccupationalexposureLimits.aggregate([
      // 先过滤掉cnCsjcCode为"-1"的记录（注意是字符串"-1"）
      {
        $match: {
          cnCsjcCode: { $ne: '-1' },
        },
      },
      {
        $project: {
          chineseName: 1,
          catetory: 1,
          _id: 1,
          cnCsjcCode: 1,
        },
      },
      {
        $group: {
          _id: '$catetory',
          children: {
            $push: {
              _id: '$_id',
              chineseName: '$chineseName',
              cnCsjcCode: '$cnCsjcCode',
            },
          },
        },
      },
      // 再次过滤子项中cnCsjcCode为"-1"的记录
      {
        $addFields: {
          children: {
            $filter: {
              input: '$children',
              as: 'child',
              cond: { $ne: [ '$$child.cnCsjcCode', '-1' ] },
            },
          },
        },
      },
      // 过滤掉没有子项的大类
      {
        $match: {
          $expr: { $gt: [{ $size: '$children' }, 0 ] },
        },
      },
    ]);
    return result;
  }
  // 省级单位暂存/发布任务和添加自定义危害因素
  async releaseTasks(data) {
    const { ctx } = this;
    const { _id, taskList, customFactorList, taskStatus } = data;
    if (!_id) throw new Error('缺少参数_id');
    if (!taskStatus) throw new Error('缺少参数taskStatus');
    if (![ '0', '1' ].includes(taskStatus)) throw new Error('taskStatus应为字符串0或1');
    const taskDetail = await ctx.model.ProvincialWorkspaceMonitoring.findOne({
      _id,
    });
    if (!taskDetail) throw new Error('未找到该监测任务: _id');
    const { regAdd, singId } = ctx.session.superUserInfo;
    if (regAdd.length !== 1) throw new Error('非省级单位无此操作权限');
    // 添加自定义危害因素
    if (customFactorList && Array.isArray(customFactorList)) {
      const factorNum =
        await ctx.model.OccupationalexposureLimits.countDocuments({
          _id: { $in: customFactorList },
          // cnCsjcCode: { $exists: true },
        });
      if (factorNum !== customFactorList.length) throw new Error('自定义危害因素不存在或格式不对');
      await ctx.model.ProvincialWorkspaceMonitoring.updateOne(
        { _id },
        {
          $set: {
            customFactorList,
          },
        }
      );
    }
    // 发布任务
    if (taskList && Array.isArray(taskList)) {
      for (let i = 0; i < taskList.length; i++) {
        const { areaName, taskNum } = taskList[ i ];
        // if (!areaName || !taskNum) throw new Error('taskList缺少参数');
        // 上面的写法不允许为0，下面的 允许taskNum为0，但不能是undefined或null
        const item = taskList[i];
        if (item.areaName === undefined || item.areaName === null ||
            item.taskNum === undefined || item.taskNum === null) {
          throw new Error(`taskList第${i + 1}项缺少参数`);
        }
        if (typeof areaName !== 'string') throw new Error('areaName应为字符串');
        if (typeof taskNum !== 'number') throw new Error('taskNum应为数字');
        const _areaName = [ ...regAdd, areaName ];
        const newData = {
          year: taskDetail.year,
          startTime: taskDetail.startTime,
          endTime: taskDetail.endTime,
          areaName: _areaName,
          provincialWorkspaceMonitoringId: _id,
          taskNum,
          lastModifierId: singId,
        };
        const task = await ctx.model.WorkspaceMonitoringTask.findOne({
          areaName: _areaName,
          provincialWorkspaceMonitoringId: _id,
          isDeleted: false,
        });
        if (task) {
          await ctx.model.WorkspaceMonitoringTask.updateOne(
            {
              _id: task._id,
            },
            { $set: newData }
          );
        } else {
          await ctx.model.WorkspaceMonitoringTask.create(newData);
        }
      }
      if (taskStatus === '1' && taskDetail.taskStatus === 0) {
        await ctx.model.ProvincialWorkspaceMonitoring.updateOne(
          { _id },
          { $set: { taskStatus: 1 } }
        );
      }
    }
  }

  // 市级单位选择任务的企业名单
  async addEnterpriseList(data) {
    const { ctx } = this;
    const { regAdd, singId } = ctx.session.superUserInfo;
    if (regAdd.length !== 2) throw new Error('非市级单位无此操作权限');

    const { _id, EnterpriseList, startTime, endTime } = data;
    if (!_id) throw new Error('缺少参数_id');
    const taskDetail = await ctx.model.WorkspaceMonitoringTask.findOne({
      _id,
      isDeleted: false,
      areaName: regAdd,
    });
    if (!taskDetail) throw new Error('未找到该监测任务: _id');
    if (!EnterpriseList || !Array.isArray(EnterpriseList)) throw new Error('缺少参数EnterpriseList');

    const EnterpriseCount = await ctx.model.Adminorg.countDocuments({
      _id: { $in: EnterpriseList },
    });
    if (EnterpriseCount !== EnterpriseList.length) throw new Error('企业名单不存在或格式不对');
    const updateData = {
      EnterpriseList,
      jcOrgId: ctx.session.superUserInfo._id,
      lastModifierId: singId,
      taskStatus: 1,
    };
    if (startTime) updateData.startTime = new Date(startTime);
    if (endTime) updateData.endTime = new Date(endTime);
    return await ctx.model.WorkspaceMonitoringTask.updateOne(
      { _id },
      {
        $set: updateData,
      }
    );
  }

  // 取可选委托单位/检测机构/用人单位
  async getOrgList(params) {
    const { ctx } = this;
    const { task_id } = params;
    if (!task_id) throw new Error('缺少参数task_id');
    const { regAdd } = ctx.session.superUserInfo;
    if (regAdd.length !== 2) throw new Error('非市级单位无此操作权限');
    const taskDetail = await ctx.model.WorkspaceMonitoringTask.findOne({
      _id: task_id,
      isDeleted: false,
    });
    if (!taskDetail) throw new Error('未找到该监测任务: task_id');
    const EnterpriseList = await ctx.model.Adminorg.find(
      {
        _id: { $in: taskDetail.EnterpriseList },
      },
      { cname: 1 }
    );
    const serviceOrgList = await ctx.model.ServiceOrg.find({}, { name: 1 });
    const superUserList = await ctx.model.SuperUser.find(
      { regAdd, type: { $in: [ 1, 3 ] } },
      { cname: 1 }
    );
    return {
      EnterpriseList,
      serviceOrgList,
      superUserList,
    };
  }
  // 创建监测记录
  async addMonitoringRecord(data) {
    const { ctx } = this;
    const { singId, regAdd, _id } = ctx.session.superUserInfo;
    if (![ 2, 3 ].includes(regAdd.length)) throw new Error('非市/区级单位无此操作权限');
    const { taskId, jcOrgId, EnterpriseID, serviceOrgId } = data;
    if (!taskId) throw new Error('缺少参数task_id');
    if (!EnterpriseID) throw new Error('请选择监测单位');
    if (!serviceOrgId) throw new Error('请选择检测机构');
    const taskDetail = await ctx.model.WorkspaceMonitoringTask.findOne({
      _id: taskId,
      isDeleted: false,
    });
    if (!taskDetail) throw new Error('未找到该监测任务: task_id');
    const workspaceMonitoringRecord =
      await ctx.model.WorkspaceMonitoringRecord.findOne({
        taskId,
        EnterpriseID,
        isDeleted: false,
      });
    if (workspaceMonitoringRecord) throw new Error('该监测单位任务已分配，请勿重复添加。');
    const res = await ctx.model.WorkspaceMonitoringRecord.create({
      ...data,
      jcOrgId: jcOrgId || _id,
      creatorId: singId,
    });
    return res;
  }
  // 更新监测记录
  async updateMonitoringRecord(data) {
    const { ctx } = this;
    const { singId, regAdd } = ctx.session.superUserInfo;
    if (![ 2, 3 ].includes(regAdd.length)) throw new Error('非市/区级单位无此操作权限');
    const { _id, jcOrgId, EnterpriseID, serviceOrgId } = data;
    if (!_id) throw new Error('缺少参数_id');
    const workspaceMonitoringRecord =
      await ctx.model.WorkspaceMonitoringRecord.findOne({
        _id,
        isDeleted: false,
      });
    if (!workspaceMonitoringRecord) throw new Error('未找到该监测记录，无法更新。');
    if (workspaceMonitoringRecord.taskStatus !== 0) throw new Error('该监测记录已提交审核，无法更新。');
    const newWorkspaceMonitoringRecord =
      await ctx.model.WorkspaceMonitoringRecord.findOne({
        taskId: workspaceMonitoringRecord.taskId,
        EnterpriseID,
        isDeleted: false,
      });
    if (
      newWorkspaceMonitoringRecord &&
      newWorkspaceMonitoringRecord._id !== _id
    ) {
      throw new Error('该监测单位任务已分配，请勿重复添加。');
    }
    const updateData = { creatorId: singId };
    if (jcOrgId) updateData.jcOrgId = jcOrgId;
    if (
      serviceOrgId &&
      workspaceMonitoringRecord.serviceOrgId !== serviceOrgId
    ) {
      if (workspaceMonitoringRecord.jobHealthId) {
        throw new Error('该监测记录已生成检测报告，无法修改检测机构。');
      } else {
        updateData.serviceOrgId = serviceOrgId;
      }
    }
    if (EnterpriseID) updateData.EnterpriseID = EnterpriseID;
    await ctx.model.WorkspaceMonitoringRecord.updateOne(
      {
        _id,
      },
      { $set: updateData }
    );
    return ctx.model.WorkspaceMonitoringRecord.findOne({
      _id,
    });
  }

  // 删除监测记录
  async deleteMonitoringRecord(data) {
    const { ctx } = this;
    console.log(111, data);
    const { singId, regAdd } = ctx.session.superUserInfo;
    if (![ 2, 3 ].includes(regAdd.length)) {
      throw new Error('非市/区级单位无此操作权限');
    }
    const { _id, ids } = data;
    if (!_id && !ids) throw new Error('缺少参数_id');
    if (ids && !Array.isArray(ids)) throw new Error('ids应为数组');
    const _ids = _id ? [ _id ] : ids;
    const list = await ctx.model.WorkspaceMonitoringRecord.find({
      _id: { $in: _ids },
    });
    let errMsg = '';
    for (const item of list) {
      if (item.taskStatus !== 0) {
        errMsg += `监测记录${item._id}已提交审核，无法删除。`;
      }
    }
    if (errMsg) throw new Error(errMsg);
    const res = await ctx.model.WorkspaceMonitoringRecord.updateMany(
      {
        _id: { $in: _ids },
      },
      {
        $set: { isDeleted: true, creatorId: singId },
      }
    );
    ctx.auditLog(singId + '删除监测记录', _ids.join('、'), 'info');
    return res;
  }
  // 获取监测记录列表
  async monitoringRecordList(params) {
    const { ctx } = this;
    const { regAdd } = ctx.session.superUserInfo;
    const { taskStatus, taskId } = params;
    const pageSize = +params.pageSize || 10;
    const curPage = +params.curPage || 1;

    const query = { isDeleted: false };

    if (taskStatus && typeof taskStatus === 'string') {
      query.taskStatus = { $in: taskStatus.split(',').map(item => +item) };
    }

    const taskQuery = { isDeleted: false, areaName: { $all: regAdd } };
    const taskList = await ctx.model.WorkspaceMonitoringTask.find(taskQuery);
    const taskIds = taskList.map(item => item._id);

    if (taskId) {
      if (!taskIds.includes(taskId)) throw new Error('taskId超出了您的权限范围');
      query.taskId = taskId;
    } else {
      query.taskId = { $in: taskIds };
    }

    const list = await ctx.model.WorkspaceMonitoringRecord.find(query)
      .skip((curPage - 1) * pageSize)
      .limit(pageSize)
      .populate('taskId')
      .populate('jcOrgId', 'cname')
      .populate('EnterpriseID', 'cname')
      .populate('serviceOrgId', 'name');

    const total = await ctx.model.WorkspaceMonitoringRecord.countDocuments(
      query
    );
    return {
      list: list.map(item => ({
        ...item.toObject(),
        createdAt: moment(item.createdAt).format('YYYY-MM-DD HH:mm'),
        updatedAt: moment(item.updatedAt).format('YYYY-MM-DD HH:mm'),
      })),
      total,
    };
  }
  // 监测进度查询
  async monitorProgress(params) {
    const { ctx } = this;
    const { regAdd } = ctx.session.superUserInfo;
    const { pageNum = 1, pageSize = 10, year, areaName } = params;
    const skip = (pageNum - 1) * pageSize;

    // 重点行业列表
    const importIndustry = [
      '煤炭开采和洗选业',
      '石油和天然气开采业',
      '黑色金属矿采选业',
      '有色金属矿采选业',
      '非金属矿采选业',
      '黑色金属冶炼和压延加工业（如涉及炼焦参照煤炭加工的炼焦岗位）',
      '有色金属冶炼和压延加工业',
      '皮革、毛皮、羽毛及其制品和制鞋业',
      '文教、工美、体育和娱乐用品制造业',
      '石油、煤炭及其他燃料加工',
      '化学原料和化学制品制造业',
      '医药制造业',
      '化学纤维制造业',
      '橡胶和塑料制品业',
      '非金属矿物制品业',
      '金属制品业',
      '汽车制造业',
      '通用设备制造业',
      '专用设备制造业',
      '电气机械和器材制造业',
      '铁路、船舶、航空航天和其他运输设备制造业',
      '木材加工和木、竹、藤、棕、草制品业',
      '家具制造业',
      '印刷和记录媒介复制业',
      '计算机、通信和其他电子设备制造业',
      '废弃资源综合利用业',
      '电力、热力生产和供应业',
      '建筑业',
    ];

    // 1. 构建完整的行业编码与名称映射表
    const industryCodeMap = {};
    const industryCategories = await ctx.model.IndustryCategory.find({});

    // 递归处理多级行业分类
    const processIndustryChildren = children => {
      if (!children || !children.length) return;

      children.forEach(child => {
        industryCodeMap[ child.value ] = child.label;
        if (child.children && child.children.length) {
          processIndustryChildren(child.children);
        }
      });
    };

    // 处理顶级行业分类
    industryCategories.forEach(category => {
      industryCodeMap[ category.value ] = category.label;
      if (category.children && category.children.length) {
        processIndustryChildren(category.children);
      }
    });

    const result = [];
    let total = 0;

    if (regAdd.length === 1) {
      // 省级逻辑
      // 查询省级任务（已发布和已完成状态 - 根据数据实际情况调整状态值）
      // 注意：这里假设1=已发布，2=已完成，根据实际业务调整
      const provincialFilter = year
        ? { year, taskStatus: { $in: [ 1, 2 ] } } // 已发布和已完成状态
        : { taskStatus: { $in: [ 1, 2 ] } };

      // 查询省级任务并输出日志以便调试
      const provincialTasks =
        await ctx.model.ProvincialWorkspaceMonitoring.find(provincialFilter);
      ctx.logger.info(
        `省级任务查询结果: ${
          provincialTasks.length
        }条，ID列表: ${provincialTasks.map(t => t._id).join(',')}`
      );

      const provincialTaskIds = provincialTasks.map(task => task._id);

      // 如果没有找到省级任务，直接返回空结果
      if (provincialTaskIds.length === 0) {
        return {
          list: [],
          pageNum: Number(pageNum),
          pageSize: Number(pageSize),
          total: 0,
        };
      }

      // 查询市级任务 - 包含所有状态（0-待发布，1-已发布，2-已完成）
      const cityTaskFilter = {
        provincialWorkspaceMonitoringId: { $in: provincialTaskIds },
        isDeleted: false,
      };

      // 当areaName参数存在且不为空时，才添加第二级地区筛选
      if (areaName && areaName.trim() !== '') {
        cityTaskFilter.$expr = {
          $eq: [{ $arrayElemAt: [ '$areaName', 1 ] }, areaName.trim() ],
        };
      }

      // 添加年份筛选条件（如果提供）
      if (year) {
        cityTaskFilter.year = year;
      }

      // 查询市级任务并输出日志以便调试
      const cityTasks = await ctx.model.WorkspaceMonitoringTask.find(
        cityTaskFilter
      )
        .skip(skip)
        .limit(Number(pageSize));
      total = await ctx.model.WorkspaceMonitoringTask.countDocuments(
        cityTaskFilter
      );
      ctx.logger.info(
        `市级任务查询结果: ${cityTasks.length}条，总条数: ${total}`
      );

      // 遍历计算统计数据
      for (const task of cityTasks) {
        const taskId = task._id;
        const areaName = task.areaName.join('-');
        const taskNum = task.taskNum || 0;

        // 查询监测记录
        const records = await ctx.model.WorkspaceMonitoringRecord.find({
          taskId,
          isDeleted: false,
        });

        // 状态统计
        const submitCount = records.filter(r => r.taskStatus > 0).length;
        const draftCount = records.filter(r => r.taskStatus === 0).length;
        const cityReviewPending = records.filter(
          r => r.taskStatus === 2
        ).length;
        const cityReviewCount = records.filter(r =>
          [ 2, 3, 4, 5, 6 ].includes(r.taskStatus)
        ).length;
        const cityCompletionRate =
          taskNum > 0 ? ((cityReviewCount / taskNum) * 100).toFixed(2) : '0.00';

        const provincialReviewCount = records.filter(r =>
          [ 3, 4, 5, 6 ].includes(r.taskStatus)
        ).length;
        const provincialCompletionRate =
          taskNum > 0
            ? ((provincialReviewCount / taskNum) * 100).toFixed(2)
            : '0.00';

        // 重点行业企业统计
        const qualifiedRecordIds = records
          .filter(r => [ 3, 5, 6 ].includes(r.taskStatus))
          .map(r => r._id);

        const basicInfos = await ctx.model.WorkspaceDcBasicInfo.find({
          workspaceMonitoringRecordId: { $in: qualifiedRecordIds },
        });

        // 计算重点行业企业数
        let keyIndustryCount = 0;
        for (const info of basicInfos) {
          if (info.industryCode && info.industryCode.length >= 2) {
            // 取行业编码数组第二项
            const code = info.industryCode[ 1 ];
            const industryName = industryCodeMap[ code ] || '';

            // 验证是否属于重点行业
            if (importIndustry.includes(industryName)) {
              keyIndustryCount++;
            }
          }
        }

        // 重点行业占比
        const keyIndustryRate =
          taskNum > 0
            ? ((keyIndustryCount / taskNum) * 100).toFixed(2)
            : '0.00';

        result.push({
          areaName,
          taskNum,
          draftCount,
          submitCount,
          cityReviewPending,
          cityReviewCount,
          cityCompletionRate,
          provincialReviewCount,
          provincialCompletionRate,
          keyIndustryCount,
          keyIndustryRate,
        });
      }
    } else {
      // 市/区级逻辑（保持不变）
      // 构建地区筛选条件（使用数组完全匹配）
      const districtTaskFilter = {
        isDeleted: false,
        areaName: regAdd, // 直接匹配数组内容，如[  "浙江省", "杭州市"  ]
      };
      if (year) districtTaskFilter.year = year;

      const districtTasks = await ctx.model.WorkspaceMonitoringTask.find(
        districtTaskFilter
      )
        .skip(skip)
        .limit(Number(pageSize));
      total = await ctx.model.WorkspaceMonitoringTask.countDocuments(
        districtTaskFilter
      );

      // 遍历计算统计数据
      for (const task of districtTasks) {
        const taskId = task._id;
        const areaName = task.areaName.join('-');
        const taskNum = task.taskNum || 0;

        // 查询监测记录
        const records = await ctx.model.WorkspaceMonitoringRecord.find({
          taskId,
          isDeleted: false,
        });

        // 状态统计
        const submitCount = records.filter(r => r.taskStatus > 0).length;
        const draftCount = records.filter(r => r.taskStatus === 0).length;
        const reviewPending = records.filter(r => r.taskStatus === 1).length;
        const cityReviewCount = records.filter(r =>
          [ 2, 3, 4, 5, 6 ].includes(r.taskStatus)
        ).length;
        const cityCompletionRate =
          taskNum > 0 ? ((cityReviewCount / taskNum) * 100).toFixed(2) : '0.00';

        result.push({
          areaName,
          taskNum,
          draftCount,
          submitCount,
          reviewPending,
          cityReviewCount,
          cityCompletionRate,
        });
      }
    }

    return {
      list: result,
      pageNum: Number(pageNum),
      pageSize: Number(pageSize),
      total,
    };
  }

  // // 监测记录查询
  // async monitorRecord(params) {
  //   const { ctx } = this;
  //   console.log(params);
  //   // 查询监测记录列表WorkspaceMonitoringRecord

  // }

  // 获取监测记录统计列表
  async monitorRecordStatistics(params) {
    const { ctx } = this;
    const { regAdd } = ctx.session.superUserInfo;
    const {
      year,
      unitName,
      industryCode,
      keyJob,
      enterpriseScale,
      economicNo,
      ifDeclare,
      ifAnnualUpdate,
      auditTimeStart,
      auditTimeEnd,
      taskStatus,
      taskId,
      pageSize = 10,
      curPage = 1,
    } = params;

    // 转换分页参数
    const page = +curPage;
    const size = +pageSize;
    const skip = (page - 1) * size;

    // 1. 权限控制：获取有权限的任务ID
    const taskQuery = { isDeleted: false, areaName: { $all: regAdd } };
    if (year) {
      taskQuery.year = +year;
    }
    const taskList = await ctx.model.WorkspaceMonitoringTask.find(
      taskQuery,
      '_id year areaName'
    );
    const taskIds = taskList.map(function(item) {
      return item._id;
    });

    // 2. 构建监测记录查询条件
    const recordQuery = { isDeleted: false };
    if (taskId) {
      if (taskIds.indexOf(taskId) === -1) {
        throw new Error('taskId超出了您的权限范围');
      }
      recordQuery.taskId = taskId;
    } else {
      recordQuery.taskId = { $in: taskIds };
    }

    if (taskStatus && typeof taskStatus === 'string') {
      recordQuery.taskStatus = {
        $in: taskStatus.split(',').map(function(item) {
          return +item;
        }),
      };
    }

    // 3. 构建基本信息查询条件（用于后续过滤）
    const hasBasicInfoConditions =
      industryCode ||
      keyJob ||
      enterpriseScale ||
      economicNo ||
      ifDeclare !== undefined ||
      ifAnnualUpdate !== undefined;

    const basicInfoConditions = {};
    // if (unitName) {
    //   basicInfoConditions.unitName = { $regex: unitName, $options: 'i' };
    // }
    if (industryCode) {
      basicInfoConditions.industryCode = { $in: industryCode.split(',') };
    }
    if (keyJob) {
      // 匹配keyJobInvestigationSituationList数组中是否存在指定岗位编码
      basicInfoConditions[ 'keyJobInvestigationSituationList.jobNo' ] = keyJob;
    }
    if (enterpriseScale) {
      basicInfoConditions.enterpriseScale = +enterpriseScale;
    }
    if (economicNo) {
      basicInfoConditions.economicNo = { $in: economicNo.split(',') };
    }
    if (ifDeclare !== undefined) {
      basicInfoConditions.ifDeclare = ifDeclare === 'true';
    }
    if (ifAnnualUpdate !== undefined) {
      basicInfoConditions.ifAnnualUpdate = ifAnnualUpdate === 'true';
    }

    // 4. 查询符合条件的基本信息记录，获取对应的监测记录ID
    let relatedRecordIds = [];
    if (hasBasicInfoConditions) {
      const basicInfoRecords = await ctx.model.WorkspaceDcBasicInfo.find(
        basicInfoConditions,
        'workspaceMonitoringRecordId EnterpriseID'
      );
      relatedRecordIds = basicInfoRecords.map(
        item => item.workspaceMonitoringRecordId
      );
    }

    // 5. 组合监测记录查询条件
    if (hasBasicInfoConditions && relatedRecordIds.length > 0) {
      recordQuery._id = { $in: relatedRecordIds };
    } else if (hasBasicInfoConditions && relatedRecordIds.length === 0) {
      return {
        list: [],
        total: 0,
        curPage: page,
        pageSize: size,
        totalPages: 0,
      };
    }

    // 新增：按EnterpriseID.cname过滤（关键修改）
    if (unitName) {
      // 查询Adminorg表中cname匹配的企业ID
      const matchedEnterprises = await ctx.model.Adminorg.find(
        { cname: { $regex: unitName, $options: 'i' } }, // 模糊匹配企业名称
        '_id'
      );
      // 即使没有匹配的企业，也不直接返回空（保留其他条件的可能）
      if (matchedEnterprises.length > 0) {
        recordQuery.EnterpriseID = {
          $in: matchedEnterprises.map(ent => ent._id),
        };
      } else {
        // 没有匹配的企业时，直接返回空
        return {
          list: [],
          total: 0,
          curPage: page,
          pageSize: size,
          totalPages: 0,
        };
      }
    }
    // 当没有基本信息查询条件时，不做额外过滤

    // 6. 审核时间过滤
    if (auditTimeStart || auditTimeEnd) {
      // 仅对taskStatus >= 3的记录应用审核时间过滤
      recordQuery.taskStatus = { $gte: 3 };
      const timeConditions = {};
      if (auditTimeStart) {
        timeConditions.$gte = new Date(auditTimeStart);
      }
      if (auditTimeEnd) {
        timeConditions.$lte = new Date(auditTimeEnd);
      }
      recordQuery.updatedAt = timeConditions;
    }

    // 7. 查询监测记录主数据（使用populate关联）
    const queryBuilder = ctx.model.WorkspaceMonitoringRecord.find(recordQuery)
      .skip(skip)
      .limit(size)
      // 关联任务表，只返回需要的字段
      .populate('taskId', 'year areaName')
      // 关联委托单位，只返回_id和cname
      .populate('jcOrgId', '_id cname')
      // 关联用人单位，只返回_id和cname
      .populate('EnterpriseID', '_id cname')
      // 关联检测机构，只返回_id和name
      .populate('serviceOrgId', '_id name');

    const list = await queryBuilder.exec();

    // 8. 关联基本信息表（一对一关系）
    const recordsWithBasicInfo = await Promise.all(
      list.map(function(record) {
        return ctx.model.WorkspaceDcBasicInfo.findOne({
          workspaceMonitoringRecordId: record._id,
        }).then(function(basicInfo) {
          // 处理任务信息
          let taskYear = null;
          let taskAreaName = null;
          if (record.taskId) {
            taskYear = record.taskId.year;
            taskAreaName = record.taskId.areaName;
          }

          // 处理审核时间 - 只对taskStatus >= 3的记录设置审核时间
          let auditTime = null;
          if (record.taskStatus >= 3) {
            auditTime = record.updatedAt;
          }

          return {
            ...record.toObject(),
            // 基本信息字段（使用传统条件判断）
            unitName: basicInfo ? basicInfo.unitName : null,
            workAddr: basicInfo ? basicInfo.workAddr : null,
            industryCode: basicInfo ? basicInfo.industryCode : null,
            economicNo: basicInfo ? basicInfo.economicNo : null,
            keyJobInvestigationSituationList: basicInfo
              ? basicInfo.keyJobInvestigationSituationList
              : null,
            enterpriseScale: basicInfo ? basicInfo.enterpriseScale : null,
            ifDeclare: basicInfo ? basicInfo.ifDeclare : null,
            ifAnnualUpdate: basicInfo ? basicInfo.ifAnnualUpdate : null,
            // 任务信息
            taskYear,
            taskAreaName,
            // 审核时间
            auditTime,
          };
        });
      })
    );

    // 9. 计算总数
    const total = await ctx.model.WorkspaceMonitoringRecord.countDocuments(
      recordQuery
    );

    // 10. 格式化时间并返回
    const resultList = recordsWithBasicInfo.map(function(item) {
      // 处理创建时间
      let createdAt = '';
      if (item.createdAt) {
        createdAt = moment(item.createdAt).format('YYYY-MM-DD HH:mm');
      }

      // 处理更新时间
      let updatedAt = '';
      if (item.updatedAt) {
        updatedAt = moment(item.updatedAt).format('YYYY-MM-DD HH:mm');
      }

      // 处理审核时间
      let auditTime = '';
      if (item.auditTime) {
        auditTime = moment(item.auditTime).format('YYYY-MM-DD HH:mm');
      }

      return {
        ...item,
        createdAt,
        updatedAt,
        auditTime,
      };
    });

    return {
      list: resultList,
      total,
      curPage: page,
      pageSize: size,
      totalPages: Math.ceil(total / size),
    };
  }
  // 获取劳动者调查表预填写信息
  async previewWorker(params) {
    const { EnterpriseID } = params;
    if (!EnterpriseID) {
      throw new Error('缺少必要参数EnterpriseID');
    }

    // 查询符合条件的workspace数据并关联员工信息
    const workspaces = await this.ctx.model.Workspace.find({ EnterpriseID })
      .select('workTypeName workspaceName workDays exposureHours employees stations')
      .populate({
        path: 'employees.employeeId',
        model: 'Employees',
        select: 'name',
      })
      .lean();

    // 处理结果，兼容不支持?.的环境
    return workspaces.map(workspace => {
      // 处理employees数组，用&&判断存在性
      const formattedEmployees = workspace.employees.map(emp => {
        // 先判断emp.employeeId是否存在，再获取name
        const employeeName = emp.employeeId && emp.employeeId.name ? emp.employeeId.name : null;
        return { name: employeeName };
      });

      return {
        workTypeName: workspace.workTypeName,
        workspaceName: workspace.workspaceName,
        workDays: workspace.workDays,
        exposureHours: workspace.exposureHours,
        employees: formattedEmployees,
        stations: workspace.stations,
      };
    });
  }
  // 体检因素预填写
  async previewPhysicalHazard(params) {
    const { EnterpriseID } = params;
    if (!EnterpriseID) {
      throw new Error('缺少必要参数EnterpriseID');
    }

    // 计算去年的年份（如2025年则为2024）
    const lastYear = new Date().getFullYear() - 1;
    // 转换为字符串，匹配healthcheck表中year字段的String类型
    const lastYearStr = lastYear.toString();

    // 查询符合条件的suspect数据并关联healthcheck表（过滤去年的记录）
    const suspects = await this.ctx.model.Suspect.find({ EnterpriseID })
      .select('harmFactors batch') // 只保留需要的字段
      .populate({
        path: 'batch',
        model: 'healthcheck',
        select: 'actuallNum re_examination normal year', // 额外保留year用于验证
        match: { year: lastYearStr }, // 只关联year为去年的healthcheck记录
      })
      .lean();

    // 处理结果，过滤掉未关联到有效healthcheck的记录（可选），并计算异常人数
    return suspects
      .filter(suspect => suspect.batch) // 只保留有有效关联的记录（如果需要）
      .map(suspect => {
        const healthcheck = suspect.batch;
        const actualNum = healthcheck.actuallNum || 0;
        const normalNum = healthcheck.normal || 0;
        const abnormalNum = actualNum - normalNum;

        return {
          harmFactors: suspect.harmFactors,
          healthcheck: {
            actuallNum: actualNum,
            re_examination: healthcheck.re_examination || 0,
            normal: normalNum,
            abnormalNum,
            year: healthcheck.year, // 可返回年份用于确认
          },
        };
      });
  }
  // 体检总结报告预填写
  async previewCheckSum(params) {
    const { EnterpriseID } = params;
    if (!EnterpriseID) {
      throw new Error('缺少必要参数EnterpriseID');
    }

    // 计算去年的年份（如当前是2025年则为2024）
    const lastYear = new Date().getFullYear() - 1;
    const lastYearStr = lastYear.toString();

    // 查询符合条件的去年的healthcheck数据并关联PhysicalExamOrg表
    const healthchecks = await this.ctx.model.Healthcheck.find({
      EnterpriseID,
      year: lastYearStr, // 筛选去年的数据
    })
      .select('organization projectNumber medicalExaminationReport physicalExaminationOrgID year')
      .populate({
        path: 'physicalExaminationOrgID',
        model: 'PhysicalExamOrg',
        select: 'organization',
      })
      .lean();

    // 处理查询结果
    return healthchecks.map(item => {
      // 处理体检报告文件名：先判断medicalExaminationReport是否存在，再取fileName
      let reportFileName = '';
      if (item.medicalExaminationReport && item.medicalExaminationReport.fileName) {
        reportFileName = item.medicalExaminationReport.fileName;
      }
      // 处理关联的体检机构社会统一信用代码
      let orgCode = '';
      if (item.physicalExaminationOrgID && item.physicalExaminationOrgID.organization) {
        orgCode = item.physicalExaminationOrgID.organization;
      }

      return {
        organization: item.organization || '', // 检测机构名（空值处理）
        projectNumber: item.projectNumber || '', // 报告编号（空值处理）
        medicalExaminationReportFileName: reportFileName, // 体检报告原文件名
        physicalExamOrgCode: orgCode, // 体检机构社会统一信用代码
        year: item.year || '', // 年份（空值处理）
      };
    });
  }
}

module.exports = WorkspaceService;
