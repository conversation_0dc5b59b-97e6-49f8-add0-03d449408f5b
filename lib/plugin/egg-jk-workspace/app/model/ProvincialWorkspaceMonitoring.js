/**
 * 省级年度工作场所职业病危害因素监测任务
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const ProvincialWorkspaceMonitoringSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    uuid: { // 省级计划唯一标识，由国家平台生成并返回
      type: String,
    },
    title: { // 任务标题
      type: String,
      trim: true,
      required: true,
    },
    year: { // 计划年份
      type: Number,
      default: new Date().getFullYear(),
    },
    startTime: { // 任务开始时间
      type: Date,
    },
    endTime: { // 任务结束时间
      type: Date,
    },
    industryJobList: [{ // 重点岗位/环节
      industryNo: [ String ], // 行业编码, 对应industryCategory表中的value
      jobList: [ String ], // 岗位编码
    }],
    industryCheckFactorList: [{ // 重点职业病危害因素
      industryNo: [ String ], // 行业编码, 对应industryCategory表中的value
      checkFactorList: [{
        factorId: { // 危害因素
          type: String,
          ref: 'occupationalExposureLimits',
        },
        selectType: {
          type: Number,
          enum: [ 1, 2 ], // 1: 必检因素，2：选检因素
        },
      }],
    }],
    // 自定义危害因素
    customFactorList: [{
      type: String,
      ref: 'occupationalExposureLimits',
    }],
    taskNum: { // 任务数
      type: Number,
      set: v => parseInt(v),
    },
    taskStatus: { // 任务状态
      type: Number,
      default: 0,
      enum: [ 0, 1 ], // 0-待发布 1-已发布
    },
  }, { timestamps: true });

  return mongoose.model('ProvincialWorkspaceMonitoring', ProvincialWorkspaceMonitoringSchema, 'provincialWorkspaceMonitoring');
};
