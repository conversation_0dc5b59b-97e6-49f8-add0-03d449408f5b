/**
 * 工作场所职业病危害因素 - 监测记录 - 体检情况
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const WorkspaceDcTjSituationSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    workspaceMonitoringRecordId: { // 监测记录ID
      type: String,
      ref: 'WorkspaceMonitoringRecord',
      unique: true,
      index: true,
    },
    EnterpriseID: { // 用人单位，即监测单位
      type: String,
      ref: 'Adminorg',
      required: true,
    },
    ifhea: Boolean, // 上一年度职业健康检查开展情况, 当未开展时以下职业健康检查开展情况数据无需上传!!!
    checkTotalPeoples: Number, // 体检总人数
    ifheaDust: Boolean, // 有无粉尘_健康检查
    ifheaChemistry: Boolean, // 有无化学物质_健康检查
    ifheaPhysics: Boolean, // 有无物理因素_健康检查
    healthcustodyReportList: [{ // 职业健康检查报告明细
      uuid: String, // 体检总结报告唯一标识(数据上传方生成，用于文件上传时匹配报告记录)
      unitName: String, // 检查机构名称
      creditCode: String, // 检查机构社会信用代码(18)
      ifExistCustodyReport: Boolean, // 是否存在体检总结报告
      reportNo: String, // 体检总结报告编号(ifExistCustodyReport为true时，必填)
      fileName: String, // 体检总结报告附件名称(ifExistCustodyReport为true时，必填)
    }],
    supervisionItemList: [{ // 职业健康检查情况-危害因素明细
      ifheaFactor: Boolean, // 是否检查
      factortype: {
        type: Number,
        enum: [ 1, 2, 3 ], // 1：粉尘因素；2：化学因素；3：物理因素
      },
      factorId: { // 监测危害因素
        type: String,
        ref: 'occupationalExposureLimits',
      },
      checkMonitorPeoples: Number, // 体检人数(ifheaFactor为true时必填, 小于等于体检总人数)
      checkShouldPeoples: Number, // 应复查人数(ifheaFactor为true时必填, 小于等于体检人数)
      checkActualPeoples: Number, // 实际复查人数(ifheaFactor为true时必填, 小于等于应复查人数)
      unusualNum: Number, // 异常人数(ifheaFactor为true时必填, 小于等于体检人数)
    }],

  }, { timestamps: true });

  return mongoose.model('WorkspaceDcTjSituation', WorkspaceDcTjSituationSchema, 'workspaceDcTjSituation');
};
