/**
 * 场所监测审核记录
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const AuditRecordSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    recordId: { // 场所监测记录ID
      type: String,
      ref: 'WorkspaceMonitoringRecord',
      required: true,
    },
    auditLevel: { // 审核级别
      type: Number,
      enum: [ 1, 2, 3 ], // 1-市级，2-省级
      default: 1,
    },
    auditOrgId: { // 审核机构ID
      type: String,
      ref: 'SuperUser',
    },
    auditor: String, // 审核人姓名
    auditStatus: { // 审核状态
      type: Number,
      enum: [ 0, 1, 2 ], // 0-待审核 1-审核通过，2-审核不通过（退回/驳回）
      default: 0,
    },
    auditOpinion: String, // 审核意见

    submitOrgId: { // 提交机构
      type: String,
      ref: 'SuperUser',
    },
    submitter: { // 提交人姓名
      type: String,
    },

  }, { timestamps: true });

  return mongoose.model('AuditRecord', AuditRecordSchema, 'auditRecord');
};
