/**
 * 工作场所职业病危害因素 - 监测记录 - 劳动者调查表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const WorkspaceDcWorkerSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    workspaceMonitoringRecordId: { // 监测记录ID
      type: String,
      ref: 'WorkspaceMonitoringRecord',
      unique: true,
      index: true,
    },
    EnterpriseID: { // 用人单位，即监测单位
      type: String,
      ref: 'Adminorg',
      required: true,
    },

    workerInvestigationSituationList: [{ // 劳动者调查表
      workplaceName: String, // 工作场所名称, 不能重复
      jobList: [{ // 岗位集合
        uuid: String, // 岗位唯一标识
        jobNo: String, // 岗位编码
        jobNameOther: String, // 其他岗位名称, 岗位环节编码为20070231时，必填
        jobType: {
          type: Number,
          enum: [ 1, 2, 3 ], // 1：固定岗位 2：流动岗位 3：巡检岗位
        },
        jobPersonNum: Number, // 岗位人数
        operateNum: Number, // 每班人数
        workType: {
          type: Number,
          enum: [ 1, 2 ], // 1：轮班制 2：单班制
        },
        workShifts: {
          type: Number,
          enum: [ 1, 2, 3, 4, 5, 6, 99 ], // 工作班制为轮班制时，必填. 1：两班两运转；2：三班两运转；3：三班三运转；4：四班三运转；5：五班三运转；6：五班四运转；99：其他
        },
        otherWorkShifts: Number, // 其他班制数, 班制数为其他时，必填
        totalHour: String, // 班制时长（h/d）范围（1~24）当工作班制为轮班制时，班制时长必须等于contactHour之和; 当工作班制为单班制时，班制时长必须大于等于contactHour之和
        weekDay: { // 每周接触天数（d/w）
          type: Number,
          enum: [ 1, 2, 3, 4, 5, 6, 7 ], // 1-7
        },
        workerName: String, // 劳动者姓名
        workerInvestigationFactorList: [{ // 接触危害因素集合
          startTimeFlag: { // 工作开始时间标记
            type: Number,
            enum: [ 1, 2 ], // 1：当日 2：次日; 当工作开始时间标记=2时，工作结束时间标记只能为2
          },
          startTime: String, // 工作开始时间（HH:mm）
          endTimeFlag: { // 工作结束时间标记
            type: Number,
            enum: [ 1, 2 ], // 1：当日 2：次日; 当工作开始时间标记=2时，工作结束时间标记只能为2
          },
          endTime: String, // 工作结束时间（HH:mm）必须大于工作开始时间
          workPlace: String, // 工作地点,jobType=1时，存在接害的工作地点必须一致
          workContent: String, // 工作内容
          factorId: [ String ], // 危害因素id, 未接触危害因素不选
          contactHour: String, // 接触时间, 单位：h
        }],
      }],

    }],
    surveyor: String, // 调查人员
    accompanier: String, // 用人单位陪同人
    surveyDate: Date, // 调查日期
    checker: String, // 复核人

  }, { timestamps: true });

  return mongoose.model('WorkspaceDcWorker', WorkspaceDcWorkerSchema, 'workspaceDcWorker');
};
