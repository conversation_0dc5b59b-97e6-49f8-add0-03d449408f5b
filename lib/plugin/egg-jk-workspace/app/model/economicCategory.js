
// 经济类型分类（工作场所 两级）
module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const EconomicCategorySchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    code: {
      type: String,
      required: true,
      unique: true,
      index: { unique: true }, // code字段的唯一索引
    },
    content: {
      type: String,
      required: true,
      trim: true,
    },
    parentId: {
      type: String,
      default: '0', // 一级分类默认'0'，子级存储父级ID
      index: true, // parentId字段的索引
    },
  });
  return mongoose.model('EconomicCategory', EconomicCategorySchema, 'economicCategory');
};
