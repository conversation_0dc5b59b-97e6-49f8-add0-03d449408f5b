/**
 * 各市/区的工作场所职业病危害因素监测任务表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const WorkspaceMonitoringTaskSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    year: { // 计划年份
      type: Number,
      default: new Date().getFullYear(),
    },
    startTime: { // 任务开始时间
      type: Date,
      default: Date.now,
    },
    endTime: { // 任务结束时间
      type: Date,
      required: true,
    },
    areaName: [ String ], // 任务所属地区名称，eg: ['浙江省', '杭州市']
    provincialWorkspaceMonitoringId: { // 省级任务ID
      type: String,
      ref: 'ProvincialWorkspaceMonitoring',
      required: true,
    },
    // cityWorkspaceMonitoringId: { // 市级任务ID，只有区级任务中才会有（新疆用不到这个字段）
    //   type: String,
    //   ref: 'WorkspaceMonitoringTask',
    // },
    jcOrgId: { // 实际执行任务的单位，也就是监测机构ID
      type: String,
      ref: 'SuperUser',
    },
    // 用人单位名单
    EnterpriseList: [
      {
        type: String,
        ref: 'Adminorg',
      },
    ],
    taskNum: { // 任务数
      type: Number,
      set: v => parseInt(v),
    },
    // 最后一次修改者id，对应监管端session.superUserInfo.singId
    lastModifierId: {
      type: String,
    },
    taskStatus: { // 任务状态
      type: Number,
      default: 0,
      enum: [ 0, 1, 2 ], // 0-待发布，1-已发布，2-已完成
    },
    isDeleted: { // 是否删除
      type: Boolean,
      default: false,
    },
  }, { timestamps: true });

  return mongoose.model('WorkspaceMonitoringTask', WorkspaceMonitoringTaskSchema, 'workspaceMonitoringTask');
};
