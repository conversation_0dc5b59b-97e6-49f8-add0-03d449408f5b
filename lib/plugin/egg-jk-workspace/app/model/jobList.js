/**
 * 岗位编码表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const JobListSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    jobName: {
      type: String,
      required: true,
    },
    cnCsjcNo: {
      type: String,
      unique: true,
      required: true,
      index: true,
    },
  });

  return mongoose.model('JobList', JobListSchema, 'jobList');
};
