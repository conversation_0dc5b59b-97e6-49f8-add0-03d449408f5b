/**
 * 工作场所职业病危害因素 - 监测记录 - 上一年度监测情况 + 定性分析
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const WorkspaceDcJcSituationSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    workspaceMonitoringRecordId: { // 监测记录ID
      type: String,
      ref: 'WorkspaceMonitoringRecord',
      unique: true,
      index: true,
    },
    EnterpriseID: { // 用人单位，即监测单位
      type: String,
      ref: 'Adminorg',
      required: true,
    },
    ifat: Boolean, // 上一年度检测情况, 当选择未检测时，危害因素检测情况下面所填数据无需上传!!!
    ifatDust: Boolean, // 是否粉尘_检测
    ifatChemistry: Boolean, // 是否化学_检测
    ifatPhysics: Boolean, // 是否物理_检测

    factorcheckReportList: [{ // 职业病危害因素检测报告明细
      uuid: String, // 检测报告唯一标识, 数据上传方生成，用于文件上传时匹配报告记录
      unitName: String, // 检测机构名称
      creditCode: String, // 检测机构社会信用代码
      ifExistCheckReport: Boolean, // 是否存在检测报告
      reportNo: String, // 检测报告编号(ifExistCheckReport为true时必填)
      fileName: String, // 检测报告附件名称(ifExistCheckReport为true时必填)
    }],
    factorCheckItemList: [{ // 职业病危害因素监测情况-危害因素明细
      ifatFactor: Boolean, // 是否检测
      factorId: String, // 危害因素id
      checkNum: Number, // 场所检测点数(ifatFactor为true时必填)
      excessNum: Number, // 场所超标点数(ifatFactor为true时必填, 应小于等于场所检测点数)
      workNum: Number, // 检测岗位/工种数(ifatFactor为true时必填)
      workExcessNum: Number, // 检测岗位/工种超标数(ifatFactor为true时必填, 应小于等于检测岗位/工种数)
    }],
    detectionResultInvestigationSituationList: [{ // 上年度检测结果调查情况明细
      workplaceName: String, // 工作场所名称(不能重复)
      jobList: [{ // 上年度检测岗位明细
        originalReportJobName: String, // 原报告中岗位名称
        jobNo: String, // 岗位编码
        jobNameOther: String, // 其他岗位名称
      }],
      detectionFactorList: [{ // 检测危害因素明细
        factorId: String, // 危害因素id
        ctwaSampleTag: { // Ctwa检测值标记, 危害因素不是噪声和甲醛时，必填
          type: Number,
          enum: [ 1, 2 ], // 1：=；2：<
        },
        ctwaCheckValue: String, // Ctwa检测值, 危害因素不是噪声和甲醛时，必填
        cstelSampleTag: { // Cstel检测值标记, 危害因素不是噪声和甲醛时，必填
          type: Number,
          enum: [ 1, 2 ], // 1：=；2：<
        },
        cstelCheckValue: String, // Cstel检测值, 危害因素不是噪声和甲醛时，必填
        noiseSampleTag: { // 噪声检测值标记, 危害因素是噪声时，必填
          type: Number,
          enum: [ 1, 2 ], // 1：=；2：<
        },
        noiseCheckValue: String, // 噪声检测值, 危害因素是噪声时，必填
        macSampleTag: { // Mac检测值标记, 危害因素是甲醛时，必填
          type: Number,
          enum: [ 1, 2 ], // 1：=；2：<
        },
        macCheckValue: String, // Mac检测值, 危害因素是甲醛时，必填
        jobIfQualified: { // 岗位合格标记
          type: Number,
          enum: [ 0, 1, 2 ], // 0：超标；1：未超标；2：未评判
        },
        workplaceIfQualified: { // 工作场所合格标记, 除噪声外必传
          type: Number,
          enum: [ 0, 1 ], // 0：超标；1：未超标
        },
        remark: String, // 备注说明
      }],
    }],

    // 定性分析情况 -------------------------
    ifExistAnalysis: Boolean, // 是否存在混合性有机溶剂
    ifAnalysis: { // 是否开展有机溶剂定性分析, 当是否存在混合性有机溶剂选择是时，必填
      type: Number,
      enum: [ 1, 2, 3 ], // 1：是；2：否；3：未监测有机溶剂
    },
    sdsFileName: String, // sds资料附件名称, 当是否开展有机溶剂定性分析为3时，必填
    analysisSituationList: [{
      substanceName: String, // 物质名称
      jobNos: [ String ], // 岗位编码
      jobNameOther: String, // 其他岗位名称, JobNos中包含20070231编码时，必填
      analysisFactorList: [{ // 定性分析危害因素列表
        factorId: String, // 危害因素id
        ifDetection: { // 是否检出
          type: Number,
          enum: [ 0, 1 ], // 0：未检出；1：检出 (同一个物质下至少有一个定性危害因素检出)
        },
        analysisValue: Number, // 含量（%）当ifDetection为1时，必填
      }],
    }],
  }, { timestamps: true });

  return mongoose.model('WorkspaceDcJcSituation', WorkspaceDcJcSituationSchema, 'workspaceDcJcSituation');
};
