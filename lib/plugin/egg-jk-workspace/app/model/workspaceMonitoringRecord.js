/**
 * 场所监测记录 created on 2025/3/24
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const WorkspaceMonitoringRecordsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    uuid: { // 工作场所监测记录唯一标识，由国家平台生成并返回
      type: String,
    },
    taskId: { // 监测任务ID
      type: String,
      ref: 'WorkspaceMonitoringTask',
      required: true,
    },
    // 委托单位
    jcOrgId: {
      type: String,
      ref: 'SuperUser',
      required: true,
    },
    // 用人单位，即监测单位
    EnterpriseID: {
      type: String,
      ref: 'Adminorg',
      required: true,
    },
    // 检测机构(全流程的机构id)
    serviceOrgId: {
      type: String,
      ref: 'ServiceOrg',
      required: true,
    },
    // 检测报告（全流程的项目id）
    jobHealthId: {
      type: String,
      ref: 'JobHealth',
    },
    // 最后一个操作人员，对应监管端session.superUserInfo.singId
    creatorId: {
      type: String,
      required: true,
    },
    taskStatus: { // 任务状态
      type: Number,
      default: 0,
      enum: [ 0, 1, 2, 3, 4, 5, 6 ], // 0-暂存（进行）中，1-待审核（市级），2-待审核（省级），3-审核通过，4-审核不通过/驳回，5-上报国家（国家审核中），6-已完成
    },
    isDeleted: { // 是否删除
      type: Boolean,
      default: false,
    },
  }, { timestamps: true });

  return mongoose.model('WorkspaceMonitoringRecord', WorkspaceMonitoringRecordsSchema, 'workspaceMonitoringRecord');
};
