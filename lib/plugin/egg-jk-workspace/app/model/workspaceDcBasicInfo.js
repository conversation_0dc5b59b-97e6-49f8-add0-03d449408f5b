/**
 * 工作场所职业病危害因素 - 监测记录 - 基本信息
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const WorkspaceDcBasicInfoSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    workspaceMonitoringRecordId: { // 监测记录ID
      type: String,
      ref: 'WorkspaceMonitoringRecord',
      unique: true,
      index: true,
    },
    EnterpriseID: { // 用人单位，即监测单位
      type: String,
      ref: 'Adminorg',
      required: true,
    },
    unitName: String, // 单位名称
    creditCode: String, // 统一社会信用代码
    regAddr: String, // 单位注册地址
    workAddr: String, // 单位实际工作场所地址
    enterpriseScale: { // 企业规模
      type: Number,
      enum: [ 1, 2, 3, 4 ], // 1-大，2-中，3-小，4-微
    },
    industryCode: [ String ], // 行业分类代码，length：3-4
    economicNo: [ String ], // 经济类型代码，length：2
    legalPerson: String, // 法定代表人
    linkManager: String, // 职业卫生管理联系人
    linkPhone: String, // 职业卫生管理联系人电话
    empNum: Number, // 本单位在册职工总数
    externalNum: Number, // 外委人员总数
    ifDeclare: Boolean, // 是否申报(职业病危害因素监测项目)
    ifAnnualUpdate: Boolean, // 是否进行了年度更新 (是否申报为true时，必填!)
    ifLeadersTrain: Boolean, // 主要负责人培训
    ifManagersTrain: Boolean, // 职业卫生管理人员培训
    trainSum: Number, // 接触职业病危害因素年度培训总人数
    ifImport: Boolean, // 引进项目情况
    workNode: [{ // 当前工作阶段, 当引进项目情况选择有时，当前字段必填!
      type: Number,
      enum: [ 1, 2, 3, 4 ], // 1：可研阶段 2：初步设计 3：建设阶段 4：竣工阶段
    }],
    ifPreLaunch: { // 预评价开展情况, 当前工作阶段存在可研阶段和建设阶段时，该字段必填!
      type: Number,
      enum: [ 1, 2, 3 ], // 1:全部；2：部分；3：无
    },
    ifDesign: { // 职业病防护设施设计专篇, 当前工作阶段存在初步设计和建设阶段时，该字段必填!
      type: Number,
      enum: [ 1, 2, 3 ], // 1:全部；2：部分；3：无
    },
    ifLaunch: { // 控制效果评价开展情况, 当前工作阶段存在竣工阶段时，该字段必填!
      type: Number,
      enum: [ 1, 2, 3 ], // 1:全部；2：部分；3：无
    },
    ifAfterDeclare: { // 调查后是否申报(非必填)
      type: Boolean,
      default: false,
    },
    dcUnitName: String, // 调查机构名称
    dcCreditCode: String, // 调查机构统一社会信用代码
    jcUnitName: String, // 监测机构名称
    jcCreditCode: String, // 监测机构统一社会信用代码
    ifNoiseDeafness: Boolean, // 上一年度是否存在职业性噪声聋
    keyJobInvestigationSituationList: [{ // 重点岗位调查情况集合
      jobNo: String, // jobList岗位编码（其他岗位不用上传）
      ifExistJob: Boolean, // 是否存在重点岗位
    }],
    crowd: { // 职业病危害因素种类及接触人数
      contactTotalPeoples: { // 接触职业病危害因素总人数
        type: Number,
        min: 1,
      },
      ifhfDust: Boolean, // 危害因素_粉尘
      hfDustPeoples: Number, // 粉尘_接触总人数(ifhfDust为true必填)
      ifhfChemistry: Boolean, // 危害因素_化学
      hfChemistryPeoples: Number, // 化学_接触总人数(ifhfChemistry为true必填)
      ifhfPhysics: Boolean, // 危害因素_物理
      hfPhysicsPeoples: Number, // 物理_接触总人数(ifhfPhysics为true必填)
    },
    factorCrowdItemList: [{ // 接触因素及人数明细
      type: {
        type: Number,
        enum: [ 1, 2, 3 ], // 1：粉尘；2：化学；3：物理
      },
      factorId: { // 危害因素id
        type: String,
        ref: 'occupationalExposureLimits',
      },
      contactNum: Number, // 接触人数
      dataType: { // 数据类型
        type: Number,
        enum: [ 1, 2 ], // 1：国家要求；2：自定义添加；
      },
    }],
  }, { timestamps: true });

  return mongoose.model('WorkspaceDcBasicInfo', WorkspaceDcBasicInfoSchema, 'workspaceDcBasicInfo');
};
