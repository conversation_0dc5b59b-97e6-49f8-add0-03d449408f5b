/**
 * 工作场所职业病危害因素 - 监测记录 - 防护设施&用品
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const WorkspaceDcProtectionSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    workspaceMonitoringRecordId: { // 监测记录ID
      type: String,
      ref: 'WorkspaceMonitoringRecord',
      unique: true,
      index: true,
    },
    EnterpriseID: { // 用人单位，即监测单位
      type: String,
      ref: 'Adminorg',
      required: true,
    },
    fcssSituation: { // 防尘设施-设置情况
      type: Number,
      enum: [ 1, 2, 3 ], // 1:有；2：部分有；3：无
    },
    fcssEffect: { // 防尘设施-防护效果
      type: Number,
      enum: [ 1, 2, 3 ], // 1:有效；2：部分有效；3：无效; fcssSituation不为3时，必填; fcssSituation为3时，无需传值.
    },
    fdssSituation: { // 防毒设施-设置情况
      type: Number,
      enum: [ 1, 2, 3 ], // 1:有；2：部分有；3：无
    },
    fdssEffect: { // 防毒设施-防护效果
      type: Number,
      enum: [ 1, 2, 3 ], // 1:有效；2：部分有效；3：无效; fdssSituation不为3时，必填; fdssSituation为3时，无需传值.
    },
    fzsssSituation: { // 防噪声设施-设置情况
      type: Number,
      enum: [ 1, 2, 3 ], // 1:有；2：部分有；3：无
    },
    fzsssEffect: { // 防噪声设施-防护效果
      type: Number,
      enum: [ 1, 2, 3 ], // 1:有效；2：部分有效；3：无效; fzsssSituation不为3时，必填; fzsssSituation为3时，无需传值.
    },
    fckzDistributionSituation: { // 防尘口罩-发放情况
      type: Number,
      enum: [ 1, 2 ], // 1:有；2：无
    },
    fckzWearSituation: { // 防尘口罩-佩戴情况
      type: Number,
      enum: [ 1, 2, 3 ], // 1：有；2：部分；3：无 ; fckzDistributionSituation为1时，必填; fckzDistributionSituation为2时，无需传值；
    },
    fdkzDistributionSituation: { // 防毒口罩或面罩-发放情况
      type: Number,
      enum: [ 1, 2 ], // 1:有；2：无
    },
    fdkzWearSituation: {
      type: Number,
      enum: [ 1, 2, 3 ], // 1：有；2：部分；3：无 ; fdkzDistributionSituation为1时，必填; fdkzDistributionSituation为2时，无需传值；
    },
    fzyesDistributionSituation: { // 防噪声耳塞或耳罩-发放情况
      type: Number,
      enum: [ 1, 2 ], // 1:有；2：无
    },
    fzyesWearSituation: { // 防噪声耳塞或耳罩-佩戴情况
      type: Number,
      enum: [ 1, 2, 3 ], // 1：有；2：部分；3：无 ; fzyesDistributionSituation为1时，必填; fzyesDistributionSituation为2时，无需传值；
    },
    signDust: { // 粉尘职业病危害警示标识及警示说明
      type: Number,
      enum: [ 1, 2, 3 ], // 1:有；2：部分有；3：无
    },
    signChemistry: { // 化学职业病危害警示标识及警示说明
      type: Number,
      enum: [ 1, 2, 3 ], // 1:有；2：部分有；3：无
    },
    signPhysics: { // 物理职业病危害警示标识及警示说明
      type: Number,
      enum: [ 1, 2, 3 ], // 1:有；2：部分有；3：无
    },

  }, { timestamps: true });

  return mongoose.model('WorkspaceDcProtection', WorkspaceDcProtectionSchema, 'workspaceDcProtection');
};
