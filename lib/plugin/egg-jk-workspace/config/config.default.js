/* eslint valid-jsdoc: "off" */
exports.jk_workspace = {
  alias: 'workspace', // 插件目录，必须为英文
  pkgName: 'egg-jk-workspace', // 插件包名
  enName: 'jk_workspace', // 插件名
  name: '工作场所职业病危害因素监测', // 插件名称
  description: '工作场所职业病危害因素监测任务管理', // 插件描述
  adminApi: [
    {
      url: 'workspace/taskManage',
      method: 'get',
      controllerName: 'getTaskList',
      details: '获取监测任务列表',
    },
    {
      url: 'workspace/taskDetail',
      method: 'get',
      controllerName: 'getTaskDetail',
      details: '获取监测任务详情',
    },
    {
      url: 'workspace/taskDetail',
      method: 'post',
      controllerName: 'releaseTasks',
      details: '省级单位发布任务和添加自定义危害因素',
    },
    {
      url: 'workspace/taskDetail',
      method: 'put',
      controllerName: 'releaseTasks2',
      details: '市级单位选择企业名单',
    },
    {
      url: 'workspace/factorList',
      method: 'get',
      controllerName: 'getFactorList',
      details: '获取可选的自定义危害因素列表',
    },
    {
      url: 'workspace/jurisdiction',
      method: 'get',
      controllerName: 'getJurisdiction',
      details: '获取辖下区域',
    },
    {
      url: 'workspace/orgList',
      method: 'get',
      controllerName: 'getOrgList',
      details: '获取可选委托单位/检测机构/用人单位',
    },
    {
      url: 'workspace/monitoringRecord',
      method: 'post',
      controllerName: 'addMonitoringRecord',
      details: '创建监测记录',
    },
    {
      url: 'workspace/monitoringRecord',
      method: 'put',
      controllerName: 'updateMonitoringRecord',
      details: '更新监测记录',
    },
    {
      url: 'workspace/monitoringRecord',
      method: 'delete',
      controllerName: 'deleteMonitoringRecord',
      details: '删除监测记录',
    },
    {
      url: 'workspace/monitoringRecord',
      method: 'get',
      controllerName: 'monitoringRecordList',
      details: '获取监测记录列表',
    },
    {
      url: 'workspace/auditRecord',
      method: 'post',
      controllerName: 'addAuditRecord',
      details: '提交审核',
    },
    {
      url: 'workspace/auditRecord',
      method: 'delete',
      controllerName: 'deleteAuditRecord',
      details: '删除审核',
    },
    {
      url: 'workspace/baseInfo',
      method: 'post',
      controllerName: 'baseInfo',
      details: '新增/修改基本信息',
    },
    {
      url: 'workspace/economicType',
      method: 'post',
      controllerName: 'economicType',
      details: '创建经济类型',
    },
    {
      url: 'workspace/getEconomicCategory',
      method: 'get',
      controllerName: 'getEconomicCategory',
      details: '获取经济类型列表',
    },
    {
      url: 'workspace/auditRecord',
      method: 'put',
      controllerName: 'updateAuditRecord',
      details: '审核',
    },
    {
      url: 'workspace/auditRecord',
      method: 'get',
      controllerName: 'auditRecordList',
      details: '审核列表',
    },
    {
      url: 'workspace/getJobList',
      method: 'get',
      controllerName: 'getJobList',
      details: '岗位编码列表',
    },
    {
      url: 'workspace/getBaseInfo',
      method: 'get',
      controllerName: 'getBaseInfo',
      details: '获取基本信息详情',
    },
    {
      url: 'workspace/monitoringAnalyse',
      method: 'post',
      controllerName: 'monitoringAnalyse',
      details: '新增/编辑监测情况+定性分析',
    },
    {
      url: 'workspace/getMonitoringAnalyseDetail',
      method: 'get',
      controllerName: 'getMonitoringAnalyseDetail',
      details: '获取监测情况+定性分析详情',
    },
    {
      url: 'workspace/checkSituation',
      method: 'post',
      controllerName: 'checkSituation',
      details: '新增/编辑检查情况',
    },
    {
      url: 'workspace/getCheckSituationDetail',
      method: 'get',
      controllerName: 'getCheckSituationDetail',
      details: '检查情况详情',
    },
    {
      url: 'workspace/safeguard',
      method: 'post',
      controllerName: 'safeguard',
      details: '新增/编辑防护设施&用品',
    },
    {
      url: 'workspace/getSafeguardDetail',
      method: 'get',
      controllerName: 'getSafeguardDetail',
      details: '防护设施&用品详情',
    },
    {
      url: 'workspace/workerForm',
      method: 'post',
      controllerName: 'workerForm',
      details: '新增/编辑劳动者调查表',
    },
    {
      url: 'workspace/getWorkerFormDetail',
      method: 'get',
      controllerName: 'getWorkerFormDetail',
      details: '劳动者调查表详情',
    },
    {
      url: 'workspace/getFactorListNation',
      method: 'get',
      controllerName: 'getFactorListNation',
      details: '国家场所监测危害因素',
    },
    {
      url: 'workspace/monitorRecordStatistics',
      method: 'get',
      controllerName: 'monitorRecordStatistics',
      details: '获取监测记录查询',
    },
    {
      url: 'workspace/monitorProgress',
      method: 'get',
      controllerName: 'monitorProgress',
      details: '获取监测进度列表',
    },
    {
      url: 'workspace/previewWorker',
      method: 'get',
      controllerName: 'previewWorker',
      details: '获取劳动者调查表预填写信息',
    },
    {
      url: 'workspace/previewPhysicalHazard',
      method: 'get',
      controllerName: 'previewPhysicalHazard',
      details: '体检因素预填写',
    },
    {
      url: 'workspace/previewCheckSum',
      method: 'get',
      controllerName: 'previewCheckSum',
      details: '体检总结报告预填写',
    },
    {
      url: 'workspace/monitoringResult',
      method: 'get',
      controllerName: 'monitoringResult',
      details: '检测结果',
    },
  ],
  fontApi: [],

  initData: '', // 初始化数据脚本
  pluginsConfig: `
    exports.jk_workspace = {\n
        enable: true,\n        package: 'egg-jk-workspace',
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
    workspaceRouter:{\n
      match: [ctx => ctx.path.startsWith('/manage/workspace'), ctx => ctx.path.startsWith('/api/workspace')],,\n
    },\n
    `,
  // 插入到 config.default.js 中的配置
  security: {
    csrf: {
      ignoreJSON: true,
    },
    // 白名单
    // domainWhiteList: ['*'],
  },
};

