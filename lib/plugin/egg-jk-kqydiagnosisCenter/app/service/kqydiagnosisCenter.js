const Service = require('egg').Service;
const path = require('path');
const fs = require('fs');
class kqydiagnosisCenterService extends Service {
  async add(data) {
    const { ctx } = this;
    try {
      data.area_code=ctx.session.superUserInfo.area_code
      const newData = new ctx.model.KqydiagnosisCenter(data);
      const savedData = await newData.save();
      return savedData;
    } catch (error) {
      ctx.logger.error('Failed to add kqydiagnosisCenter:', error);
      throw new Error('Failed to add kqydiagnosisCenter');
    }
  }

  async getlist(params) {
    const { ctx } = this;
    try {
      const { pageNum = 1, pageSize = 10, ...rawQuery } = params;
      console.log(rawQuery,'rawQueryrawQuery');
      // 1. 动态构建查询条件
      const query = {};
      let code=await ctx.service.dashboard.getSubArea(ctx.session.superUserInfo.area_code)
      code= Array.isArray(code)? code.map(item=>{return item.area_code}) : code
      query.area_code = { $in: [
        ...(Array.isArray(ctx.session.superUserInfo.area_code)
            ? ctx.session.superUserInfo.area_code
            : ctx.session.superUserInfo.area_code
              ? [ctx.session.superUserInfo.area_code]
              : []),
        ...code
      ]};
      const allowedFields = [ 'InstitutionName', 'submitDepartment','AcceptingDepartment']; // 允许查询的字段
      Object.keys(rawQuery).forEach(key => {
        if (allowedFields.includes(key) && rawQuery[key] !== undefined && rawQuery[key] !== '') {
          // 根据字段类型处理查询条件
          if (key === 'InstitutionName') {
            query[key] = { $regex: rawQuery[key], $options: 'i' }; // 文本字段模糊匹配
          } else{
            query[key] = rawQuery[key];
          }
        }
      });

      // 2. 分页查询
      const skip = (pageNum - 1) * pageSize;
      const list = await ctx.model.KqydiagnosisCenter.find(query)
        .skip(skip)
        .limit(parseInt(pageSize))
        .populate('AcceptingDepartment', 'cname')
        .populate('submitDepartment', 'cname')
        .lean();

      // 3. 总数统计
      const total = await ctx.model.KqydiagnosisCenter.countDocuments(query);
      const filePath = path.join('/static', this.config.upload_http_path, 'kqydiagnosisCenter');

      const resList=list.map(item=>{
        // 创建对象副本避免修改原文档
        const newItem = item.toObject ? item.toObject() : { ...item };

      // 处理 QualityControlResult（字符串）
      newItem.QualityControlResult = newItem.QualityControlResult && typeof newItem.QualityControlResult === 'string' 
      ? path.join(filePath, newItem.QualityControlResult) 
      : '';

      // 处理 SupervisoryOpinion（字符串）
      newItem.SupervisoryOpinion = newItem.SupervisoryOpinion && typeof newItem.SupervisoryOpinion === 'string' 
      ? path.join(filePath, newItem.SupervisoryOpinion) 
      : '';

      // 处理 QualityControlReport（字符串）
      newItem.QualityControlReport = newItem.QualityControlReport && typeof newItem.QualityControlReport === 'string' 
      ? path.join(filePath, newItem.QualityControlReport) 
      : '';

        return newItem;
      })
      return { resList, total };
    } catch (error) {
      ctx.logger.error('查询联合执法记录失败:', error);
      throw new Error('查询失败');
    }
  }

  async updateById(_id, data) {
    const { ctx } = this;
    
    // 1. 查询旧记录
    const oldRecord = await ctx.model.KqydiagnosisCenter.findById(_id);
    if (!oldRecord) {
      throw new Error('未找到对应记录');
    }

    // 2. 提取允许更新的字段
    const updateFields = {};
    if (data.InstitutionName !== undefined) {
      updateFields.InstitutionName = data.InstitutionName;
    }
    if (data.AcceptingDepartment !== undefined) {
      updateFields.AcceptingDepartment = data.AcceptingDepartment;
    }

    // 3. 检查文件字段是否被修改
    const fileFields = ['QualityControlResult', 'SupervisoryOpinion', 'QualityControlReport'];
    const modifiedFiles = {};

    for (const field of fileFields) {
      if (data[field] !== undefined && data[field] !== oldRecord[field]) {
        updateFields[field] = data[field]; // 更新字段
        if (oldRecord[field]) {
          modifiedFiles[field] = oldRecord[field]; // 记录旧文件名
        }
      }
    }

    // 4. 执行更新
    const updatedRecord = await ctx.model.KqydiagnosisCenter.findByIdAndUpdate(
      _id,
      { $set: updateFields },
      { new: true }
    );

    // 5. 删除被修改字段的旧文件
    for (const [filed, oldFileName] of Object.entries(modifiedFiles)) {
      console.log(filed)
      const filePath = path.join(this.app.config.upload_path, 'KqydiagnosisCenter', oldFileName);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }

    return updatedRecord;
  }
  async deleteById(_id) {
    const { ctx } = this;
    const item = await ctx.model.KqydiagnosisCenter.findOne({ _id });
    const res = await ctx.model.KqydiagnosisCenter.deleteOne({ _id });
    if (res.deletedCount > 0 ) {
       // 删除 QualityControlResult 文件
       if (item.QualityControlResult && typeof item.QualityControlResult === 'string') {
        const filePath = path.join(this.app.config.upload_path, 'kqydiagnosisCenter', item.QualityControlResult);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      }

      // 删除 SupervisoryOpinion 文件
      if (item.SupervisoryOpinion && typeof item.SupervisoryOpinion === 'string') {
        const filePath = path.join(this.app.config.upload_path, 'kqydiagnosisCenter', item.SupervisoryOpinion);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      }

      // 删除 QualityControlReport 文件
      if (item.QualityControlReport && typeof item.QualityControlReport === 'string') {
        const filePath = path.join(this.app.config.upload_path, 'kqydiagnosisCenter', item.QualityControlReport);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      }
    }
    return res;
  }
  // 处理反馈逻辑
  async feedbackwithfile(data) {
    // 1. 数据预处理
    const updateFields = {
      ...data,
      feedbackTime: new Date(),
      state: 1
    };
    // 2. 数据库操作
    const result = await this.ctx.model.KqydiagnosisCenter.findByIdAndUpdate(
      updateFields._id,
      { $set: updateFields },
      { new: true}
    );

    if (!result) {
      throw new Error('未找到对应记录');
    }
    return result;
  }

}

module.exports = kqydiagnosisCenterService;
