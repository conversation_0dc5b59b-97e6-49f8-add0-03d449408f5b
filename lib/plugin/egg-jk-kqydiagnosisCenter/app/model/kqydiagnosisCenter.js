module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 法律法规
  const kqydiagnosisCenterSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    InstitutionName:{ //机构名称
      type: String,
    },
    AcceptingDepartment:{
      type: String,
      ref:'SuperUser'
    },//接受部门
    submitBy:String, //报送人
    QualityControlResult:String, //质控结果
    SupervisoryOpinion:String, //督导意见管理
    QualityControlReport:String, //质控报告
    area_code:[String],//发起方机构区域编码
    submitDepartment:{
      type: String,
      ref:'SuperUser'
    } //报送部门
  }, { timestamps: true });

  return mongoose.model('kqydiagnosisCenter', kqydiagnosisCenterSchema, 'kqydiagnosisCenter');
};
