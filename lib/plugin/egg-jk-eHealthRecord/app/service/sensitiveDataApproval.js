const Service = require('egg').Service;

class SensitiveDataApprovalService extends Service {

  /**
   * 获取敏感数据审批列表
   * @param {Object} params - 查询参数
   * @returns {Object} 分页数据
   */
  async getDataReportApprovalList(params) {
    const {
      pageNum = 1,
      pageSize = 10,
      workerName,
      approvalStatus,
      startDate,
      endDate,
      superUserInfo
    } = params;

    const query = {};

    // 根据用户权限过滤数据
    // if (superUserInfo && superUserInfo._id) {
    //   query.superUserId = superUserInfo._id;
    // }

    // 按劳动者姓名搜索
    if (workerName) {
      query.dataName = new RegExp(workerName, 'i');
    }

    // 按审批状态搜索
    if (approvalStatus) {
      const statusMap = {
        'pending': 0,    // 待审批
        'processing': 0, // 审批中（也是0，通过process数组长度区分）
        'approved': 1,   // 已通过
        'rejected': 2    // 已拒绝（需要在model中添加此状态）
      };

      if (approvalStatus === 'processing') {
        // 审批中：状态为0且有审批记录
        query.status = 0;
        query['process.0'] = { $exists: true };
      } else if (approvalStatus === 'pending') {
        // 待审批：状态为0且无审批记录
        query.status = 0;
        query.process = { $size: 0 };
      } else {
        query.status = statusMap[approvalStatus];
      }
    }

    // 按时间范围搜索
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) {
        query.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        query.createdAt.$lte = new Date(endDate);
      }
    }

    // 根据账号辖区进行筛选

    const list = await this.ctx.model.SensitiveDataApproval
      .find(query)
      .sort({ createdAt: -1 })
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .populate('superUserId', 'name')
      .lean();

    const total = await this.ctx.model.SensitiveDataApproval.countDocuments(query);

    // 格式化返回数据
    const formattedList = list.map(item => ({
      id: item._id,
      workerName: item.dataName,
      approvalStatus: this.getApprovalStatus(item),
      reportReason: this.getReportReason(item),
      currentApprovalNode: this.getCurrentApprovalNode(item),
      reportTime: item.createdAt,
      enterpriseName: item.superUserName || (item.superUserId && item.superUserId.name) || '未知企业',
      dataModel: item.dataModel
    }));

    return { list: formattedList, total };
  }

  /**
   * 获取审批流程详情
   * @param {Object} params - 查询参数
   * @returns {Object} 流程详情
   */
  async getApprovalFlowDetail(params) {
    const { id } = params;

    const approval = await this.ctx.model.SensitiveDataApproval
      .findById(id)
      .populate('process.approver', 'name')
      .populate('superUserId', 'name')
      .lean();

    if (!approval) {
      throw new Error('审批记录不存在');
    }

    // 使用实际的审批步骤
    const approvalSteps = approval.steps || [];

    // 计算当前步骤
    let currentStep = 0;
    if (approval.status === 2) {
      currentStep = approvalSteps.length; // 已完成所有步骤
    } else if (approval.status === 3) {
      currentStep = approval.process.length; // 拒绝时显示到拒绝的步骤
    } else {
      currentStep = approval.process.length; // 当前进行到的步骤
    }

    // 格式化审批历史
    const approvalHistory = approval.process.map(item => ({
      nodeName: item.level,
      approverName: item.approver ? item.approver.name : '未知',
      approvalTime: item.time,
      approvalResult: item.result ? '通过' : '拒绝',
      approvalComment: item.comment || '无'
    }));

    return {
      currentStep,
      approvalSteps: approvalSteps.map(step => ({
        nodeName: step.levelName,
        description: `${step.levelName}级审核`
      })),
      approvalHistory
    };
  }

  /**
   * 提交审批结果
   * @param {Object} params - 审批参数
   * @returns {Object} 审批结果
   */
  async submitApprovalResult(params) {
    const { id, result, comment, superUserInfo } = params;

    const approval = await this.ctx.model.SensitiveDataApproval.findById(id);
    if (!approval) {
      throw new Error('审批记录不存在');
    }

    if (approval.status === 2) {
      throw new Error('该记录已审批完成，无法重复审批');
    }

    if (approval.status === 3) {
      throw new Error('该记录已被拒绝，无法重复审批');
    }

    // 检查是否有权限审批
    if (!this.canApprove(approval, superUserInfo)) {
      throw new Error('您没有权限审批此记录');
    }

    const userLevel = this.getUserLevel(superUserInfo);
    const currentStepIndex = approval.process.length;
    const currentStep = approval.steps[currentStepIndex];

    // 添加审批记录
    const approvalRecord = {
      approver: superUserInfo._id,
      time: new Date(),
      result: result === 'approve',
      comment: comment,
      level: currentStep.levelName
    };

    approval.process.push(approvalRecord);

    // 更新审批状态和当前节点
    if (result === 'reject') {
      // 审批被拒绝，重新获取最新的审批流程
      const flowInfo = await this.getAndValidateApprovalFlow(approval.dataModel);
      approval.flowId = flowInfo.flowId;
      approval.steps = flowInfo.steps;
      approval.currentNode = flowInfo.steps[0].levelName; // 重新从第一步开始
      approval.status = 3; // 设置为拒绝状态
      approval.process = [approvalRecord]; // 清空之前的审批记录，只保留当前拒绝记录
    } else {
      // 审批通过
      const nextStepIndex = currentStepIndex + 1;
      if (nextStepIndex >= approval.steps.length) {
        // 已完成所有审批步骤
        approval.status = 2;
        approval.currentNode = '已完成';
      } else {
        // 更新到下一个审批节点
        approval.status = 1; // 审批中
        approval.currentNode = approval.steps[nextStepIndex].levelName;
      }
    }

    await approval.save();

    return { success: true, message: '审批提交成功' };
  }

  /**
   * 创建敏感数据审批记录
   * @param {Object} params - 创建参数
   * @returns {Object} 创建结果
   */
  async createApprovalRecord(params) {
    const {
      dataId,
      dataName,
      dataModel,
      reportReason,
      superUserInfo
    } = params;

    // 检查用户是否有上报权限
    const canReport = await this.getCanReport();
    if (!canReport) {
      throw new Error('您没有敏感数据上报权限，无法提交上报申请');
    }

    // 检查是否已存在待审批的记录
    const existingRecord = await this.ctx.model.SensitiveDataApproval.findOne({
      dataId,
      dataModel,
      status: { $in: [0, 1] } // 待审批或审批中
    });

    if (existingRecord) {
      throw new Error('该数据已有待审批记录，请勿重复提交');
    }

    // 获取审批流程
    const flowInfo = await this.getAndValidateApprovalFlow(dataModel);

    // 确定初始审批节点（从最高levelCode开始，即连队级4）
    const firstStep = flowInfo.steps[0]; // 已经按levelCode降序排列

    const approvalRecord = new this.ctx.model.SensitiveDataApproval({
      dataId,
      dataName,
      dataModel,
      reportReason,
      superUserId: superUserInfo._id,
      superUserName: superUserInfo.name,
      flowId: flowInfo.flowId,
      steps: flowInfo.steps,
      currentNode: firstStep.levelName,
      status: 0,
      process: []
    });

    await approvalRecord.save();
    return { success: true, data: approvalRecord };
  }

  /**
   * 删除审批记录
   * @param {Object} params - 删除参数
   * @returns {Object} 删除结果
   */
  async deleteApprovalRecord(params) {
    const { id, superUserInfo } = params;

    const approval = await this.ctx.model.SensitiveDataApproval.findById(id);
    if (!approval) {
      throw new Error('审批记录不存在');
    }

    // 只有创建者可以删除，且只能删除未开始审批的记录
    if (approval.superUserId !== superUserInfo._id) {
      throw new Error('您没有权限删除此记录');
    }

    if (approval.process.length > 0) {
      throw new Error('已开始审批的记录无法删除');
    }

    await this.ctx.model.SensitiveDataApproval.findByIdAndDelete(id);
    return { success: true, message: '删除成功' };
  }

  // 辅助方法
  getApprovalStatus(item) {
    if (item.status === 2) return 'approved'; // 审批完成
    if (item.status === 3) return 'rejected'; // 审批被拒绝
    if (item.status === 1) return 'processing'; // 审批中
    return 'pending'; // 待审批
  }

  getReportReason(item) {
    return item.reportReason || '敏感数据上报审批';
  }

  getCurrentApprovalNode(item) {
    if (item.status === 2) return '已完成';
    if (item.status === 3) return '已拒绝';
    return item.currentNode || '待审批';
  }

  getLevelName(level) {
    const levelMap = {
      'district': '团场',
      'city': '师市',
      'province': '兵团'
    };
    return levelMap[level] || level;
  }

  getUserLevel(superUserInfo) {
    // 根据用户regAdd管辖区域判断级别
    const { regAdd } = superUserInfo;
    if (!regAdd || !Array.isArray(regAdd)) {
      return null;
    }

    // 判断是否包含"建设兵团"
    const hasXJBT = regAdd.includes('建设兵团');
    const regAddLength = regAdd.length;

    if (hasXJBT) {
      if (regAddLength === 1) {
        // 只有"建设兵团"，为兵团级
        return '1'; // 兵团级对应levelCode为1
      } else if (regAddLength === 2) {
        // ["建设兵团", "建设兵团第一师"]，为师市级
        return '2'; // 师市级对应levelCode为2
      } else if (regAddLength === 3) {
        // ["建设兵团", "建设兵团第一师", "某团场"]，为团场级
        return '3'; // 团场级对应levelCode为3
      }
    }

    // 如果不包含"建设兵团"，根据长度判断
    if (regAddLength === 1) {
      return '4'; // 连队级对应levelCode为4
    }

    return null;
  }

  canApprove(approval, superUserInfo) {
    // 检查用户是否有权限审批当前记录
    if (!approval.steps || approval.steps.length === 0) {
      return false;
    }

    const userLevel = this.getUserLevel(superUserInfo);
    if (!userLevel) {
      return false;
    }

    // 找到当前应该审批的步骤
    const currentStepIndex = approval.process.length;
    if (currentStepIndex >= approval.steps.length) {
      return false; // 已经完成所有审批步骤
    }

    const currentStep = approval.steps[currentStepIndex];
    return currentStep.levelCode === userLevel;
  }

  getNextNode(currentLevel) {
    const nextNodeMap = {
      'district': '师市',
      'city': '兵团',
      'province': null
    };
    return nextNodeMap[currentLevel];
  }

  /**
   * 更新审批记录
   * @param {Object} params - 更新参数
   * @returns {Object} 更新结果
   */
  async updateApprovalRecord(params) {
    const { id, dataName, reportReason, superUserInfo } = params;

    const approval = await this.ctx.model.SensitiveDataApproval.findById(id);
    if (!approval) {
      throw new Error('审批记录不存在');
    }

    // 只有创建者可以更新，且只能更新未开始审批的记录
    if (approval.superUserId !== superUserInfo._id) {
      throw new Error('您没有权限更新此记录');
    }

    if (approval.process.length > 0) {
      throw new Error('已开始审批的记录无法更新');
    }

    // 更新字段
    if (dataName) approval.dataName = dataName;
    if (reportReason) approval.reportReason = reportReason;

    await approval.save();
    return { success: true, data: approval };
  }

  /**
   * 获取审批记录详情
   * @param {Object} params - 查询参数
   * @returns {Object} 记录详情
   */
  async getApprovalRecordDetail(params) {
    const { id } = params;

    const approval = await this.ctx.model.SensitiveDataApproval
      .findById(id)
      .populate('superUserId', 'name')
      .populate('process.approver', 'name')
      .lean();

    if (!approval) {
      throw new Error('审批记录不存在');
    }

    return {
      id: approval._id,
      dataId: approval.dataId,
      dataName: approval.dataName,
      dataModel: approval.dataModel,
      reportReason: approval.reportReason,
      status: approval.status,
      currentNode: approval.currentNode,
      superUserName: approval.superUserName,
      createdAt: approval.createdAt,
      updatedAt: approval.updatedAt,
      process: approval.process
    };
  }

  // 获取当前账号是否有上报权
  async getCanReport() {
    const { ctx } = this;
    const superUserInfo = ctx.session.superUserInfo;
    const superUser = await this.ctx.model.SuperUser.findOne({ _id: superUserInfo._id });
    return superUser.dataReportAuth;
  }

  // 获取企业列表（用于上报时搜索）
  async getEnterpriseList(params) {
    const { pageNum = 1, pageSize = 10, cname, superUserInfo } = params;
    const query = {
      isDelete: false,
      productionStatus: { $ne: '0' } // 排除注销状态
    };

    // 根据管辖区域筛选企业
    const { regAdd } = superUserInfo;
    if (regAdd && regAdd.length > 0) {
      query['workAddress.districts'] = { $all: regAdd };
    }

    if (cname) {
      query.cname = new RegExp(cname, 'i');
    }

    const list = await this.ctx.model.Adminorg
      .find(query)
      .sort({ createdAt: -1 })
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .select('_id cname workAddress industryCategory')
      .lean();

    const total = await this.ctx.model.Adminorg.countDocuments(query);

    // 格式化返回数据
    const formattedList = list.map(item => ({
      id: item._id,
      name: item.cname,
      address: item.workAddress ? item.workAddress.districts.join(' ') : '',
      industryCategory: item.industryCategory || ''
    }));

    return { list: formattedList, total };
  }

  // 获取 当前审批事项的审批流程
  async getApprovalFlow(params) {
    const { ctx } = this;
    const { key } = params;
    const code = '100' // 当前账号类型
    // key值枚举：
    // "worker_health_record_approval"
    // "enterprise_health_record_approval"

    const result = await ctx.curl(
      `${ctx.app.config.xjbtportal}/api/uaProcess/versions/current?itemCode=${key}&orgCategoryCode=${code}`,
      {
        method: 'get',
        dataType: 'json', // 返回的数据类型
      })
    if (result.status === 200 && result.data && result.data.data) {
      return result.data.data;
    } else {
      throw new Error('获取审批流程失败');
    }
  }

  // 获取并验证审批流程（内部使用）
  async getAndValidateApprovalFlow(dataModel) {
    const key = dataModel === 'Employees' ? 'worker_health_record_approval' : 'enterprise_health_record_approval';
    const flowData = await this.getApprovalFlow({ key });

    if (!flowData || !flowData.steps || flowData.steps.length === 0) {
      throw new Error('当前没有可用的审批流程，无法提交上报');
    }

    // 按levelCode排序审批步骤（4->3->2->1）
    const sortedSteps = flowData.steps.sort((a, b) => parseInt(b.levelCode) - parseInt(a.levelCode));

    return {
      flowId: flowData._id,
      steps: sortedSteps,
      itemName: flowData.itemName
    };
  }
    // 接口示例
    // https://xjbtportal.jkqy.cn/api/uaProcess/versions/current?itemCode=worker_health_record_approval&orgCategoryCode=100
    // {
    //   "status": 200,
    //     "data": {
    //     "_id": "d1fb9ca1-17b3-4cdf-bb1c-4d395b9f6e75", // 流程id
    //       "orgCategoryCodes": [
    //         "120",
    //         "121",
    //         "100"
    //       ],
    //         "templateId": "96sG4jigw",
    //           "itemCode": "worker_health_record_approval",
    //             "steps": [ // 审批步骤
    //               {
    //                 "order": 1, //  顺序（无实际意义，可忽略
    //                 "levelCode": "3", // 审批级别 目前固定 1-4级，从第4级开始，可以按照这个排序，实际也是按照这个审批而不是order
    //                 "levelName": "团场/区县级"
    //               },
    //               {
    //                 "order": 2,
    //                 "levelCode": "2",
    //                 "levelName": "师市级"
    //               },
    //               {
    //                 "order": 3,
    //                 "levelCode": "1",
    //                 "levelName": "兵团/省级"
    //               },
    //               {
    //                 "order": 4,
    //                 "levelCode": "4",
    //                 "levelName": "连队/乡镇级"
    //               }
    //             ],
    //               "itemName": "“一人一档”敏感数据上报与审批", // 审批流程名称
    //                 "orgCategoryNames": [
    //                   "疾病预防控制中心",
    //                   "职业病防治院所",
    //                   "卫生行政管理部门"
    //                 ]
    //   },
    //   "message": ""
    // }

  }

}

module.exports = SensitiveDataApprovalService;
