const sensitiveDataApprovalController = {

  /**
   * 获取敏感数据审批列表
   */
  async getDataReportApprovalList(ctx) {
    try {
      const payload = ctx.request.body;
      payload.superUserInfo = ctx.session.superUserInfo;

      let result = await ctx.service.sensitiveDataApproval.getDataReportApprovalList(payload);
      ctx.helper.renderSuccess(ctx, { data: result, message: '获取成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  /**
   * 获取审批流程详情
   */
  async getApprovalFlowDetail(ctx) {
    try {
      const payload = ctx.query;
      const result = await ctx.service.sensitiveDataApproval.getApprovalFlowDetail(payload);
      ctx.helper.renderSuccess(ctx, { data: result, message: '获取成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  /**
   * 提交审批结果
   */
  async submitApprovalResult(ctx) {
    try {
      const payload = ctx.request.body;
      payload.superUserInfo = ctx.session.superUserInfo;
      const result = await ctx.service.sensitiveDataApproval.submitApprovalResult(payload);
      ctx.helper.renderSuccess(ctx, { data: result, message: '审批提交成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  /**
   * 创建敏感数据审批记录
   */
  async createApprovalRecord(ctx) {
    try {
      const payload = ctx.request.body;
      payload.superUserInfo = ctx.session.superUserInfo;
      const result = await ctx.service.sensitiveDataApproval.createApprovalRecord(payload);
      ctx.helper.renderSuccess(ctx, { data: result, message: '创建成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  /**
   * 更新审批记录
   */
  async updateApprovalRecord(ctx) {
    try {
      const payload = ctx.request.body;
      payload.superUserInfo = ctx.session.superUserInfo;
      const result = await ctx.service.sensitiveDataApproval.updateApprovalRecord(payload);
      ctx.helper.renderSuccess(ctx, { data: result, message: '更新成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  /**
   * 删除审批记录
   */
  async deleteApprovalRecord(ctx) {
    try {
      const payload = ctx.query;
      payload.superUserInfo = ctx.session.superUserInfo;
      const result = await ctx.service.sensitiveDataApproval.deleteApprovalRecord(payload);
      ctx.helper.renderSuccess(ctx, { data: result, message: '删除成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  /**
   * 获取审批记录详情
   */
  async getApprovalRecordDetail(ctx) {
    try {
      const payload = ctx.query;
      const result = await ctx.service.sensitiveDataApproval.getApprovalRecordDetail(payload);
      ctx.helper.renderSuccess(ctx, { data: result, message: '获取成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  /**
   * 获取企业列表（用于上报时搜索）
   */
  async getEnterpriseList(ctx) {
    try {
      const payload = ctx.request.body;
      payload.superUserInfo = ctx.session.superUserInfo;
      const result = await ctx.service.sensitiveDataApproval.getEnterpriseList(payload);
      ctx.helper.renderSuccess(ctx, { data: result, message: '获取成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  /**
   * 获取审批流程
   */
  async getApprovalFlow(ctx) {
    try {
      const payload = ctx.query;
      const result = await ctx.service.sensitiveDataApproval.getApprovalFlow(payload);
      ctx.helper.renderSuccess(ctx, { data: result, message: '获取成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

  /**
   * 检查用户上报权限
   */
  async getCanReport(ctx) {
    try {
      const result = await ctx.service.sensitiveDataApproval.getCanReport();
      ctx.helper.renderSuccess(ctx, { data: { canReport: result }, message: '获取成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: error.message });
    }
  },

};

module.exports = sensitiveDataApprovalController;