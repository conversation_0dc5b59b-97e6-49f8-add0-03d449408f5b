'use strict';

module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  // 定义鉴定记录模式
  const DeterminationRecordSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate
    },
    determinationCategory: {
      type: String,
      enum: ['1', '2'],
      required: true,
      comment: '鉴定类别：1首次鉴定 2再鉴定'
    },
    employerName: {
      type: String,
      required: true,
      comment: '用人单位名称'
    },
    employerCreditCode: {
      type: String,
      comment: '用人单位统一社会信用代码'
    },
    laborEmployerName: {
      type: String,
      comment: '用工单位名称'
    },
    laborEmployerCreditCode: {
      type: String,
      comment: '用工单位统一社会信用代码'
    },
    applicationReason: {
      type: String,
      comment: '申请鉴定主要理由'
    },
    determinationBasis: {
      type: String,
      comment: '鉴定依据'
    },
    hasOccupationalDisease: {
      type: Boolean,
      required: true,
      comment: '鉴定结论，false代表不是职业病，true代表是职业病'
    },
    determinationConclusionDescription: {
      type: String,
      comment: '鉴定结论描述'
    },
    occupationalDisease: [{
      _id: {
        type: String,
        default: shortid.generate
      },
      name: { type: String, comment: '职业病名称' },
      code: { type: String, comment: '职业病编码' },
      category: { type: String, comment: '职业病类别' },
    }],
    determinationCommittee: {
      type: String,
      comment: '诊断鉴定委员会'
    },
    determinationDate: {
      type: Date,
      comment: '鉴定日期'
    },
    workerName: {
      type: String,
      required: true,
      comment: '劳动者名称'
    },
    phone: {
      type: String,
      comment: '手机号'
    },
    gender: {
      type: String,
      enum: ['1', '2'],
      comment: '性别 1男 2女'
    },
    idNumber: {
      type: String,
      required: true,
      comment: '身份证号码'
    },
    diagnosisNumber: {
      type: String,
      comment: '对应的诊断编号'
    },
    determinationNumber: {
      type: String,
      required: true,
      unique: true,
      comment: '鉴定编号'
    },
    firstDeterminationNumber: {
      type: String,
      comment: '(再鉴定对应的)首次鉴定编号'
    },
  }, {
    timestamps: true,
  });

  DeterminationRecordSchema.index({ diagnosisId: 1 });
  DeterminationRecordSchema.index({ firstDeterminationId: 1 });

  return mongoose.model('DeterminationRecord', DeterminationRecordSchema, 'determinationRecords');
};