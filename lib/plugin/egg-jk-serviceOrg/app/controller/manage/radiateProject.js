// const path = require('path');
const moment = require('moment');
// const fs = require('fs');
// const mkdirp = require('mkdirp');
// const shortid = require('shortid');

const RadiateProjectsController = {
  async getRadiateProjects(ctx, app) {
    const { crossRegionManage } = app.config;
    let regAddr = [];
    if (crossRegionManage) {
      // 如果开启了跨区域管理，regAdd是二维数组，需要取每个数组的最后一个元素
      regAddr = ctx.session.superUserInfo.regAdd.map(arr => arr[arr.length - 1]);
    } else {
      // 原来的逻辑
      regAddr = [ ctx.session.superUserInfo.regAdd[ctx.session.superUserInfo.regAdd.length - 1] ];
    }
    // const regAddr = ctx.session.superUserInfo.regAdd ? ctx.session.superUserInfo.regAdd[ctx.session.superUserInfo.regAdd.length - 1] : '';
    if (!regAddr[0].trim()) {
      ctx.body = {
        status: 400,
        masg: '找不到缓存的注册地',
      };
      return;
    }
    // 获取当前登录机构信息
    // const serviceOrg = await ctx.model.ServiceOrg.find({
    //   _id: ctx.session.userInfo.org_id,
    // });
    // console.log(1111111, regAddr, ctx.session.superUserInfo.regAdd);

    const params = ctx.request.body;
    // params.serviceID = serviceOrg[0]._id;
    const img_static_path = app.config.static.prefix + app.config.upload_http_path;

    if (params.regAddr && params.regAddr.length) { // 传了选择区域
      const tempArr = [];
      params.regAddr.forEach(ele => {
        tempArr.push(ele.area_code);
      });
      params.regAddr = tempArr;
    } else { // 没有传选择区域
      if (!regAddr.includes(app.config.China.name)) {
        // 并行查询所有区域的 parentArea
        const parentAreas = await Promise.all(regAddr.map(addr => 
          ctx.model.District.findOne({ name: addr })
        ));
        // 收集所有有效的 area_code
        const areaCodes = parentAreas
          .filter(area => area) // 过滤掉未找到的区域
          .map(area => area.area_code);
        if (areaCodes.length > 0) {
          params.regAddr = areaCodes;
        }
      } else {
        params.regAddr = null;
      }
    }
    // params.superUserID = regAddr === '中国' ? '' : (ctx.session._id || '');
    // const result = await ctx.model.ServiceOrg.findOne({ _id: serviceOrg[0]._id });
    await ctx.service.radiateProjects.list(params).then(async res => {
      const data = [];
      for (const ele of res) {
        const newItem = JSON.parse(JSON.stringify(ele));
        // console.log(newItem.EnterpriseID, 'ssssssssssssssssssssssssss');
        // newItem.name = result.name;
        // newItem.applyTime = ele.completedTime ? moment(ele.completedTime).format('YYYY-MM-DD') : '';
        newItem.applyTime = ele.applyTime ? moment(ele.applyTime).format('YYYY-MM-DD') : '';
        // newItem.reportTime = ele.reportTime ? moment(ele.reportTime).format('YYYY-MM-DD') : '';
        // newItem.investigationTime = ele.investigationTime ? moment(ele.investigationTime).format('YYYY-MM-DD') : '';
        newItem.completedTime = ele.completedTime ? moment(ele.completedTime).format('YYYY-MM-DD') : '';
        // 文件前缀
        newItem.file_static_path = img_static_path;

        // if (newItem.preventAssess) {
        //   newItem.preventAssess.level = newItem.preventAssess.level ? newItem.preventAssess.level + '级' : newItem.preventAssess.level;
        // }

        data.push(newItem);
      }
      // for (const res of data) {
      //   // eslint-disable-next-line array-callback-return
      //   res.report.map(item => {
      //     item.url = '/static/upload' + app.config.enterprise_http_path + '/' + res.EnterpriseID + '/' + item.url;
      //     item.name = item.fileName;
      //     delete item.source;
      //     delete item._id;
      //     delete item.fileName;
      //   });
      // }
      // /upload/enterprise/12345/name
      // 查询总数
      const total = await ctx.service.radiateProjects.getCount(params) || 0;
      const completed = await ctx.service.radiateProjects.getCount({
        ...params,
        completeStatus: 1,
      }) || 0;
      const updated = await ctx.service.radiateProjects.getCount({
        ...params,
        completeUpdate: 1,
      }) || 0;
      ctx.body = {
        status: 200,
        data,
        count: {
          total,
          completed,
          updated,
        },
      };
    })
      .catch(err => {
        console.log(err, 'rrrrrrrrrrrrrrrrrrrrrr');
        ctx.body = {
          status: 404,
          data: err,
        };
      });
  },
  // 获取放射检测项目的检测结果
  async getRadiateProjectResult(ctx) {
    const { id } = ctx.query;
    console.log('放射检测结果id', id);
    let doc = await ctx.model.RadiateProjectResult.aggregate([
      {
        $match: { radiateProjectId: id },
      },
      {
        $lookup: {
          from: 'machineManage',
          localField: 'radProtDetectInWrkplc.equipParamId',
          foreignField: '_id',
          as: 'machineManage',
        },
      },
      {
        $lookup: {
          from: 'machineManage',
          localField: 'eqptQualCtrlDetect.equipParamId',
          foreignField: '_id',
          as: 'machineManage_eqp',
        },
      },
    ]);
    doc = JSON.parse(JSON.stringify(doc));
    doc = doc[0] ? doc[0] : {};
    let data = {};
    data.qualifiedNum = 0;
    // 没有检测结果的话，就初始化
    if (!doc) {
      const radiateProject = await ctx.model.RadiateProjects.findOne({ _id: id });
      data = {
        radiateProjectId: radiateProject._id,
        technicalServiceType: radiateProject.technicalServiceType,
        radProtDetectInWrkplc: [],
        eqptQualCtrlDetect: [],
        personalDoseMonitoring: [],
        qualifiedNum: 0,
      };
    } else {
      const machineManage = doc.machineManage;
      doc.radProtDetectInWrkplc.forEach(item => {
        const target = machineManage.find(e => item.equipParamId === e._id);
        if (target) {
          item.equipParams = target;
        }
      });

      const machineManage_eqp = doc.machineManage_eqp;
      doc.eqptQualCtrlDetect.forEach(item => {
        const target = machineManage_eqp.find(e => item.equipParamId === e._id);
        if (target) {
          item.equipParams = target;
        }
      });

      data = {
        radiateProjectId: doc._id,
        technicalServiceType: doc.technicalServiceType,
        radProtDetectInWrkplc: doc.radProtDetectInWrkplc,
        eqptQualCtrlDetect: doc.eqptQualCtrlDetect,
        personalDoseMonitoring: doc.personalDoseMonitoring,
        qualifiedNum: 0,
      };
      let index = 0;
      const countedNames = new Set();
      let equipmentNum = 0;
      // 遍历radProtDetectInWrkplc、personalDoseMonitoring和eqptQualCtrlDetect这三个数组
      if (data.radProtDetectInWrkplc && (data.radProtDetectInWrkplc.length > 0 || data.eqptQualCtrlDetect.length > 0 || data.personalDoseMonitoring.length > 0)) {
        [ data.radProtDetectInWrkplc, data.eqptQualCtrlDetect, data.personalDoseMonitoring ].forEach(array => {
          array.forEach(item => {
            if (!countedNames.has(item.name)) {
              const hasUnqualified = item.formData.some(formDataItem => formDataItem.result === '不合格');

              if (hasUnqualified) {
                index++;
              }

              countedNames.add(item.name);
            }
          });
        });
        equipmentNum = data.radProtDetectInWrkplc.length + data.eqptQualCtrlDetect.length + data.personalDoseMonitoring.length;
      }
      data.qualifiedNum = equipmentNum - index;
    }
    ctx.helper.renderSuccess(ctx, {
      data,
    });
  },
};

module.exports = RadiateProjectsController;
