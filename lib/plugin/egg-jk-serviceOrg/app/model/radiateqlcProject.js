/**
 * 放射全流程表
 */


module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const RadiateqlcProjectSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    createdAt: {
      type: Date,
      default: Date.now(),
    }, // 创建时间
    updatedAt: Date, // 修改时间
    parentId: { // 项目父级id，根级是0
      type: String,
      default: '0',
    },
    subProjects_type: {
      type: String,
      default: '',
    }, // 子项目类型，如果是那么属于哪一类，Y是预采样，F是复测，B是补测，Z是子项目，正式项目则为空字符串
    pigeonholeFile: { // 归档文件夹
      name: { type: String, default: '' }, // 文件夹名称
      url: { type: String, default: '' }, // 文件夹下载地址
    },
    officialReportUploadFileName: { // 正式稿
      name: { type: String, default: '' }, // 文件夹名称
      url: { type: String, default: '' }, // 文件夹下载地址
      noStampUrl: { type: String, default: '' }, // 未盖章文件下载地址 pdf
    },
    pigeonholeCatalogue: String, // 归档目录文件
    issuer: { // 报告签发人
      name: String,
      employeeId: String, // serviceEmployee id
    },
    issueDate: Date, // 报告签发日期

    completeReportArchive: {
      status: { // 0 未完成 1 已完成
        type: Number,
        default: 0,
      }, // 是否确认签收归档 默认不签收0 已经签收1
      completedTime: Date, // 完成时间
    },
    reportArchiveSigner: {
      signPath: String, // 签名
      serviceEmployeeId: String,
    }, // 档案签收人

    // ==================== 受检单位信息
    EnterpriseID: { type: String, ref: 'Adminorg' }, // 企业ID
    EnterpriseName: { type: String }, // 企业名称
    companyID: {
      type: String,
    }, // 用人单位ID ，取信用代码
    // companyAddress: [{
    //   _id: {
    //     type: String,
    //     default: shortid.generate,
    //   },
    //   districts: Array,
    //   address: String, // 具体地址
    // }], // 用人单位地址 为什么下面还有一个用人单位地址，是否可以合并？？？？
    corp: String, // 受检单位法人
    regType: String, // 受检单位注册类型/经济类型
    companyScale: {
      type: String,
      enum: [ '大型', '中型', '小型', '微型', '其他' ],
    },
    companyContact: {
      type: String,
      default: '',
    }, // 受检单位联系人
    companyContactPhoneNumber: {
      type: String,
      default: '',
    }, // 受检单位联系人手机号码
    companyContactEmail: {
      type: String,
      default: '',
    }, // 受检单位邮箱
    companyIndustry: {
      type: Array,
      default: [],
    }, // 受检单位'所属行业',
    riskLevel: {
      type: String,
    }, // 受检单位风险等级，初始时从adminorg中的level来，随行业分类（companyIndustry）而改变
    regAdd: String, // 注册地址
    districtRegAdd: {
      type: Array,
      default: [],
    }, // 受检单位注册地址
    // 申报地址，由于用户可以自行修改，所以和下面的workAdd区别开，
    // 存area_code，该code用于后注册的行政端也能读取申报的项目列表，
    // 存area_code前六位，即区县代码有效位数，其父级单位通过代码即可找到，全国的则需特殊处理
    workPlaces: [{ // 受检单位的工作场所地址
      _id: {
        type: String,
        default: shortid.generate,
      },
      workAddName: String, // 工作场所的中文地址

      name: String, // 工作场所的名称
      workAdd: {
        type: Array,
      }, // 工作场所具体地址，area_code
      checked: {
        type: Boolean,
        default: true,
      }, // 是否被检测，默认一般是检测了
    }], // 检测的工作场所，用于识别
    // ==================== 委托单位信息
    anthemEnterpriseID: {
      type: String,
      ref: 'Adminorg',
    }, // 委托单位id
    anthemCompanyID: {
      type: String,
    }, // 委托单位ID ，取信用代码
    newAnthemCompany: {
      type: String,
    }, // 新增的委托单位
    anthemCompanyName: {
      type: String,
      default: '',
    }, // 委托单位名称
    anthemCompanyContact: {
      type: String,
      default: '',
    }, // 用人单位联系人
    anthemCompanyContactPhoneNumber: {
      type: String,
      default: '',
    }, // 委托单位联系人手机号码
    anthemCompanyContactEmail: {
      type: String,
      default: '',
    }, // 委托单位邮箱
    anthemCompanyRegAdd: String, // 注册地址
    anthemCompanyDistrictRegAdd: {
      type: Array,
      default: [],
    }, // 委托单位注册地址
    // anthemCompanyAddress: [{
    //   districts: Array, // 营业执照注册地址
    //   address: String, // 具体地址
    //   _id: {
    //     type: String,
    //     default: shortid.generate,
    //   },
    // }], // 委托单位工作地址
    serviceOrgId: { type: String, ref: 'ServiceOrg' }, // 机构id
    serviceCon: {
      type: String,
      default: '',
    }, // 服务内容
    // ============================================项目相关信息
    preparedDate: Date, // 方案编制日期
    RadiateProjectId: { type: String, ref: 'radiateProject' }, // 放射项目id
    projectName: { type: String }, // 检测项目名称
    shortProjectName: { type: String }, // 检测项目简称
    projectSN: { type: String }, // 检测项目编号
    serviceType: { type: String }, // 技术服务类型
    detectionType: { type: Array }, // 设备类型 检测类型根据服务类型而变化
    expectStartTime: [ Date, Date ], // 预计开始时间 拟定日期
    expectStopTime: {
      type: Date,
    }, // 预计结束时间,
    requiredTime: {
      type: Date,
    }, // 要求/期望完成的时间
    detectionDate: [ Date, Date ], //  检测时间 和现场检测时间对应
    completeStatus: {
      type: Number,
      default: 1,
    }, // 完成状态，1，未完成； 2，已完成
    // applyTime: {
    //   type: Date,
    // }, // 上报时间
    completedTime: {
      type: Date,
    }, // 实际完成时间
    // status: {
    //   type: Number,
    //   default: 0,
    // }, // 申报状态,0，未报送，1，已报送，
    // projectStatus:{
    //   type:Number,
    //   default:0
    // }, // 项目状态 0 新创建 1 进行中 2 已完成 3 暂停 4 终止
    projectStop: {
      type: Boolean,
      dafault: false,
    }, // 项目是否暂停，false：未暂停，true：暂停
    projectCancel: {
      type: Boolean,
      dafault: false,
    }, // 项目是否终止，false：未终止，true：终止
    projectGroup: {
      type: String,
      default: '',
      ref: 'serviceDingtrees',
    }, // '项目检测组',
    EvaluationProjectManager: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // '评价项目成员
    EvaluationProjectManagerAdmin: {
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }, // 评价组经理
    EvaluationGroup: {
      type: String,
      default: '',
      ref: 'serviceDingtrees',
    }, // 评价组
    checkPeopleCount: {
      type: Number,
      default: 0,
    }, // 方案预计检测人数
    personInCharge: {
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }, // '项目负责人ID',
    personsOfProject: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // '项目组成员ID',
    personsOfCompiling: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // 编制人成员ID
    personsOfReviewer: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // 审核人成员ID
    archivePerson: { // 归档人
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    },
    personPairings: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // 现场检测复核人成员ID
    detectPerson: [{
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }], // 检测人成员ID
    salesman: { // 业务经理 serviceemployeeId
      type: String,
      default: '',
    },
    farmOut: { // 是否分包lht+(chenke)
      type: Boolean,
    },
    farmOutParams: { // 分包参数
      type: String,
    },
    description: { // 项目简介
      type: String,
    },
    vipRequirement: { // vip需求
      type: String,
    },

    // =============钱money
    projectPrice: {
      type: Number,
      default: 0,
    }, // 项目价格(合同金额)
    cooperationFee: {
      type: Number,
      default: 0,
    }, // 合作费
    expectedCooperationFee: {
      type: Number,
      default: 0,
    }, // 预计合作费
    reviewFee: {
      type: Number,
      default: 0,
    }, // 评审费
    expectedReviewFee: {
      type: Number,
      default: 0,
    }, // 预计评审费
    otherFee: {
      type: Number,
      default: 0,
    }, // 其他费
    expectedOtherFee: {
      type: Number,
      default: 0,
    }, // 预计其他费
    netEarnings: {
      type: Number,
      default: 0,
    }, // 净赚=价格-合作费-评审费-其他费
    expectedNetEarnings: {
      type: Number,
      default: 0,
    }, // 预计净赚=价格-合作费-评审费-其他费
    outsourceFee: { // 分包费
      type: Number,
      default: 0,
    },
    expectedOutsourceFee: { // 预计分包费
      type: Number,
      default: 0,
    },

    // ============== 快递
    mailingAddress: Array, // 邮寄地址
    mailingAddressInfo: String, // 邮寄详细地址
    recipient: {
      type: String,
      default: '',
    }, // 收件人
    recipientPhoneNum: {
      type: String,
      default: '',
    }, // 收件号码

    // =============银行
    accountsBank: {
      type: String,
      default: '',
    }, // 开户行
    accountsAddress: {
      type: String,
      default: '',
    }, // 开户地址
    accountsNumber: {
      type: String,
      default: '',
    }, // 开户账号
    accountsPhoneNum: {
      type: String,
      default: '',
    }, // 开票电话
    tariffNumber: {
      type: String,
      default: '',
    }, // 税号
    isVIP: { // 会员类型：战略客户、vip客户、普通客户
      type: String, // svip、vip、ordinary
      default: 'ordinary',
    },
    source: {
      type: String,
      default: 'service', // ck || service
    }, // 数据来源
    isUrgent: {
      type: Boolean,
      default: false,
    }, // 是否加急
    comment: String, // 整个项目的备注
    serviceArea: [{
      type: String,
      match: /^[0-9]*$/,
    }], // 关于此次项目的企业涉及的技术服务领域，存得是编码，报送信息中要用 [采矿业:'1','化工、石化及医药':'2','冶金、建材':'3','机械制造、电力、纺织、建筑和交通运输等行业领域':'4','核设施':'5','核技术应用':'6']
    entrustDate: { type: Date }, // 委托时间
    process_instance_id: String, // 合同审批实例id

    checkType: String, // 检测类型
    // instrumentIds: [], // 检测仪器
    environmentInfo: { // 检测环境信息
      startTemperature: String, //  检测开始温度
      endTemperature: String, // 检测结束温度
      starthumidity: String, //  检测开始湿度
      endhumidity: String, // 检测结束湿度
      pressure: String, // 气压
    },
    measurementSituation: {
      category: String, // 防护检测|性能检测
      measureValues: Array, // 测量值
      measurerange: [ String, String ],
      averageVal: String, // 平均值
      calibrationVal: String, // 校准值
      deviationValue: String, // 偏差值
    }, // 本底测量情况
    // 设备检测数据


    // 文件
    samplingSchemeFileName: String, // 方案文件名称
    performanceDetectionFileName: String, // 性能现场检测原始记录单
    protectionDetectionFileName: String, // 防护现场检测原始记录单
    performanceDetectionReportName: {
      name: String,
      url: String,
    }, // 性能现场检测报告单
    protectionDetectionReportName: {
      name: String,
      url: String,
    }, // 防护现场检测报告单
    reportReviewFileName: String, // 报告审核记录单
    radiateDetectReportDate: Date, // 初稿时间
    firstDraftFileName: {
      name: String,
      url: String,
    }, // 初稿文件
    radiateDetectReportFileName: {
      name: String,
      url: String,
    }, // 检测报告单 防护和性能整体的
    instrumentsApplyId: { type: String, ref: 'RadiateDeviceApply' }, // 仪器申请Id

    // 修改审批
    samplingSchemesMApplyId: String, // 方案修改审批id
    samplingSchemesMApplyStatus: Number, // 方案修改审批状态 1 审批中 2审批通过
    samplingSchemesMApplyTime: Date, // 方案修改审批完成时间 只记录第一次修改审批的完成时间
    spotRecordMApplyId: String, // 现场检测修改审批id
    spotRecordMApplyStatus: Number, // 现场检测修改审批状态 1 审批中 2审批通过
    spotRecordMApplyTime: Date, // 现场检测修改审批完成时间 只记录第一次修改审批的完成时间

    backgroundInstrumentId: String, // 本底校准仪器id
    backgroundSubInstrumentId: String, // 本底校准仪器id

    detectionData: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      remark: String, // 防护备注
      performanceRemark: String, // 性能备注
      // checkConditions: [
      //   {
      //     checkConditionsOne: Array, // kV mA s mAs cm 球管
      //     checkConditionsTwo: Array,
      //     correctionFactor: Number, // 仪器时间修正因子 只有仪器编号14040才有
      //     calibrationFactor: Number, // 校准因子
      //   },
      // ], // 检测条件
      // checkPointData: [{
      //   DistributeArea: String, // 检测点位置
      //   DistributeSN: String, // 检测点编号
      //   dataOne: String, // 读数1
      //   dataTwo: String, // 读数2
      //   dataThree: String, // 读数3
      //   aver: String, // 平均值
      //   result: Array, // 检测结果1
      //   // resultTwo: Array, // 检测结果2
      // }], // 布点信息

      checkConditionDatas: [ // 防护检测条件和点位信息
        {
          _id: {
            type: String,
            default: shortid.generate,
          },
          checkConditions: {
            checkConditionsOne: Array, // kV mA s mA*s cm 球管
            checkConditionsTwo: Array,
            correctionFactor: Number, // 仪器时间修正因子 只有仪器编号14040才有
            calibrationFactor: Number, // 校准因子
          }, // 检测条件
          checkPointData: [{
            DistributeArea: String, // 检测点位置
            DistributeSN: String, // 检测点编号
            dataOne: String, // 读数1
            dataTwo: String, // 读数2
            dataThree: String, // 读数3
            aver: String, // 平均值
            result: String, // 检测结果
            checkResult: String, // 判定结果 符合 不符合
            _id: {
              type: String,
              default: shortid.generate,
            },
          }], // 布点信息
        },
      ],
      // projectCategorys: Array, // 检测类别汇总 [放射，性能]
      machineRoom: String, // 机房名称 机房和设备是一对一的关系
      machineRoomId: String, // 机房id
      isMobileDevice: Boolean, // 是否可移动设备 如果是可移动设备，那么没有机房名称（会存储一条机房数据，只是名称为空）
      radiateParameterId: String, // 设备id
      radiateParameterName: String, // 设备名称
      radiateDeviceTypeId: String, // 设备大类id
      generalOrspecialized: String, // 通用或专用设备 general通用 specialized专用
      generalParameterId: String, // 专用设备对应的通用设备id 如果当前选择的设备是专用设备才有这个字段
      // commonProjectName: String, // 专用或通用项目名称
      detectionSN: String, // 检测编号
      performanceInstruments: [{
        _id: {
          type: String,
          default: shortid.generate,
        },
        // instruments: [{
        //   certificateIds: Array, // 鉴定记录id
        //   instrumentId: String, // 仪器id
        // }],
        certificateIds: Array, // 鉴定记录id
        instrumentId: String, // 仪器id
        useType: Number, // 用途  1 检测仪器 2 模体仪器  鉴定记录从检测仪器中获取
      }], // 性能检测仪器

      protectionInstrumentId: String, // 防护检测仪器id
      // protectionSubInstrumentId: String, // 防护检测仪器id
      // performanceInstrumentId: String, // 性能检测仪器id
      // performanceCertificateId: String, // 性能仪器鉴定记录id
      protectionCertificateId: String, // 防护仪器鉴定记录id
      // checkType: String, // 检测类型
      // // 防护
      // checkData:[{ // 检测数据
      //   condition:Array,
      //   condition2:Array,
      //   detectionData:[{// 所有点位对应的检测数据
      //     checkPointNum:Number,// 检测点位
      //     checkPointName:String,// 点位名称
      //     checkResult:String,// 检测结果
      //     checkResult2:String,// 检测结果2
      //   }]
      // }],

      // 性能
      checkProjects: [{ // 检测项目
        _id: {
          type: String,
          default: shortid.generate,
        },
        projectCategory: {
          type: String,
        }, // 项目分类 - 1.性能 2.防护
        // projectCategory: String, // 检测类别
        projectName: String, // 检测项目名称
        checkProjectId: String, // 检测项目id，关联的是radiateParameter表中的projects的id字段
        testCriterion: String, // 检测方法
        generalOrspecialized: String, // 通用或者专用项目
        noFeature: { // 无此功能
          type: Boolean,
          default: false,
        },
        noFeatureText: String, // 异常说明

        checkConditions: { // 现场检测条件
          voltage: Number, // 管电压 kv
          current: Number, // 管电流 ma
          exposureTime: Number, // 曝光时间 ms
          productOfCurrentAndTime: Number, // 管电流时间积 mAs
          distance: Number, // 探测器到焦点的距离 cm
          scanTime: Number, // 扫描时间
          scanMethod: String, // 扫描方式 轴扫、或螺旋、（1/10）标称层厚间距重建
          thickness: Number, // 厚度
          checkProjectType: String, // 检测类型
          SID: Number, // SID值
          lightField: Number, // 光野
          distance2: Number, // 照片上曝光区域边沿与台边距离 乳腺X射线
          targetFiltration: String,
        },
        conditionCheckDatas: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          focusType: String, // 焦点类型 大焦点 小焦点
          checkProjectType: String, // 检测类型
          detectionMold: String, // 检测模体
          circleType: String, // 内外圈
          moldPart: String, // 模体部位 头部、体部
          size: String, // 尺寸
          diameter: String, // 直径
          hasFilterGrid: String, // 有无滤线栅

          measureValue: Number, // 检测结果
          // 参数信息
          testingRequirement: String, // 检测要求
          limitSelectionPrecondition: String, // 前置条件
          // beforeCondition: Array, // 检测要求、前置条件
          limitVal: String, // 限值
          limitSymbol: String, // 限值条件 大于等于 小于等于 正负
          conditionId: String, // 检测类别ID
          detectionCondition: String, // 检测条件 判断是否为不做的项目

          // 平均值
          // averageValues: {
          //   voltage: Number, // 管电压(kV)
          //   exposureTime: Number, // 曝光时间(ms)
          //   kerma: Number, // 比释动能(mGy)
          //   correctedKerma: Number, // 校正比释动能(mGy)
          //   outputVal: Number, // 输出量K(mGy/mAs)

          // },
          // calculatedAverageValue: Number, // 计算值 平均值*0.42  重建层厚偏差 定位光精度

          // 辐射输出量重复性
          correctedFactor: Number, // 校准因子
          outputValAverageVal: Number, // 输出量平均值
          repeatability: Number, // 重复性

          // 乳腺DR
          targetFiltration: String, // 靶/滤过

          // 输出量线性   X射线摄影设备
          linearBetweenAdjacentGears: Number, // 相邻两档间线性(%)  (k1-k2)/(k1+k2) k指辐射输出量Ki（mGy /mAs）

          // 有用线束半值层  Ｘ射线摄影设备 牙科X射线设备
          reportedValue: String, // 报出值(mmAl)

          // 管电压指示的偏离

          // AEC响应
          twoAverageValue: Number, // 2次检测的平均值（mGy）

          // AEC重复性
          DDIValue: Number, // DDI的显示值
          productOfCurrentAndTimeAvg: Number, // 管电流时间积平均值

          // AEC电离室之间一致性
          maxRelativeDeviation: Number, // 最大相对偏差(%)

          // CT值（水）、噪声
          waterCTVaule: Number, // CT值（水）
          waterSD: Number, // SD值 水
          airCTVaule: Number, // CT值（空气）
          airSD: Number, // SD值 空气
          noiseValue: String, // 噪声值

          // 诊断床定位精度
          weight: Number, // 检测时床上重物（kg）

          // 信号传递特性（STP）
          formula: Number, // 为1是线性 2是对数 3指数
          correlationCoefficient: Number, // 计算相关系数的平方（R2）

          // 信号传递特性（STP）
          gradient: Number, // 斜率
          intercept: Number, // 截距

          // 响应均匀性
          coefficientOfVariation: Number, // 剂量值的变异系数（%）

          // 残影
          hasGhost: Boolean, // 是否发现残影
          ghostMeasureValue: Number, // 残影中像素值
          noGhostMeasureValue: Number, // 非残影中像素值
          relativeDeviation: Number, // 相对偏差（%）
          // DR设备 伪影
          artifactDesc: String, // 伪影描述情况
          // DR设备 高对比度分辨力
          horizontalMaxCount: Number, // 水平方向最大线对组数目（lp/mm）
          verticalMaxCount: Number, // 垂直方向最大线对组数目（lp/mm）
          halfVerticalMaxCount: Number, // 45°放置最大线对组数目（lp/mm）
          specifiedValue: Number, // 生产厂家给出的规定值（lp/mm）
          establishBaselineValue: Number, // 建立基线值
          // DR设备 低对比度分辨力
          oneKerma: Number, // 1μGy入射空气比释动能
          fiveKerma: Number, // 5μGy入射空气比释动能
          tenKerma: Number, // 10μGy入射空气比释动能
          checkResult: String, // 检测结果 符合 or 不符合
          voltage: Number, // 管电压
          averageValue: Number, // 多功能计量仪直接测量半值层平均值mmAl
          computedType: String, // 计算方式
          differenceValue: Number, // 差值（kV）  和 曝光时间指示的偏离 共用
          // 检测值
          checkData: [{
            _id: {
              type: String,
              default: shortid.generate,
            },
            kerma2: Number, // k2
            diameter: String, // 可分辨的细节直径
            conversionFactor: Number, // 转换因子
            breastCorrectionFactor: Number, // 乳房成分修正因子
            targetFilterCorrection: Number, // 不同靶/滤过修正因子
            angleCorrectionFactor: Number, // 角度修正因子
            // KAP指示偏离
            displayValue: Number, // 显示值P0
            doseValue1: Number, // 剂量值
            doseValue2: Number, // 剂量值
            doseValue3: Number, // 剂量值
            irradiationArea: Number, // 照射野面积（cm 2）


            checkResult: String, // 检测结果 符合 or 不符合
            // 辐射输出量重复性  牙科X射线设备
            voltage: Number, // 管电压 kv
            // exposureTime: Number, // 曝光时间 ms
            kerma: Number, // 比释动能 mGy
            correctedValue: Number, // 校正比释动能 mGy    输出量线性
            outputVal: Number, // 输出量K (mGy/mAs)  和 有用线束半值层 共用

            // 输出量线性   X射线摄影设备
            productOfCurrentAndTime: Number, // 电流和时间的乘积
            measureValue1: Number, // 测量值
            measureValue2: Number, // 测量值
            measureValue3: Number, // 测量值
            // measureValues: Array, // 测量值 和 管电压指示的偏离、曝光时间指示的偏离 共用
            averageValue: Number, // 平均值 和 管电压指示的偏离、曝光时间指示的偏离 共用
            // correctedValue: Number, // 校正结果 平均值+校正因子 和 管电压指示的偏离、曝光时间指示的偏离 共用
            radiationOutput: Number, // 辐射输出量Ki（mGy /mAs）
            targetFiltration: String, // 靶/滤过


            // 有用线束半值层  Ｘ射线摄影设备 牙科X射线设备
            addAluminumThickness: Number, // 附加铝片厚度（mm）
            airKerma: Number, // 空气比释动能(mGy)

            // 管电压指示的偏离
            correctedFactor: Number, // 校准因子
            nominalValue: Number, // 标称值V0  和 曝光时间指示的偏离 共用
            relativeDeviation: Number, // 相对偏差（%）  和 曝光时间指示的偏离 AEC响应 共用
            differenceValue: Number, // 差值（kV）  和 曝光时间指示的偏离 共用

            // 曝光时间指示的偏离
            exposureTimeRange: Number, // 曝光时间范围

            // 光野与照射野四边的偏差
            beforemeasureValueA: Number, // 曝光前a值
            beforemeasureValueB: Number, // 曝光前b值
            aftermeasureValueA: Number, // 曝光后a值
            aftermeasureValueB: Number, // 曝光后b值
            differenceValueA: Number, // 偏差a 后-前
            differenceValueB: Number, // 偏差b 后-前

            // AEC响应
            checkProjectType: String, // 检测类型 ['铝','铝加铜']

            // AEC重复性
            DDIValue: Number, // DDI的显示值

            // AEC电离室之间一致性
            ionizationChamber: String, // 选定的电离室
            current: Number, // 电流
            exposureTime: Number, // 曝光时间

            // 聚焦滤线栅与有用线束中心对准
            leftIonizationChamber1: String, // 左侧两孔影像光密度（OD）
            leftIonizationChamber2: String, // 左侧两孔影像光密度（OD）
            centerDensity: String, // 中心孔影像光密度（OD）
            rightIonizationChamber1: String, // 左侧两孔影像光密度（OD）
            rightIonizationChamber2: String, // 左侧两孔影像光密度（OD）

            // 口腔CBCT设备检测项目
            distanceValLeadRuler: Number, // 铅尺的真实距离do（mm）
            distanceValImage: Number, // 影像中铅尺的距离dm(mm)

            // 定位光精度  CT设备质量控制检测
            positioningDiff: String, // 定位差
            distanceValTop: Number, // 距离值 上
            distanceValRight: Number, // 距离值 右
            distanceValBottom: Number, // 距离值 下
            distanceValLeft: Number, // 距离值左
            positioningAccuracy: Number, // 定位光精度

            evaluateResult: String, // 评价结果

            // 重建层厚偏差
            windowWidth: Number, // 窗宽
            backgroundCTValue: Number, // 背景CT值
            highestWidthPosition: Number, // 最高宽位
            halfHighWidthPosition: Number, // 半高窗位
            measureValue1Top: Number, // 实测值（mm）上
            measureValue1Right: Number, // 实测值（mm）右
            measureValue1Bottom: Number, // 实测值（mm）下
            measureValue1Left: Number, // 实测值（mm）左
            calculatedAverageValue: Number, // 计算值 平均值*0.42  重建层厚偏差

            // 均匀性
            selectedArea: String, // 选定区域
            CTVaule: Number, // CT值
            SDVaule: Number, // SD

            // 高对比分辨力
            windowPosition: String, // 窗位
            conventionMeasureValue: Number, // 常规算法：实测结果（LP/cm）
            highResolutionMeasureValue: Number, // 高分辨力算法：实测结果（LP/cm）

            // 扫描架倾角精度
            checkProject: String,
            parameter1: Number, // 参数1
            parameter2: Number, // 参数2
            measureValue: Number, // 实测值
            inclinationAngle: Number, // 倾角大小

            // 低对比可探测能力
            backgroundMValue: Number, // 背景CT值M
            backgroundSDValue: Number, // 背景CT值SD
            targetMValue: Number, // 目标CT值M
            targetSDValue: Number, // 目标CT值SD
            contrastRatio: Number, // 对比度（%）

            // CTDIw
            centerMeasureValue: Number, // 中心实测值
            direction12: Number, // 表面实测值12点钟方向
            direction9: Number, // 表面实测值9点钟方向
            direction3: Number, // 表面实测值3点钟方向
            direction6: Number, // 表面实测值6点钟方向
            directionAverageValue: Number, // 表面平均值
            centerCorrectedValue: Number, // 中心校准值
            surfaceCorrectedValue: Number, // 表面校准值

            // CT值线性
            material: String, // 检测材料
            uValue: Number, // µ值
            standardCTValue: Number, // 标准CT值
            measureCTValue: Number, // 测量CT值
            SDValue: Number, // SD
            deviationValue: Number, // 标准与测量的偏差

            fieldViewMode: String, // 视野模式

            // 伪影
            hasArtifact: Boolean, // 有无明显伪影 有 无

            // 探测器剂量指示（DDI）
            DDIComputedValue: Number, // 生产厂家提供的DDI公式计算值

            // 响应均匀性
            quadrant: String, // 象限
            centerPixelMeasureValue: Number, // 影像中央像素值
            convertedMeasureDose: Number, // 换算剂量

            // 测距误差
            position: String, // 位置
            trueValue: Number, // 实测值

            // establishBaselineValue:Number,// 建立基线值（lp/mm）


            checkPointDesc: String, // 检测点位置描述

            reportedValue: String, // 报出值/结果
            reportedValue2: String, // 绝对偏差（mm） 重建层厚偏差
            reportedValue3: String, // 口腔CBCT 高对比度分辨力
            checkCondition: String, // 检测条件
          }],
        }],

      }],
    }],

    // 防护
    // progress: {
    //   // 项目创建时间
    //   createProject: {
    //     status: { // 0 未完成 1 进行中 2 已完成
    //       type: Number,
    //       default: 2,
    //     },
    //     completedTime: {
    //       type: Date,
    //       default: Date.now,
    //     },
    //   },
    //   // 合同评审状态
    //   approved: {
    //     status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止  5 已取消
    //       type: Number,
    //       default: 0,
    //     },
    //     completedTime: Date, // 状态变化时间
    //   },
    //   // 评价项目经理分配状态
    //   evaluationManagerAdmin: {
    //     status: { // 0 未完成 1 进行中 2 已完成
    //       type: Number,
    //       default: 0,
    //     },
    //     completedTime: Date, // 状态变化时间
    //   },
    //   // 检测项目经理分配状态
    //   inspectionManagerAdmin: {
    //     status: { // 0 未完成 1 进行中 2 已完成
    //       type: Number,
    //       default: 0,
    //     },
    //     completedTime: Date, // 状态变化时间
    //   },
    //   // 采样方案整体完成进度
    //   samplingSchemes: {
    //     status: { // 0 未完成 1 进行中 2 已完成
    //       type: Number,
    //       default: 0,
    //     },
    //     completedTime: Date, // 完成时间
    //   },
    //   instrumentsApply: { // 仪器申请单
    //     status: { // 0 未完成 1 进行中 2 已完成
    //       type: Number,
    //       default: 0,
    //     },
    //     completedTime: Date, // 完成时间
    //   },
    //   instrumentsReceive: { // 仪器领用
    //     status: { // 0 未完成 1 进行中 2 已完成
    //       type: Number,
    //       default: 0,
    //     },
    //     completedTime: Date, // 完成时间
    //   },
    //   // 现场检测完成完成进度
    //   spotRecord: {
    //     status: { // 0 未完成 1 进行中 2 已完成
    //       type: Number,
    //       default: 0,
    //     },
    //     completedTime: Date, // 完成时间
    //   },
    //   reportPreparation: { // 报告编制
    //     status: { // 0 未完成 1 进行中 2 已完成
    //       type: Number,
    //       default: 0,
    //     },
    //     completedTime: Date, // 完成时间
    //   },
    //   reportReview: { // 报告审核
    //     status: { // 0 未完成 1 进行中 2 已完成
    //       type: Number,
    //       default: 0,
    //     },
    //     completedTime: Date, // 完成时间
    //   },
    //   reportArchive: { // 报告归档
    //     status: { // 0 未进行 1 进行中 2 已完成
    //       type: Number,
    //       default: 0,
    //     },
    //     completedTime: Date, // 完成时间
    //     operator: {
    //       type: String,
    //       ref: 'ServiceEmployee',
    //     },
    //   },
    // },

    // // 归档压缩包
    // archiveFileZip: {
    //   path: String,
    //   name: String,
    // },
    // // 归档目录文件
    // archiveFileContents: {
    //   path: String,
    //   name: String,
    // },


    // 报告审批状态以及审批实例id
    reportProcessInstanceId: String, // 报告单审批实例id
    reportApprovedStartTime: Date, // 报告单审批发起时间
    progress: { // 进度
      completeReportArchive: {
        status: { // 0 未完成 1 已完成
          type: Number,
          default: 0,
        }, // 是否确认签收归档 默认不签收0 已经签收1
        completedTime: Date, // 完成时间
      },
      // 合同评审状态
      approved: {
        status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止  5 已取消
          type: Number,
          default: 0,
        },
        completedTime: Date, // 状态变化时间
      },
    },
    noticeForm: { // 是否存在不合格
      _id: String,
      url: String,
      fileName: String,
    },
    approvedWordFileName: String, // 合同审批word文件名称
    modifyRecordFileName: String, // 归档修改记录单
    contractApprovedUsers: [{ // 合同审批人
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: String, // employeeName
      signType: String, // 审批类型 approvedUsers(评审人员)|reviewTeamLeader（评审负责人）
      serviceEmployeeId: String, // serviceEmployeeId
      fileName: String, // 文件名
    }],
    // 现场照片
    scenePhotos: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        fileName: String,
        src: String,
        describe: String,
        latitude: String,
        longitude: String,
        address: String,
        createTime: Date,
        sceneType: String, // 照片类型：现场照片、标志物前照片
      },
    ],
    // 签名
    signManage: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      signRole: String, // 陪同人 采样人 测量人
      name: String, // 姓名
      signPath: String, // 签名文件储存位置
      signTime: Date, // 签名时间
    }],
    // 陪同人签名库
    companionLib: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      signRole: String, // 陪同人 采样人 测量人
      name: String, // 姓名
      signPath: String, // 签名文件储存位置
      signTime: Date, // 签名时间
    }],
    offlineCompleted: {
      type: Boolean,
      default: false,
    }, // 线下完成
  });

  return mongoose.model('radiateqlcProject', RadiateqlcProjectSchema, 'radiateqlcProject');

};

