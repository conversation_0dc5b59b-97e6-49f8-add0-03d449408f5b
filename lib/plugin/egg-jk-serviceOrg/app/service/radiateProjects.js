// const moment = require('moment');
const path = require('path');
// const fs = require('fs');
const {
  // _create,
  _item,
  _list,
} = require(path.join(process.cwd(), 'app/service/general'));

const Service = require('egg').Service;

class RadiateProjectsService extends Service {
  async create(payload) {
    const { ctx } = this;
    return await new ctx.model.RadiateProjects(payload).save();
    // return _create('RadiateProjects', payload, this.ctx);
  }
  async item(res, params = {}) {
    return _item(res, this.ctx.model.RadiateProjects, params);
  }
  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
    sort = {
      giveBackStatus: -1, // 退回的需要置顶，所以把退回的排前面
      year: -1,
      _id: 1,
    },
  } = {}) {
    const orderName = payload.orderName;
    const orderBy = payload.orderBy;
    if (orderName === '是否上报') {
      sort = {
        status: orderBy,
        _id: 1,
      };
    }
    if (orderName === '完成状态') {
      sort = {
        completeStatus: orderBy,
        _id: 1,
      };
    }
    if (orderName === '完成时间') {
      sort = {
        completeTime: orderBy,
        _id: 1,
      };
    }
    const listdata = _list(this.ctx.model.RadiateProjects, payload, {
      query,
      searchKeys,
      populate,
      files,
      sort,
    });
    // console.log(listdata, 'listdata--------------------------');
    return listdata;
  }
  async list(params) {
    const reg = new RegExp(params.keyWords, 'i'); // 不区分大小写
    console.log(2222222222, params);
    const query = {
      $or: [
        { name: reg },
        { EnterpriseName: reg },
        { projectName: reg },
        { projectSN: reg },
      ],
    };
    // query.serviceID = params.serviceID;
    // console.log(query, params.regAddr, '1111111111111111111');
    // if (params.status) query.status = +params.status;
    if (params.company) query.companyID = params.company; // 实际用的是公司统一社会信用代码
    // if (params.orgCode) query.serviceOrgID = params.orgCode; // 实际用的是机构统一社会信用代码
    // if (params.org && !params.orgCode) {
    if (params.org) {
      // const org = await this.ctx.model.RadiateqlcProject.findOne({ _id: params.org }, { organization: 1 });
      // if (org)query.serviceOrgID = org._id;
      query.serviceOrgId = params.org;
    }
    // if (params.superUserID) query.superUserID = params.superUserID; // 监管端的id
    if (params.regAddr) query.workPlaces = { $elemMatch: { workAdd: { $in: params.regAddr } } }; // 工作场所
    // if (params.industry && params.industry.length) query.companyIndustry = { $all: params.industry }; // 所属行业
    if (params.serviceAreas && params.serviceAreas.length) query.serviceArea = { $in: params.serviceAreas }; // 技术服务类型
    if (params.dateType && params.dates && params.dates.length === 2 && params.dateType !== 'applyTime') {
      query[params.dateType] = {
        $lt: new Date(new Date(params.dates[1]).getTime() + 24 * 3600000),
        $gte: new Date(params.dates[0]),
      };
    } else if (params.year && params.year !== 'NaN' && params.dateType !== 'applyTime') {
      query[params.dateType] = {
        $lt: new Date(`${+params.year + 1}-1-1`),
        $gte: new Date(`${params.year}-1-1`),
      };
    }
    console.log(333333, query);

    if (!query.companyIndustry) { // 没有传行业分类
      // try {
      //   const result = await this.ctx.model.RadiateProjects.aggregate([
      //     { $match: query },
      //     { $sort: { applyTime: -1 } },
      //     { $skip: (params.curPage - 1) * (+params.limit) },
      //     { $limit: +params.limit || 10 },
      //   ]);
      //   console.log(result);
      //   return result;
      // } catch (err) {
      //   console.error(err, 99999999999999);
      //   return null;
      // }
      delete query.completeTime;

      // 构建applyTime的日期筛选条件
      let applyTimeMatch = {};
      if (params.dateType === 'applyTime' && params.dates && params.dates.length === 2) {
        applyTimeMatch = {
          'progressInfo.progresses': {
            $elemMatch: {
              field: 'apply',
              status: 2,
              completedTime: {
                $gte: new Date(params.dates[0]),
                $lt: new Date(new Date(params.dates[1]).getTime() + 24 * 3600000),
              },
            },
          },
        };
      } else if (params.dateType === 'applyTime' && params.year && params.year !== 'NaN') {
        applyTimeMatch = {
          'progressInfo.progresses': {
            $elemMatch: {
              field: 'apply',
              status: 2,
              completedTime: {
                $gte: new Date(`${params.year}-1-1`),
                $lt: new Date(`${+params.year + 1}-1-1`),
              },
            },
          },
        };
      } else {
        applyTimeMatch = {
          'progressInfo.progresses': {
            $elemMatch: {
              field: 'apply',
              status: 2,
            },
          },
        };
      }

      const resultRadiate = await this.ctx.model.RadiateqlcProject.aggregate([
        { $match: query },
        {
          $lookup: {
            from: 'radiateqlcProgress', // 联查进度表
            localField: '_id', // 本地关联的字段（项目ID）
            foreignField: 'radiateqlcProjectId', // 进度表中的项目ID字段
            as: 'progressInfo', // 查询到的进度信息
          },
        },
        {
          $match: applyTimeMatch,
        },
        {
          $lookup: {
            from: 'serviceEmployee', // 从哪个Schema中查询（一般需要复数，除非声明Schema的时候专门有处理）
            localField: 'personInCharge', // 本地关联的字段
            foreignField: '_id', // user中用的关联字段
            as: 'personInCharge', // 查询到所有user后放入的字段名，这个是自定义的，是个数组类型。
          },
        },
        {
          $lookup: {
            from: 'serviceEmployee', // 从哪个Schema中查询（一般需要复数，除非声明Schema的时候专门有处理）
            localField: 'personsOfProject', // 本地关联的字段
            foreignField: '_id', // user中用的关联字段
            as: 'personsOfProject', // 查询到所有user后放入的字段名，这个是自定义的，是个数组类型。
          },
        },
        {
          $lookup: {
            from: 'serviceOrg', // 从哪个Schema中查询（一般需要复数，除非声明Schema的时候专门有处理）
            localField: 'serviceOrgId', // 本地关联的字段
            foreignField: '_id', // user中用的关联字段
            as: 'serviceOrg', // 查询到所有user后放入的字段名，这个是自定义的，是个数组类型。
          },
        },
        {
          $addFields: {
            name: '$serviceOrg.name',
            serviceAddress: '$serviceOrg.regAddr',
          },
        },
        // 上报时间
        {
          $addFields: {
            applyTime: {
              $let: {
                vars: {
                  progressDoc: { $arrayElemAt: [ '$progressInfo', 0 ] },
                },
                in: {
                  $let: {
                    vars: {
                      applyProgress: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: '$$progressDoc.progresses',
                              cond: { $eq: [ '$$this.field', 'apply' ] },
                            },
                          },
                          0,
                        ],
                      },
                    },
                    in: '$$applyProgress.completedTime',
                  },
                },
              },
            },
          },
        },
        {
          $project: {
            serviceOrg: 0,
            progressInfo: 0, // 不返回进度信息，只用于筛选
          },
        },
        {
          $sort: { applyTime: -1 },
        },
        {
          $skip: (params.curPage - 1) * +params.limit,
        },
        {
          $limit: +params.limit || 10,
        },
      ]);
      // const resultRange = await this.ctx.model.RangeProjects.aggregate([
      //   {
      //     $match: {
      //       _id: 'taMjzGLs0',
      //     },
      //   }, {
      //     $lookup: {
      //       from: 'serviceOrg',
      //       localField: 'serviceID',
      //       foreignField: '_id',
      //       as: 'serviceOrgInfo',
      //     },
      //   }, {
      //     $project: {
      //       _id: 1,
      //       serviceOrgInfo: 1,
      //     },
      //   }, {
      //     $unwind: {
      //       path: '$serviceOrgInfo',
      //       preserveNullAndEmptyArrays: true,
      //     },
      //   }, {
      //     $unwind: {
      //       path: '$serviceOrgInfo.qualifies',
      //       preserveNullAndEmptyArrays: true,
      //     },
      //   }, {
      //     $lookup: {
      //       from: 'detectionMechanism',
      //       localField: 'serviceOrgInfo.qualifies',
      //       foreignField: '_id',
      //       as: 'cer',
      //     },
      //   },
      // ]);
      return resultRadiate;
    }
    console.log(1111111, query);
    return await this.ctx.model.JobHealth.aggregate([
      { $unwind: '$companyIndustry' },
      { $match: query },
      { $group: {
        _id: '$_id',
        companyIndustry1: { $push: '$companyIndustry' },
        info: { $first: '$$ROOT' },
      } },
      { $addFields: { 'info.companyIndustry': '$companyIndustry1' } },
      { $replaceRoot: { newRoot: '$info' } },
      {
        $lookup: {
          from: 'serviceEmployee', // 从哪个Schema中查询（一般需要复数，除非声明Schema的时候专门有处理）
          localField: 'personInCharge', // 本地关联的字段
          foreignField: '_id', // user中用的关联字段
          as: 'personInCharge', // 查询到所有user后放入的字段名，这个是自定义的，是个数组类型。
        },
      },
      {
        $lookup: {
          from: 'serviceEmployee', // 从哪个Schema中查询（一般需要复数，除非声明Schema的时候专门有处理）
          localField: 'personsOfProject', // 本地关联的字段
          foreignField: '_id', // user中用的关联字段
          as: 'personsOfProject', // 查询到所有user后放入的字段名，这个是自定义的，是个数组类型。
        },
      },
      {
        $lookup: {
          from: 'riskAssessmentReport',
          localField: '_id',
          foreignField: 'jobHealthId',
          as: 'riskAssessmentReport',
        },
      },
      {
        $lookup: {
          from: 'radiateProjects',
          localField: '_id',
          foreignField: 'jobHealthId',
          as: 'radiateProjects',
        },
      },
      {
        $sort: { applyTime: -1 },
      },
      {
        $skip: (params.curPage - 1) * (+params.limit),
      },
      {
        $limit: +params.limit || 10,
      },
    ]);


  }

  async getCount(params) {
    const reg = new RegExp(params.keyWords, 'i'); // 不区分大小写
    const query = {
      // projectStop: { $ne: true },
      // status: 1,
      $or: [
        { name: { $regex: reg } },
        { EnterpriseName: { $regex: reg } },
        { projectName: { $regex: reg } },
      ],
    };
    // query.serviceID = params.serviceID;
    // if (params.status) query.status = +params.status;
    if (params.company) query.companyID = params.company; // 实际用的是公司统一社会信用代码
    // if (params.orgCode) query.serviceOrgID = params.orgCode; // 实际用的是机构统一社会信用代码
    if (params.serviceAreas && params.serviceAreas.length) query.serviceArea = { $in: params.serviceAreas }; // 技术服务类型
    // if (params.org && !params.orgCode) {
    if (params.org) {
      // const org = await this.ctx.model.RadiateProjects.findOne({ _id: params.org }, { organization: 1 });
      // if (org)query.serviceOrgID = org.organization;
      query.serviceOrgId = params.org;
    }
    // if (params.superUserID) query.superUserID = params.superUserID; // 监管端的id
    if (params.industry && params.industry.length) query.companyIndustry = { $all: params.industry }; // 行业分类

    if (params.regAddr) query.workPlaces = { $elemMatch: { workAdd: { $in: params.regAddr } } }; // 工作场所

    if (params.completeStatus) query.completeStatus = params.completeStatus;
    if (params.completeUpdate) query.completeUpdate = params.completeUpdate;
    if (params.dateType && params.dates && params.dates.length === 2 && params.dateType !== 'applyTime') {
      query[params.dateType] = {
        $lte: new Date(new Date(params.dates[1]).getTime() + 24 * 3600000),
        $gte: new Date(params.dates[0]),
      };
    } else if (params.year && params.year !== 'NaN' && params.dateType !== 'applyTime') {
      query[params.dateType] = {
        $lt: new Date(`${+params.year + 1}-1-1`),
        $gte: new Date(`${params.year}-1-1`),
      };
    }

    // 构建applyTime的日期筛选条件
    let applyTimeMatch = {};
    if (params.dateType === 'applyTime' && params.dates && params.dates.length === 2) {
      applyTimeMatch = {
        'progressInfo.progresses': {
          $elemMatch: {
            field: 'apply',
            status: 2,
            completedTime: {
              $gte: new Date(params.dates[0]),
              $lt: new Date(new Date(params.dates[1]).getTime() + 24 * 3600000),
            },
          },
        },
      };
    } else if (params.dateType === 'applyTime' && params.year && params.year !== 'NaN') {
      applyTimeMatch = {
        'progressInfo.progresses': {
          $elemMatch: {
            field: 'apply',
            status: 2,
            completedTime: {
              $gte: new Date(`${params.year}-1-1`),
              $lt: new Date(`${+params.year + 1}-1-1`),
            },
          },
        },
      };
    } else {
      applyTimeMatch = {
        'progressInfo.progresses': {
          $elemMatch: {
            field: 'apply',
            status: 2,
          },
        },
      };
    }

    if (!query.companyIndustry) {
      const countResult = await this.ctx.model.RadiateqlcProject.aggregate([
        { $match: query },
        {
          $lookup: {
            from: 'radiateqlcProgress',
            localField: '_id',
            foreignField: 'radiateqlcProjectId',
            as: 'progressInfo',
          },
        },
        {
          $match: applyTimeMatch,
        },
        {
          $count: 'count',
        },
      ]);
      const count = countResult && countResult[0] ? countResult[0].count : 0;
      console.log(*********, count);
      return new Promise(res => res(count));
    }
    const res = await this.ctx.model.RadiateqlcProject.aggregate([
      { $match: query },
      {
        $lookup: {
          from: 'radiateqlcProgress',
          localField: '_id',
          foreignField: 'radiateqlcProjectId',
          as: 'progressInfo',
        },
      },
      {
        $match: applyTimeMatch,
      },
      { $group: {
        _id: '$_id',
      } },
      { $count: 'count' },
    ]);
    const count2 = res && res[0] ? res[0].count : 0;
    // const count = await this.ctx.model.JobHealth.count(query);
    // console.log(33333, count);
    return new Promise(res => res(count2));
  }
}

module.exports = RadiateProjectsService;
