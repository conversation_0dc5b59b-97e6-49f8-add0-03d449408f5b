const pkgInfo = require('../package.json');
exports.jk_physicalExaminationOrg = {
  alias: 'physicalExaminationOrg', // 插件目录，必须为英文
  pkgName: 'egg-jk-physicalExaminationOrg', // 插件包名
  enName: 'jk_physicalExaminationOrg', // 插件名
  name: '体检机构', // 插件名称
  description: '体检机构管理', // 插件描述
  isadm: 1, // 是否有后台管理，1：有，0：没有，入口地址:'/ext/devteam/admin/index'
  isindex: 0, // 是否需要前台访问，1：需要，0：不需要,入口地址:'/ext/devteam/index/index'
  version: pkgInfo.version, // 版本号
  iconName: 'icon_service', // 主菜单图标名称
  adminUrl: '/physicalExaminationOrg/js/app.js',
  adminApi: [
    {
      url: 'physicalExaminationOrg/getAllOrgIds',
      method: 'get',
      controllerName: 'getAllOrgIds',
      details: '获取体检机构Id',
    },
    {
      url: 'physicalExaminationOrg/getAllOrg',
      method: 'post',
      controllerName: 'getAllOrg',
      details: '获取体检机构列表',
    },
    {
      url: 'physicalExaminationOrg/getOne',
      method: 'get',
      controllerName: 'getOne',
      details: '获取单条企业管理信息',
    },
    {
      url: 'physicalExaminationOrg/getQualifies',
      method: 'post',
      controllerName: 'getQualifies',
      details: '获取资质信息',
    },
    {
      url: 'physicalExaminationOrg/getOrgInformation',
      method: 'get',
      controllerName: 'getOrgInformation',
      details: '获取指定机构信息',
    },
    {
      url: 'physicalExaminationProjects/getList',
      method: 'post',
      controllerName: 'getList',
      details: '获取用人单位列表',
    },
    {
      url: 'physicalExaminationProjects/getProjects',
      method: 'post',
      controllerName: 'getProjects',
      details: '根据companyID获取项目列表',
    },
    {
      url: 'physicalExaminationProjects/appointmentList',
      method: 'get',
      controllerName: 'getAppointmentList',
      details: '获取预约单列表',
    },
    {
      url: 'physicalExaminationProjects/appointment',
      method: 'get',
      controllerName: 'getAppointment',
      details: '获取预约单详情',
    },
    {
      url: 'physicalExaminationProjects/harmFactors',
      method: 'get',
      controllerName: 'getHarmFactors',
      details: '根据条件查询危害因素',
    },
    {
      url: 'physicalExaminationProjects/getIndustry',
      method: 'get',
      controllerName: 'getIndustry',
      details: '获取所有行业分类',
    },
    {
      url: 'physicalExaminationProjects/update',
      method: 'post',
      controllerName: 'updateHealthCheck',
      details: '修改体检项目',
    },
    {
      url: 'physicalExaminationProjects/exportEmployeeInfo',
      method: 'post',
      controllerName: 'exportEmployeeInfo',
      details: '导出体检员工信息',
    },
    {
      url: 'physicalExaminationOrg/updateOneOrg',
      method: 'post',
      controllerName: 'updateOneOrg',
      details: '修改体检机构信息',
    },
    {
      url: 'physicalExaminationOrg/delete',
      method: 'get',
      controllerName: 'deleteOne',
      details: '删除体检机构',
    },
    {
      url: 'physicalExamDetectionMechanism/list',
      method: 'get',
      controllerName: 'list',
      details: '获取资质证书列表',
    },
    {
      url: 'physicalExamDetectionMechanism/getOne',
      method: 'get',
      controllerName: 'getOne',
      details: '获取单条资质证书信息',
    },
    {
      url: 'physicalExamDetectionMechanism/create',
      method: 'post',
      controllerName: 'create',
      details: '添加单个资质证书',
    },
    {
      url: 'physicalExamDetectionMechanism/update',
      method: 'post',
      controllerName: 'update',
      details: '更新资质证书信息',
    },
    {
      url: 'medicalInfo/getMedicalInfoList',
      method: 'post',
      controllerName: 'getMedicalInfoList',
      details: '获取体检人员信息列表',
    },
    {
      url: 'medicalInfo/exportPersonInfo',
      method: 'post',
      controllerName: 'exportPersonInfo',
      details: '导出体检人员信息列表',
    },
    {
      url: 'physicalExaminationOrg/getSuspectList',
      method: 'post',
      controllerName: 'getSuspectList',
      details: '获取疑似职业病上报列表',
    },
    {
      url: 'physicalExaminationOrg/getSusReportDetail',
      method: 'get',
      controllerName: 'getSusReportDetail',
      details: '获取上报详情',
    },
    // 项目回执
    // {
    //   url: 'projectBack/findProject',
    //   method: 'post',
    //   controllerName: 'findProject',
    //   details: '查询项目报送信息',
    // }, {
    //   url: 'projectBack/generateBackWord',
    //   method: 'post',
    //   controllerName: 'generateBackWord',
    //   details: '生成回执word',
    // }, {
    //   url: 'projectBack/postBackInfo',
    //   method: 'post',
    //   controllerName: 'postBackInfo',
    //   details: '发送回执',
    // }, {
    //   url: 'projectBack/postBackSet',
    //   method: 'post',
    //   controllerName: 'postBackSet',
    //   details: '设置回执方式',
    // }, {
    //   url: 'projectBack/findBackType',
    //   method: 'get',
    //   controllerName: 'findBackType',
    //   details: '查询回执方式',
    // }
    {
      url: 'healthcheck/workInjuryRecognition',
      method: 'get',
      controllerName: 'workInjuryRecognitionList',
      details: '获取工伤认定申请列表',
    },
    {
      url: 'healthcheck/workInjuryRecognitionDetail',
      method: 'get',
      controllerName: 'workInjuryRecognitionDetail',
      details: '工伤认定申请详情',
    },
    {
      url: 'healthcheck/workInjuryRecognitionReview',
      method: 'put',
      controllerName: 'workInjuryRecognitionReview',
      details: '工伤认定申请审核',
    },
    // 职业健康检测统计接口
    {
      url: 'jobHealthStatistics/getEnterpriseStatsByTime',
      method: 'get',
      controllerName: 'getEnterpriseStatsByTime',
      details: '获取职业健康检测用人单位统计数据',
    },
    {
      url: 'jobHealthStatistics/getFactorEnterpriseStats',
      method: 'get',
      controllerName: 'getFactorEnterpriseStats',
      details: '获取各职业病危害因素检测的用人单位数',
    },
    {
      url: 'jobHealthStatistics/getEnterpriseRanking',
      method: 'get',
      controllerName: 'getEnterpriseRanking',
      details: '获取职业健康检测完成情况排名',
    },
    {
      url: 'jobHealthStatistics/getExceedStandardStats',
      method: 'get',
      controllerName: 'getExceedStandardStats',
      details: '获取用人单位检测超标统计数据',
    },
    {
      url: 'jobHealthStatistics/getFactorExceedRanking',
      method: 'get',
      controllerName: 'getFactorExceedRanking',
      details: '获取危害因素检测超标排名',
    },
    {
      url: 'jobHealthStatistics/getServiceOrgList',
      method: 'get',
      controllerName: 'getServiceOrgList',
      details: '获取服务机构列表',
    },
    {
      url: 'jobHealthStatistics/getFactorDistributionMap',
      method: 'get',
      controllerName: 'getFactorDistributionMap',
      details: '获取危害因素检测分布图',
    },
    // -----体检数据统计-----开始-----
    {
      url: 'healthCheckStatistics/getEnterpriseExamStatsByTime',
      method: 'get',
      controllerName: 'getEnterpriseExamStatsByTime',
      details: '获取职业健康检查用人单位统计数据',
    },
    {
      url: 'healthCheckStatistics/healthCheckFactorEmployees',
      method: 'get',
      controllerName: 'healthCheckFactorEmployees',
      details: '统计不同危害因素员工数量',
    },
    {
      url: 'healthCheckStatistics/getFullyCheckedEnterpriseCountByCity',
      method: 'get',
      controllerName: 'getFullyCheckedEnterpriseCountByCity',
      details: '各区域职业健康检查完成情况排名',
    },
    {
      url: 'healthCheckStatistics/countJobConclusionTypes',
      method: 'get',
      controllerName: 'countJobConclusionTypes',
      details: '统计各类体检结论类型人数成功',
    },
    {
      url: 'healthCheckStatistics/calcJobHealthAbnormalRate',
      method: 'get',
      controllerName: 'calcJobHealthAbnormalRate',
      details: '计算健康体检异常率',
    },
    {
      url: 'healthCheckStatistics/getAbnormalEnterpriseCountByCity',
      method: 'get',
      controllerName: 'getAbnormalEnterpriseCountByCity',
      details: '各区域职业健康体检异常用人单位数排名',
    },
    // -----体检数据统计-----结束-----
  ],
  fontApi: [
    {
      url: 'physicalExaminationOrg/getSuspectsData',
      method: 'get',
      controllerName: 'getSuspectsData',
      details: '获取对应项目的个人体检信息',
    },
    {
      url: 'physicalExaminationOrg/uploadLicensePic',
      method: 'post',
      controllerName: 'uploadLicensePic',
      details: '上传图片',
    },
  ],
  initData: '', // 初始化数据脚本
  pluginsConfig: ` 
    exports.jk_physicalExaminationOrg = {\n
        enable: true,\n
         \n
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
  physicalExaminationOrgRouter:{\n
        match: [ctx => ctx.path.startsWith('/manage/physicalExaminationOrg'), ctx => ctx.path.startsWith('/api/physicalExaminationOrg'),ctx => ctx.path.startsWith('/manage/physicalExaminationProjects'),ctx => ctx.path.startsWith('/manage/healthcheck'),ctx => ctx.path.startsWith('/manage/jobHealthStatistics'),ctx => ctx.path.startsWith('/manage/healthCheckStatistics')],\n
    },\n
    `, // 插入到 config.default.js 中的配置
};
