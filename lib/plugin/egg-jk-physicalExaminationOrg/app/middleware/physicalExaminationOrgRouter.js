
const physicalExaminationOrgApiController = require('../controller/api/physicalExaminationOrg');
const physicalExaminationOrgAdminController = require('../controller/manage/physicalExaminationOrg');
const PEDetectionMechanismAdminController = require('../controller/manage/physicalExamDetectionMechanism');
const healthCheckTaskController = require('../controller/manage/healthCheckTask');

// const projectBackController = require('../controller/manage/projectBack');
const physicalExaminationProjectsAdminController = require('../controller/manage/physicalExaminationProjects');
const medicalInfoAdminController = require('../controller/manage/medicalInfo');
const jobHealthStatisticsController = require('../controller/manage/jobHealthStatistics');
const healthCheckStatisticsController = require('../controller/manage/healthCheckStatistics');
module.exports = (options, app) => {

  return async function physicalExaminationOrgRouter(ctx, next) {
    const pluginConfig = app.config.jk_physicalExaminationOrg;
    // if (ctx.request.url.startsWith('/manage/projectBack/')) {
    //   await app.initPluginRouter(ctx, pluginConfig, projectBackController);
    // } else
    if (ctx.request.url.startsWith('/manage/physicalExaminationProjects')) {
      await app.initPluginRouter(ctx, pluginConfig, physicalExaminationProjectsAdminController);
    } else if (ctx.request.url.startsWith('/manage/physicalExaminationOrg') || ctx.request.url.startsWith('/api/physicalExaminationOrg')) {
      await app.initPluginRouter(ctx, pluginConfig, physicalExaminationOrgAdminController, physicalExaminationOrgApiController);
    } else if (ctx.url.startsWith('/manage/physicalExamDetectionMechanism')) {
      await app.initPluginRouter(ctx, pluginConfig, PEDetectionMechanismAdminController);
    } else if (ctx.request.url.startsWith('/api/physicalExaminationOrg')) {
      await app.initPluginRouter(ctx, pluginConfig, physicalExaminationOrgApiController);
    } else if (ctx.request.url.startsWith('/manage/medicalInfo')) {
      await app.initPluginRouter(ctx, pluginConfig, medicalInfoAdminController);
    } else if (ctx.request.url.startsWith('/manage/healthCheckTask') || ctx.request.url.startsWith('/manage/healthcheck')) {
      await app.initPluginRouter(ctx, pluginConfig, healthCheckTaskController);
    } else if (ctx.request.url.startsWith('/manage/jobHealthStatistics')) {
      await app.initPluginRouter(ctx, pluginConfig, jobHealthStatisticsController);
    } else if (ctx.request.url.startsWith('/manage/healthCheckStatistics')) { // 职业健康检查统计
      await app.initPluginRouter(ctx, pluginConfig, healthCheckStatisticsController);
    }
    await next();

  };

};
