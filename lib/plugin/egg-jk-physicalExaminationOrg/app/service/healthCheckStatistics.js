const Service = require('egg').Service;
// const axios = require('axios');
// 定义所有危害因素类型
// const factorTypes = [
//   {
//     field: 'biologicalFactors',
//     name: '生物',
//   },
//   {
//     field: 'chemistryFactors',
//     name: '化学物质',
//   },
//   {
//     field: 'dustFactors',
//     name: '粉尘',
//   },
//   {
//     field: 'handBorneVibrationFactors',
//     name: '手传振动',
//   },
//   {
//     field: 'heatFactors',
//     name: '高温',
//   },
//   {
//     field: 'highFrequencyEleFactors',
//     name: '高频电磁场',
//   },
//   {
//     field: 'laserFactors',
//     name: '激光辐射',
//   },
//   {
//     field: 'microwaveFactors',
//     name: '微波辐射',
//   },
//   {
//     field: 'noiseFactors',
//     name: '噪声',
//   },
//   {
//     field: 'powerFrequencyElectric',
//     name: '工频电场',
//   },
//   {
//     field: 'SiO2Factors',
//     name: '游离二氧化硅',
//   },
//   {
//     field: 'ultraHighRadiationFactors',
//     name: '超高频辐射',
//   },
//   {
//     field: 'ultravioletFactors',
//     name: '紫外辐射',
//   },
//   {
//     field: 'ionizatioSourceFactors',
//     name: '电离辐射-含源装置',
//   },
//   {
//     field: 'ionizatioRadialFactors',
//     name: '电离辐射-射线装置',
//   },
// ];
/**
 * 职业健康检测统计服务
 */
class JobHealthStatisticsService extends Service {
  /**
 * 构建时间查询条件
 * @param {Object} params - 查询参数对象
 * @param {Date|String} [params.startTime] - 开始时间
 * @param {Date|String} [params.endTime] - 结束时间
 * @param {Date|String} [params.timePoint] - 时间点
 * @return {Object} 查询条件对象
 */
  _buildTimeCondition(params) {
    const timeCondition = {};
    const { startTime, endTime, timePoint } = params;
    if (timePoint) {
      // 如果提供时间点，按时间点查询
      const pointDate = new Date(timePoint);
      // 获取当天的开始和结束
      const startOfDay = new Date(pointDate.setHours(0, 0, 0, 0));
      const endOfDay = new Date(pointDate.setHours(23, 59, 59, 999));

      timeCondition.reportTime = {
        $gte: startOfDay,
        $lte: endOfDay,
      };
    } else if (startTime && endTime) {
      // 如果提供开始和结束时间，按时间段查询
      timeCondition.reportTime = {
        $gte: new Date(startTime),
        $lte: new Date(endTime),
      };
    }

    return timeCondition;
  }

  /**
   * 构建地区查询条件
   * @param {Object} params - 查询参数
   *  @param {String} [params.regionCode] - 区域代码
   * @return {Object} 包含企业ID条件的查询对象
   */
  async _buildRegionQuery(params) {
    const { ctx } = this;
    const query = {};

    if (params.regionCode) {
      // 通过区域代码查询district表获取地区名称
      const district = await ctx.model.District.findOne(
        { area_code: params.regionCode },
        { name: 1 }
      ).lean();
      console.log('district', district);
      if (district && district.name) {
        const searchAddr = district.name;
        console.log('searchAddr', searchAddr);

        // 查询符合地区条件的企业ID
        const Adminorg = ctx.model.Adminorg;
        const enterpriseIdsWithRegion = await Adminorg.find(
          {
            isDelete: { $ne: true },
            'workAddress.districts': { $elemMatch: { $all: [ searchAddr ] } },
          },
          { _id: 1 }
        ).lean();

        const filteredEnterpriseIds = enterpriseIdsWithRegion.map(org => org._id);

        // 将筛选后的企业ID添加到查询条件
        if (filteredEnterpriseIds.length > 0) {
          query.EnterpriseID = { $in: filteredEnterpriseIds };
        } else {
          // 如果找到了地区但没有匹配的企业，返回一个必定为空的查询条件
          query.EnterpriseID = { $in: [] };
        }
      } else {
        // 如果没有找到地区，返回一个必定为空的查询条件
        query.EnterpriseID = { $in: [] };
      }
    }

    console.log('query', query);
    return query;
  }

  /**
   * 获取用人单位检查统计数据
   * 包括：全部检查、部分检查、未检查的用人单位数及检查率
   * @param {Object} params - 查询参数对象，包含以下可选字段：
   *   @param {Date|String} [params.startTime] - 开始时间
   *   @param {Date|String} [params.endTime] - 结束时间
   *   @param {Date|String} [params.timePoint] - 时间点
   *   @param {String} [params.regionCode] - 地区代码
   * @return {Object} 统计结果
   */
  // 一个企业adminorg有多个员工employee，员工表上的EnterpriseID为企业表adminorg的_id。healthCheckRegister为体检记录表，每条记录有employeeID为员工表的_id。请统计所有员工都体检的企业，部分员工体检的企业，所有员工都未体检的企业
  async getEnterpriseExamStatsByTime(params = {}) {
    const { ctx } = this;
    const Employee = ctx.model.Employee;
    const Adminorg = ctx.model.Adminorg;
    const HealthCheckRegister = ctx.model.HealthCheckRegister;

    // 构建时间查询条件
    const timeCondition = this._buildTimeCondition(params);
    console.log('params', params);
    // 构建地区查询条件
    const regionQuery = await this._buildRegionQuery(params);

    // 合并查询条件
    // const combinedQuery = {
    //   ...timeCondition,
    //   ...regionQuery,
    // };

    // 如果有地区查询条件但没有匹配的企业，则返回空结果
    if (params.regionCode && regionQuery.EnterpriseID && regionQuery.EnterpriseID.$in.length === 0) {
      return {
        totalEnterpriseCount: 0,
        fullyCheckedCount: 0,
        partiallyCheckedCount: 0,
        notCheckedCount: 0,
        checkRate: '0%',
      };
    }

    // 查询符合条件企业
    const adminorgQuery = {
      isDelete: { $ne: true },
      productionStatus: { $ne: '0' },
      isactive: { $ne: '2' },
    };
    if (regionQuery.EnterpriseID) {
      adminorgQuery._id = regionQuery.EnterpriseID;
    }
    const adminorgs = await Adminorg.find(adminorgQuery, { _id: 1 }).lean();
    // 企业id数组
    const enterpriseIds = adminorgs.map(org => org._id);

    // 查询企业员工
    const employees = await Employee.find({ EnterpriseID: { $in: enterpriseIds } }, { _id: 1, EnterpriseID: 1 }).lean();

    // 按企业分组员工
    // 企业id: 员工id数组
    const enterpriseEmployeeMap = {};
    for (const emp of employees) {
      const eid = emp.EnterpriseID;
      if (!enterpriseEmployeeMap[eid]) enterpriseEmployeeMap[eid] = [];
      enterpriseEmployeeMap[eid].push(emp._id);
    }

    // 查询所有体检记录（加时间条件）
    const healthCheckQuery = {};
    if (Object.keys(timeCondition).length > 0) {
      // 体检表体检时间
      if (timeCondition.reportTime) {
        healthCheckQuery.registerTime = timeCondition.reportTime;
      }
    }
    healthCheckQuery.employeeID = { $in: employees.map(e => e._id) };
    // 查询时间范围内企业员工的体检记录
    const healthChecks = await HealthCheckRegister.find(healthCheckQuery, { employeeID: 1 }).lean();
    // 已体检员工id集合
    const checkedEmployeeSet = new Set(healthChecks.map(h => String(h.employeeID)));

    // 分类统计
    let fullyCheckedCount = 0;
    let partiallyCheckedCount = 0;
    let notCheckedCount = 0;

    for (const eid of enterpriseIds) {
      const empIds = enterpriseEmployeeMap[eid] || [];
      if (empIds.length === 0) {
        // 没有员工的企业不计入统计
        continue;
      }
      let checked = 0;
      for (const empId of empIds) {
        if (checkedEmployeeSet.has(String(empId))) checked++;
      }
      if (checked === empIds.length) {
        fullyCheckedCount++;
      } else if (checked === 0) {
        notCheckedCount++;
      } else {
        partiallyCheckedCount++;
      }
    }

    const totalEnterpriseCount = fullyCheckedCount + partiallyCheckedCount + notCheckedCount;
    const checkRate = totalEnterpriseCount > 0 ? ((fullyCheckedCount + partiallyCheckedCount) / totalEnterpriseCount * 100).toFixed(2) : '0';

    return {
      totalEnterpriseCount,
      fullyCheckedCount,
      partiallyCheckedCount,
      notCheckedCount,
      checkRate: `${checkRate}%`,
    };
  }

  /**
   * 查询不同危害因素种类的员工数量
   * @param {Object} params - 查询参数对象，包含以下可选字段：
   *   @param {Date|String} [params.startTime] - 开始时间
   *   @param {Date|String} [params.endTime] - 结束时间
   *   @param {Date|String} [params.timePoint] - 时间点
   *   @param {String} [params.regionCode] - 地区代码
   * @return {Object} 统计结果
   */
  // healthCheckRegister体检记录表有checkHazardFactors字段，是对象数组，每个对象有code字段，对应到occupationalexposurelimits危害因素表的code字段，occupationalexposurelimits表有catetory字段表示危害因素的种类，一个category有多个code。统计不同危害因素种类（category)的员工的数量
  async healthCheckFactorEmployees(params = {}) {
    const { ctx } = this;
    const Employee = ctx.model.Employee;
    const Adminorg = ctx.model.Adminorg;
    const HealthCheckRegister = ctx.model.HealthCheckRegister;
    const OccupationalExposureLimits = ctx.model.OccupationalexposureLimits;

    // 构建时间查询条件
    const timeCondition = this._buildTimeCondition(params);
    console.log('params', params);
    // 构建地区查询条件
    const regionQuery = await this._buildRegionQuery(params);

    // 合并查询条件
    // const combinedQuery = {
    //   ...timeCondition,
    //   ...regionQuery,
    // };

    // 如果有地区查询条件但没有匹配的企业，则返回空结果
    if (params.regionCode && regionQuery.EnterpriseID && regionQuery.EnterpriseID.$in.length === 0) {
      return [];
    }

    // 查询符合条件企业
    const adminorgQuery = {
      isDelete: { $ne: true },
      productionStatus: { $ne: '0' },
      isactive: { $ne: '2' },
    };
    if (regionQuery.EnterpriseID) {
      adminorgQuery._id = regionQuery.EnterpriseID;
    }
    const adminorgs = await Adminorg.find(adminorgQuery, { _id: 1 }).lean();
    // 企业id数组
    const enterpriseIds = adminorgs.map(org => org._id);

    // 查询企业员工
    const employees = await Employee.find({ EnterpriseID: { $in: enterpriseIds } }, { _id: 1, EnterpriseID: 1 }).lean();

    // 按企业分组员工
    // 企业id: 员工id数组
    const enterpriseEmployeeMap = {};
    for (const emp of employees) {
      const eid = emp.EnterpriseID;
      if (!enterpriseEmployeeMap[eid]) enterpriseEmployeeMap[eid] = [];
      enterpriseEmployeeMap[eid].push(emp._id);
    }

    // 查询所有体检记录（加时间条件）
    const healthCheckQuery = {};
    if (Object.keys(timeCondition).length > 0) {
      // 体检表体检时间
      if (timeCondition.reportTime) {
        healthCheckQuery.registerTime = timeCondition.reportTime;
      }
    }
    healthCheckQuery.employeeID = { $in: employees.map(e => e._id) };
    // 查询时间范围内企业员工的体检记录
    const healthChecks = await HealthCheckRegister.find(healthCheckQuery, { employeeID: 1, checkHazardFactors: 1 }).lean();

    // 1. 收集所有涉及的危害因素 code
    const allCodes = new Set();
    for (const record of healthChecks) {
      if (Array.isArray(record.checkHazardFactors)) {
        for (const factor of record.checkHazardFactors) {
          if (factor.code) allCodes.add(factor.code);
        }
      }
    }

    // 2. 查询 code 到 category 的映射
    const codeArr = Array.from(allCodes);
    const occList = await OccupationalExposureLimits.find({ code: { $in: codeArr } }, { code: 1, category: 1, catetory: 1 }).lean();
    const codeToCategory = {};
    for (const item of occList) {
      codeToCategory[item.code] = item.catetory;
    }

    // 3. 统计每个 category 下的员工数量（去重）
    // category: Set<employeeID>
    const categoryEmployeeMap = {};// 每个种类的员工id数组
    for (const record of healthChecks) {
      const empId = String(record.employeeID);
      if (Array.isArray(record.checkHazardFactors)) {
        // 危害因素
        const categories = new Set();
        for (const factor of record.checkHazardFactors) {
          const code = factor.code;
          const category = codeToCategory[code];
          if (category) categories.add(category);
        }
        for (const category of categories) {
          if (!categoryEmployeeMap[category]) categoryEmployeeMap[category] = new Set();
          categoryEmployeeMap[category].add(empId);
        }
      }
    }

    // 4. 输出结果
    const result = [];
    for (const category in categoryEmployeeMap) {
      result.push({
        category,
        employeeCount: categoryEmployeeMap[category].size,
      });
    }

    return result;
  }

  /**
   * 查询各市所有员工都体检的企业数量，按数量降序
   * @param {Object} params - 可选参数，支持地区、时间等筛选
   * @return {Array} [{ cityName, fullyCheckedCount }]
   */
  async getFullyCheckedEnterpriseCountByCity(params = {}) {//eslint-disable-line
    const { ctx } = this;
    const District = ctx.model.District;
    const Adminorg = ctx.model.Adminorg;
    const Employee = ctx.model.Employee;
    const HealthCheckRegister = ctx.model.HealthCheckRegister;

    // 查询所有市级行政区
    const cities = await District.find({ level: 1 }, { name: 1 }).lean();

    // 查询所有企业及其workAddress
    const adminorgs = await Adminorg.find({
      isDelete: { $ne: true },
      productionStatus: { $ne: '0' },
      isactive: { $ne: '2' },
    }, { _id: 1, workAddress: 1 }).lean();

    // 按市分组企业ID（workAddress.districts包含市名）
    const cityNames = cities.map(c => c.name);
    const cityEnterpriseMap = {};
    for (const city of cityNames) {
      const eids = adminorgs
        .filter(org =>
          Array.isArray(org.workAddress) &&
          org.workAddress.some(addr =>
            Array.isArray(addr.districts) && addr.districts.includes(city)
          )
        )
        .map(org => org._id);
      cityEnterpriseMap[city] = eids;
    }

    // 查询所有员工
    const allEnterpriseIds = [].concat(...Object.values(cityEnterpriseMap));
    const employees = await Employee.find({ EnterpriseID: { $in: allEnterpriseIds } }, { _id: 1, EnterpriseID: 1 }).lean();

    // 企业ID到员工ID数组
    const enterpriseEmployeeMap = {};
    for (const emp of employees) {
      const eid = emp.EnterpriseID;
      if (!enterpriseEmployeeMap[eid]) enterpriseEmployeeMap[eid] = [];
      enterpriseEmployeeMap[eid].push(emp._id);
    }

    // 查询所有体检记录
    const healthChecks = await HealthCheckRegister.find({ employeeID: { $in: employees.map(e => e._id) } }, { employeeID: 1 }).lean();
    const checkedEmployeeSet = new Set(healthChecks.map(h => String(h.employeeID)));

    // 统计每市所有员工都体检的企业数量
    const result = [];
    for (const city of cityNames) {
      const eids = cityEnterpriseMap[city] || [];
      let fullyCheckedCount = 0;
      for (const eid of eids) {
        const empIds = enterpriseEmployeeMap[eid] || [];
        if (empIds.length === 0) continue;
        let checked = 0;
        for (const empId of empIds) {
          if (checkedEmployeeSet.has(String(empId))) checked++;
        }
        if (checked === empIds.length) {
          fullyCheckedCount++;
        }
      }
      result.push({ cityName: city, fullyCheckedCount });
    }

    // 按数量降序排序
    result.sort((a, b) => b.fullyCheckedCount - a.fullyCheckedCount);

    return result;
  }
  /**
   * 职业健康检查异常人数分布情况
  */
  /**
   * 统计不同jobConclusion类型的人数（体检记录按时间筛选，员工企业需符合地区筛选）
   * @param {Object} params - 查询参数，支持时间和地区筛选
   * @return {Array} [{ type, count }]
   */
  async countJobConclusionTypes(params = {}) {
    const { ctx } = this;
    const Employee = ctx.model.Employee;
    const Adminorg = ctx.model.Adminorg;
    const HealthCheckRegister = ctx.model.HealthCheckRegister;

    // 1. 地区筛选企业
    const regionQuery = await this._buildRegionQuery(params);
    const adminorgQuery = {
      isDelete: { $ne: true },
      productionStatus: { $ne: '0' },
      isactive: { $ne: '2' },
    };
    if (regionQuery.EnterpriseID) {
      adminorgQuery._id = regionQuery.EnterpriseID;
    }
    const adminorgs = await Adminorg.find(adminorgQuery, { _id: 1 }).lean();
    const enterpriseIds = adminorgs.map(org => org._id);

    // 2. 查询企业下所有员工
    const employees = await Employee.find({ EnterpriseID: { $in: enterpriseIds } }, { _id: 1 }).lean();
    const employeeIds = employees.map(e => e._id);

    // 3. 时间筛选体检记录
    const timeCondition = this._buildTimeCondition(params);
    const healthCheckQuery = {};
    if (Object.keys(timeCondition).length > 0) {
      if (timeCondition.reportTime) {
        healthCheckQuery.registerTime = timeCondition.reportTime;
      }
    }
    healthCheckQuery.employeeID = { $in: employeeIds };

    // 4. 查询体检记录
    const healthChecks = await HealthCheckRegister.find(healthCheckQuery, { jobConclusion: 1 }).lean();

    // 5. 统计jobConclusion类型人数
    // 结论类型映射
    const typeMap = {
      // 1: '目前未见异常',
      2: '复查',
      3: '疑似职业病',
      4: '禁忌证',
      5: '其他疾病或异常',
    };
    // const typeCount = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    const typeCount = { 2: 0, 3: 0, 4: 0, 5: 0 };

    for (const record of healthChecks) {
      if (Array.isArray(record.jobConclusion)) {
        // 用Set去重，一个人多条结论只算一次
        const uniqueTypes = new Set(record.jobConclusion);
        for (const t of uniqueTypes) {
          if (typeCount[t] !== undefined) typeCount[t]++;
        }
      }
    }

    // 6. 输出结果
    return Object.keys(typeCount).map(type => ({
      type: typeMap[type],
      count: typeCount[type],
    }));
  }
  /**
   * 计算职业健康检查异常率
   * 异常率 = 异常人数 / 全部体检人数
   * 异常人数即jobConclusion不为['1']的人数
   * @param {Object} params - 查询参数，支持时间和地区筛选
   * @return {Object} { total: 体检人数, abnormal: 异常人数, abnormalRate: 'xx.xx%' }
   */
  async calcJobHealthAbnormalRate(params = {}) {
    const { ctx } = this;
    const Employee = ctx.model.Employee;
    const Adminorg = ctx.model.Adminorg;
    const HealthCheckRegister = ctx.model.HealthCheckRegister;

    // 地区筛选企业
    const regionQuery = await this._buildRegionQuery(params);
    const adminorgQuery = {
      isDelete: { $ne: true },
      productionStatus: { $ne: '0' },
      isactive: { $ne: '2' },
    };
    if (regionQuery.EnterpriseID) {
      adminorgQuery._id = regionQuery.EnterpriseID;
    }
    const adminorgs = await Adminorg.find(adminorgQuery, { _id: 1 }).lean();
    const enterpriseIds = adminorgs.map(org => org._id);

    // 查询企业下所有员工
    const employees = await Employee.find({ EnterpriseID: { $in: enterpriseIds } }, { _id: 1 }).lean();
    const employeeIds = employees.map(e => e._id);

    // 时间筛选体检记录
    const timeCondition = this._buildTimeCondition(params);
    const healthCheckQuery = {};
    if (Object.keys(timeCondition).length > 0) {
      if (timeCondition.reportTime) {
        healthCheckQuery.registerTime = timeCondition.reportTime;
      }
    }
    healthCheckQuery.employeeID = { $in: employeeIds };

    // 查询体检记录
    const healthChecks = await HealthCheckRegister.find(healthCheckQuery, { jobConclusion: 1 }).lean();

    // 统计
    let total = 0;
    let abnormal = 0;
    for (const record of healthChecks) {
      if (Array.isArray(record.jobConclusion)) {
        total++;
        // 异常定义：jobConclusion不是仅有'1'（即不是['1']），只要有不是'1'的就算异常
        const uniqueTypes = new Set(record.jobConclusion);
        if (!(uniqueTypes.size === 1 && (uniqueTypes.has('1') || uniqueTypes.has(1)))) {
          abnormal++;
        }
      }
    }
    const abnormalRate = total > 0 ? ((abnormal / total) * 100).toFixed(2) + '%' : '0%';

    return {
      total,
      abnormal,
      abnormalRate,
    };
  }


  // 统计各市有异常人员的企业的数量，降序排列
  async getAbnormalEnterpriseCountByCity(params = {}) {
    const { ctx } = this;
    const District = ctx.model.District;
    const Adminorg = ctx.model.Adminorg;
    const Employee = ctx.model.Employee;
    const HealthCheckRegister = ctx.model.HealthCheckRegister;

    // 查询所有市级行政区
    const cities = await District.find({ level: 1 }, { name: 1 }).lean();

    // 查询所有企业及其workAddress
    const adminorgs = await Adminorg.find({
      isDelete: { $ne: true },
      productionStatus: { $ne: '0' },
      isactive: { $ne: '2' },
    }, { _id: 1, workAddress: 1 }).lean();

    // 按市分组企业ID（workAddress.districts包含市名）
    const cityNames = cities.map(c => c.name);
    const cityEnterpriseMap = {};
    for (const city of cityNames) {
      const eids = adminorgs
        .filter(org =>
          Array.isArray(org.workAddress) &&
          org.workAddress.some(addr =>
            Array.isArray(addr.districts) && addr.districts.includes(city)
          )
        )
        .map(org => org._id);
      cityEnterpriseMap[city] = eids;
    }

    // 查询所有员工
    const allEnterpriseIds = [].concat(...Object.values(cityEnterpriseMap));
    const employees = await Employee.find({ EnterpriseID: { $in: allEnterpriseIds } }, { _id: 1, EnterpriseID: 1 }).lean();

    // 企业ID到员工ID数组
    const enterpriseEmployeeMap = {};
    for (const emp of employees) {
      const eid = emp.EnterpriseID;
      if (!enterpriseEmployeeMap[eid]) enterpriseEmployeeMap[eid] = [];
      enterpriseEmployeeMap[eid].push(emp._id);
    }

    // 时间筛选体检记录
    const timeCondition = this._buildTimeCondition(params);
    const healthCheckQuery = {};
    if (Object.keys(timeCondition).length > 0) {
      if (timeCondition.reportTime) {
        healthCheckQuery.registerTime = timeCondition.reportTime;
      }
    }
    healthCheckQuery.employeeID = { $in: employees.map(e => e._id) };

    // 查询所有体检记录
    const healthChecks = await HealthCheckRegister.find(healthCheckQuery, { employeeID: 1, jobConclusion: 1 }).lean();
    // 员工id到是否异常的映射
    const employeeAbnormalMap = {};
    for (const record of healthChecks) {
      if (Array.isArray(record.jobConclusion)) {
        // 异常定义：jobConclusion不是仅有'1'（即不是['1']），只要有不是'1'的就算异常
        const uniqueTypes = new Set(record.jobConclusion);
        const isAbnormal = !(uniqueTypes.size === 1 && (uniqueTypes.has('1') || uniqueTypes.has(1)));
        employeeAbnormalMap[String(record.employeeID)] = isAbnormal;
      }
    }
    // 统计每市有异常人员的企业数量
    const result = [];
    for (const city of cityNames) {
      const eids = cityEnterpriseMap[city] || [];
      let abnormalEnterpriseCount = 0;
      for (const eid of eids) {
        const empIds = enterpriseEmployeeMap[eid] || [];
        if (empIds.length === 0) continue;
        // 判断企业是否有异常员工
        const hasAbnormal = empIds.some(empId => employeeAbnormalMap[String(empId)]);
        if (hasAbnormal) {
          abnormalEnterpriseCount++;
        }
      }
      result.push({ cityName: city, abnormalEnterpriseCount });
    }
    // 按数量降序排序
    result.sort((a, b) => b.abnormalEnterpriseCount - a.abnormalEnterpriseCount);
    return result;
  }


}

module.exports = JobHealthStatisticsService;
