const Service = require('egg').Service;
const axios = require('axios');
/**
 * 职业健康检测统计服务
 */
class JobHealthStatisticsService extends Service {
  /**
 * 构建时间查询条件
 * @param {Object} params - 查询参数对象
 * @param {Date|String} [params.startTime] - 开始时间
 * @param {Date|String} [params.endTime] - 结束时间
 * @param {Date|String} [params.timePoint] - 时间点
 * @return {Object} 查询条件对象
 */
  _buildTimeCondition(params) {
    const timeCondition = {};
    const { startTime, endTime, timePoint } = params;
    if (timePoint) {
      // 如果提供时间点，按时间点查询
      const pointDate = new Date(timePoint);
      // 获取当天的开始和结束
      const startOfDay = new Date(pointDate.setHours(0, 0, 0, 0));
      const endOfDay = new Date(pointDate.setHours(23, 59, 59, 999));

      timeCondition.reportTime = {
        $gte: startOfDay,
        $lte: endOfDay,
      };
    } else if (startTime && endTime) {
      // 如果提供开始和结束时间，按时间段查询
      timeCondition.reportTime = {
        $gte: new Date(startTime),
        $lte: new Date(endTime),
      };
    }

    return timeCondition;
  }

  /**
   * 构建地区查询条件
   * @param {Object} params - 查询参数
   * @return {Object} 包含企业ID条件的查询对象
   */
  async _buildRegionQuery(params) {
    const { ctx } = this;
    const query = {};

    if (params.regionCode) {
      // 通过区域代码查询district表获取地区名称
      const district = await ctx.model.District.findOne(
        { area_code: params.regionCode },
        { name: 1 }
      ).lean();
      console.log('district', district);
      if (district && district.name) {
        const searchAddr = district.name;
        console.log('searchAddr', searchAddr);

        // 查询符合地区条件的企业ID
        const Adminorg = ctx.model.Adminorg;
        const enterpriseIdsWithRegion = await Adminorg.find(
          {
            isDelete: { $ne: true },
            'workAddress.districts': { $elemMatch: { $all: [ searchAddr ] } },
          },
          { _id: 1 }
        ).lean();

        const filteredEnterpriseIds = enterpriseIdsWithRegion.map(org => org._id);

        // 将筛选后的企业ID添加到查询条件
        if (filteredEnterpriseIds.length > 0) {
          query.EnterpriseID = { $in: filteredEnterpriseIds };
        } else {
          // 如果找到了地区但没有匹配的企业，返回一个必定为空的查询条件
          query.EnterpriseID = { $in: [] };
        }
      } else {
        // 如果没有找到地区，返回一个必定为空的查询条件
        query.EnterpriseID = { $in: [] };
      }
    }

    console.log('query', query);
    return query;
  }

  /**
   * 获取用人单位检测统计数据
   * 包括：全部检测、部分检测、未检测的用人单位数及检测率
   * @param {Object} params - 查询参数对象，包含以下可选字段：
   *   @param {Date|String} [params.startTime] - 开始时间
   *   @param {Date|String} [params.endTime] - 结束时间
   *   @param {Date|String} [params.timePoint] - 时间点
   *   @param {String} [params.regionCode] - 地区代码
   * @return {Object} 统计结果
   */
  async getEnterpriseStatsByTime(params = {}) {
    const { ctx } = this;
    const JcqlcProject = ctx.model.JcqlcProject;
    const CheckAssessment = ctx.model.CheckAssessment;
    const Adminorg = ctx.model.Adminorg;

    // 构建时间查询条件
    const timeCondition = this._buildTimeCondition(params);
    console.log('params', params);
    // 构建地区查询条件
    const regionQuery = await this._buildRegionQuery(params);

    // 合并查询条件
    const combinedQuery = {
      ...timeCondition,
      ...regionQuery,
    };

    // 如果有地区查询条件但没有匹配的企业，则返回空结果
    if (params.regionCode && regionQuery.EnterpriseID && regionQuery.EnterpriseID.$in.length === 0) {
      return {
        totalEnterpriseCount: 0,
        fullyCheckedCount: 0,
        partiallyCheckedCount: 0,
        notCheckedCount: 0,
        checkRate: '0%',
      };
    }
    console.log('combinedQuery', combinedQuery);
    // 查询所有符合条件的项目
    const projects = await JcqlcProject.find(combinedQuery).lean();

    // 获取所有检测结果
    const assessments = await CheckAssessment.find({
      jcqlcProjectId: { $in: projects.map(project => project._id) },
    }).lean();

    // 建立项目完成状态映射表
    const projectCompletionMap = {};
    assessments.forEach(assessment => {
      if (assessment.jcqlcProjectId) {
        projectCompletionMap[assessment.jcqlcProjectId] = true;
      }
    });

    // 按企业ID分组项目
    const enterpriseProjects = {};

    for (const project of projects) {
      const enterpriseId = project.EnterpriseID;
      if (!enterpriseId) continue;

      if (!enterpriseProjects[enterpriseId]) {
        enterpriseProjects[enterpriseId] = {
          total: 0,
          completed: 0,
        };
      }

      enterpriseProjects[enterpriseId].total++;

      // 检查项目是否完成
      if (projectCompletionMap[project._id]) {
        enterpriseProjects[enterpriseId].completed++;
      }
    }

    // 分类企业完成情况
    let fullyCheckedCount = 0;
    let partiallyCheckedCount = 0;

    Object.values(enterpriseProjects).forEach(stats => {
      if (stats.total > 0) {
        if (stats.completed === stats.total) {
          fullyCheckedCount++;
        } else if (stats.completed > 0) {
          partiallyCheckedCount++;
        }
      }
    });

    // 获取检测的企业总数
    const checkedCount = fullyCheckedCount + partiallyCheckedCount;

    // 获取adminorg总数（所有用人单位）
    const adminorgQuery = {
      isDelete: { $ne: true },
      productionStatus: { $ne: '0' },
      isactive: { $ne: '2' },
    };

    // 如果有地区筛选，添加到adminorg查询条件
    if (regionQuery.EnterpriseID) {
      adminorgQuery._id = regionQuery.EnterpriseID;
    }

    const totalEnterpriseCount = await Adminorg.countDocuments(adminorgQuery);

    // 计算未检测的企业数
    const notCheckedCount = totalEnterpriseCount - checkedCount;

    // 计算检测率
    const checkRate = totalEnterpriseCount > 0 ? (checkedCount / totalEnterpriseCount * 100).toFixed(2) : 0;

    return {
      totalEnterpriseCount, // 总企业数
      fullyCheckedCount, // 全部检测企业数
      partiallyCheckedCount, // 部分检测企业数
      notCheckedCount, // 未检测企业数
      checkRate: `${checkRate}%`, // 检测率
    };
  }
  /**
   * 获取用人单位检查统计数据
   * 包括：全部检查、部分检查、未检查的用人单位数及检查率
   * @param {Object} params - 查询参数对象，包含以下可选字段：
   *   @param {Date|String} [params.startTime] - 开始时间
   *   @param {Date|String} [params.endTime] - 结束时间
   *   @param {Date|String} [params.timePoint] - 时间点
   *   @param {String} [params.regionCode] - 地区代码
   * @return {Object} 统计结果
   */
  async getEnterpriseExamStatsByTime(params = {}) {
    const { ctx } = this;
    const Employee = ctx.model.Employee;
    const Adminorg = ctx.model.Adminorg;
    const HealthCheckRegister = ctx.model.HealthCheckRegister;

    // 构建时间查询条件
    const timeCondition = this._buildTimeCondition(params);
    console.log('params', params);
    // 构建地区查询条件
    const regionQuery = await this._buildRegionQuery(params);

    // 合并查询条件
    // const combinedQuery = {
    //   ...timeCondition,
    //   ...regionQuery,
    // };

    // 如果有地区查询条件但没有匹配的企业，则返回空结果
    if (params.regionCode && regionQuery.EnterpriseID && regionQuery.EnterpriseID.$in.length === 0) {
      return {
        totalEnterpriseCount: 0,
        fullyCheckedCount: 0,
        partiallyCheckedCount: 0,
        notCheckedCount: 0,
        checkRate: '0%',
      };
    }

    // 查询符合条件企业
    const adminorgQuery = {
      isDelete: { $ne: true },
      productionStatus: { $ne: '0' },
      isactive: { $ne: '2' },
    };
    if (regionQuery.EnterpriseID) {
      adminorgQuery._id = regionQuery.EnterpriseID;
    }
    const adminorgs = await Adminorg.find(adminorgQuery, { _id: 1 }).lean();
    // 企业id数组
    const enterpriseIds = adminorgs.map(org => org._id);

    // 查询企业员工
    const employees = await Employee.find({ EnterpriseID: { $in: enterpriseIds } }, { _id: 1, EnterpriseID: 1 }).lean();

    // 按企业分组员工
    // 企业id: 员工id数组
    const enterpriseEmployeeMap = {};
    for (const emp of employees) {
      const eid = emp.EnterpriseID;
      if (!enterpriseEmployeeMap[eid]) enterpriseEmployeeMap[eid] = [];
      enterpriseEmployeeMap[eid].push(emp._id);
    }

    // 查询所有体检记录（加时间条件）
    const healthCheckQuery = {};
    if (Object.keys(timeCondition).length > 0) {
      // 体检表体检时间
      if (timeCondition.reportTime) {
        healthCheckQuery.registerTime = timeCondition.reportTime;
      }
    }
    healthCheckQuery.employeeID = { $in: employees.map(e => e._id) };
    // 查询时间范围内企业员工的体检记录
    const healthChecks = await HealthCheckRegister.find(healthCheckQuery, { employeeID: 1 }).lean();
    // 已体检员工id集合
    const checkedEmployeeSet = new Set(healthChecks.map(h => String(h.employeeID)));

    // 分类统计
    let fullyCheckedCount = 0;
    let partiallyCheckedCount = 0;
    let notCheckedCount = 0;

    for (const eid of enterpriseIds) {
      const empIds = enterpriseEmployeeMap[eid] || [];
      if (empIds.length === 0) {
        // 没有员工的企业不计入统计
        continue;
      }
      let checked = 0;
      for (const empId of empIds) {
        if (checkedEmployeeSet.has(String(empId))) checked++;
      }
      if (checked === empIds.length) {
        fullyCheckedCount++;
      } else if (checked === 0) {
        notCheckedCount++;
      } else {
        partiallyCheckedCount++;
      }
    }

    const totalEnterpriseCount = fullyCheckedCount + partiallyCheckedCount + notCheckedCount;
    const checkRate = totalEnterpriseCount > 0 ? ((fullyCheckedCount + partiallyCheckedCount) / totalEnterpriseCount * 100).toFixed(2) : '0';

    return {
      totalEnterpriseCount,
      fullyCheckedCount,
      partiallyCheckedCount,
      notCheckedCount,
      checkRate: `${checkRate}%`,
    };
  }

  /**
   * 获取各职业病危害因素检测的用人单位数
   * @param {Date|String} startTime - 开始时间
   * @param {Date|String} endTime - 结束时间
   * @param {Date|String} timePoint - 时间点
   * @param {Object} params - 其他查询参数
   * @param {String} params.regionCode - 地区代码(可选)
   * @return {Array} 各危害因素统计结果
   */
  async getFactorEnterpriseStats(params = {}) {
    const { ctx } = this;
    const CheckAssessment = ctx.model.CheckAssessment;

    // 构建时间查询条件
    const timeCondition = this._buildTimeCondition(params);
    timeCondition.jcqlcProjectId = { $exists: true };

    // 构建地区查询条件
    const regionQuery = await this._buildRegionQuery(params);

    // 合并查询条件
    const combinedQuery = {
      ...timeCondition,
      ...regionQuery,
    };

    // 如果有地区查询条件但没有匹配的企业，则返回空结果
    if (params.regionCode && regionQuery.EnterpriseID && regionQuery.EnterpriseID.$in.length === 0) {
      return [];
    }

    // 定义所有危害因素类型
    const factorTypes = [
      {
        field: 'biologicalFactors',
        name: '生物',
      },
      {
        field: 'chemistryFactors',
        name: '化学物质',
      },
      {
        field: 'dustFactors',
        name: '粉尘',
      },
      {
        field: 'handBorneVibrationFactors',
        name: '手传振动',
      },
      {
        field: 'heatFactors',
        name: '高温',
      },
      {
        field: 'highFrequencyEleFactors',
        name: '高频电磁场',
      },
      {
        field: 'laserFactors',
        name: '激光辐射',
      },
      {
        field: 'microwaveFactors',
        name: '微波辐射',
      },
      {
        field: 'noiseFactors',
        name: '噪声',
      },
      {
        field: 'powerFrequencyElectric',
        name: '工频电场',
      },
      {
        field: 'SiO2Factors',
        name: '游离二氧化硅',
      },
      {
        field: 'ultraHighRadiationFactors',
        name: '超高频辐射',
      },
      {
        field: 'ultravioletFactors',
        name: '紫外辐射',
      },
      {
        field: 'ionizatioSourceFactors',
        name: '电离辐射-含源装置',
      },
      {
        field: 'ionizatioRadialFactors',
        name: '电离辐射-射线装置',
      },
    ];

    // 获取所有检测结果
    const assessments = await CheckAssessment.find(combinedQuery).lean();

    // 按因素类型统计企业数
    const factorStats = [];
    const factorEnterpriseMap = {};

    // 初始化每种因素的企业ID集合
    factorTypes.forEach(factorType => {
      factorEnterpriseMap[factorType.field] = new Set();
    });

    // 统计每种因素涉及的企业
    for (const assessment of assessments) {
      const enterpriseId = assessment.EnterpriseID;
      if (!enterpriseId) continue;

      // 遍历所有因素类型，查看当前检测结果中是否包含
      for (const factorType of factorTypes) {
        const fieldName = factorType.field;
        const factor = assessment[fieldName];

        // 如果存在此因素并且有formData数据，则添加企业ID到集合
        if (factor && factor.formData && factor.formData.length > 0) {
          factorEnterpriseMap[fieldName].add(enterpriseId);
        }
      }
    }

    // 转换结果为数组
    for (const factorType of factorTypes) {
      const fieldName = factorType.field;
      const displayName = factorType.name;
      const enterpriseCount = factorEnterpriseMap[fieldName].size;

      if (enterpriseCount > 0) {
        factorStats.push({
          factor: displayName,
          enterpriseCount,
        });
      }
    }

    // 按企业数量降序排序
    factorStats.sort((a, b) => b.enterpriseCount - a.enterpriseCount);

    return factorStats;
  }

  /**
   * 获取职业健康检测完成情况排名
   * @param {Date|String} startTime - 开始时间
   * @param {Date|String} endTime - 结束时间
   * @param {Date|String} timePoint - 时间点
   * @param {Number} limit - 返回结果限制数量
   * @param {Object} params - 其他查询参数
   * @param {String} params.regionCode - 地区代码(可选)
   * @return {Array} 企业排名列表
   */
  async getEnterpriseRanking(params = {}) {
    const { ctx } = this;
    const JcqlcProject = ctx.model.JcqlcProject;
    const CheckAssessment = ctx.model.CheckAssessment;

    // 构建时间查询条件
    const timeCondition = this._buildTimeCondition(params);

    // 构建地区查询条件
    const regionQuery = await this._buildRegionQuery(params);

    // 合并查询条件
    const combinedQuery = {
      ...timeCondition,
      ...regionQuery,
    };

    // 如果有地区查询条件但没有匹配的企业，则返回空结果
    if (params.regionCode && regionQuery.EnterpriseID && regionQuery.EnterpriseID.$in.length === 0) {
      return [];
    }

    // 查询所有符合条件的项目
    const projects = await JcqlcProject.find(combinedQuery).lean();

    // 获取所有检测结果
    const assessments = await CheckAssessment.find({
      jcqlcProjectId: { $in: projects.map(project => project._id) },
    }).lean();

    // 建立项目完成状态映射表
    const completedProjects = new Set();
    assessments.forEach(assessment => {
      if (assessment.jcqlcProjectId) {
        completedProjects.add(assessment.jcqlcProjectId);
      }
    });

    // 按企业分组，统计项目完成情况
    const enterpriseStats = {};

    for (const project of projects) {
      const enterpriseId = project.EnterpriseID;
      const companyName = project.EnterpriseName;

      if (!enterpriseId) continue;

      if (!enterpriseStats[enterpriseId]) {
        enterpriseStats[enterpriseId] = {
          enterpriseId,
          companyName,
          totalProjects: 0,
          completedProjects: 0,
        };
      }

      enterpriseStats[enterpriseId].totalProjects++;

      // 检查项目是否完成
      if (completedProjects.has(project._id)) {
        enterpriseStats[enterpriseId].completedProjects++;
      }
    }

    // 转换为数组并计算完成率
    const enterpriseRanking = Object.values(enterpriseStats).map(stats => {
      const completionRate = stats.totalProjects > 0
        ? (stats.completedProjects / stats.totalProjects * 100)
        : 0;

      return {
        ...stats,
        completionRate: `${completionRate.toFixed(2)}%`,
      };
    });

    // 按完成率和项目数量降序排序
    enterpriseRanking.sort((a, b) => {
      // 先按完成率降序
      const rateA = parseFloat(a.completionRate);
      const rateB = parseFloat(b.completionRate);

      if (rateB !== rateA) {
        return rateB - rateA;
      }

      // 完成率相同时，按项目总数降序
      return b.totalProjects - a.totalProjects;
    });

    // 限制返回数量
    return enterpriseRanking.slice(0, params.limit || 10);
  }

  /**
   * 获取检测超标统计数据
   * 包括：超标用人单位数、超标率
   * @param {Date|String} startTime - 开始时间
   * @param {Date|String} endTime - 结束时间
   * @param {Date|String} timePoint - 时间点
   * @param {Object} params - 其他查询参数
   * @param {String} params.regionCode - 地区代码(可选)
   * @return {Object} 超标统计结果
   */
  async getExceedStandardStats(params = {}) {
    const { ctx } = this;
    const CheckAssessment = ctx.model.CheckAssessment;

    // 构建时间查询条件
    const timeCondition = this._buildTimeCondition(params);

    // 构建地区查询条件
    const regionQuery = await this._buildRegionQuery(params);

    // 合并查询条件
    const combinedQuery = {
      ...timeCondition,
      ...regionQuery,
      jcqlcProjectId: { $exists: true },
    };

    // 如果有地区查询条件但没有匹配的企业，则返回空结果
    if (params.regionCode && regionQuery.EnterpriseID && regionQuery.EnterpriseID.$in.length === 0) {
      return {
        exceedCount: 0,
        exceedRate: '0%',
        exceedEnterprises: [],
        totalExceedPoints: 0,
      };
    }

    // 获取所有检测结果
    const assessments = await CheckAssessment.find(combinedQuery).lean();

    // 统计超标企业
    const enterpriseMap = new Map();

    // 遍历所有检测结果，标记超标情况和计数超标点
    for (const assessment of assessments) {
      if (!assessment.EnterpriseID) continue;

      const enterpriseId = assessment.EnterpriseID;

      // 检查该企业是否已在Map中
      if (!enterpriseMap.has(enterpriseId)) {
        // 初始化企业状态
        enterpriseMap.set(enterpriseId, {
          hasExceeded: false,
          companyName: assessment.EnterpriseName || '未知企业',
          exceedPointCount: 0, // 企业的超标点数
        });
      }

      // 获取企业的统计信息
      const enterpriseStats = enterpriseMap.get(enterpriseId);

      // 定义所有需要检查的危害因素类型
      const factorTypes = [
        'biologicalFactors', 'chemistryFactors', 'dustFactors',
        'handBorneVibrationFactors', 'heatFactors', 'highFrequencyEleFactors',
        'laserFactors', 'microwaveFactors', 'noiseFactors',
        'powerFrequencyElectric', 'SiO2Factors', 'ultraHighRadiationFactors',
        'ultravioletFactors', 'ionizatioSourceFactors', 'ionizatioRadialFactors',
      ];

      // 检查每种危害因素是否有超标结果
      for (const factorType of factorTypes) {
        const factor = assessment[factorType];
        if (!factor || !factor.formData || !Array.isArray(factor.formData)) continue;

        // 检查该因素的所有检测结果
        for (const item of factor.formData) {
          // 检查判定结果是否超标 - 精确检查"不符合"
          const checkResult = item.checkResult || '';
          if (checkResult === '不符合') {
            enterpriseStats.hasExceeded = true;
            enterpriseStats.exceedPointCount++; // 增加超标点数
          }

          // 如果有检测项结果数组，也检查其中的判定结果
          if (item.checkResults && Array.isArray(item.checkResults)) {
            for (const result of item.checkResults) {
              const resultItem = result.checkResultItem || '';
              if (resultItem === '不符合') {
                enterpriseStats.hasExceeded = true;
                enterpriseStats.exceedPointCount++; // 增加超标点数
              }
            }
          }
        }
      }
    }

    // 统计超标企业数
    let exceedCount = 0;
    const exceedEnterprises = [];
    let totalExceedPoints = 0;

    for (const [ id, status ] of enterpriseMap.entries()) {
      if (status.hasExceeded) {
        exceedCount++;
        totalExceedPoints += status.exceedPointCount;
        exceedEnterprises.push({
          enterpriseId: id,
          companyName: status.companyName,
          exceedPointCount: status.exceedPointCount,
        });
      }
    }

    // 计算超标率
    const totalEnterpriseCount = enterpriseMap.size;
    const exceedRate = totalEnterpriseCount > 0 ? (exceedCount / totalEnterpriseCount * 100).toFixed(2) : 0;

    return {
      exceedCount, // 超标企业数
      exceedRate: `${exceedRate}%`, // 超标率
      exceedEnterprises, // 超标企业列表
      totalExceedPoints, // 总超标点数
    };
  }

  /**
   * 获取危害因素检测超标排名
   * @param {Date|String} startTime - 开始时间
   * @param {Date|String} endTime - 结束时间
   * @param {Date|String} timePoint - 时间点
   * @param {Number} limit - 返回结果限制数量
   * @param {Object} params - 其他查询参数
   * @param {String} params.regionCode - 地区代码(可选)
   * @return {Array} 危害因素超标排名
   */
  async getFactorExceedRanking(params = {}) {
    const { ctx } = this;
    const CheckAssessment = ctx.model.CheckAssessment;

    // 构建时间查询条件
    const timeCondition = this._buildTimeCondition(params);

    // 构建地区查询条件
    const regionQuery = await this._buildRegionQuery(params);

    // 合并查询条件
    const combinedQuery = {
      ...timeCondition,
      ...regionQuery,
      jcqlcProjectId: { $exists: true },
    };

    // 如果有地区查询条件但没有匹配的企业，则返回空结果
    if (params.regionCode && regionQuery.EnterpriseID && regionQuery.EnterpriseID.$in.length === 0) {
      return [];
    }

    // 获取所有检测结果
    const assessments = await CheckAssessment.find(combinedQuery).lean();

    // 定义所有危害因素类型
    const factorTypes = [
      {
        field: 'biologicalFactors',
        name: '生物',
      },
      {
        field: 'chemistryFactors',
        name: '化学物质',
      },
      {
        field: 'dustFactors',
        name: '粉尘',
      },
      {
        field: 'handBorneVibrationFactors',
        name: '手传振动',
      },
      {
        field: 'heatFactors',
        name: '高温',
      },
      {
        field: 'highFrequencyEleFactors',
        name: '高频电磁场',
      },
      {
        field: 'laserFactors',
        name: '激光辐射',
      },
      {
        field: 'microwaveFactors',
        name: '微波辐射',
      },
      {
        field: 'noiseFactors',
        name: '噪声',
      },
      {
        field: 'powerFrequencyElectric',
        name: '工频电场',
      },
      {
        field: 'SiO2Factors',
        name: '游离二氧化硅',
      },
      {
        field: 'ultraHighRadiationFactors',
        name: '超高频辐射',
      },
      {
        field: 'ultravioletFactors',
        name: '紫外辐射',
      },
      {
        field: 'ionizatioSourceFactors',
        name: '电离辐射-含源装置',
      },
      {
        field: 'ionizatioRadialFactors',
        name: '电离辐射-射线装置',
      },
    ];

    // 统计各危害因素超标情况
    const factorStats = {};

    // 初始化每种因素的统计数据
    factorTypes.forEach(factorType => {
      factorStats[factorType.field] = {
        name: factorType.name,
        totalEnterprises: new Set(), // 检测该因素的企业集合
        exceedEnterprises: new Set(), // 该因素超标的企业集合
        exceedPointCount: 0, // 超标点数
      };
    });

    // 遍历所有检测结果，统计超标情况
    for (const assessment of assessments) {
      if (!assessment.EnterpriseID) continue;

      const enterpriseId = assessment.EnterpriseID;

      // 遍历所有因素类型
      for (const factorType of factorTypes) {
        const fieldName = factorType.field;
        const factor = assessment[fieldName];

        // 如果存在此因素并且有formData数据
        if (factor && factor.formData && Array.isArray(factor.formData) && factor.formData.length > 0) {
          // 添加企业到该因素的检测企业集合
          factorStats[fieldName].totalEnterprises.add(enterpriseId);

          // 检查该因素的所有检测结果是否有超标
          for (const item of factor.formData) {
            // 检查判定结果是否为"不符合"
            const checkResult = item.checkResult || '';
            if (checkResult === '不符合') {
              factorStats[fieldName].exceedEnterprises.add(enterpriseId);
              factorStats[fieldName].exceedPointCount++;
            }

            // 如果有检测项结果数组，也检查其中的判定结果
            if (item.checkResults && Array.isArray(item.checkResults)) {
              for (const result of item.checkResults) {
                const resultItem = result.checkResultItem || '';
                if (resultItem === '不符合') {
                  factorStats[fieldName].exceedEnterprises.add(enterpriseId);
                  factorStats[fieldName].exceedPointCount++;
                }
              }
            }
          }
        }
      }
    }

    // 转换为数组并计算比例
    const factorRanking = [];

    for (const factorType of factorTypes) {
      const fieldName = factorType.field;
      const stats = factorStats[fieldName];

      const totalCount = stats.totalEnterprises.size;
      const exceedCount = stats.exceedEnterprises.size;
      const exceedPointCount = stats.exceedPointCount;

      if (totalCount > 0) {

        factorRanking.push({
          factor: stats.name,
          exceedEnterpriseCount: exceedCount,
          exceedPointCount,
        });
      }
    }

    // 按超标点数降序排序
    factorRanking.sort((a, b) => b.exceedPointCount - a.exceedPointCount);

    // 限制返回数量
    return factorRanking.slice(0, params.limit || 10);
  }

  /**
   * 获取服务机构列表
   * @param {Object} params - 查询参数
   * @param {Number} [params.page=1] - 当前页码
   * @param {Number} [params.pageSize=10] - 每页数量
   * @param {String} [params.keyword] - 关键字搜索(机构名称或社会信用代码)
   * @param {Array} [params.status] - 状态筛选，例如 [3, 4] 表示只查询审核通过和不通过的
   * @param {Array} [params.regAddr] - 区域筛选，例如 ['浙江省', '杭州市']
   * @param {String} [params.sortField='ctime'] - 排序字段
   * @param {String} [params.sortOrder='desc'] - 排序方向，'desc'降序，'asc'升序
   * @return {Object} 分页结果对象，包含总数和机构列表
   */
  async getServiceOrgList(params = {}) {
    const { ctx } = this;
    // 使用ServiceOrg模型获取服务机构数据
    const ServiceOrg = ctx.model.ServiceOrg;

    // 构建查询条件
    const query = {};

    // 关键字搜索
    if (params.keyword) {
      const keyword = params.keyword.trim();
      query.$or = [
        { name: new RegExp(keyword, 'i') },
        { organization: new RegExp(keyword, 'i') },
      ];
    }

    // 状态筛选
    if (params.status && Array.isArray(params.status) && params.status.length > 0) {
      query.status = { $in: params.status };
    }


    // 分页参数
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const skip = (page - 1) * pageSize;

    // 排序
    const sortField = params.sortField || 'ctime';
    const sortOrder = params.sortOrder === 'asc' ? 1 : -1;
    const sort = { [sortField]: sortOrder };

    // 投影字段，只返回必要的信息
    const projection = {
      _id: 1,
      name: 1,
      organization: 1,
      regAddr: 1,
      address: 1,
      corp: 1,
      status: 1,
      ctime: 1,
    };

    // 查询总数
    const total = await ServiceOrg.countDocuments(query);

    // 查询数据
    const serviceOrgList = await ServiceOrg.find(query, projection)
      .sort(sort)
      .skip(skip)
      .limit(pageSize)
      .lean();
    // 获取经纬度
    for (const org of serviceOrgList) {
      if (!org.address) {
        continue;
      }
      const regAddr = org.regAddr.join('/') + org.address;
      const { longitude, latitude } = await this.getCoordinatesByAddress(regAddr);
      org.longitude = longitude;
      org.latitude = latitude;
    }
    return {
      total,
      list: serviceOrgList,
      page,
      pageSize,
    };
  }

  /**
   * 根据地址获取经纬度坐标
   * @param {String} address - a地址字符串
   * @return {Promise<Object>} 包含经纬度的对象 {longitude, latitude}
   */
  async getCoordinatesByAddress(address) {
    const { ctx } = this;

    try {
      // 构建天地图API参数

      const requestParams = { keyWord: address };
      const url = `${ctx.app.config.tiandimap.url}/geocoder?ds=${encodeURIComponent(JSON.stringify(requestParams))}&tk=${ctx.app.config.tiandimap.key}`;

      const response = await axios.get(url);
      const data = response.data;

      if (data.status === '0' && data.location) {
        console.log('data', data);
        return {
          longitude: parseFloat(data.location.lon),
          latitude: parseFloat(data.location.lat),
        };
      }
    } catch (error) {
      ctx.logger.error('地理编码查询异常:', error);

    }
  }

  /**
   * 获取管辖区内各类职业病危害因素检测分布情况
   * @param {Date|String} startTime - 开始时间
   * @param {Date|String} endTime - 结束时间
   * @param {Date|String} timePoint - 时间点
   * @param {Object} params - 其他查询参数
   * @param {String} params.regionCode - 地区代码(可选)
   * @return {Array} 包含企业位置和危害因素的分布数据
   */
  async getFactorDistributionMap(params = {}) {
    console.log('params', params);
    const { ctx } = this;
    const CheckAssessment = ctx.model.CheckAssessment;
    const Adminorg = ctx.model.Adminorg;
    const Employee = ctx.model.Employee;

    // 构建时间查询条件
    const timeCondition = this._buildTimeCondition(params);

    // 构建地区查询条件
    const regionQuery = await this._buildRegionQuery(params);

    // 合并查询条件
    const combinedQuery = {
      ...timeCondition,
      ...regionQuery,
      jcqlcProjectId: { $exists: true },
    };
    // 如果有地区查询条件但没有匹配的企业，则返回空结果
    if (params.regionCode && regionQuery.EnterpriseID && regionQuery.EnterpriseID.$in.length === 0) {
      return [];
    }

    // 获取所有检测结果
    const assessments = await CheckAssessment.find(combinedQuery).lean();

    // 定义所有危害因素类型
    let factorTypes = [
      {
        field: 'biologicalFactors',
        name: '生物',
      },
      {
        field: 'chemistryFactors',
        name: '化学物质',
      },
      {
        field: 'dustFactors',
        name: '粉尘',
      },
      {
        field: 'handBorneVibrationFactors',
        name: '手传振动',
      },
      {
        field: 'heatFactors',
        name: '高温',
      },
      {
        field: 'highFrequencyEleFactors',
        name: '高频电磁场',
      },
      {
        field: 'laserFactors',
        name: '激光辐射',
      },
      {
        field: 'microwaveFactors',
        name: '微波辐射',
      },
      {
        field: 'noiseFactors',
        name: '噪声',
      },
      {
        field: 'powerFrequencyElectric',
        name: '工频电场',
      },
      {
        field: 'SiO2Factors',
        name: '游离二氧化硅',
      },
      {
        field: 'ultraHighRadiationFactors',
        name: '超高频辐射',
      },
      {
        field: 'ultravioletFactors',
        name: '紫外辐射',
      },
      {
        field: 'ionizatioSourceFactors',
        name: '电离辐射-含源装置',
      },
      {
        field: 'ionizatioRadialFactors',
        name: '电离辐射-射线装置',
      },
    ];
    if (params.hazardFactors && params.hazardFactors.length > 0) {
      params.hazardFactors = params.hazardFactors.split(',').map(factor => factor.trim());
      factorTypes = factorTypes.filter(i => params.hazardFactors.includes(i.field));
    }
    // 收集所有有检测数据的企业ID
    const enterpriseIds = new Set();

    // 企业危害因素数据映射
    const enterpriseFactorsMap = new Map();

    // 遍历所有检测结果，收集企业ID和危害因素数据
    for (const assessment of assessments) {
      if (!assessment.EnterpriseID) continue;

      const enterpriseId = assessment.EnterpriseID;
      const enterpriseName = assessment.EnterpriseName || '未知企业';

      // 添加企业ID到集合
      enterpriseIds.add(enterpriseId);

      // 初始化企业危害因素数据
      if (!enterpriseFactorsMap.has(enterpriseId)) {
        enterpriseFactorsMap.set(enterpriseId, {
          enterpriseId,
          enterpriseName,
          factors: [],
        });
      }

      // 获取企业危害因素数据
      const enterpriseData = enterpriseFactorsMap.get(enterpriseId);

      // 遍历所有因素类型
      for (const factorType of factorTypes) {
        const fieldName = factorType.field;
        const factorName = factorType.name;
        const factor = assessment[fieldName];

        // 如果存在此因素并且有formData数据
        if (factor && factor.formData && Array.isArray(factor.formData) && factor.formData.length > 0) {
          // 检查该因素是否已添加到企业数据
          const existingFactor = enterpriseData.factors.find(f => f.name === factorName);

          if (!existingFactor) {
            // 创建新的因素数据
            const factorData = {
              name: factorName,
              pointName: '-',
              qualified: true,
            };

            // 检查所有检测点是否合格
            for (const item of factor.formData) {
              const pointName = item.checkAddress || '未命名点位';
              const checkResult = item.checkResult || '';
              const isQualified = checkResult !== '不符合';

              // 添加检测点数据
              factorData.pointName = pointName;
              factorData.qualified = isQualified;
            }

            // 添加因素数据到企业数据
            enterpriseData.factors.push(factorData);
          }
        }
      }
    }
    console.log('enterpriseFactorsMap', enterpriseFactorsMap);
    // 从adminorg表获取企业位置信息
    const enterpriseIdsArray = Array.from(enterpriseIds);
    const enterprises = await Adminorg.find(
      { _id: { $in: enterpriseIdsArray } },
      { _id: 1, cname: 1, workAddress: 1, industryCategory: 1, regAdd: 1 }
    ).lean();
    // 获取企业员工数
    const employeeCompanyMap = await Employee.aggregate([
      {
        $match: {
          EnterpriseID: { $in: enterpriseIdsArray },
        },
      },
      {
        $group: {
          _id: '$EnterpriseID', // 按 EnterpriseID 分组
          employeeCount: { $sum: 1 }, // 每条记录计数 +1
        },
      },
    ]);
    console.log('enterprises', JSON.stringify(enterprises, null, 2));

    // 将位置信息添加到企业数据
    const result = [];

    for (const enterprise of enterprises) {
      const enterpriseId = enterprise._id;
      const factorsData = enterpriseFactorsMap.get(enterpriseId.toString());

      if (factorsData) {
        // 获取企业位置
        let longitude = null;
        let latitude = null;

        // 处理workAddress是数组的情况
        if (enterprise.workAddress && Array.isArray(enterprise.workAddress) && enterprise.workAddress.length > 0) {
          const firstWorkAddress = enterprise.workAddress[0];

          if (firstWorkAddress && firstWorkAddress.point && Array.isArray(firstWorkAddress.point) && firstWorkAddress.point.length >= 2) {
            // 去除空格并转换为数字
            // fix 数字类型不能调trim方法
            const lng = Object.prototype.toString.call(firstWorkAddress.point[0]) === '[object String]' ? firstWorkAddress.point[0].trim() : firstWorkAddress.point[0];
            const lat = Object.prototype.toString.call(firstWorkAddress.point[1]) === '[object String]' ? firstWorkAddress.point[1].trim() : firstWorkAddress.point[1];

            // 检查是否为空字符串或能转换为有效数字
            if (lng && lat && !isNaN(parseFloat(lng)) && !isNaN(parseFloat(lat))) {
              longitude = parseFloat(lng);
              latitude = parseFloat(lat);
              console.log(`企业[${enterprise.cname}]坐标: ${longitude}, ${latitude}`);
            }
          }
        }

        // 行业
        const industryName = [];
        if (enterprise.industryCategory && enterprise.industryCategory.length > 0 && enterprise.industryCategory[0][0]) {
          for (const industry of enterprise.industryCategory) {
            const industryRes = await ctx.model.IndustryCategory.findOne({ value: industry[0] });
            industryName.push(industryRes.label);
          }
        }
        // 只有在有位置信息的情况下才添加到结果
        if (longitude !== null && latitude !== null) {
          result.push({
            enterpriseId,
            enterpriseName: enterprise.cname || factorsData.enterpriseName,
            longitude,
            latitude,
            factors: factorsData.factors,
            employeeCount: (employeeCompanyMap.find(i => i.EnterpriseID === enterpriseId) && employeeCompanyMap.find(i => i.EnterpriseID === enterpriseId).employeeCount) || 0,
            industryName,
            address: enterprise.regAdd,
          });
        } else {
          console.log(`企业[${enterprise.cname || factorsData.enterpriseName}]缺少有效的坐标信息`);
        }
      }
    }

    console.log(`找到${result.length}个有效的企业位置数据`);
    return result;
  }
}

module.exports = JobHealthStatisticsService;
