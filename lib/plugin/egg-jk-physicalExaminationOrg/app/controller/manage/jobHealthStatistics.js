/**
 * 职业健康检测统计控制器
 */
const JobHealthStatisticsController = {
  /**
   * 获取用人单位检测统计数据
   * 包括：全部检测、部分检测、未检测的用人单位数及检测率
   * @param {Object} ctx - 上下文对象
   */
  async getEnterpriseStatsByTime(ctx) {
    const params = ctx.query;
    const data = await ctx.service.jobHealthStatistics.getEnterpriseStatsByTime(params);
    ctx.helper.renderCustom(ctx, { data, message: '获取用人单位检测统计数据成功' });
  },

  /**
   * 获取各职业病危害因素检测的用人单位数
   * @param {Object} ctx - 上下文对象
   */
  async getFactorEnterpriseStats(ctx) {
    const params = ctx.query;
    const data = await ctx.service.jobHealthStatistics.getFactorEnterpriseStats(params);
    ctx.helper.renderCustom(ctx, { data, message: '获取职业病危害因素统计数据成功' });
  },

  /**
   * 获取职业健康检测完成情况排名
   * @param {Object} ctx - 上下文对象
   */
  async getEnterpriseRanking(ctx) {
    const params = ctx.query;
    const data = await ctx.service.jobHealthStatistics.getEnterpriseRanking(params);
    ctx.helper.renderCustom(ctx, { data, message: '获取企业检测排名数据成功' });
  },

  /**
   * 获取检测超标统计数据
   * 包括：超标用人单位数、超标率
   * @param {Object} ctx - 上下文对象
   */
  async getExceedStandardStats(ctx) {
    const params = ctx.query;
    const data = await ctx.service.jobHealthStatistics.getExceedStandardStats(params);
    ctx.helper.renderCustom(ctx, { data, message: '获取检测超标统计数据成功' });
  },

  /**
   * 获取危害因素检测超标排名
   * @param {Object} ctx - 上下文对象
   */
  async getFactorExceedRanking(ctx) {
    const params = ctx.query;
    const data = await ctx.service.jobHealthStatistics.getFactorExceedRanking(params);
    ctx.helper.renderCustom(ctx, { data, message: '获取危害因素超标排名数据成功' });
  },

  /**
   * 获取服务机构列表
   * @param {Object} ctx - 上下文对象
   */
  async getServiceOrgList(ctx) {
    const params = ctx.query;
    try {
      const data = await ctx.service.jobHealthStatistics.getServiceOrgList(params);
      ctx.helper.renderCustom(ctx, { data, message: '获取服务机构列表成功' });
    } catch (error) {
      console.log('error', error);
      ctx.helper.renderCustom(ctx, { data: null, message: '获取服务机构列表失败' });
    }
  },

  async getFactorDistributionMap(ctx) {
    const params = ctx.query;
    try {
      const data = await ctx.service.jobHealthStatistics.getFactorDistributionMap(params);
      ctx.helper.renderCustom(ctx, { data, message: '获取危害因素检测分布图数据成功' });
    } catch (error) {
      console.log('error', error);
      ctx.helper.renderCustom(ctx, { data: null, message: '获取危害因素检测分布图数据失败' });
    }
  },
};

module.exports = JobHealthStatisticsController;
