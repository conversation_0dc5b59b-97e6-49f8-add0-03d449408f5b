/**
 * 职业健康体检检查统计控制器
 */
const HealthCheckStatisticsController = {
  /**
   * 获取用人单位检查统计数据
   * 包括：全部检查、部分检查、未检查的用人单位数及检查率
   * @param {Object} ctx - 上下文对象
   */
  async getEnterpriseExamStatsByTime(ctx) {
    try {
      const params = ctx.query;
      const data = await ctx.service.healthCheckStatistics.getEnterpriseExamStatsByTime(params);
      ctx.helper.renderCustom(ctx, { data, message: '获取用人单位检查统计数据成功' });
    } catch (error) {
      ctx.logger.error('获取用人单位检查统计数据失败:', error);
      ctx.helper.renderCustom(ctx, { code: 500, message: '获取用人单位检查统计数据失败' });
    }
  },

  async healthCheckFactorEmployees(ctx) {
    try {
      const params = ctx.query;
      const data = await ctx.service.healthCheckStatistics.healthCheckFactorEmployees(params);
      ctx.helper.renderCustom(ctx, { data, message: '查询不同危害因素种类的员工数量成功' });
    } catch (error) {
      ctx.logger.error('查询不同危害因素种类的员工数量失败:', error);
      ctx.helper.renderCustom(ctx, { code: 500, message: '查询不同危害因素种类的员工数量失败' });
    }
  },

  async getFullyCheckedEnterpriseCountByCity(ctx) {
    try {
      const params = ctx.query;
      const data = await ctx.service.healthCheckStatistics.getFullyCheckedEnterpriseCountByCity(params);
      ctx.helper.renderCustom(ctx, { data, message: '各区域职业健康检查完成情况排名' });
    } catch (error) {
      ctx.logger.error('获取各区域职业健康检查完成情况排名失败:', error);
      ctx.helper.renderCustom(ctx, { code: 500, message: '获取各区域职业健康检查完成情况排名失败' });
    }
  },

  async countJobConclusionTypes(ctx) {
    try {
      const params = ctx.query;
      const data = await ctx.service.healthCheckStatistics.countJobConclusionTypes(params);
      ctx.helper.renderCustom(ctx, { data, message: '统计各类体检结论类型人数成功' });
    } catch (error) {
      ctx.logger.error('统计各类体检结论类型人数失败:', error);
      ctx.helper.renderCustom(ctx, { code: 500, message: '统计各类体检结论类型人数失败' });
    }
  },

  async calcJobHealthAbnormalRate(ctx) {
    try {
      const params = ctx.query;
      const data = await ctx.service.healthCheckStatistics.calcJobHealthAbnormalRate(params);
      ctx.helper.renderCustom(ctx, { data, message: '计算健康体检异常率成功' });
    } catch (error) {
      ctx.logger.error('计算健康体检异常率失败:', error);
      ctx.helper.renderCustom(ctx, { code: 500, message: '计算健康体检异常率失败' });
    }
  },
  async getAbnormalEnterpriseCountByCity(ctx) {
    try {
      const params = ctx.query;
      const data = await ctx.service.healthCheckStatistics.getAbnormalEnterpriseCountByCity(params);
      ctx.helper.renderCustom(ctx, { data, message: '各区域职业健康体检异常用人单位数排名' });
    } catch (error) {
      ctx.logger.error('获取各区域职业健康体检异常用人单位数排名失败:', error);
      ctx.helper.renderCustom(ctx, { code: 500, message: '获取各区域职业健康体检异常用人单位数排名失败' });
    }
  },

};

module.exports = HealthCheckStatisticsController;
