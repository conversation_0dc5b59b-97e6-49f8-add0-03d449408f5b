exports.jk_testPaper = {
  alias: 'testPaper', // 插件目录，必须为英文
  pkgName: 'egg-jk-testPaper', // 插件包名
  enName: 'jk_testPaper', // 插件名
  name: '试卷管理', // 插件名称
  description: '试卷管理', // 插件描述
  adminApi: [
    {
      url: 'testPaper/getTopicsRandom',
      method: 'post',
      controllerName: 'getTopicsRandom',
      details: '随机获取考题',
    },
    {
      url: 'testPaper/getDetail',
      method: 'get',
      controllerName: 'getDetail',
      details: '获取试卷详情',
    },
    {
      url: 'testPaper/getList',
      method: 'post',
      controllerName: 'getList',
      details: '获取试卷列表',
    },
    {
      url: 'testPaper/add',
      method: 'post',
      controllerName: 'add',
      details: '添加试卷',
    },
    {
      url: 'testPaper/del',
      method: 'post',
      controllerName: 'del',
      details: '删除试卷',
    },
    {
      url: 'testPaper/edit',
      method: 'post',
      controllerName: 'edit',
      details: '编辑试卷',
    },
    {
      url: 'testPaper/answer',
      method: 'get',
      controllerName: 'getMyAnswerList',
      details: '获取我填写的问卷调查列表',
    },
    {
      url: 'testPaper/answer',
      method: 'post',
      controllerName: 'addMyAnswer',
      details: '添加我的问卷调查列表',
    },
    {
      url: 'testPaper/answer',
      method: 'put',
      controllerName: 'editMyAnswer',
      details: '编辑我的问卷调查',
    },
    {
      url: 'testPaper/answer/detail',
      method: 'get',
      controllerName: 'getMyAnswerDetail',
      details: '获取我的问卷调查详情',
    },
    {
      url: 'testPaper/answer',
      method: 'delete',
      controllerName: 'deleteMyAnswer',
      details: '删除我的问卷调查',
    },
  ],
  fontApi: [],
  initData: '', // 初始化数据脚本
  pluginsConfig: ` 
    exports.jk_testPaper = {\n
        enable: true,\n        package: 'egg-jk-testPaper',
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
  testPaperRouter:{\n
        match: [ctx => ctx.path.startsWith('/manage/testPaper')],\n
    },\n
    `, // 插入到 config.default.js 中的配置

  // 列表查询每页显示的数量，默认为10
  // pageSize: 10,
  multipart: {
    fileSize: '50mb',
    fields: '100',
    // mode: 'stream',
    fileExtensions: [ 'docx', 'doc', 'xlsx', 'mp4', 'pdf', 'avi', 'rar' ],
  },
  // 图片上传路径
  propagateUploadPath: './app/public/upload/propagate',
  security: {
    csrf: {
      ignoreJSON: true,
    },
    // 白名单
    // domainWhiteList: ['*'],
  },
};

