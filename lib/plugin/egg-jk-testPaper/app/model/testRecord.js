module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 培训的考试记录
  const testRecordSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    personalTrainingId: { // 个人培训id/问卷id
      type: String,
      ref: 'PersonalTraining',
    },
    courseId: { // 课程id, 没有的话就表示是大考
      type: String,
      ref: 'PersonalTraining',
    },
    EnterpriseID: { // 企业id
      type: String,
      ref: 'Adminorg',
    },
    adminUserId: { // 用户id
      type: String,
      ref: 'AdminUser',
    },
    userId: { // 用户id
      type: String,
      ref: 'User',
    },
    testResult: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      topicType: Number, // 题目类型
      answer: Array, // 参考答案
      myAnswer: Array, // 我的答案
      score: Number, // 分数
      topicId: String, // 题目id
      correctAnswer: Boolean, // 是否正确
    }],
    resultStatistics: { // 考试结果统计
      scoreLine: Number, // 分数线
      totleScore: Number, // 总分
      submitTime: Date, // 提交时间
      spendTime: Number, // 耗时
      topicScores: Array, // 各题型分数
      topicNum: Array, // 各题型数量
      actualScore: Number, // 实际分数
    },
  }, {
    timestamps: true,
  });

  return mongoose.model('TestRecord', testRecordSchema, 'testRecord');
};
