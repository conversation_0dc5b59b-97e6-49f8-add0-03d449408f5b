module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 试卷
  const TestPaperSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    method: { // 出题方式
      type: Number,
      default: 3,
      enum: [ 1, 2, 3 ], // 1是手动录入 2从题库选择 3随机组卷
    },
    examSyllabusId: { // 考试大纲
      type: String,
      ref: 'ExamSyllabus',
    },
    category: { // 试卷分类
      type: Number,
      enum: [ 1, 2 ], // 1培训 2问卷调查
      default: 1,
    },
    startTime: { // 开始时间
      type: Date,
    },
    endTime: { // 截止时间
      type: Date,
    },
    EnterpriseID: { // 企业id，这个在运营端是不填的
      type: String,
      ref: 'Adminorg',
    },
    combinationRule: [ // 随机组卷的组合规则
      {
        questionBankId: String,
        topicType: String,
        num: String,
      },
    ],
    name: { // 试卷名称
      type: String,
      require: true,
    },
    typeSummary: [ // 题型数量及分数统计  添加会有5中题型
      { topicType: Number, num: Number, score: Number }, // score指分数 topicType指题型
    ],
    totleAcount: Number, // 题目总数
    totalScore: Number, // 总分
    passingScore: Number, // 及格分
    questions: [{ // 试卷题目
      type: String,
      ref: 'Topic',
    }],
    createPresonId: { // 创建人ID
      type: String,
    },
    source: { // 试卷来源
      type: String,
      default: 'super', // super: 监管端
    },
    status: { // 假删用的
      type: Boolean,
      default: true,
    },
    description: {
      type: String,
      default: '',
    },
  }, { timestamps: true });


  return mongoose.model('TestPaper', TestPaperSchema, 'testPaper');
};
