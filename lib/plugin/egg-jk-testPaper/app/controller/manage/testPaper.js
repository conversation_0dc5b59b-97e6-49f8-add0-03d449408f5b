// 考卷
const testPaperController = {
  async getTopicsRandom(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.testPaper.getTopicsRandom(data);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '试题获取成功',
      status: 200,
    });
  },
  async add(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.testPaper.create(data);
    if (res) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '试卷添加成功',
        status: 200,
      });
    } else {
      ctx.helper.renderFail(ctx, {
        data: res,
        message: '试卷名称已存在',
        status: 400,
      });
    }

  },
  async edit(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.testPaper.update(data);
    if (res && (res.ok === 1)) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '试卷编辑成功',
        status: 200,
      });
    } else {
      ctx.helper.renderFail(ctx, {
        data: res,
        message: '试卷编辑失败',
        status: 400,
      });
    }
  },
  async getDetail(ctx) {
    const res = await ctx.service.testPaper.getById(ctx.query._id);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: 'success',
      status: 200,
    });
  },
  async del(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.testPaper.del(data._id);
    res && ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '删除成功',
      status: 200,
    });
  },
  // 获取试卷列表
  async getList(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.testPaper.getList(data);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: 'success',
      status: 200,
    });
  },

  // 获取我的问卷调查列表
  async getMyAnswerList(ctx) {
    const data = ctx.query;
    const res = await ctx.service.testPaper.getMyAnswerList(data);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: 'success',
    });
  },

  // 添加我的问卷调查信息
  async addMyAnswer(ctx) {
    try {
      const data = ctx.request.body;
      const res = await ctx.service.testPaper.addMyAnswer(data);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: 'success',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '试卷添加失败',
      });
    }
  },

  async getMyAnswerDetail(ctx) {
    try {
      const data = ctx.query;
      const res = await ctx.service.testPaper.getMyAnswerDetail(data);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: 'success',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '试卷获取失败',
      });
    }
  },

  async editMyAnswer(ctx) {
    try {
      const data = ctx.request.body;
      const res = await ctx.service.testPaper.editMyAnswer(data);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: 'success',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '试卷编辑失败',
      });
    }
  },
  async deleteMyAnswer(ctx) {
    try {
      const { ids = [] } = ctx.request.body;
      const res = await ctx.service.testPaper.deleteMyAnswer(ids);
      res && ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '删除成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '删除失败',
      });
    }
  },

};

module.exports = testPaperController;
