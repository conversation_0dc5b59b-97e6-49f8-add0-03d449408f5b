const Service = require('egg').Service;
const moment = require('moment');
class TestPaperService extends Service {
  // 随机获取考题
  async getTopicsRandom(query) {
    return await this.ctx.model.Topic.find(query);
  }
  // 获取试卷/问卷调查表列表
  async getList(data) {
    const adminUserInfo = this.ctx.session.superUserInfo || {};
    const query = {
      createPresonId: adminUserInfo._id,
      status: true,
    };
    if (data.keyWord) {
      query.name = { $regex: data.keyWord };
    }
    if (data.category) {
      query.category = +data.category;
      if (+data.category === 2) {
        delete query.status;
      }
    } else {
      query.$or = [{ category: 1 }, { category: { $exists: false } }];
    }
    const res = await this.ctx.model.TestPaper.find(query)
      .sort({ createdAt: -1 })
      .skip((data.pageCurrent - 1) * data.size)
      .limit(data.size);
    // .populate('createPresonId', 'name');

    const pageInfo = await this.getPageInfo('TestPaper', data.size, data.pageCurrent, query);

    if (+data.category !== 2) {
      return { res, pageInfo };
    }
    // 获取每套试卷的答题记录数量
    const testPaperIds = res.map(item => item._id);
    const testRecordCount = await this.ctx.model.TestRecord.aggregate([
      {
        $match: {
          personalTrainingId: { $in: testPaperIds },
        },
      },
      {
        $group: {
          _id: '$personalTrainingId',
          count: { $sum: 1 },
        },
      },
    ]);
    const testRecordCountMap = {};
    testRecordCount.forEach(item => {
      testRecordCountMap[item._id] = item.count;
    });
    const res2 = res.map(item => {
      const temp = JSON.parse(JSON.stringify(item));
      if (testRecordCountMap[item._id]) {
        temp.answerCount = testRecordCountMap[item._id];
      } else {
        temp.answerCount = 0;
      }
      return temp;
    });
    return { res: res2, pageInfo };
  }
  async getPageInfo(collection, size, pageCurrent, query = {}) {
    const total = await this.ctx.model[collection].find(query).count();
    const pageInfo = {
      total,
      size,
      pageCurrent,
    };
    return pageInfo;
  }
  // data 存在_id 就更新，不存在就 新建
  async create(data) {
    const { ctx } = this;
    const doc = await ctx.model.TestPaper.findOne({ name: data.name });
    if (!doc) {
      const res = await new ctx.model.TestPaper({
        createPresonId: ctx.session.superUserInfo._id || '',
        ...data,
      }).save();
      return res;
    }
  }
  async update(data) {
    // return await this.ctx.model.TestPaper.update(
    //   { _id: data._id },
    //   data,
    //   { new: true }
    // );
    return await this.ctx.service.db.updateOne('TestPaper', { _id: data._id }, data, { new: true });
  }
  async del(_id) {
    const { ctx } = this;
    // return await ctx.model.TestPaper.findByIdAndRemove(_id);
    return await ctx.service.db.remove('TestPaper', { _id });
  }
  async getById(_id) {
    return await this.ctx.model.TestPaper.findOne({ _id }).populate('questions');
  }

  // 根据考试大纲ID更新试卷信息
  async updateByExamSyllabus(examSyllabusId, operate) {
    const { ctx } = this;
    try {
      if (['add', 'del', 'update'].indexOf(operate) === -1) {
        throw new Error('operate参数错误');
      }
      if (!examSyllabusId) {
        throw new Error('examSyllabusId不能为空');
      }
      const examSyllabus = await ctx.model.ExamSyllabus.findOne({ _id: examSyllabusId });
      if (operate === 'del' && !examSyllabus) {
        ctx.auditLog('删除试卷', examSyllabusId, 'info');
        return await ctx.model.TestPaper.remove({ examSyllabusId });
      }
      if (!examSyllabus) {
        throw new Error('examSyllabusId不存在');
      }
      if (operate === 'update') {
        ctx.auditLog('更新试卷 - 假删原试卷', examSyllabusId, 'info');
        await ctx.model.TestPaper.updateMany({ examSyllabusId }, { status: false });
      }
      for (const no of ['A', 'B']) {
        // 生成2套试卷
        await this.createByExamSyllabus(examSyllabus, no);
      }
    } catch (e) {
      ctx.auditLog('更新试卷失败', e.message, 'error');
      return e.message;
    }
  }
  // 根据考试大纲生成试卷
  async createByExamSyllabus(examSyllabus, testPaperNo = '') {
    const { ctx } = this;
    const examSyllabusId = examSyllabus._id;
    const { outlineList, passingPercentage } = examSyllabus;
    const questions = []; // 试卷题目
    const typeSummary = [
      // 题型数量及分数
      { topicType: 1, num: 0, score: 0 },
      { topicType: 2, num: 0, score: 0 },
      { topicType: 3, num: 0, score: 0 },
    ];
    for (const item of outlineList) {
      // 大纲内容列表
      const topicList = await ctx.model.Topic.find(
        { outline: item.outline },
        { _id: 1, topicType: 1 }
      );
      for (const rule of item.rules) {
        if (!rule.quantity) continue;
        let topic = topicList.filter(topic => topic.topicType === rule.topicType);
        if (topic.length < rule.quantity) {
          const num = rule.quantity - topic.length;
          const newTopic = await ctx.model.Topic.find(
            { outline: { $ne: item.outline }, topicType: rule.topicType },
            { _id: 1 }
          ).limit(num);
          topic.push(...newTopic);
          ctx.auditLog('题目数量不足' + rule.quantity, item.outline, 'error');
        }
        if (topic.length > rule.quantity) {
          topic = this.getRandomElements(topic, rule.quantity); // 随机取题
        } else {
          topic = this.getRandomElements(topic, topic.length); // 乱序
        }
        typeSummary[rule.topicType - 1].num += topic.length;
        typeSummary[rule.topicType - 1].score = rule.topicScore;
        questions.push(...topic.map(t => t._id));
      }
    }
    if (questions.length === 0) {
      throw new Error('生成试卷失败, 题目数量为0');
    }
    // 计算实际总分
    let totleAcount = 0,
      totalScore = 0;
    typeSummary.forEach(item => {
      totleAcount += item.num;
      totalScore += item.num * item.score;
    });
    if (totalScore === 0) {
      throw new Error('生成试卷失败, 总分为0');
    }
    const passingScore = parseInt((totalScore * passingPercentage) / 100); // 及格分
    const name = examSyllabus.name + ' 卷' + testPaperNo;
    const res = await new ctx.model.TestPaper({
      examSyllabusId,
      name,
      typeSummary,
      totleAcount,
      totalScore,
      passingScore,
      questions,
      createPresonId: ctx.session.superUserInfo._id || '',
    }).save();
    ctx.auditLog('生成新的试卷成功', 'testPaper_id: ' + res._id, 'info');
  }
  // 从数组中随机取出count个元素
  getRandomElements(arr, count) {
    const shuffled = arr.slice(0);
    let i = arr.length;
    const min = i - count;
    let temp, index;
    while (i-- > min) {
      index = Math.floor((i + 1) * Math.random());
      temp = shuffled[i];
      shuffled[i] = shuffled[index];
      shuffled[index] = temp;
    }
    return shuffled.slice(min);
  }

  // 获取问卷调查记录
  async getMyAnswerList(data) {
    const { ctx } = this;
    // 获取调查表记录
    const adminUserInfo = this.ctx.session.superUserInfo || {};
    const testPaperList = await ctx.model.TestPaper.find(
      { status: true, category: 2, createPresonId: adminUserInfo._id },
      'name'
    );
    const query = {
      // adminUserId: { $exists: true },
      // EnterpriseID: { $exists: true },
      personalTrainingId: { $exists: true, $in: testPaperList.map(item => item._id) },
    };
    const { keyWord, testPaperIds } = data;
    if (testPaperIds && typeof testPaperIds === 'string') {
      query.personalTrainingId = { $in: testPaperIds.split(',') };
    }

    const curPage = parseInt(data.curPage, 10) || 1;
    const pageSize = parseInt(data.pageSize, 10) || 10;

    const query2 = {};
    if (keyWord) {
      query2.$or = [
        { 'adminUser.name': { $regex: keyWord } },
        { 'adminUser.userName': { $regex: keyWord } },
        { 'adminOrg.name': { $regex: keyWord } },
        { 'user.name': { $regex: keyWord } },
        { 'testPaper.name': { $regex: keyWord } },
      ];
    }

    const res = await ctx.model.TestRecord.aggregate([
      {
        $match: query,
      },
      {
        $lookup: {
          from: 'testPaper',
          localField: 'personalTrainingId',
          foreignField: '_id',
          as: 'testPaper',
        },
      },
      {
        $lookup: {
          from: 'adminusers',
          localField: 'adminUserId',
          foreignField: '_id',
          as: 'adminUser',
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user',
        },
      },
      {
        $lookup: {
          from: 'adminorgs',
          localField: 'EnterpriseID',
          foreignField: '_id',
          as: 'adminOrg',
        },
      },
      {
        $match: query2,
      },
      {
        $sort: { createdAt: -1 },
      },
      {
        $skip: (curPage - 1) * pageSize,
      },
      {
        $limit: pageSize,
      },

      {
        $project: {
          createdAt: 1,
          updatedAt: 1,
          testPaper: { $arrayElemAt: ['$testPaper', 0] },
          adminUser: { $arrayElemAt: ['$adminUser', 0] },
          adminOrg: { $arrayElemAt: ['$adminOrg', 0] },
          user: { $arrayElemAt: ['$user', 0] },
          testResult: 1,
          resultStatistics: 1,
        },
      },
    ]);
    const list = res.map(item => {
      const temp = JSON.parse(JSON.stringify(item));
      temp.createdAt = moment(item.createdAt).format('YYYY-MM-DD HH:mm');
      temp.updatedAt = moment(item.updatedAt).format('YYYY-MM-DD HH:mm');
      const testResult = item.testResult.filter(ele => ele.myAnswer && ele.myAnswer.length);
      const totleAcount = item.testPaper.questions.length;
      temp.answerProgress =
        testResult.length && totleAcount ? parseInt((testResult.length / totleAcount) * 100) : 0;
      if (temp.answerProgress > 100) temp.answerProgress = 100;
      return temp;
    });
    const total = await ctx.model.TestRecord.countDocuments(query);
    // 查询answerProgress =100的数量
    const answerProgressCount = await ctx.model.TestRecord.countDocuments({
      ...query,
      testResult: {
        $not: {
          $elemMatch: {
            myAnswer: [],
          },
        },
      },
    });
    return {
      list,
      testPaperList,
      total,
      answerProgressCount,
    };
  }

  async addMyAnswer(data) {
    const { ctx } = this;
    const addRes = await ctx.service.db.create('TestRecord', {
      adminUserId: ctx.session.adminUserInfo._id,
      EnterpriseID: ctx.session.adminUserInfo.EnterpriseID,
      personalTrainingId: data.personalTrainingId,
      testResult: data.testResult,
      resultStatistics: data.resultStatistics,
    });
    return addRes;
  }

  async getMyAnswerDetail(query) {
    const { ctx } = this;
    let testRecord = await ctx.model.TestRecord.findOne({ _id: query._id }).populate(
      'adminUserId',
      'name userName'
    );
    if (!testRecord) throw new Error('没有找到该记录, _id = ' + query._id);
    testRecord = JSON.parse(JSON.stringify(testRecord));
    testRecord.createdAt = moment(testRecord.createdAt).format('YYYY-MM-DD HH:mm');
    testRecord.updatedAt = moment(testRecord.updatedAt).format('YYYY-MM-DD HH:mm');
    const testPaper = await ctx.model.TestPaper.findOne({
      _id: testRecord.personalTrainingId,
    }).populate('questions');
    if (!testPaper) throw new Error('没有找到该试卷, _id = ' + testRecord.personalTrainingId);
    return {
      testRecord,
      testPaper,
    };
  }

  async editMyAnswer(data) {
    const { ctx } = this;
    if (!data._id) throw new Error('缺少参数_id');
    const res = await ctx.service.db.updateOne('TestRecord', { _id: data._id }, data);
    return res;
  }

  async deleteMyAnswer(ids = []) {
    const { ctx } = this;
    const res = await ctx.service.db.remove('TestRecord', { _id: { $in: ids } });
    return res;
  }
}

module.exports = TestPaperService;
