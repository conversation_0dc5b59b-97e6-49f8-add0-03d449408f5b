const Service = require('egg').Service;

class CoursesService extends Service {
  async getType() {
    const referer = this.ctx.header['x-custom-referer'];
    return referer && (referer.includes('fhCourses') || referer.includes('fhTraining')) ? '1' : '0';
  }
  async getCoursesList(query, current, pageSize = 10, from = 'trainingPlan') {
    const { regAdd } = this.ctx.session.superUserInfo;
    const queryArrForAdd = [];
    for (let i = 0; i < regAdd.length - 1; i++) {
      queryArrForAdd.push(regAdd.slice(0, i + 1));
    }
    if (from === 'manage') {
      query.authorID = this.ctx.session.superUserInfo;
    } else {
      // 培训计划选择的时候用
      query.$or = [
        { powerStatus: true, superUsers: this.ctx.session.superUserInfo._id, source: 'operate' }, // 运营端权限有设置的
        { powerStatus: { $ne: true }, source: 'operate' }, // 运营端权限没有设置的
        { createRange: { $in: queryArrForAdd }, source: 'super' }, // 上级区域
        { authorID: this.ctx.session.superUserInfo._id, source: 'super' }, // 自己本身
      ];
      query.allowToOpen = true;
      // {createRange:{$in:[['浙江省'],['浙江省','杭州市'],['浙江省','杭州市','西湖区']]}}
    }
    const type = await this.getType();
    query.type = type === '1' ? '1' : { $ne: '1' };
    const list = await this.ctx.model.Courses.find(query)
      .populate([
        {
          path: 'authorID',
          select: 'name _id logo',
        },
        {
          path: 'classification',
          select: 'name _id explain',
        },
      ])
      .skip((Number(current) - 1) * Number(pageSize))
      .limit(Number(pageSize))
      .sort({
        updateTima: -1,
      });
    const count = await this.ctx.model.Courses.countDocuments(query).exec();
    return {
      list,
      count,
    };
  }

  async getClassification(level, parentID) {
    const list = await this.ctx.model.CourseClassification.find({
      level,
      parentID,
    });
    return list;
  }

  async getCourseOne(_id) {
    const course = await this.ctx.model.Courses.findOne({
      _id,
    }).populate([
      {
        path: 'videoInfos',
        select: 'VideoId times _id Size times author date classHours ',
      },
      {
        path: 'authorID',
        select: 'name _id logo',
      },
      {
        path: 'documents',
      },
      {
        path: 'classification',
      },
    ]);
    return course;
  }
  async createCourse(newData) {
    // const backData = await this.ctx.model.Courses.create(newData);
    const type = await this.getType();
    if (type === '1') {
      newData.type = '1';
    }
    // newData.allowToOpen = true;
    newData.powerStatus = true;
    const backData = await this.ctx.service.db.create('Courses', newData);
    return backData;
  }
  async updateCourse(id, newData) {
    // const backData = await this.ctx.model.Courses.updateOne({
    // _id: id,
    // }, newData);
    const backData = await this.ctx.service.db.updateOne(
      'Courses',
      {
        _id: id,
      },
      newData
    );
    return backData;
  }
  async resortContent(_id, id1, id2) {
    const course = await this.ctx.model.Courses.findOne(
      {
        _id,
      },
      {
        sort: 1,
      }
    );
    let index1, index2;
    for (let index = 0; index < course.sort.length; index++) {
      if (course.sort[index]._id === id1) index1 = index;
      if (course.sort[index]._id === id2) index2 = index;
    }
    const temp = course.sort[index1].sequence;
    course.sort[index1].sequence = course.sort[index2].sequence;
    course.sort[index2].sequence = temp;
    // const back = await this.ctx.model.Courses.updateOne({
    //   _id,
    // }, {
    //   $set: {
    //     sort: course.sort,
    //   },
    // });
    const back = await this.ctx.service.db.updateOne(
      'Courses',
      {
        _id,
      },
      {
        $set: {
          sort: course.sort,
        },
      }
    );
    return back;
  }
  async insertContent(_id, contentType, project) {
    let contentID = '';
    if (contentType === 'videoInfos') {
      let response = {};
      try {
        response = await this.ctx.helper.request_alivod(
          'GetPlayInfo',
          {
            VideoId: project.VideoId,
            Formats: 'mp4',
          },
          {}
        );
      } catch (error) {
        this.ctx.logger.error(error);
      }
      project.duration = response.PlayInfoList.PlayInfo[0].Duration || 0;
      // const newVideoInfos = await this.ctx.model.VideoInfos.create(project);
      const newVideoInfos = await this.ctx.service.db.create('VideoInfos', project);
      contentID = newVideoInfos._id;
    } else if (contentType === 'documents') {
      // const newTrainingDocument = await this.ctx.model.TrainingDocument.create(project);
      const newTrainingDocument = await this.ctx.service.db.create('TrainingDocument', project);
      contentID = newTrainingDocument._id;
    }
    // await this.ctx.model.Courses.updateOne({
    //   _id,
    // }, {
    //   $push: {
    //     [contentType]: contentID,
    //   },
    //   $inc: {
    //     classHours: Number(project.classHours),
    //   },
    // });
    await this.ctx.service.db.updateOne(
      'Courses',
      {
        _id,
      },
      {
        $push: {
          [contentType]: contentID,
        },
        $inc: {
          classHours: Number(project.classHours),
        },
      }
    );
    const course = await this.ctx.model.Courses.findOne(
      {
        _id,
      },
      {
        sort: 1,
      }
    );
    this.ctx.auditLog(
      '追加章节',
      `${contentID} ** ${_id} ** ${contentID} ** ${JSON.stringify(course)}`,
      'info'
    );
    // const backData = await this.ctx.model.Courses.updateOne({
    //   _id,
    // }, {
    //   $push: {
    //     sort: {
    //       contentType,
    //       ID: contentID,
    //       sequence: course.sort.length + 1,
    //     },
    //   },
    // });
    const backData = await this.ctx.service.db.updateOne(
      'Courses',
      {
        _id,
      },
      {
        $push: {
          sort: {
            contentType,
            ID: contentID,
            sequence: course.sort.length + 1,
          },
        },
      }
    );
    return backData;
  }

  async createClassification(name, explain, level, parentID, author) {
    // const backData = await this.ctx.model.CourseClassification.create({
    //   name,
    //   explain,
    //   level,
    //   parentID,
    //   author,
    // });
    const backData = await this.ctx.service.db.create('CourseClassification', {
      name,
      explain,
      level,
      parentID,
      author,
    });

    return backData;
    // console.log(backData)
  }

  async recursionDelete(id) {
    const chirldArray = await this.ctx.model.CourseClassification.find(
      {
        parentID: id,
      },
      {
        _id: 1,
      }
    );
    // await this.ctx.model.CourseClassification.deleteMany({
    //   parentID: id,
    // });
    await this.ctx.service.db.deleteMany('CourseClassification', {
      parentID: id,
    });

    if (chirldArray.length) {
      for (let index = 0; index < chirldArray.length; index++) {
        await this.recursionDelete(chirldArray[index]._id);
      }
    } else {
      return;
    }
  }

  async deleteClassficication(id) {
    // await this.ctx.model.CourseClassification.deleteMany({
    //   _id: id,
    // });
    await this.ctx.service.db.deleteMany('CourseClassification', {
      _id: id,
    });
    await this.recursionDelete(id);
    return;
  }

  async updateClassficication(_id, name, explain) {
    // const backData = await this.ctx.model.CourseClassification.updateOne({
    //   _id,
    // }, {
    //   $set: {
    //     name,
    //     explain,
    //   },
    // });
    const backData = await this.ctx.service.db.updateOne(
      'CourseClassification',
      {
        _id,
      },
      {
        $set: {
          name,
          explain,
        },
      }
    );
    return backData;
  }
}

module.exports = CoursesService;
