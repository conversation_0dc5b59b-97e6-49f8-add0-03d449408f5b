const Service = require('egg').Service;
const _ = require('lodash');
class AdminTrainingService extends Service {
  async getTrainingType() {
    const referer = this.ctx.header['x-custom-referer'];
    return referer && referer.includes('fhTraining') ? 3 : 1;
  }
  // 创建培训计划
  async createTrainingPlan(newPlan) {
    try {
      newPlan.breakpointRange.top = Number(newPlan.breakpointRange.top);
      newPlan.breakpointRange.botton = Number(newPlan.breakpointRange.botton);
      // console.log(newPlan)
      // return await this.ctx.model.AdminTraining.create(newPlan);
      const trainingType = await this.getTrainingType();
      if (trainingType === 3) {
        newPlan.trainingType = 2;
        newPlan.certificateReview = false;
      }
      return await this.ctx.service.db.create('AdminTraining', newPlan);
    } catch (error) {
      console.error(error);
      return -1;
    }
  }

  async updatePlan(query, newData) {
    try {
      console.log(query, newData);
      // return await this.ctx.model.AdminTraining.updateOne(query, newData);
      return await this.ctx.service.db.updateOne('AdminTraining', query, newData);
    } catch (error) {
      console.error(error);
      return -1;
    }
  }

  async getPlanOne(query) {
    try {
      // if(cname)
      const plan = await this.ctx.model.AdminTraining.findOne(query).populate([
        {
          path: 'coursesID',
          select: 'name classHours explain cover',
        },
        {
          path: 'electives',
          select: 'name classHours explain cover',
        },
      ]);
      return plan;
    } catch (error) {
      console.error(error);
      return -1;
    }
  }

  async getParticipatingEnterprises(query, pageSize = 10, current = 1, cname = '', completeState) {
    try {
      const match = {};
      if (completeState) {
        const tempplan = await this.ctx.model.AdminTraining.findOne(query);
        if (completeState === 'true' || completeState === true) {
          match._id = {
            $in: tempplan.completedEnterprise,
          };
        } else if (completeState === 'false' && tempplan.completedEnterprise.length) {
          // match._id = {
          //   $nin: tempplan.completedEnterprise,
          // };
          match._id = {
            $in: tempplan.EnterpriseID.filter(function (val) {
              return tempplan.completedEnterprise.indexOf(val) === -1;
            }),
          };
        } else if (completeState === 'ing') {
          match._id = {
            $in: tempplan.incompleteEnterprise.filter(function (val) {
              return tempplan.completedEnterprise.indexOf(val) === -1;
            }),
          };
        }
      }
      if (cname) {
        match.cname = {
          $regex: cname,
        };
      }
      console.log('match', match);
      const plan = await this.ctx.model.AdminTraining.findOne(query).populate([
        {
          path: 'EnterpriseID',
          select: 'cname adminUserId',
          populate: [
            {
              path: 'adminUserId',
              select: 'name phoneNum',
            },
          ],
          limit: Number(pageSize),
          skip: Number((current - 1) * Number(pageSize)),
          match,
        },
        {
          path: 'coursesID',
          select: 'name classHours explain',
        },
        {
          path: 'completedEnterprise',
          select: 'cname adminUserId',
        },
      ]);
      return plan;
    } catch (error) {
      console.error(error);
      return -1;
    }
  }

  // 获取培训计划列表
  async getPlanList(query, current = 1, pageSize = 10) {
    try {
      const trainingType = await this.getTrainingType();
      query.trainingType = trainingType === 3 ? 2 : 1;
      return {
        list: await this.ctx.model.AdminTraining.find(query)
          .populate([
            {
              path: 'EnterpriseID',
              select: 'cname',
            },
            {
              path: 'completedEnterprise',
              select: 'cname',
            },
            {
              path: 'coursesID',
              select: 'name',
            },
          ])
          .sort({
            date: -1,
          })
          .skip((Number(current) - 1) * Number(pageSize))
          .limit(Number(pageSize)),
        count: await this.ctx.model.AdminTraining.countDocuments(query),
      };
    } catch (error) {
      console.error(error);
      return -1;
    }
  }

  //
  async numberOfPlanParticipant(query) {
    // const count = await this.ctx.model.PersonalTraining.countDocuments(query);
    const persons = await this.ctx.model.PersonalTraining.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$adminUserId',
          completeState: { $push: '$completeState' },
        },
      },
    ]);
    // xxn update
    const hasCompleted = persons.filter(ele => ele.completeState[0]);
    return [persons.length, hasCompleted.length];
  }

  // ========================================
  async findAdminorgList(
    Model,
    payload,
    {
      collections = [],
      query = {},
      query2 = {},
      searchKeys = [],
      // statisticsFiles = [],
    } = {}
  ) {
    const {
      searchkey,
      // isPaging,
    } = payload;
    let docs = [];
    // const count = 0;
    // const comprehensiveLevels = 0;
    query = query || {};
    query2 = query2 || {};
    // isPaging = isPaging !== '0';
    const aggpip = [];
    // const countPip = [];
    // const regAdd = ctx.session.superUserInfo.regAdd;
    collections.forEach(collection => {
      const lookup = {
        $lookup: {
          from: collection.name,
          localField: collection.selfKey,
          foreignField: collection.foreignKey,
          as: collection.asKey,
        },
      };
      aggpip.push(lookup);
    });
    // 因为工作地址是数组，有多个，所以需要拆开之后根据第一个工作地址进行筛选
    aggpip.push({
      $unwind: '$workAddress',
    });
    aggpip.push({
      $group: {
        _id: '$_id',
        onlineDeclarationFiles: {
          $first: '$onlineDeclarationFiles',
        },
        leadIn: {
          $first: '$leadIn',
        },
        isactive: {
          $first: '$isactive',
        },
        cname: {
          $first: '$cname',
        },
        code: {
          $first: '$code',
        },
        corp: {
          $first: '$corp',
        },
        workAddress: {
          $first: '$workAddress',
        },
        createTime: {
          $first: '$createTime',
        },
        adminUserId: {
          $first: '$adminUserId',
        },
        industryCategory: {
          $first: '$industryCategory',
        },
        level: {
          $first: '$level',
        },
        checkResult: {
          $first: '$checkResult',
        },
        reportTime: {
          $first: '$reportTime',
        },
        healcheckInfo: {
          $first: '$healcheckInfo',
        },
        harmStatistics: {
          $first: '$harmStatistics',
        },
        riskSortLevel: {
          $first: '$riskSortLevel',
        },
        exposeRiskLevel: {
          $first: '$exposeRiskLevel',
        },
        riskAssessmentReport: {
          $first: '$riskAssessmentReport',
        },
        preventAssessId: {
          $first: '$preventAssessId',
        },
      },
    });
    // 规定需要哪些字段
    aggpip.push({
      $project: {
        leadIn: 1,
        isactive: 1,
        cname: 1,
        code: 1,
        corp: 1,
        workAddress: 1,
        createTime: 1,
        adminUserId: 1,
        industryCategory: 1,
        level: 1,
        checkResult: 1,
        reportTime: 1,
        healcheckInfo: 1,
        harmStatistics: 1,
        riskSortLevel: 1,
        exposeRiskLevel: 1,
        onlineDeclarationFiles: 1,
        'riskAssessmentReport.assessmentResult': 1,
        'riskAssessmentReport.year': 1,
        'preventAssessId.level': 1,
        'preventAssessId.assessDate': 1,
      },
    });
    aggpip.push({
      $project: {
        leadIn: 1,
        isactive: 1,
        cname: 1,
        code: 1,
        corp: 1,
        workAddress: 1,
        createTime: 1,
        adminUserId: 1,
        industryCategory: 1,
        level: 1,
        checkResult: 1,
        reportTime: 1,
        healcheckInfo: 1,
        harmStatistics: 1,
        riskSortLevel: 1,
        exposeRiskLevel: 1,
        onlineDeclarationFiles: 1,
        'riskAssessmentReport.assessmentResult': 1,
        'riskAssessmentReport.year': 1,
        preventAssessId: {
          $filter: {
            input: '$preventAssessId',
            as: 'item',
            cond: {
              $lte: ['$$item.assessDate', new Date()],
            },
          },
        },
      },
    });
    if (query2.selfAssessment) {
      aggpip.push({
        $match: {
          'preventAssessId.0': {
            $exists: 1,
          },
        },
      });
      aggpip.push({
        $unwind: '$preventAssessId',
      });
      aggpip.push({
        $sort: {
          'preventAssessId.assessDate': -1,
        },
      });
      aggpip.push({
        $group: {
          _id: '$_id',
          onlineDeclarationFiles: {
            $first: '$onlineDeclarationFiles',
          },
          leadIn: {
            $first: '$leadIn',
          },
          isactive: {
            $first: '$isactive',
          },
          cname: {
            $first: '$cname',
          },
          code: {
            $first: '$code',
          },
          corp: {
            $first: '$corp',
          },
          workAddress: {
            $first: '$workAddress',
          },
          createTime: {
            $first: '$createTime',
          },
          adminUserId: {
            $first: '$adminUserId',
          },
          industryCategory: {
            $first: '$industryCategory',
          },
          checkResult: {
            $first: '$checkResult',
          },
          reportTime: {
            $first: '$reportTime',
          },
          healcheckInfo: {
            $first: '$healcheckInfo',
          },
          harmStatistics: {
            $first: '$harmStatistics',
          },
          level: {
            $first: '$level',
          },
          riskSortLevel: {
            $first: '$riskSortLevel',
          },
          exposeRiskLevel: {
            $first: '$exposeRiskLevel',
          },
          riskAssessmentReport: {
            $first: '$riskAssessmentReport',
          },
          preventAssessId: {
            $first: '$preventAssessId',
          },
        },
      });
      aggpip.push({
        $match: {
          $and: [
            {
              'preventAssessId.level': {
                $eq: query2.selfAssessment,
              },
            },
            {
              'preventAssessId.assessDate': {
                $gt: new Date(new Date().setMonth(new Date().getMonth() - 12)),
              },
            },
          ],
        },
      });
    }
    if (query2.assessmentResult || query2.assessmentResult === 0) {
      // aggpip.push({
      //   $match: { $and: [{ 'riskAssessmentReport.assessExposeLevel': query2.assessmentResult }, { 'preventAssessId.assessDate': { $gt: new Date(new Date().setMonth(new Date().getMonth() - 12)) } }] },
      // });
      aggpip.push({
        $match: {
          exposeRiskLevel: query2.assessmentResult,
        },
      });
    }
    if (query2.comprehensiveLevel || query2.comprehensiveLevel === 0) {
      aggpip.push({
        $match: {
          'riskAssessmentReport.0': {
            $exists: 1,
          },
        },
      });
      aggpip.push({
        $unwind: '$riskAssessmentReport',
      });
      aggpip.push({
        $sort: {
          'riskAssessmentReport.year': -1,
        },
      });
      aggpip.push({
        $group: {
          _id: '$_id',
          onlineDeclarationFiles: {
            $first: '$onlineDeclarationFiles',
          },
          leadIn: {
            $first: '$leadIn',
          },
          isactive: {
            $first: '$isactive',
          },
          cname: {
            $first: '$cname',
          },
          code: {
            $first: '$code',
          },
          corp: {
            $first: '$corp',
          },
          workAddress: {
            $first: '$workAddress',
          },
          createTime: {
            $first: '$createTime',
          },
          adminUserId: {
            $first: '$adminUserId',
          },
          industryCategory: {
            $first: '$industryCategory',
          },
          checkResult: {
            $first: '$checkResult',
          },
          reportTime: {
            $first: '$reportTime',
          },
          healcheckInfo: {
            $first: '$healcheckInfo',
          },
          harmStatistics: {
            $first: '$harmStatistics',
          },
          level: {
            $first: '$level',
          },
          riskSortLevel: {
            $first: '$riskSortLevel',
          },
          exposeRiskLevel: {
            $first: '$exposeRiskLevel',
          },
          riskAssessmentReport: {
            $first: '$riskAssessmentReport',
          },
          preventAssessId: {
            $first: '$preventAssessId',
          },
        },
      });
      aggpip.push({
        $match: {
          'riskAssessmentReport.assessmentResult': query2.comprehensiveLevel,
        },
      });
    }
    if (query2.OnlineDeclarationOption || query2.OnlineDeclarationOption === 0) {
      let time = {};
      if (query2.OnlineDeclarationOption === 3) {
        time = {
          $lte: new Date(),
          $gt: new Date(new Date().setMonth(new Date().getMonth() - 10)),
        };
      } else if (query2.OnlineDeclarationOption === 2) {
        time = {
          $lte: new Date(new Date().setMonth(new Date().getMonth() - 10)),
          $gt: new Date(new Date().setMonth(new Date().getMonth() - 12)),
        };
      } else if (query2.OnlineDeclarationOption === 1) {
        time = {
          $lt: new Date(new Date().setMonth(new Date().getMonth() - 12)),
        };
      }
      if (query2.OnlineDeclarationOption === 0) {
        aggpip.push({
          $match: {
            $or: [
              {
                onlineDeclarationFiles: {
                  $exists: false,
                },
              },
              {
                onlineDeclarationFiles: null,
              },
            ],
          },
        });
      } else {
        aggpip.push({
          $match: {
            'onlineDeclarationFiles.monthD': time,
          },
        });
      }
      // let time = {};
      // if (query2.OnlineDeclarationOption === 3) {
      //   time = { $lte: new Date(), $gt: new Date(new Date().setMonth(new Date().getMonth() - 10)) };
      // } else if (query2.OnlineDeclarationOption === 2) {
      //   time = { $lte: new Date(new Date().setMonth(new Date().getMonth() - 10)), $gt: new Date(new Date().setMonth(new Date().getMonth() - 12)) };
      // } else if (query2.OnlineDeclarationOption === 1) {
      //   time = { $lt: new Date(new Date().setMonth(new Date().getMonth() - 12)) };
      // }
      // if (query2.OnlineDeclarationOption === 0) {
      //   aggpip.push({
      //     $match: { $or: [{ onlineDeclarationFilesId: { $size: 0 } }, { 'onlineDeclarationFilesId.monthD': { $exists: false } }, { 'onlineDeclarationFilesId.monthD': { $gt: new Date() } }] },
      //   });
      // } else {
      //   aggpip.push({
      //     $unwind: '$onlineDeclarationFilesId',
      //   });
      //   aggpip.push({ $sort: { 'onlineDeclarationFilesId.monthD': -1 } });
      //   aggpip.push({ $group: {
      //     _id: '$_id',
      //     onlineDeclarationFiles: { $first: '$onlineDeclarationFiles' },
      //     leadIn: { $first: '$leadIn' },
      //     isactive: { $first: '$isactive' },
      //     cname: { $first: '$cname' },
      //     code: { $first: '$code' },
      //     corp: { $first: '$corp' },
      //     workAddress: { $first: '$workAddress' },
      //     createTime: { $first: '$createTime' },
      //     adminUserId: { $first: '$adminUserId' },
      //     industryCategory: { $first: '$industryCategory' },
      //     level: { $first: '$level' },
      //     checkResult: { $first: '$checkResult' },
      //     reportTime: { $first: '$reportTime' },
      //     healcheckInfo: { $first: '$healcheckInfo' },
      //     harmStatistics: { $first: '$harmStatistics' },
      //     riskSortLevel: { $first: '$riskSortLevel' },
      //     riskAssessmentReport: { $first: '$riskAssessmentReport' },
      //     preventAssessId: { $first: '$preventAssessId' },
      //   } });
      //   aggpip.push({
      //     $match: { 'onlineDeclarationFilesId.monthD': time },
      //   });
      // }
    }
    if (query2.jobHealthOption || query2.jobHealthOption === 0) {
      let time = {};
      if (query2.jobHealthOption === 3) {
        time = {
          $lte: new Date(),
          $gt: new Date(new Date().setMonth(new Date().getMonth() - 10)),
        };
      } else if (query2.jobHealthOption === 2) {
        time = {
          $lte: new Date(new Date().setMonth(new Date().getMonth() - 10)),
          $gt: new Date(new Date().setMonth(new Date().getMonth() - 12)),
        };
      } else if (query2.jobHealthOption === 1) {
        time = {
          $lt: new Date(new Date().setMonth(new Date().getMonth() - 12)),
        };
      }
      // else if (query2.jobHealthOption === 0) {
      //   time = { $or: [{ $exists: false }, { $eq: null }] };
      // }
      if (query2.jobHealthOption === 0) {
        aggpip.push({
          $match: {
            $or: [
              {
                reportTime: {
                  $exists: false,
                },
              },
              {
                reportTime: null,
              },
            ],
          },
        });
      } else {
        aggpip.push({
          $match: {
            reportTime: time,
          },
        });
      }
    }
    if (query2.ratioArrOption || query2.ratioArrOption === 0) {
      let time = {};
      if (query2.ratioArrOption === 3) {
        time = {
          $lte: new Date(),
          $gt: new Date(new Date().setMonth(new Date().getMonth() - 10)),
        };
      } else if (query2.ratioArrOption === 2) {
        time = {
          $lte: new Date(new Date().setMonth(new Date().getMonth() - 10)),
          $gt: new Date(new Date().setMonth(new Date().getMonth() - 12)),
        };
      } else if (query2.ratioArrOption === 1) {
        time = {
          $lt: new Date(new Date().setMonth(new Date().getMonth() - 12)),
        };
      }
      if (query2.ratioArrOption === 0) {
        aggpip.push({
          $match: {
            $or: [
              {
                healcheckInfo: {
                  $exists: false,
                },
              },
              {
                'healcheckInfo.recentDay': null,
              },
            ],
          },
        });
      } else {
        aggpip.push({
          $match: {
            'healcheckInfo.recentDay': time,
          },
        });
      }
    }
    if (query2.exceedOption || query2.exceedOption === 0) {
      if (query2.exceedOption === 1) {
        aggpip.push({
          $match: {
            $and: [
              {
                $or: [
                  {
                    biological: {
                      $exists: true,
                    },
                  },
                  {
                    radiation: {
                      $exists: true,
                    },
                  },
                  {
                    physical: {
                      $exists: true,
                    },
                  },
                  {
                    chemical: {
                      $exists: true,
                    },
                  },
                  {
                    dust: {
                      $exists: true,
                    },
                  },
                ],
              },
              {
                $or: [
                  {
                    'biological.exceed': {
                      $ne: '0',
                    },
                  },
                  {
                    'radiation.exceed': {
                      $ne: '0',
                    },
                  },
                  {
                    'physical.exceed': {
                      $ne: '0',
                    },
                  },
                  {
                    'chemical.exceed': {
                      $ne: '0',
                    },
                  },
                  {
                    'dust.exceed': {
                      $ne: '0',
                    },
                  },
                  {
                    'handBorneVibration.exceed': {
                      $ne: '0',
                    },
                  },
                  {
                    'heat.exceed': {
                      $ne: '0',
                    },
                  },
                  {
                    'highFrequencyEle.exceed': {
                      $ne: '0',
                    },
                  },
                  {
                    'laser.exceed': {
                      $ne: '0',
                    },
                  },
                  {
                    'microwave.exceed': {
                      $ne: '0',
                    },
                  },
                  {
                    'noise.exceed': {
                      $ne: '0',
                    },
                  },
                  {
                    'powerFrequencyElectric.exceed': {
                      $ne: '0',
                    },
                  },
                  {
                    'ultraHighRadiation.exceed': {
                      $ne: '0',
                    },
                  },
                  {
                    'ultraviolet.exceed': {
                      $ne: '0',
                    },
                  },
                ],
              },
            ],
          },
        });
      } else {
        aggpip.push({
          $match: {
            $or: [
              {
                $and: [
                  {
                    biological: {
                      $exists: false,
                    },
                  },
                  {
                    radiation: {
                      $exists: false,
                    },
                  },
                  {
                    physical: {
                      $exists: false,
                    },
                  },
                  {
                    chemical: {
                      $exists: false,
                    },
                  },
                  {
                    dust: {
                      $exists: false,
                    },
                  },
                  {
                    handBorneVibration: {
                      $exists: false,
                    },
                  },
                  {
                    heat: {
                      $exists: false,
                    },
                  },
                  {
                    highFrequencyEle: {
                      $exists: false,
                    },
                  },
                  {
                    laser: {
                      $exists: false,
                    },
                  },
                  {
                    microwave: {
                      $exists: false,
                    },
                  },
                  {
                    noise: {
                      $exists: false,
                    },
                  },
                  {
                    powerFrequencyElectric: {
                      $exists: false,
                    },
                  },
                  {
                    ultraHighRadiation: {
                      $exists: false,
                    },
                  },
                  {
                    ultraviolet: {
                      $exists: false,
                    },
                  },
                ],
              },
              {
                $and: [
                  {
                    'biological.exceed': {
                      $eq: '0',
                    },
                  },
                  {
                    'radiation.exceed': {
                      $eq: '0',
                    },
                  },
                  {
                    'physical.exceed': {
                      $eq: '0',
                    },
                  },
                  {
                    'chemical.exceed': {
                      $eq: '0',
                    },
                  },
                  {
                    'dust.exceed': {
                      $eq: '0',
                    },
                  },
                  {
                    'handBorneVibration.exceed': {
                      $eq: '0',
                    },
                  },
                  {
                    'heat.exceed': {
                      $eq: '0',
                    },
                  },
                  {
                    'highFrequencyEle.exceed': {
                      $eq: '0',
                    },
                  },
                  {
                    'laser.exceed': {
                      $eq: '0',
                    },
                  },
                  {
                    'microwave.exceed': {
                      $eq: '0',
                    },
                  },
                  {
                    'noise.exceed': {
                      $eq: '0',
                    },
                  },
                  {
                    'powerFrequencyElectric.exceed': {
                      $eq: '0',
                    },
                  },
                  {
                    'ultraHighRadiation.exceed': {
                      $eq: '0',
                    },
                  },
                  {
                    'ultraviolet.exceed': {
                      $eq: '0',
                    },
                  },
                ],
              },
            ],
          },
        });
      }
    }
    if (query2.odiseasedOption || query2.odiseasedOption === 0) {
      if (query2.odiseasedOption === 1) {
        aggpip.push({
          // $match: { 'odiseasesId.0': { $exists: 1 } },
          $match: {
            $and: [
              {
                healcheckInfo: {
                  $exists: true,
                },
              },
              {
                'healcheckInfo.occupational': {
                  $ne: '0',
                },
              },
            ],
          },
        });
      } else {
        aggpip.push({
          // $match: { odiseasesId: { $size: 0 } },
          $match: {
            $or: [
              {
                healcheckInfo: {
                  $exists: false,
                },
              },
              {
                'healcheckInfo.occupational': '0',
              },
            ],
          },
        });
      }
    }
    if (query2.suspectedOption || query2.suspectedOption === 0) {
      if (query2.suspectedOption === 1) {
        aggpip.push({
          $match: {
            $and: [
              {
                healcheckInfo: {
                  $exists: true,
                },
              },
              {
                'healcheckInfo.suspected': {
                  $ne: '0',
                },
              },
            ],
          },
        });
      } else {
        aggpip.push({
          $match: {
            $or: [
              {
                healcheckInfo: {
                  $exists: false,
                },
              },
              {
                'healcheckInfo.suspected': '0',
              },
            ],
          },
        });
      }
    }
    const arr = [];
    if (query2.industryCategory) {
      const industryCategoryLength = query2.industryCategory;
      industryCategoryLength.forEach(item => {
        arr.push({
          industryCategory: {
            $all: item,
          },
        });
      });
      aggpip.push({
        $match: {
          $or: arr,
        },
      });
    }
    // 输入搜索和地区和行业分类和风险评估的搜索
    if (!_.isEmpty(searchkey) || !_.isEmpty(query)) {
      const match = {
        $match: {},
      };
      if (!_.isEmpty(searchkey)) {
        if (typeof searchKeys === 'object' && searchKeys.length > 0) {
          const searchStr = [];
          searchKeys.forEach(keyname => {
            searchStr.push({
              [keyname]: {
                $regex: searchkey,
              },
            });
          });
          match.$match.$or = searchStr;
        }
      }
      if (!_.isEmpty(query)) {
        const and = [];
        Object.keys(query).forEach(keyname => {
          let item = {};
          if (keyname === 'trainState' && query[keyname] !== '' && query[keyname] !== undefined) {
            aggpip.push({
              $lookup: {
                from: 'adminTraining',
                localField: '_id',
                foreignField: 'EnterpriseID',
                as: 'adminTraining',
              },
            });
            let trainMatch = {};
            const yearNow = new Date().getFullYear();
            // console.log(typeof query[keyname], query[keyname])
            if (query[keyname] === 'true') {
              trainMatch = {
                'adminTraining.date': {
                  $gte: new Date(String(yearNow)),
                  $lt: new Date(String(yearNow + 1)),
                },
              };
            } else if (query[keyname] === 'false') {
              trainMatch = {
                $or: [
                  {
                    'adminTraining.date': {
                      $lt: new Date(String(yearNow)),
                      $gte: new Date(String(yearNow + 1)),
                    },
                  },
                  {
                    'adminTraining.0': {
                      $exists: false,
                    },
                  },
                ],
              };
            }
            and.push(trainMatch);
          } else if (_.isArray(query[keyname])) {
            // item = { [keyname]: { $all: query[keyname] } };
            item = {
              'workAddress.districts': {
                $all: query[keyname],
              },
            };
            // console.log(item, '还有这个？？');
          } else {
            item = {
              [keyname]: query[keyname],
            };
          }
          and.push(item);
        });
        match.$match.$and = and;
      }
      aggpip.push(match);
    }
    // 排序
    let sort = {};
    if (
      query2.sortType &&
      query2.sortType.order !== '' &&
      query2.sortType.order !== null &&
      query2.sortType.prop !== ''
    ) {
      const order = query2.sortType.order;
      const prop = query2.sortType.prop;
      if (order === 'ascending') {
        // if (prop === 'exceed') {
        //   sort = { $sort: { exceed: 1 } };
        // } else
        if (prop === 'odiseases') {
          sort = {
            $sort: {
              'healcheckInfo.occupational': 1,
            },
          };
        } else if (prop === 'suspected') {
          sort = {
            $sort: {
              'healcheckInfo.suspected': 1,
            },
          };
        } else if (prop === 'forbid') {
          sort = {
            $sort: {
              'healcheckInfo.forbid': 1,
            },
          };
        } else if (prop === 're_examination') {
          sort = {
            $sort: {
              'healcheckInfo.recheck': 1,
            },
          };
        }
      } else if (order === 'descending') {
        // if (prop === 'exceed') {
        //   sort = { $sort: { exceed: -1 } };
        // } else
        if (prop === 'odiseases') {
          sort = {
            $sort: {
              'healcheckInfo.occupational': -1,
            },
          };
        } else if (prop === 'suspected') {
          sort = {
            $sort: {
              'healcheckInfo.suspected': -1,
            },
          };
        } else if (prop === 'forbid') {
          sort = {
            $sort: {
              'healcheckInfo.forbid': -1,
            },
          };
        } else if (prop === 're_examination') {
          sort = {
            $sort: {
              'healcheckInfo.recheck': -1,
            },
          };
        }
      }
    } else {
      sort = {
        $sort: {
          createTime: -1,
        },
      };
    }
    aggpip.push(sort);
    // 获取所有满足条件的企业的数量
    aggpip.push({
      $count: 'totalItems',
    });
    // const totalItems = await Model.aggregate(aggpip);
    aggpip.splice(aggpip.length - 1, 1);
    docs = await Model.aggregate(aggpip);
    const dataList = docs.map(function (item) {
      return item._id;
    });
    const backData = {
      dataList,
    };
    return new Promise(function (resolve) {
      resolve(backData);
    });
  }

  async deleteOne(_id) {
    // await this.ctx.model.AdminTraining.deleteOne({
    // _id,
    // });
    await this.ctx.service.db.deleteOne('AdminTraining', {
      _id,
    });
  }
  // 导入培训清单 管理员培训
  async importTrainList(data) {
    try {
      let list = data.list;
      let params = data.params;
      const superID = this.ctx.session.superUserInfo ? this.ctx.session.superUserInfo._id : '';
      console.log('this.ctx.session.superUserInfo', this.ctx.session.superUserInfo);
      const superName = this.ctx.session.superUserInfo ? this.ctx.session.superUserInfo.cname : '';
      // 根据enterprise字段去adminorg企业表里面匹配name字段获得企业id先找到企业id
      // 分类找到的数据和没找到的数据
      // console.log('开始处理', list);
      // 首先跟具enterprise字段去adminorg企业表里面匹配cname字段获得企业id，返回一个数组
      // 1.拿到了数据库内存在的企业的id
      if (data.trainPlanId) {
        // 如果是继续导入，需要先找到已经存在的人，然后再去创建
        const hasImport = await this.ctx.model.Certificate.aggregate([
          {
            $lookup: {
              from: 'personalTraining',
              localField: 'personalTrainingId',
              foreignField: '_id',
              as: 'personalTraining',
            },
          },
          {
            $unwind: '$personalTraining',
          },
          {
            $match: {
              'personalTraining.adminTrainingId': data.trainPlanId,
            },
          },
          {
            $addFields: {
              idCard: '$winner.IDNum',
            },
          },
          {
            $project: {
              _id: 0,
              idCard: 1,
            },
          },
          {
            $group: {
              _id: null,
              idCard: { $push: '$idCard' },
            },
          },
        ]);
        console.log('hasImport', hasImport);
        if (hasImport.length > 1 && hasImport[0].idCard) {
          list = list.filter(item => !hasImport[0].idCard.includes(item.idCard));
        }
      }
      const findEnterprise = await this.ctx.model.Adminorg.aggregate([
        {
          $match: {
            cname: {
              $in: list.map(item => item.enterprise),
            },
          },
        },
        {
          $lookup: {
            from: 'adminusers',
            localField: 'adminUserId',
            foreignField: '_id',
            as: 'adminusers',
          },
        },
        {
          $project: {
            _id: 1,
            cname: 1,
            districtRegAdd: 1,
            regAdd: 1,
            adminusers: 1,
          },
        },
      ]);
      let exitArr = [];
      const noExistArr = [];
      // findEnterprise处理一下 返回enterpriseArr=[xxx,xxx,xx],将list 拆分成两部分，一部分是企业名称存在的，一部分是不存在的
      const enterpriseSet = new Set();
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        const findItem = findEnterprise.find(findItem => findItem.cname === item.enterprise);
        if (findItem) {
          exitArr.push({
            ...item,
            enterpriseId: findItem._id, // 用于创建培训计划
          });
          enterpriseSet.add(findItem._id);
        } else {
          const districts = ['福建省', '福州市'];
          const dist = {
            districts,
            address: item.address,
            point: [],
          };
          const creatAdminuser = await this.ctx.model.AdminUser.create({
            IDcard: item.idCard,
            group: this.ctx.app.config.groupID.adminGroupID,
            name: item.name || '',
            password: item.idCard.slice(-6),
            enable: true,
          });
          const newCompany = await this.ctx.model.Adminorg.create({
            cname: item.enterprise,
            workAddress: [dist] || [],
            districtRegAdd: districts || [],
            adminUserId: creatAdminuser._id,
            isactive: 1,
          });
          exitArr.push({
            ...item,
            enterpriseId: newCompany._id, // 用于创建培训计划
          });
          enterpriseSet.add(newCompany._id);
        }
      }
      const enterpriseArr = Array.from(enterpriseSet);
      let createTrainingPlanRes = null;
      if (!data.trainPlanId) {
        const newPlan = {
          superID,
          certificateReview: false,
          name: params.name,
          Introduction: params.Introduction,
          date: params.date,
          completeTime: params.completeTime,
          completedEnterprise: enterpriseArr,
          needExam: false,
          breakpoint: 1,
          breakpointRange: {
            botton: 5,
            top: 10,
          },
          EnterpriseID: enterpriseArr,
          requiredCoursesHours: params.requiredCoursesHours,
        };
        // 3.根据培训计划具体到人去创建个人培训记录
        createTrainingPlanRes = await this.createTrainingPlan(newPlan);
      } else {
        await this.ctx.model.AdminTraining.updateOne(
          { _id: data.trainPlanId },
          { $addToSet: { EnterpriseID: { $each: enterpriseArr } } }
        );
        createTrainingPlanRes = await this.ctx.model.AdminTraining.findOne({
          _id: data.trainPlanId,
        });
        params = createTrainingPlanRes;
      }
      // 2 创建导入的培训计划记录

      // console.log('newPlan', createTrainingPlanRes);
      // 4.根据培训计划具体到人去创建个人培训记录,需要根据exitArr中的身份证号去查到adminuserId
      const findAdminUser = await this.ctx.model.AdminUser.aggregate([
        {
          $match: {
            IDcard: {
              $in: exitArr.map(item => item.idCard),
            },
          },
        },
        {
          $project: {
            _id: 1,
            IDcard: 1,
            userId: 1,
          },
        },
      ]);
      console.log('findAdminUser', findAdminUser);
      // 5. 在exitArr中添加adminuserId
      const midExitArr = [];
      for (let i = 0; i < exitArr.length; i++) {
        const item = exitArr[i];
        const findItem = findAdminUser.find(findItem => findItem.IDcard === item.idCard);
        if (findItem) {
          item.adminuserId = findItem._id;
          item.userId = findItem.userId;
          midExitArr.push(item);
        } else {
          // 如果没有找到匹配的adminuserId，就将这条数据放到noExistArr中,并且从exitArr中删除 创建啊
          let employee = await this.ctx.model.Employee.findOne({
            name: item.name,
            EnterpriseID: item.enterpriseId,
          });
          if (!employee) {
            employee = await this.ctx.model.Employee.create({
              IDNum: item.idCard,
              name: item.name,
              EnterpriseID: item.enterpriseId,
            });
          } else {
            await this.ctx.model.Employee.updateOne(
              { name: item.name, EnterpriseID: item.enterpriseId },
              { IDNum: item.idCard }
            );
          }
          const newAdminUser = await this.ctx.model.AdminUser.create({
            IDcard: item.idCard,
            group: this.ctx.app.config.groupID.adminGroupID,
            name: item.name || '',
            password: item.idCard.slice(-6),
            enable: true,
            employees: [employee._id],
          });
          console.log('newAdminUse创建', newAdminUser);
          const newUser = await this.ctx.model.User.create({
            name: item.name || '', // 姓名
            idNo: item.idCard || '', // 身份证号
            companyId: [item.enterpriseId], // 所在企业
            companyStatus: 2, // 企业审核通过
          });
          console.log('newUser创建', newUser);
          await this.ctx.model.AdminUser.updateOne(
            { _id: newAdminUser._id },
            { userId: newUser._id }
          );
          // const res22 = await tools.updateData(this.ctx, 'employee', {
          //   _id: employee._id,
          //   EnterpriseID: item.enterpriseId,
          //   type: '1',
          //   employeeIsRole: true,
          //   IDNum: item.idCard,
          // });
          midExitArr.push({
            ...item,
            adminuserId: newAdminUser._id,
            userId: newUser._id,
          });
          // item.reason = '身份证号不存在';
          // noExistArr.push(item);
        }
      }
      exitArr = midExitArr;
      // 把么有adminuserId的数据过滤掉，放到noExistArr中 采用倒序防止索引改变出错
      for (let i = exitArr.length - 1; i >= 0; i--) {
        const item = exitArr[i];
        if (!item.adminuserId) {
          noExistArr.push({
            ...item,
            reason: 'adminuserId不存在',
          });
          exitArr.splice(i, 1);
        }
      }
      // 6.开始走创建个人培训记录的流程
      // 整理出一个数组为了创建个人培训记录
      const toCreatePersonalTrainingRecordArr = [];
      for (let i = 0; i < exitArr.length; i++) {
        const item = exitArr[i];
        toCreatePersonalTrainingRecordArr.push({
          trainingType: 1,
          adminTrainingId: createTrainingPlanRes._id,
          EnterpriseID: item.enterpriseId,
          completeState: true,
          status: true,
          adminUserId: item.adminuserId,
          userId: item.userId,
          superID,
        });
      }
      const createPersonalTrainingRecordRes = await this.ctx.model.PersonalTraining.insertMany(
        toCreatePersonalTrainingRecordArr
      );
      // 根据createPersonalTrainingRecordRes的创建结果生成证书

      const certificateArr = [];
      // 获取证书表里number字段里面包含FZ的数量
      const certificateCount = await this.ctx.model.Certificate.count({
        number: {
          $regex: 'FZ',
        },
      });
      const year = new Date(params.completeTime).getFullYear();
      // 6位数000000，从certificateCount数量+1
      createPersonalTrainingRecordRes.forEach((item, index) => {
        certificateArr.push({
          number: 'FZ02' + year + (certificateCount + index + 1).toString().padStart(6, '0'),
          personalTrainingId: item._id,
          trainingType: 1,
          type: 1,
          superID,
          unit: exitArr[index].unit || superName,
          source: 2,
          winner: {
            name: exitArr[index].name,
            IDNum: exitArr[index].idCard,
            companyName: exitArr[index].enterprise,
            companyRegAdd: exitArr[index].regAdd,
            districtRegAdd: exitArr[index].districtRegAdd,
          },
          trainingDetail: {
            name: params.name,
            time: new Date(),
            coursesList: [
              {
                name: params.name,
                classHours: params.requiredCoursesHours,
              },
            ],
          },
          certificateStatus: 2,
          issuanceTime: new Date(exitArr[index].issuanceTime || new Date()),
          effectiveTime: new Date(
            exitArr[index].effectiveTime || new Date().getTime() + 1000 * 60 * 60 * 24 * 365 * 3
          ),
        });
      });
      console.log('certificateArr', certificateArr);
      // 创建证书
      const createCertificateRes = await this.ctx.model.Certificate.insertMany(certificateArr);
      for (let i = 0; i < createCertificateRes.length; i++) {
        const item = createCertificateRes[i];
        const img = await this.ctx.service.certificatePdf.createCertificatePdf(item);
        await this.ctx.model.Certificate.updateOne({ _id: item._id }, { $set: { img } });
        await this.ctx.model.PersonalTraining.updateOne(
          { _id: item.personalTrainingId },
          { $set: { certificateID: item._id } }
        );
      }
      return {
        success: createCertificateRes.length,
        fail: noExistArr.length,
        failList: noExistArr,
      };
    } catch (error) {
      console.log(error);
      this.ctx.auditLog('导入培训记录失败', JSON.stringify(error), 'error');
    }
  }
  // 导入培训清单 劳动者培训
  async importTrainListLabourers(data) {
    try {
      const list = data.list;
      const params = data.params;
      const superID = this.ctx.session.superUserInfo ? this.ctx.session.superUserInfo._id : '';
      const superName = this.ctx.session.superUserInfo ? this.ctx.session.superUserInfo.cname : '';
      // 根据enterprise字段去adminorg企业表里面匹配name字段获得企业id先找到企业id
      // 分类找到的数据和没找到的数据
      // console.log('开始处理', list);
      // 首先跟具enterprise字段去adminorg企业表里面匹配cname字段获得企业id，返回一个数组
      // 1.拿到了数据库内存在的企业的id
      const nowYear = new Date().getFullYear();
      const failList = [];
      const successList = [];
      const grouped = list.reduce((result, item) => {
        const recKey = item.enterprise;
        if (!result[recKey]) {
          result[recKey] = {
            enterpriseId: '',
            list: [],
          };
        }
        result[recKey].list.push(item);
        return result;
      }, {});
      const keys = Object.keys(grouped);
      for (const outKey of keys) {
        console.log('outKey', outKey);
        for (const item of grouped[outKey].list) {
          console.log('item', item);
          try {
            // 使用企业名作为键
            const key = item.enterprise;
            // 如果这个键在结果对象中还不存在，就创建一个空数组
            if (!grouped[key].enterpriseId) {
              const findEnterprise = await this.ctx.model.Adminorg.aggregate([
                {
                  $match: {
                    cname: item.enterprise,
                  },
                },
                {
                  $lookup: {
                    from: 'adminusers',
                    localField: 'adminUserId',
                    foreignField: '_id',
                    as: 'adminusers',
                  },
                },
                {
                  $project: {
                    _id: 1,
                    cname: 1,
                    districtRegAdd: 1,
                    regAdd: 1,
                    adminusers: 1,
                  },
                },
              ]);
              if (!findEnterprise.length) {
                const districts = ['福建省', '福州市'];
                const dist = {
                  districts,
                  address: item.address,
                  point: [],
                };
                const createAdminuser = await this.ctx.model.AdminUser.create({
                  IDcard: item.idCard,
                  group: this.ctx.app.config.groupID.adminGroupID,
                  name: item.enterprise,
                  userName: item.idCard.slice(-8),
                  password: item.idCard.slice(-6),
                  enable: true,
                });

                const newCompany = await this.ctx.model.Adminorg.create({
                  cname: item.enterprise,
                  workAddress: [dist] || [],
                  districtRegAdd: districts || [],
                  adminUserId: createAdminuser._id,
                  isactive: 1,
                });
                item.enterpriseId = newCompany._id;
                grouped[key].enterpriseId = newCompany._id;
                grouped[key].adminuserId = createAdminuser._id;
              } else {
                item.enterpriseId = findEnterprise[0]._id;
                grouped[key].enterpriseId = findEnterprise[0]._id;
                grouped[key].adminuserId = findEnterprise[0].adminusers[0]._id;
              }
            } else {
              item.enterpriseId = grouped[key].enterpriseId;
            }
            // 查询 user 和employee
            let findUser = await this.ctx.model.User.findOne({
              idNo: item.idCard,
            });
            if (!findUser) {
              const userInfo = {
                name: item.name || '', // 姓名
                idNo: item.idCard, // 身份证号
                companyId: [item.enterpriseId], // 所在企业
                companyStatus: 2, // 企业审核通过
              };
              findUser = await this.ctx.model.User.create(userInfo);
            }
            let findEmployee = await this.ctx.model.Employee.findOne({
              IDNum: item.idCard,
              EnterpriseID: item.enterpriseId,
            });
            if (!findEmployee) {
              const employeeInfo = {
                name: item.name || '', // 姓名
                IDNum: item.idCard, // 身份证号
                EnterpriseID: grouped[key].enterpriseId, // 所在企业
                status: 1, // 在职状态
                userId: findUser._id,
              };
              findEmployee = await this.ctx.model.Employee.create(employeeInfo);
            }
            if (!findUser.employeeId) {
              await this.ctx.model.User.updateOne(
                { _id: findUser._id },
                { employeeId: findEmployee._id }
              );
            }
            if (!grouped[key].trainPlanId) {
              const newEmployeePlan = {
                name: params.name,
                authorID: grouped[key].adminuserId,
                EnterpriseID: grouped[key].enterpriseId,
                employees: [findEmployee._id], // 需要培训的人
                completedEmployees: [findEmployee._id], // 完成的人
                introduction: params.Introduction,
                date: new Date(params.date), // 创建时间
                completeTime: params.completeTime, // 完成时间
                needExam: false, // 是否需要考试
                certificateReview: false,
              };
              const createEmployeePlanRes = await this.ctx.model.EmployeesTrainingPlan.create(
                newEmployeePlan
              );
              item.trainPlanId = createEmployeePlanRes._id;
              grouped[key].trainPlanId = createEmployeePlanRes._id;
            } else {
              // 更新EmployeesTrainingPlan 里面的employees
              await this.ctx.model.EmployeesTrainingPlan.updateOne(
                { _id: grouped[key].trainPlanId },
                {
                  $addToSet: {
                    employees: findEmployee._id,
                    completedEmployees: findEmployee._id,
                  },
                }
              );
            }
            // 创建个人培训记录
            const newPersonalTrainingRecord = {
              trainingType: 3,
              EnterpriseID: item.enterpriseId,
              completeState: true,
              status: true,
              adminUserId: item.adminuserId,
              userId: item.userId,
              employeesTrainingPlanId: item.trainPlanId,
              superID,
            };
            const createPersonalTrainingRecordRes = await this.ctx.model.PersonalTraining.create(
              newPersonalTrainingRecord
            );
            item.personalTrainingId = createPersonalTrainingRecordRes._id;
            // 创建证书
            const certificateCount = await this.ctx.model.Certificate.count({
              number: {
                $regex: 'FZ',
              },
            });
            const certificate = {
              number: 'FZ02' + nowYear + (certificateCount + 1).toString().padStart(6, '0'),
              personalTrainingId: createPersonalTrainingRecordRes._id,
              trainingType: 3,
              EnterpriseID: grouped[key].enterpriseId,
              template: 1,
              type: 1,
              superID,
              unit: item.unit || superName,
              source: 2,
              winner: {
                name: item.name,
                IDNum: item.idCard,
                companyName: item.enterprise,
                companyRegAdd: item.regAdd,
                districtRegAdd: item.districtRegAdd,
              },
              trainingDetail: {
                name: params.name,
                time: new Date(),
                coursesList: [
                  {
                    name: params.name,
                    classHours: params.requiredCoursesHours,
                  },
                ],
              },
              certificateStatus: 2,
              issuanceTime: new Date(item.issuanceTime || new Date()),
              effectiveTime: new Date(
                item.effectiveTime || new Date().getTime() + 1000 * 60 * 60 * 24 * 365 * 3
              ),
            };
            const createCertificateRes = await this.ctx.model.Certificate.create(certificate);
            const img = await this.ctx.service.certificatePdf.createCertificatePdf(
              createCertificateRes
            );
            await this.ctx.model.Certificate.updateOne(
              { _id: createCertificateRes._id },
              { $set: { img } }
            );
            successList.push(item);
            // 返回更新后的结果对象
          } catch (error) {
            failList.push({
              ...item,
              reason: '导入失败',
            });
            this.ctx.auditLog('导入培训记录失败', JSON.stringify(error), 'error');
          }
        }
      }
      return {
        success: successList.length,
        fail: failList.length,
        failList,
      };
    } catch (error) {
      console.log(error);
      this.ctx.auditLog('导入培训记录失败', JSON.stringify(error), 'error');
    }
  }
}

module.exports = AdminTrainingService;
