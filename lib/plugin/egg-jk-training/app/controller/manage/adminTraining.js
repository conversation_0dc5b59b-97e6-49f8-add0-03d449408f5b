// 管理员培训
const AdminTrainingController = {
  async getPlanList(ctx) {
    try {
      const {
        searchKey,
        searchPlanStatus,
        current,
        pageSize,
      } = ctx.query;
      let {
        selectYear,
      } = ctx.query;
      const query = {
        superID: ctx.session.superUserInfo._id,
      };
      if (searchKey) {
        query.name = {
          $regex: searchKey,
        };
      }
      if (searchPlanStatus) {
        query.completeTime = {
          [searchPlanStatus]: new Date(),
        };
      }
      if (selectYear) {
        selectYear = new Date(selectYear);
        selectYear.setDate(1);
        selectYear.setMonth(0);

        const nextSearchYear = new Date(selectYear);
        nextSearchYear.setDate(1);
        nextSearchYear.setMonth(0);
        nextSearchYear.setYear(selectYear.getFullYear() + 1);

        query.completeTime = {
          $lt: nextSearchYear, // 比下一年元旦早
          $gte: selectYear, // 比搜索年元旦晚
        };
      }

      const {
        list,
        count,
      } = await ctx.service.adminTraining.getPlanList(query, current, pageSize);
      ctx.helper.renderSuccess(ctx, {
        data: {
          list,
          count,
        },
        status: 200,
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async getPlanOne(ctx) {
    try {
      const {
        _id,
      } = ctx.query;
      const query = {
        _id,
      };
      const plan = await ctx.service.adminTraining.getPlanOne(query);
      const numberOfPlanParticipants = [];
      // for (let index = 0; index < plan.EnterpriseID.length; index++) {
      //   const element = plan.EnterpriseID[index];
      //   const numberOfPlanParticipant = await ctx.service.adminTraining.numberOfPlanParticipant({
      //     adminTrainingId: _id,
      //     EnterpriseID: element,
      //   });
      //   numberOfPlanParticipants.push(numberOfPlanParticipant)
      // }

      ctx.helper.renderSuccess(ctx, {
        data: {
          plan,
          numberOfPlanParticipants,
        },
        status: 200,
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // 获取参与的企业，无痕滚动
  async getParticipatingEnterprises(ctx) {
    try {
      const {
        _id,
        current,
        pageSize,
        completeState,
        cname,
      } = ctx.query;

      const query = {
        _id,
      };
      const tempPlan = await ctx.service.adminTraining.getParticipatingEnterprises(query, pageSize, current, cname, completeState);
      const enterprises = [];
      for (let index = 0; index < tempPlan.EnterpriseID.length; index++) {
        const element = tempPlan.EnterpriseID[index];
        const company = await ctx.model.Adminorg.findOne({
          _id: element._id,
        }, {
          adminArray: 1,
        });
        const numberOfPlanParticipant = await ctx.service.adminTraining.numberOfPlanParticipant({
          status: true,
          adminTrainingId: _id,
          adminUserId: {
            $in: company.adminArray,
          },
        });
        enterprises.push({
          _id: element._id,
          cname: element.cname,
          adminUserId: element.adminUserId,
          numberOfPlanParticipant: company.adminArray.length ? numberOfPlanParticipant[0] : '/',
          hasCompleted: company.adminArray.length ? numberOfPlanParticipant[1] : '/',
          needTrainNum: company.adminArray.length || '/',
        });
      }

      ctx.helper.renderSuccess(ctx, {
        data: {
          enterprises,
        },
        status: 200,
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // 提醒未完成的企业
  async remindEnterprises(ctx, app) {
    try {
      const {
        planID,
      } = ctx.request.body;
      const plan = await ctx.model.AdminTraining.findOne({ _id: planID }, { name: 1, EnterpriseID: 1, completedEnterprise: 1 });
      const notCompleteEnterprises = plan.EnterpriseID.concat(plan.completedEnterprise).filter(v => !plan.completedEnterprise.includes(v));
      const superID = ctx.session.superUserInfo._id;
      const superUser = await ctx.service.superUser.item({
        _id: superID,
      });
      const newMessage = {
        authorID: superID,
        authorGroup: app.config.groupID.superGroupID,
        title: '一条新的培训提醒',
        message: `${superUser.cname}提醒您有未完成的培训“${plan.name}”，请及时处理`,
        reader: [],
      };
      for (let index = 0; index < notCompleteEnterprises.length; index++) {
        newMessage.reader.push({
          readerID: notCompleteEnterprises[index],
          readerGroup: app.config.groupID.adminGroupID,
        });
      }
      // const result = await ctx.model.MessageNotification.create(newMessage);
      const result = await ctx.service.db.create('MessageNotification', newMessage);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '成功',
        status: 200,
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // 提醒一个企业
  async remindOne(ctx, app) {
    try {
      const {
        planID,
        EnterpriseID,
      } = ctx.request.body;
      const plan = await ctx.model.AdminTraining.findOne({ _id: planID }, { name: 1 });
      const superID = ctx.session.superUserInfo._id;
      const superUser = await ctx.service.superUser.item({
        _id: superID,
      });
      const newMessage = {
        authorID: superID,
        authorGroup: app.config.groupID.superGroupID,
        title: '一条新的培训提醒',
        message: `${superUser.cname}提醒您有未完成的培训“${plan.name}”，请及时处理`,
        reader: [],
      };
      for (let index = 0; index < EnterpriseID.length; index++) {
        newMessage.reader.push({
          readerID: EnterpriseID[index],
          readerGroup: app.config.groupID.adminGroupID,
        });
      }
      // const result = await ctx.model.MessageNotification.create(newMessage);
      const result = await ctx.service.db.create('MessageNotification', newMessage);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '成功',
        status: 200,
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // 创建培训
  async createPlan(ctx, app) {
    try {
      const {
        newPlan,
      } = ctx.request.body;
      const superID = ctx.session.superUserInfo._id;
      newPlan.superID = superID;
      const result = await ctx.service.adminTraining.createTrainingPlan(newPlan);
      // 发送站内消息
      const superUser = await ctx.model.SuperUser.findOne({ _id: superID }, { cname: 1 });
      const newMessage = {
        authorID: superID,
        authorGroup: app.config.groupID.superGroupID,
        title: '一条新的培训计划',
        message: `${superUser ? superUser.cname : '管理员培训'}将您添加至“${newPlan.name}”中，请及时处理`,
        reader: [],
      };
      const len = newPlan.EnterpriseID.length;
      for (let index = 0; index < len; index++) {
        newMessage.reader.push({
          readerID: newPlan.EnterpriseID[index],
          readerGroup: app.config.groupID.adminGroupID,
        });
      }
      // ctx.model.MessageNotification.create(newMessage);
      ctx.service.db.create('MessageNotification', newMessage);
      // // 发短信
      ctx.helper.renderSuccess(ctx, {
        data: {
          result,
          status: 200,
        },
        message: '数据创建成功',
        status: 200,
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }

  },

  async updatePlan(ctx) {
    try {
      const {
        newEnterprises,
        newCourses,
        newExaminationInfo,
        _id,
        completeTime,
        name,
        Introduction,
        breakpoint,
        breakpointInterval,
        breakpointRange,
        certificateReview,
        electivesCoursesHours,
        protectiveClassification,
      } = ctx.request.body;
      const newData = {
        $addToSet: {
          updateTime: new Date(),
        },
        $set: {},
      };
      let haveNewEnterprise = false;
      if (newEnterprises && newEnterprises.length) {
        newData.$addToSet.EnterpriseID = newEnterprises;
        haveNewEnterprise = true;
      }

      if (newCourses && newCourses.length) {
        newData.$addToSet.coursesID = newCourses;
      }

      if (completeTime) {
        newData.$set.completeTime = completeTime;
      }

      if (newExaminationInfo) {
        newData.$set.examination = newExaminationInfo.examination;
        newData.$set.allowTestOnly = newExaminationInfo.allowTestOnly;
        newData.$set.completeTime = newExaminationInfo.completeTime;
        // newData.$set.name = newExaminationInfo.name;
      }

      if (certificateReview !== void 0) {
        newData.$set.certificateReview = certificateReview;
      }

      if (electivesCoursesHours !== void 0) {
        newData.$set.electivesCoursesHours = electivesCoursesHours;
      }
      if (protectiveClassification.length) {
        newData.$set.protectiveClassification = protectiveClassification;
      }

      if (typeof breakpoint === 'number') {
        newData.$set.breakpoint = breakpoint;
      }
      if (breakpointInterval) {
        newData.$set.breakpointInterval = breakpointInterval;
      }
      if (breakpointRange) {
        newData.$set.breakpointRange = breakpointRange;
      }

      if (name) newData.$set.name = name;
      if (Introduction) newData.$set.Introduction = Introduction;

      const backData = await ctx.service.adminTraining.updatePlan({
        _id,
      }, newData);
      ctx.helper.renderSuccess(ctx, {
        data: backData,
        message: '数据修改成功',
        status: 200,
      });

      if (haveNewEnterprise) {
        await ctx.service.adminTraining.getPlanOne({
          _id,
        });
      }

    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // 删除需要学习的企业
  async deleteEnterprise(ctx) {
    try {
      const {
        EnterpriseID,
        _id,
      } = ctx.request.body;
      const backData = await ctx.service.adminTraining.updatePlan({
        _id,
      }, {
        $pull: {
          EnterpriseID: {
            $in: [ EnterpriseID ],
          },
        },
      });
      const company = await ctx.model.Adminorg.findOne({
        _id: EnterpriseID,
      }, {
        adminArray: 1,
      });
      if (company) {
        ctx.service.personalTraining.updateMany({
          adminUserId: {
            $in: company.adminArray,
          },
          adminTrainingId: _id,
          trainingType: 1,
        }, {
          $set: {
            status: false,
          },
        });
      }
      ctx.helper.renderSuccess(ctx, {
        data: backData,
        message: '成功',
        status: 200,
      });
    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // 删除培训计划中的课程
  async deleteCourse(ctx) {
    try {
      const {
        coursesID,
        _id,
      } = ctx.request.body;
      const backData = await ctx.service.adminTraining.updatePlan({
        _id,
      }, {
        $pull: {
          coursesID,
        },
      });
      ctx.helper.renderSuccess(ctx, {
        data: backData,
        message: '成功',
        status: 200,
      });
    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // 修改课程顺序
  async modifyCourseOder(ctx) {
    try {
      const {
        _id,
        coursesID,
        electives,
      } = ctx.request.body;
      let key = '';
      if (electives) key = 'electives';
      if (coursesID) key = 'coursesID';
      if (!key) {
        ctx.helper.renderFail(ctx, {
          message: '非法参数',
        });
        return;
      }
      const params = {
        _id,
        coursesID,
        electives,
      };
      const tempPlan = await ctx.model.AdminTraining.findOne({
        _id,
      });
      const courses = JSON.parse(JSON.stringify(tempPlan[key]));
      const index = courses.indexOf(params[key]);
      if (index > 0) {
        [ courses[index - 1], courses[index] ] = [ courses[index], courses[index - 1] ];
        await ctx.service.adminTraining.updatePlan({
          _id,
        }, {
          $set: {
            [key]: courses,
          },
        });
      }
      ctx.helper.renderSuccess(ctx, {
        message: '数据修改成功',
        status: 200,
      });
    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // 证书
  async getCertificateList(ctx) {
    try {
      const {
        searchKey,
      } = ctx.query;
      const query = {};
      if (searchKey) query.searchKey = searchKey;
      const list = await ctx.model.Certificate.find(query, {
        name: 1,
        _id: 1,
        desc: 1,
      });
      ctx.helper.renderSuccess(ctx, {
        data: {
          list,
          message: 'OK',
        },
      });
    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // 批量获取课程
  async getCoursesList(ctx) {
    try {
      const current = ctx.query.current || 1;
      const pageSize = ctx.query.pageSize || 10;
      const searchkey = ctx.query.searchkey || '';
      const selectedCourses = ctx.query.selectedCourses && JSON.parse(ctx.query.selectedCourses) || [];
      const query = {
        complete: true,
        _id: {
          $nin: selectedCourses,
        },
      };
      if (searchkey.length) {
        query.$or = [{
          name: {
            $regex: searchkey,
          },
        }];
      }
      const {
        list,
        count,
      } = await ctx.service.courses.getCoursesList(query, current, pageSize);
      ctx.helper.renderSuccess(ctx, {
        data: {
          list,
          count,
          message: 'OK',
        },
      });
    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
  // 获取课程分类
  async getClassification(ctx) {
    try {
      const {
        level,
        parentID,
      } = ctx.query;
      const list = await ctx.service.courses.getClassification(level, parentID);
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
          list,
        },
      });
    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },


  // 获取统计信息
  async getSatisticByYear(ctx, app) {
    try {
      let {
        searchYear,
        span,
      } = ctx.query;
      if (!span) span = 1;
      const superID = ctx.session.superUserInfo._id;
      const query = {
        superID,
      };
      const certificateQuery = {
        superID: ctx.session.superUserInfo._id,
      };

      if (searchYear) {
        // 保险处理
        searchYear = new Date(searchYear);
        searchYear.setDate(1);
        searchYear.setMonth(0);

        const nextSearchYear = new Date(searchYear);
        nextSearchYear.setDate(1);
        nextSearchYear.setMonth(0);
        nextSearchYear.setYear(searchYear.getFullYear() + span);

        // console.log(1111111111111111, searchYear, nextSearchYear)
        query.completeTime = {
          $lt: nextSearchYear, // 比下一年元旦早
          $gte: searchYear, // 比搜索年元旦晚
        };

        certificateQuery.createdAt = {
          $lt: nextSearchYear, // 比下一年元旦早
          $gte: searchYear, // 比搜索年元旦晚
        };
      }
      const list = await ctx.model.AdminTraining.find(query);
      const certificate = await ctx.model.Certificate.countDocuments(certificateQuery);
      // console.log(list)
      const satisticData = {
        planCount: list.length,
        enterpriseCount: 0,
        participantsCount: 0,
        completedEnterprisesCount: 0,
        certificate,
      };
      const tempEnterprises = [];
      const incompleteEnterprise = [];
      // 遍历今年所有的培训
      for (let i = 0; i < list.length; i++) {
        const plan = list[i];
        const differenceSet = plan.EnterpriseID.filter(function(v) {
          return !plan.completedEnterprise.includes(v);
        });
        // 计算参与企业数避免重复
        const planEnterpriseLen = plan.EnterpriseID.length;
        for (let j = 0; j < planEnterpriseLen; j++) {
          const enterprise = plan.EnterpriseID[j];
          if (!tempEnterprises.includes(enterprise)) {
            tempEnterprises.push(enterprise);
          }
        }
        // 计算为完成企业数，比计算完成企业容易，只要一个未完成就算未完成
        // 若一个企业参与多个培训，需都完成才算完成
        const differenceSetLen = differenceSet.length;
        for (let j = 0; j < differenceSetLen; j++) {
          const enterprise = differenceSet[j];
          if (!incompleteEnterprise.includes(enterprise)) {
            incompleteEnterprise.push(enterprise);
          }
        }
        // 计算参与人次，可以重复
        const persons = await ctx.model.PersonalTraining.countDocuments({
          adminTrainingId: plan._id,
        });
        satisticData.participantsCount += persons;
      }
      satisticData.enterpriseCount = tempEnterprises.length;
      satisticData.completedEnterprisesCount = tempEnterprises.length - incompleteEnterprise.length;

      const regAdd = ctx.session.superUserInfo.regAdd;
      const regaddQuery = {};
      if (regAdd[0] !== app.config.China.name) {
        regaddQuery['workAddress.districts'] = {
          $all: regAdd,
        };
      }
      const enterpriseCount = await ctx.model.Adminorg.countDocuments(regaddQuery);

      satisticData.allEnterpriseCount = enterpriseCount;
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
          satisticData,
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // 获取最近好多年的覆盖率
  async getCoverageLastManyYears(ctx, app) {
    try {
      let {
        span,
      } = ctx.query;
      if (!span) span = 3;

      const toYear = new Date();
      toYear.setDate(1);
      toYear.setMonth(0);

      const upperBoundYear = new Date(toYear);
      const lowerBoundYear = new Date(toYear);
      const result = [];
      for (let index = 0; index < span; index++) {
        lowerBoundYear.setYear(toYear.getFullYear() - index);
        upperBoundYear.setYear(toYear.getFullYear() - index + 1);
        const query = {
          completeTime: {
            $lt: upperBoundYear, // 比下一年元旦早
            $gte: lowerBoundYear, // 比搜索年元旦晚
          },
          superID: ctx.session.superUserInfo._id,
        };
        const list = await ctx.model.AdminTraining.find(query);
        // console.log(11111111111,lowerBoundYear, upperBoundYear, list)
        const satisticData = {
          year: lowerBoundYear.getFullYear(),
          planCount: list.length,
          enterpriseCount: 0,
          completedEnterprisesCount: 0,
        };
        const tempEnterprises = [];
        const incompleteEnterprise = [];
        // 遍历今年所有的培训
        for (let i = 0; i < list.length; i++) {
          const plan = list[i];
          const differenceSet = plan.EnterpriseID.filter(function(v) {
            return !plan.completedEnterprise.includes(v);
          });
          // 计算参与企业数避免重复
          for (let j = 0; j < plan.EnterpriseID.length; j++) {
            const enterprise = plan.EnterpriseID[j];
            if (!tempEnterprises.includes(enterprise)) {
              tempEnterprises.push(enterprise);
            }
          }
          // 计算为完成企业数，比计算完成企业容易，只要一个未完成就算未完成
          // 若一个企业参与多个培训，需都完成才算完成
          for (let j = 0; j < differenceSet.length; j++) {
            const enterprise = differenceSet[j];
            if (!incompleteEnterprise.includes(enterprise)) {
              incompleteEnterprise.push(enterprise);
            }
          }
        }
        satisticData.enterpriseCount = tempEnterprises.length;
        satisticData.completedEnterprisesCount = tempEnterprises.length - incompleteEnterprise.length;
        result.push(satisticData);
      }
      const regAdd = ctx.session.superUserInfo.regAdd;
      const regaddQuery = {};
      if (regAdd[0] !== app.config.China.name) {
        regaddQuery['workAddress.districts'] = {
          $all: regAdd,
        };
      }
      // const aaa = await ctx.model.Adminorg.find(regaddQuery);
      // console.log('aaa', aaa, regAdd)
      const enterpriseCount = await ctx.model.Adminorg.countDocuments(regaddQuery);
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
          result,
          enterpriseCount,
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },


  // deletePlan
  async deletePlan(ctx) {
    const {
      planID,
    } = ctx.query;
    if (!planID) {
      ctx.helper.renderFail(ctx, {
        message: '参数有误',
      });
      return;
    }
    // 删除这个培训下的所有记录；
    await ctx.service.personalTraining.delManyYJT(planID);
    await ctx.service.adminTraining.deleteOne(planID);
    ctx.helper.renderSuccess(ctx, {
      data: {
        message: 'OK',
      },
    });
  },

  // 获取课程所有内容
  async getCourseAllContent(ctx) {
    try {
      const {
        _id,
      } = ctx.query;
      const course = await ctx.service.courses.getCourseOne(_id);
      const contentList = [];
      course.sort.sort(function(a, b) {
        return a.sequence - b.sequence;
      });
      for (let index = 0; index < course.sort.length; index++) {
        const element = course[course.sort[index].contentType].find(function(item) {
          return item._id === course.sort[index].ID;
        });
        let Video;
        switch (course.sort[index].contentType) {
          case 'videoInfos':
            Video = await ctx.helper.request_alivod('GetVideoInfo', {
              VideoId: element.VideoId,
            }, {});
            contentList.push({
              contentType: course.sort[index].contentType,
              _id: course.sort[index]._id,
              ID: course.sort[index].ID,
              VideoId: element.VideoId,
              name: Video && Video.Video ? Video.Video.Title : '',
              cover: Video && Video.Video ? Video.Video.CoverURL : '',
              Description: Video && Video.Video ? Video.Video.Description : '',
              times: element.times,
            });
            break;
          case 'documents':
            contentList.push({
              contentType: course.sort[index].contentType,
              _id: course.sort[index]._id,
              ID: course.sort[index].ID,
              htmlContent: element.htmlContent,
              name: element.name,
              cover: element.cover,
              Description: element.Description,
            });
            break;
          default:
            break;
        }
      }
      ctx.helper.renderSuccess(ctx, {
        data: {
          contentList,
          course,
          message: 'OK',
        },
      });
    } catch (error) {
      // ctx.auditLog('课程内容获取错误', `${error} 。`, 'error');
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }

  },
  // 获取视频播放凭证
  async getVideoPlayAuth(ctx) {
    const {
      VideoId,
    } = ctx.query;
    try {
      const response = await ctx.helper.request_alivod('GetVideoPlayAuth', {
        VideoId,
      }, {});
      ctx.helper.renderSuccess(ctx, {
        data: {
          PlayAuth: response.PlayAuth,
          message: 'OK',
        },
      });

    } catch (response) {
      ctx.auditLog('视频播放凭证获取错误', `${response.stack} 。`, 'error');
      // console.log('responseresponse', response);
      // console.error('ErrorCode = ' + response.code);
      // console.error('ErrorMessage = ' + response.Message);
      // console.error('RequestId = ' + response.RequestId);
      ctx.helper.renderFail(ctx, {
        message: JSON.stringify(response),
      });
    }
  },


  // 二分查找的思想
  // 将数组从中间分为俩子数组A B，分别查询A B数组在数据库中企业个数，结果小于等于子数组长度。
  // 等于子数组长度，就表示企业全存在，小于的那个子数组继续当做参数递归调用此函数，直到某个子数组长度为一，这个就是数据库没有的，记录在result数组中，result是引用
  // 如果所有企业都存在，那只要查询两次，如果所有企业都不存在，那就要查询一百多次，比直接循环好点，返回数据也只是数字，体积小
  async divideConquer(arr, model, result) {
    const len = arr.length;
    if (!len) {
      return;
    }
    if (len === 1) {
      result.push(arr[0]);
      return;
    }
    const mid = Math.floor(len / 2);
    const countL = await model.countDocuments({
      cname: {
        $in: arr.slice(0, mid),
      },
    });
    const countR = await model.countDocuments({
      cname: {
        $in: arr.slice(mid, len),
      },
    });
    if (countL < mid) await this.divideConquer(arr.slice(0, mid), model, result);
    if (countR < len - mid) await this.divideConquer(arr.slice(mid, len), model, result);
    return 0;
  },
  // excel导入，传入一个只有企业名称的数组，查询这些企业在数据库中是否存在，挑出不存在的企业。
  async checkoutCompany(ctx) {
    try {
      const {
        postData,
      } = ctx.request.body;
      const result = [];
      // 由于数组一般情况下是几百上千个，不存在于数据库的企业在少数，为了较少查询次数，这里用分治查询
      await this.divideConquer(postData, ctx.model.Adminorg, result);
      ctx.helper.renderSuccess(ctx, {
        data: {
          noExists: result,
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      ctx.helper.renderFail(ctx, {
        message: JSON.stringify(error),
      });
    }
  },


  // 获取到企业，一个一个查，然后把id给查出来
  async getCompanysId(ctx) {
    try {
      const {
        postData,
      } = ctx.request.body;
      const companys = await ctx.model.Adminorg.find({
        cname: {
          $in: postData,
        },
      }, {
        _id: 1,
      });
      const result = companys.map(item => {
        return item._id;
      });
      ctx.helper.renderSuccess(ctx, {
        data: {
          result,
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      ctx.helper.renderFail(ctx, {
        message: JSON.stringify(error),
      });
    }
  },

  // 一键导出企业学习情况
  async outputAll(ctx) {
    try {
      const {
        trainingID,
      } = ctx.request.body;

      console.log(trainingID);
      const plan = await ctx.model.AdminTraining.aggregate([{
        $match: {
          _id: trainingID,
        },
      }, {
        $lookup: {
          from: 'adminorgs',
          localField: 'EnterpriseID',
          foreignField: '_id',
          as: 'companys',
        },
      }]);

      console.log(plan instanceof Array);
      for (let index = 0; index < plan[0].companys.length; index++) {
        const element = plan[0].companys[index];
        const company = await ctx.model.Adminorg.findOne({
          _id: element._id,
        }, {
          adminArray: 1,
        });
        if (company) {
          element.personalTraining = await ctx.model.PersonalTraining.aggregate([{
            $match: {
              // EnterpriseID: element._id,
              adminUserId: {
                $in: company.adminArray,
              },
              adminTrainingId: trainingID,
              // status: true,
            },
          }, {
            $lookup: {
              from: 'adminusers',
              localField: 'adminUserId',
              foreignField: '_id',
              as: 'adminuser',
            },
          },
          {
            $unwind: '$adminuser',
          },
          {
            $lookup: {
              from: 'testRecord',
              localField: 'bigTestList',
              foreignField: '_id',
              as: 'bigTestList',
            },
          },
          {
            $lookup: {
              from: 'certificate',
              localField: 'certificateID',
              foreignField: '_id',
              as: 'certificate',
            },
          },
            // {

            // },
          ]);
        } else {
          element.personalTraining = [];
        }
      }
      ctx.helper.renderSuccess(ctx, {
        data: {
          result: plan[0],
        },
      });
    } catch (error) {
      ctx.logger.error(error);
      ctx.helper.renderFail(ctx, {
        message: JSON.stringify(error),
      });
    }
  },

  // =========================================
  // 获取企业管理列表,YJT定制版
  async selectAllEnterprises(ctx, app) {
    try {
      const paramss = ctx.query;
      const query = {};
      const query2 = {};
      const regAdd = ctx.session.superUserInfo.regAdd;
      const serchArr = [];
      if (regAdd[regAdd.length - 1] !== app.config.China.name) { // 不是搜索但是非全国用户的时候
        serchArr.push(...regAdd);
        query.districtWorkAdd = serchArr;
      }
      // 排序
      if (paramss.sortType && paramss.sortType !== '') {
        query2.sortType = JSON.parse(paramss.sortType);
      }
      // 疑似职业病
      if (paramss.suspectedOption && paramss.suspectedOption !== '') {
        query2.suspectedOption = Number(paramss.suspectedOption);
      }
      // 职业病
      if (paramss.odiseasedOption && paramss.odiseasedOption !== '') {
        query2.odiseasedOption = Number(paramss.odiseasedOption);
      }
      // 是否超标
      if (paramss.exceedOption && paramss.exceedOption !== '') {
        query2.exceedOption = Number(paramss.exceedOption);
      }
      // 申报情况
      if (paramss.OnlineDeclarationOption && paramss.OnlineDeclarationOption !== '') {
        query2.OnlineDeclarationOption = Number(paramss.OnlineDeclarationOption);
      }
      // 检测情况
      if (paramss.jobHealthOption && paramss.jobHealthOption !== '') {
        query2.jobHealthOption = Number(paramss.jobHealthOption);
      }
      // 体检情况
      if (paramss.ratioArrOption && paramss.ratioArrOption !== '') {
        query2.ratioArrOption = Number(paramss.ratioArrOption);
      }
      // 责任自查有选项时
      if (paramss.selfAssessmentOption && paramss.selfAssessmentOption !== '') {
        query2.selfAssessment = paramss.selfAssessmentOption === '0' ? 'A' : (paramss.selfAssessmentOption === '1' ? 'B' : 'C');
      }
      // 危害暴露等级有选项时
      if (paramss.assessmentResultOption && paramss.assessmentResultOption !== '') {
        query2.assessmentResult = Number(paramss.assessmentResultOption);
      }
      // 当综合风险等级有选项时
      if (paramss.comprehensiveLevelOption !== undefined && paramss.comprehensiveLevelOption !== '') {
        query2.comprehensiveLevel = Number(paramss.comprehensiveLevelOption);
      }
      // 当分类等级有选项时
      if (paramss.riskSortLevelOption !== undefined && paramss.riskSortLevelOption !== '') {
        query.riskSortLevel = Number(paramss.riskSortLevelOption);
      }
      // 地区有搜索
      if (paramss.district) {
        paramss.district = JSON.parse(paramss.district);
      }
      // console.log(paramss, 'paramss2222222222222222222222222222222222');
      // 如果地区有筛选,那么先根据选的辖区进行筛选
      if (paramss.district !== undefined && paramss.district !== '' && paramss.district[0]) {
        query.districtWorkAdd = paramss.district;
      }
      // 当行业分类有选择筛选时
      if (paramss.industryCategory && paramss.industryCategory !== '' && paramss.industryCategory !== '[]') {
        const industryCategorys = JSON.parse(paramss.industryCategory);
        query2.industryCategory = industryCategorys;
      }
      // 风险评估有搜索
      if (paramss.levelOption !== undefined && paramss.levelOption !== '') {
        query.level = paramss.levelOption;
      }
      // 当有培训状态时
      if (paramss.trainState !== undefined && paramss.trainState !== '') {
        query.trainState = paramss.trainState;
      }
      // 分页
      // query2.pageInfos = JSON.parse(paramss.pageInfos);
      const collections = [{
        name: 'riskAssessmentReport',
        selfKey: '_id',
        foreignKey: 'EnterpriseID',
        asKey: 'riskAssessmentReport',
      },
      {
        name: 'preventAssess',
        selfKey: '_id',
        foreignKey: 'EnterpriseID',
        asKey: 'preventAssessId',
      },
      ];
      const searchKeys = [ 'cname', 'regAdd', 'corp' ];
      const params = {
        collections,
        query,
        query2,
        searchKeys,
        // statisticsFiles,
      };
      const playLoad = {
        searchkey: paramss.searchkey,
      };
      // console.log('params', params)
      const adminorgList = await ctx.service.adminTraining.findAdminorgList(ctx.model.Adminorg, playLoad, params, ctx, app);
      ctx.helper.renderSuccess(ctx, {
        data: adminorgList,
      });
    } catch (err) {
      console.log(err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },
  // 导入培训清单列表
  async importTrainList(ctx) {
    try {
      const data = ctx.request.body;
      let res = null;
      if (data.params.trainType === 3) {
        res = await ctx.service.adminTraining.importTrainListLabourers(data);
      } else {
        res = await ctx.service.adminTraining.importTrainList(data);
      }
      if (!res) {
        throw new Error('导入失败');
      }
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '导入成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取衢州的所有培训记录
  async qzList(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.propagate.qzList(data);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '数据获取成功',
    });
  },

  // 获取培训报名记录
  async registrationList(ctx) {
    const query = ctx.query;
    const res = await ctx.service.personalTraining.registrationList(query);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '操作成功。',
    });
  },
  // 报名统计
  async personalTrainingStatistics(ctx) {
    const query = ctx.request.body;
    try {
      const res = await ctx.service.personalTraining.personalTrainingStatistics(query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '操作成功。',
      });
    } catch (error) {
      console.error(4444, error);
      ctx.helper.renderFail(ctx, {
        message: error.message,
      });
    }
  },

};

module.exports = AdminTrainingController;
