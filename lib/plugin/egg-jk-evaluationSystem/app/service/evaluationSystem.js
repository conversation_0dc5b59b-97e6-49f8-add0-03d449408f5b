const Service = require("egg").Service;
const ExcelJS = require("exceljs");

class EvaluationSystemService extends Service {
  async addTarget(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/addOrEditIndicatorClassify`,
        {
          method: "POST",
          dataType: "json", // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // getTargets
  async getTargets() {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/getIndicatorClassifyTreeList`,
        {
          method: "GET",
          dataType: "json", // 返回的数据类型
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // removeTarget
  async removeTarget(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/deleteIndicatorClassify?id=${params.id}`,
        {
          method: "DELETE",
          dataType: "json", // 返回的数据类型
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // addTargetContent
  async addTargetContent(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/addOrEditIndicator`,
        {
          method: "POST",
          dataType: "json", // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // getTargetContents
  async getTargetContents(params) {
    try {
      const { ctx } = this;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/getIndicatorTreeList`,
        {
          method: "GET",
          dataType: "json", // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // updateContent
  async updateContent(params) {
    try {
      const { ctx } = this;
      const res = await ctx.model.TargetContent.updateOne(
        { _id: params._id },
        {
          $set: {
            content: params.content,
            allTargetId: params.ids,
          },
        }
      );

      return res;
    } catch (error) {
      throw new Error(error);
    }
  }

  // removeContent
  async removeContent(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/deleteIndicator?id=${params.id}`,
        {
          method: "DELETE",
          dataType: "json", // 返回的数据类型
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // addBasicItem
  async addBasicItem(params) {
    try {
      const { ctx } = this;
      const res = await ctx.model.BasicItem.create({ content: params.content });
      return res;
    } catch (error) {
      throw new Error(error);
    }
  }

  // getAllModels

  async getAllModels() {
    try {
      const { ctx } = this;
      const res1 = await ctx.model.BasicItem.find();
      const res2 = await ctx.model.AccessItem.find();

      return {
        basicList: res1,
        infoList: res2,
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  // addAccessItem
  async addAccessItem(params) {
    try {
      const { ctx } = this;
      const res = await ctx.model.AccessItem.insertMany(params.list);

      return res;
    } catch (error) {
      throw new Error(error);
    }
  }

  // removeAccessItem
  async removeAccessItem(params) {
    try {
      const { ctx } = this;
      let res;
      if (params.type === 1) {
        res = await ctx.model.BasicItem.deleteOne({
          _id: params._id,
        });
      } else {
        res = await ctx.model.AccessItem.deleteOne({
          _id: params._id,
        });
      }

      return res;
    } catch (error) {
      throw new Error(error);
    }
  }

  // updateAccessItem
  async updateAccessItem(params) {
    try {
      const { ctx } = this;
      const res = await ctx.model.AccessItem.updateOne(
        {
          _id: params.id,
        },
        {
          $set: {
            score: params.data.score,
            way: params.data.way,
            type: params.data.type,
          },
        }
      );

      return res;
    } catch (error) {
      throw new Error(error);
    }
  }

  // getDeclarations
  async getDeclarations(params) {
    try {
      const { ctx } = this;
      const { currentPage, limit } = params;
      const res = await ctx.model.Declaration.aggregate([
        {
          $match: {
            isSubmit: true,
          },
        },
        {
          $lookup: {
            from: "adminorgs",
            localField: "enterpriseId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  cname: 1,
                  code: 1,
                  contract: 1,
                  phoneNum: 1,
                },
              },
            ],
            as: "enterpriseInfo",
          },
        },
        {
          $unwind: "$enterpriseInfo", // 将数组展开为单个对象
        },
        {
          $skip: (Number(currentPage) - 1) * Number(limit),
        },
        {
          $limit: Number(limit),
        },
      ]);

      const total = await ctx.model.Declaration.countDocuments({
        isSubmit: true,
      });

      return {
        list: res,
        total,
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  // getDeclarationInfo
  // async getDeclarationInfo(params) {
  //   try {
  //     const { ctx } = this;
  //     const { _id } = params;
  // async getDeclarationInfo(params) {
  //   try {
  //     const { ctx } = this;
  //     const { _id } = params;
  // async getDeclarationInfo(params) {
  //   try {
  //     const { ctx } = this;
  //     const { _id } = params;

  //     const res = await ctx.model.Declaration.find({
  //       _id,
  //     });
  //     const res = await ctx.model.Declaration.find({
  //       _id,
  //     });
  //     const res = await ctx.model.Declaration.find({
  //       _id,
  //     });

  //     return res;
  //   } catch (error) {
  //     throw new Error(error);
  //   }
  // }
  //     return res;
  //   } catch (error) {
  //     throw new Error(error);
  //   }
  // }
  //     return res;
  //   } catch (error) {
  //     throw new Error(error);
  //   }
  // }

  // reviewDeclaration
  async reviewDeclaration(params) {
    try {
      const { ctx } = this;
      const { type, id } = params;

      let res;
      if (type === 1) {
        res = await ctx.model.Declaration.update(
          {
            _id: id,
          },
          {
            $set: {
              status: "初审通过",
            },
          }
        );
      }

      if (type === 2) {
        res = await ctx.model.Declaration.update(
          {
            _id: id,
          },
          {
            $set: {
              status: "退回修改",
              rejectReason: params.reason,
            },
          }
        );
      }

      if (type === 3) {
        res = await ctx.model.Declaration.update(
          {
            _id: id,
          },
          {
            $set: {
              status: "初审不通过",
              rejectReason: params.reason,
            },
          }
        );
      }
      return res;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 查询评估模型列表
  async queryAssessmentModelList(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/getAssessmentModelList`,
        {
          method: "GET",
          dataType: "json", // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 查询评估模型
  async queryAssessmentModel(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/getAssessmentModel`,
        {
          method: "GET",
          dataType: "json", // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 新增编辑评估模型
  async addOrEditAssessmentModel(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/addOrEditAssessmentModel`,
        {
          method: "POST",
          dataType: "json", // 返回的数据类型
          data: params,
          contentType: "json",
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 禁用启用评估模型
  async enableAssessmenModel(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/enableAssessmenModel`,
        {
          method: "PUT",
          dataType: "json", // 返回的数据类型
          data: params,
          contentType: "json",
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 删除评估模型
  async deleteAssessmentModel(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/deleteAssessmentModel?id=${params.id}`,
        {
          method: "DELETE",
          dataType: "json", // 返回的数据类型
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 查询评估模型是否重名
  async checkAssessmentModelSameName(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/checkAssessmentModelSameName`,
        {
          method: "POST",
          dataType: "json", // 返回的数据类型
          data: params,
          contentType: "json",
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 查询申报列表
  async queryDeclarationList(params) {
    console.log("params", params);
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/getDeclarationList`,
        {
          method: "POST",
          dataType: "json", // 返回的数据类型
          data: params,
          contentType: "json",
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 导出列表
  async exportDeclarationList(ctx, records) {
    // 2. 创建 Excel 工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Sheet 1");

    // 3. 设置表头
    worksheet.columns = [
      { header: "ID", key: "id", width: 10 },
      { header: "单位名称", key: "unit_name", width: 50 },
      { header: "年度", key: "year", width: 30 },
      { header: "自评状态", key: "self_assessment_status", width: 30 },
      { header: "申报时间", key: "declaration_time_format", width: 30 },
      { header: "申报状态", key: "declaration_status", width: 30 },
      { header: "审批时间", key: "city_review_time_format", width: 30 },
    ];

    // 处理数据
    records.forEach((record) => {
      switch (record.declaration_status) {
        case "0":
          record.declaration_status = "待初审";
          break;
        case "1":
          record.declaration_status = "审核需修改";
          break;
        case "2":
          record.declaration_status = "初审不通过";
          break;
        case "3":
          record.declaration_status = "待审核";
          break;
        case "4":
          record.declaration_status = "审核不通过";
          break;
        case "5":
          record.declaration_status = "审核通过";
          break;
        default:
          break;
      }

      switch (record.self_assessment_status) {
        case "0":
          record.self_assessment_status = "不通过";
          break;
        case "1":
          record.self_assessment_status = "通过";
          break;
        default:
          break;
      }
    });

    records.forEach((record) => {
      worksheet.addRow({
        id: record.id,
        unit_name: record.unit_name,
        year: record.year,
        self_assessment_status: record.self_assessment_status,
        declaration_time_format: record.declaration_time_format,
        declaration_status: record.declaration_status,
        city_review_time_format: record.city_review_time_format,
      });
    });

    // 5. 设置响应头（触发浏览器下载）
    ctx.set(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    ctx.set("Content-Disposition", 'attachment; filename="export.xlsx"');
    ctx.body = await workbook.xlsx.writeBuffer();
    ctx.status = 200;
  }

  // 获取评估模型字典
  async getAssessmentModelDictionary() {
    try {
      const { ctx } = this;

      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/getAssessmentModelDictionary`,
        {
          method: "GET",
          dataType: "json", // 返回的数据类型
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  // 查询评估模型
  // async queryAssessmentModel(params) {
  //   try {
  //     const { ctx } = this;
  //     const { data } = await ctx.curl(
  //       `${this.config.iService2Host}/healthy-adminorgs/getAssessmentModel`,
  //       {
  //         method: "GET",
  //         dataType: "json", // 返回的数据类型
  //         data: params,
  //       }
  //     );

  //     if (data.code !== 200) throw new Error(data.message);

  //     return data.data;
  //   } catch (error) {
  //     throw new Error(error);
  //   }
  // }

  async getDeclarationInfo(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/getDeclaration`,
        {
          method: "GET",
          dataType: "json", // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  async addDeclaration(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/addOrEditDeclaration`,
        {
          method: "POST",
          dataType: "json", // 返回的数据类型
          data: params,
          contentType: "json",
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  async rejectDeclaration(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/rejectDeclaration`,
        {
          method: "PUT",
          dataType: "json", // 返回的数据类型
          data: params,
          contentType: "json",
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  async submitDeclaration(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/submitDeclaration`,
        {
          method: "PUT",
          dataType: "json", // 返回的数据类型
          data: params,
          contentType: "json",
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateReReviewStatus(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/updateReReviewStatus`,
        {
          method: "PUT",
          dataType: "json", // 返回的数据类型
          data: params,
          contentType: "json",
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  async assignExperts(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/assignExperts`,
        {
          method: "POST",
          dataType: "json", // 返回的数据类型
          data: params,
          contentType: "json",
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  async getExpertList(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/getExpertReviewListByDeclarationId`,
        {
          method: "GET",
          dataType: "json", // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteExpert(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/deleteExpert?id=${params.id}`,
        {
          method: "DELETE",
          dataType: "json", // 返回的数据类型
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  async getExpertReviewDetail(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/getExpertReviewDetail`,
        {
          method: "GET",
          dataType: "json", // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  async joinHealthEnterprise(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/joinHealthEnterprise`,
        {
          method: "POST",
          dataType: "json", // 返回的数据类型
          data: params,
          contentType: "json",
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  async getQueryConditionList(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/getQueryConditionList`,
        {
          method: "GET",
          dataType: "json", // 返回的数据类型
          data: params,
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  async saveQueryCondition(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/saveQueryCondition`,
        {
          method: "POST",
          dataType: "json", // 返回的数据类型
          data: params,
          contentType: "json",
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteQueryCondition(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/deleteQueryCondition?id=${params.id}`,
        {
          method: "DELETE",
          dataType: "json", // 返回的数据类型
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  async getDeclarationStatistics(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/getDeclarationTotal`,
        {
          method: "POST",
          dataType: "json", // 返回的数据类型
          data: params,
          contentType: "json",
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      const total = params.enterprise_ids.length;

      return [
        {
          name: "未申报",
          value: total - data.data,
        },
        {
          name: "已申报",
          value: data.data,
        },
      ];
    } catch (error) {
      throw new Error(error);
    }
  }

  async getSelectionStatistics(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/getSelectionStatistics`,
        {
          method: "POST",
          dataType: "json", // 返回的数据类型
          data: params,
          contentType: "json",
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }

  async getYearDeclarationStatistics(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/getYearDeclarationTotal`,
        {
          method: "POST",
          dataType: "json", // 返回的数据类型
          data: params,
          contentType: "json",
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      const total = params.enterprise_ids.length;

      const { years, declared } = data.data;
      const undeclared = [];

      declared.forEach((item) => {
        undeclared.push(total - item);
      });

      return {
        years,
        declared,
        undeclared,
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getYearSelectionStatistics(params) {
    try {
      const { ctx } = this;
      const { data } = await ctx.curl(
        `${this.config.iService2Host}/healthy-adminorgs/getYearSelectionStatistics`,
        {
          method: "POST",
          dataType: "json", // 返回的数据类型
          data: params,
          contentType: "json",
        }
      );

      if (data.code !== 200) throw new Error(data.message);

      return data.data;
    } catch (error) {
      throw new Error(error);
    }
  }
}

module.exports = EvaluationSystemService;
