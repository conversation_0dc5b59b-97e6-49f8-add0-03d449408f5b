const EvaluationSystemController = {
  async addTarget(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.addTarget(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },
  // getTargets
  async getTargets(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.getTargets();
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // removeTarget
  async removeTarget(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.removeTarget(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // addTargetContent
  async addTargetContent(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.addTargetContent(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // getTargetContents
  async getTargetContents(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.getTargetContents(
        ctx.query
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // updateContent
  async updateContent(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.updateContent(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // removeContent
  async removeContent(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.removeContent(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // addBasicItem
  async addBasicItem(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.addBasicItem(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // getAllModels
  async getAllModels(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.getAllModels();
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // addAccessItem
  async addAccessItem(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.addAccessItem(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // removeAccessItem
  async removeAccessItem(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.removeAccessItem(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },
  // updateAccessItem
  async updateAccessItem(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.updateAccessItem(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // getDeclarations
  async getDeclarations(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.getDeclarations(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // getDeclarationInfo
  // async getDeclarationInfo(ctx) {
  //   try {
  //     const res = await ctx.service.evaluationSystem.getDeclarationInfo(
  //       ctx.query
  //     );
  //     ctx.helper.renderSuccess(ctx, {
  //       data: res,
  //       status: 200,
  //       message: "success",
  //     });
  //   } catch (err) {
  //     ctx.helper.renderFail(ctx, {
  //       message: err,
  //     });
  //   }
  // },
  // async getDeclarationInfo(ctx) {
  //   try {
  //     const res = await ctx.service.evaluationSystem.getDeclarationInfo(
  //       ctx.query
  //     );
  //     ctx.helper.renderSuccess(ctx, {
  //       data: res,
  //       status: 200,
  //       message: "success",
  //     });
  //   } catch (err) {
  //     ctx.helper.renderFail(ctx, {
  //       message: err,
  //     });
  //   }
  // },
  // async getDeclarationInfo(ctx) {
  //   try {
  //     const res = await ctx.service.evaluationSystem.getDeclarationInfo(
  //       ctx.query
  //     );
  //     ctx.helper.renderSuccess(ctx, {
  //       data: res,
  //       status: 200,
  //       message: "success",
  //     });
  //   } catch (err) {
  //     ctx.helper.renderFail(ctx, {
  //       message: err,
  //     });
  //   }
  // },

  // reviewDeclaration

  async reviewDeclaration(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.reviewDeclaration(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // expertDeclaration
  async expertDeclaration(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.getExpertList(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // getExpertList
  async getExpertList(ctx) {
    try {
      const query = ctx.query;
      const { data } = await ctx.curl(
        `${ctx.app.config.iService2Host}/expert`,
        {
          method: "GET",
          dataType: "json",
          data: query,
        }
      );
      ctx.helper.renderSuccess(ctx, {
        data,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // assignExperts
  async assignExperts(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.assignExperts(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取评估模型列表
  async getAssessmentModelList(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.queryAssessmentModelList(
        ctx.query
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取评估模型数据
  async getAssessmentModel(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.queryAssessmentModel(
        ctx.query
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 添加编辑评估模型数据
  async addOrEditAssessmentModel(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.addOrEditAssessmentModel(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 禁用启用评估模型
  async enableAssessmenModel(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.enableAssessmenModel(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 删除评估模型
  async deleteAssessmentModel(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.deleteAssessmentModel(
        ctx.query
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 检查评估模型是否重名
  async checkAssessmentModelSameName(ctx) {
    try {
      const res =
        await ctx.service.evaluationSystem.checkAssessmentModelSameName(
          ctx.request.body
        );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取申报列表
  async getDeclarationList(ctx) {
    console.log("ctx.query", ctx.query);
    try {
      const enterprise_ids = await ctx.service.adminorg.getEnterpriseIDs();

      const res = await ctx.service.evaluationSystem.queryDeclarationList({
        ...ctx.query,
        enterprise_ids,
      });
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async getDeclarationList2(ctx) {
    console.log("ctx.request.body", ctx.request.body);

    const enterprise_ids = await ctx.service.adminorg.getEnterpriseIDs();
    try {
      const res = await ctx.service.evaluationSystem.queryDeclarationList({
        ...ctx.request.body,
        enterprise_ids,
      });
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async exportDeclarationList(ctx) {
    try {
      const enterprise_ids = await ctx.service.adminorg.getEnterpriseIDs();

      const res = await ctx.service.evaluationSystem.queryDeclarationList({
        ...ctx.request.body,
        enterprise_ids,
      });
      await ctx.service.evaluationSystem.exportDeclarationList(
        ctx,
        res.records
      );
    } catch (err) {
      console.log(err);
      ctx.set("Content-Type", "application/json");
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async getAssessmentModelDictionary(ctx) {
    try {
      const res =
        await ctx.service.evaluationSystem.getAssessmentModelDictionary(
          ctx.query
        );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 获取评估模型数据
  // async getAssessmentModel(ctx) {
  //   try {
  //     const res = await ctx.service.evaluationSystem.queryAssessmentModel(
  //       ctx.query
  //     );
  //     ctx.helper.renderSuccess(ctx, {
  //       data: res,
  //       status: 200,
  //       message: "success",
  //     });
  //   } catch (err) {
  //     ctx.helper.renderFail(ctx, {
  //       message: err,
  //     });
  //   }
  // },

  async getDeclarationInfo(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.getDeclarationInfo(
        ctx.query
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async addDeclaration(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.addDeclaration(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async rejectDeclaration(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.rejectDeclaration(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async submitDeclaration(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.submitDeclaration(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async updateReReviewStatus(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.updateReReviewStatus(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async deleteExpert(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.deleteExpert(ctx.query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async getExpertReviewDetail(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.getExpertReviewDetail(
        ctx.query
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async joinHealthEnterprise(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.joinHealthEnterprise(
        ctx.request.body
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async getQueryConditionList(ctx) {
    const { _id } = ctx.session.superUserInfo;
    try {
      const res = await ctx.service.evaluationSystem.getQueryConditionList({
        ...ctx.query,
        _id,
      });
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async saveQueryCondition(ctx) {
    const { _id } = ctx.session.superUserInfo;
    try {
      const res = await ctx.service.evaluationSystem.saveQueryCondition({
        ...ctx.request.body,
        _id,
      });
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async deleteQueryCondition(ctx) {
    try {
      const res = await ctx.service.evaluationSystem.deleteQueryCondition(
        ctx.query
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async getDeclarationStatistics(ctx) {
    const enterprise_ids = await ctx.service.adminorg.getEnterpriseIDs();

    try {
      const res = await ctx.service.evaluationSystem.getDeclarationStatistics({
        ...ctx.query,
        enterprise_ids,
      });
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },
  async getSelectionStatistics(ctx) {
    try {
      const enterprise_ids = await ctx.service.adminorg.getEnterpriseIDs();

      const res = await ctx.service.evaluationSystem.getSelectionStatistics({
        ...ctx.query,
        enterprise_ids,
      });
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },
  async getYearDeclarationStatistics(ctx) {
    try {
      const enterprise_ids = await ctx.service.adminorg.getEnterpriseIDs();

      const res =
        await ctx.service.evaluationSystem.getYearDeclarationStatistics({
          ...ctx.query,
          enterprise_ids,
        });
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },
  async getYearSelectionStatistics(ctx) {
    try {
      const enterprise_ids = await ctx.service.adminorg.getEnterpriseIDs();

      const res = await ctx.service.evaluationSystem.getYearSelectionStatistics(
        {
          ...ctx.query,
          enterprise_ids,
        }
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
        status: 200,
        message: "success",
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },
};

module.exports = EvaluationSystemController;
