const pkgInfo = require("../package.json");
exports.jk_evaluationSystem = {
  alias: "evaluationSystem", // 插件目录，必须为英文
  pkgName: "egg-jk-evaluationSystem", // 插件包名
  enName: "jk_evaluationSystem", // 插件名
  name: "", // 插件名称
  description: "健康企业评估", // 插件描述
  isadm: 1, // 是否有后台管理，1：有，0：没有，入口地址:'/ext/devteam/admin/index'
  isindex: 0, // 是否需要前台访问，1：需要，0：不需要,入口地址:'/ext/devteam/index/index'
  version: pkgInfo.version, // 版本号
  iconName: "icon_service", // 主菜单图标名称
  adminUrl: "/serviceOrg/js/app.js",
  adminApi: [
    {
      url: "evaluationSystem/addTarget",
      method: "post",
      controllerName: "addTarget",
      details: "新增指标",
    },
    {
      url: "evaluationSystem/getTargets",
      method: "get",
      controllerName: "getTargets",
      details: "获取指标列表",
    },
    {
      url: "evaluationSystem/removeTarget",
      method: "post",
      controllerName: "removeTarget",
      details: "删除指标",
    },

    {
      url: "evaluationSystem/addTargetContent",
      method: "post",
      controllerName: "addTargetContent",
      details: "添加指标内容",
    },

    {
      url: "evaluationSystem/getTargetContents",
      method: "get",
      controllerName: "getTargetContents",
      details: "获取指标内容",
    },

    // updateContent
    {
      url: "evaluationSystem/updateContent",
      method: "post",
      controllerName: "updateContent",
      details: "更新指标内容",
    },

    {
      url: "evaluationSystem/removeContent",
      method: "post",
      controllerName: "removeContent",
      details: "删除指标内容",
    },

    {
      url: "evaluationSystem/addBasicItem",
      method: "post",
      controllerName: "addBasicItem",
      details: "新增基本条件",
    },
    // getAllModels
    {
      url: "evaluationSystem/getAllModels",
      method: "get",
      controllerName: "getAllModels",
      details: "获取所有模型",
    },

    // addAccessItem
    {
      url: "evaluationSystem/addAccessItem",
      method: "post",
      controllerName: "addAccessItem",
      details: "添加评估信息",
    },
    {
      url: "evaluationSystem/removeAccessItem",
      method: "post",
      controllerName: "removeAccessItem",
      details: "删除评估信息",
    },
    // updateAccessItem
    {
      url: "evaluationSystem/updateAccessItem",
      method: "post",
      controllerName: "updateAccessItem",
      details: "更新评估信息",
    },
    {
      url: "evaluationSystem/getDeclarations",
      method: "get",
      controllerName: "getDeclarations",
      details: "获取申报列表",
    },
    // getDeclarationInfo
    {
      url: "evaluationSystem/getDeclarationInfo",
      method: "get",
      controllerName: "getDeclarationInfo",
      details: "获取申报详情",
    },
    {
      url: "evaluationSystem/reviewDeclaration",
      method: "post",
      controllerName: "reviewDeclaration",
      details: "审批",
    },
    {
      url: "evaluationSystem/expertDeclaration",
      method: "get",
      controllerName: "expertDeclaration",
      details: "现场评审列表",
    },
    // getExpertList
    {
      url: "evaluationSystem/getExpertList",
      method: "get",
      controllerName: "getExpertList",
      details: "获取专家列表",
    },
    // assignExperts
    {
      url: "evaluationSystem/assignExperts",
      method: "post",
      controllerName: "assignExperts",
      details: "分配专家",
    },
    {
      url: "evaluationSystem/getAssessmentModelList",
      method: "get",
      controllerName: "getAssessmentModelList",
      details: "获取评估模型列表",
    },
    {
      url: "evaluationSystem/getAssessmentModel",
      method: "get",
      controllerName: "getAssessmentModel",
      details: "获取评估模型",
    },
    {
      url: "evaluationSystem/addOrEditAssessmentModel",
      method: "post",
      controllerName: "addOrEditAssessmentModel",
      details: "添加编辑评估模型",
    },
    {
      url: "evaluationSystem/enableAssessmenModel",
      method: "put",
      controllerName: "enableAssessmenModel",
      details: "禁用启用评估模型",
    },
    {
      url: "evaluationSystem/deleteAssessmentModel",
      method: "delete",
      controllerName: "deleteAssessmentModel",
      details: "删除评估模型",
    },
    {
      url: "evaluationSystem/checkAssessmentModelSameName",
      method: "post",
      controllerName: "checkAssessmentModelSameName",
      details: "检查评估模型是否重名",
    },
    {
      url: "evaluationSystem/getDeclarationList",
      method: "get",
      controllerName: "getDeclarationList",
      details: "获取申报列表",
    },
    {
      url: "evaluationSystem/getDeclarationList2",
      method: "post",
      controllerName: "getDeclarationList2",
      details: "获取申报列表2",
    },
    {
      url: "evaluationSystem/getDeclarationInfo",
      method: "get",
      controllerName: "getDeclarationInfo",
      details: "获取申报详情",
    },
    {
      url: "evaluationSystem/getAssessmentModelDictionary",
      method: "get",
      controllerName: "getAssessmentModelDictionary",
      details: "获取评估模型字典",
    },
    {
      url: "evaluationSystem/getAssessmentModel",
      method: "get",
      controllerName: "getAssessmentModel",
      details: "获取评估模型数据",
    },
    {
      url: "evaluationSystem/addDeclaration",
      method: "post",
      controllerName: "addDeclaration",
      details: "新增申报",
    },
    {
      url: "evaluationSystem/rejectDeclaration",
      method: "put",
      controllerName: "rejectDeclaration",
      details: "审核需修改",
    },
    {
      url: "evaluationSystem/updateReReviewStatus",
      method: "put",
      controllerName: "updateReReviewStatus",
      details: "修改复评审提醒状态",
    },
    {
      url: "evaluationSystem/submitDeclaration",
      method: "put",
      controllerName: "submitDeclaration",
      details: "提交申请到兵团",
    },
    {
      url: "evaluationSystem/deleteExpert",
      method: "delete",
      controllerName: "deleteExpert",
      details: "删除评审专家",
    },
    {
      url: "evaluationSystem/getExpertReviewDetail",
      method: "get",
      controllerName: "getExpertReviewDetail",
      details: "获取专家评审详情",
    },
    {
      url: "evaluationSystem/joinHealthEnterprise",
      method: "post",
      controllerName: "joinHealthEnterprise",
      details: "加入健康企业",
    },
    {
      url: "evaluationSystem/getQueryConditionList",
      method: "get",
      controllerName: "getQueryConditionList",
      details: "获取查询条件列表",
    },
    {
      url: "evaluationSystem/saveQueryCondition",
      method: "post",
      controllerName: "saveQueryCondition",
      details: "保存查询条件",
    },
    {
      url: "evaluationSystem/deleteQueryCondition",
      method: "delete",
      controllerName: "deleteQueryCondition",
      details: "删除查询条件",
    },
    {
      url: "evaluationSystem/exportDeclarationList",
      method: "post",
      controllerName: "exportDeclarationList",
      details: "导出列表",
    },
    {
      url: "evaluationSystem/getDeclarationStatistics",
      method: "get",
      controllerName: "getDeclarationStatistics",
      details: "申报情况统计",
    },
    {
      url: "evaluationSystem/getSelectionStatistics",
      method: "get",
      controllerName: "getSelectionStatistics",
      details: "评选情况统计",
    },
    {
      url: "evaluationSystem/getYearDeclarationStatistics",
      method: "get",
      controllerName: "getYearDeclarationStatistics",
      details: "年度申报情况统计",
    },
    {
      url: "evaluationSystem/getYearSelectionStatistics",
      method: "get",
      controllerName: "getYearSelectionStatistics",
      details: "年度评选情况统计",
    },
  ],
  fontApi: [],
  initData: "", // 初始化数据脚本
  pluginsConfig: ` 
    exports.jk_evaluationSystem = {\n
        enable: true,\n
         \n
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
    evaluationSystemRouter:{\n
        match: [ctx => ctx.path.startsWith('/manage/evaluationSystem'), ctx => ctx.path.startsWith('/api/evaluationSystem'),ctx => ctx.path.startsWith('/manage/evaluationSystem'), ctx => ctx.path.startsWith('/api/evaluationSystem'), ctx => ctx.path.startsWith('/manage/evaluationSystem')],\n
    },\n
    `, // 插入到 config.default.js 中的配置
};
