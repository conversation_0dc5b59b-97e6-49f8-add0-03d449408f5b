stages:
  - test
  - release

unit test:
  image: registry.duopu.cn/zyws/docker/node:18-alpine
  stage: test
  tags:
    - nodejs-dock
  cache:
    key: JGNPM
    paths:
      - .npm/
  services:
    - name: registry.duopu.cn/zyws/docker/mongo:7.0
      alias: mongo
    - name: registry.duopu.cn/zyws/docker/valkey/valkey:8
      alias: valkey
    - name: registry.duopu.cn/zyws/docker/rabbitmq:3-management-alpine
      alias: rabbitmq
      variables:
        RABBITMQ_DEFAULT_USER: "su"
        RABBITMQ_DEFAULT_PASS: "Duopu666"
  script:
    - ls $CI_PROJECT_DIR
    - npm config set registry https://registry.npmmirror.com
    # - npm config set proxy http://***********:7890
    # - npm config set https-proxy http://***********:7890
    # - npm config set registry https://registry.npmjs.org/
    - npm config get registry
    - npm install
    - export NODE_ENV=test
    - export EGG_SERVER_ENV=unittest
    - npm run test
    - mkdir -p public/$CI_COMMIT_REF_NAME
    - cp -r coverage/lcov-report/* public/$CI_COMMIT_REF_NAME
  artifacts:
    when: always
    paths:
      # save the coverage results
      - coverage
      - public
    expire_in: 10 days
  except:
    - tags

release-image:
  stage: release
  image: registry.duopu.cn/zyws/docker/docker:26.1.4
  tags:
    - nodejs-dock
  services:
    - registry.duopu.cn/zyws/docker/docker:26.1.4-dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
    CONTAINER_RELEASE_IMAGE: $CI_REGISTRY_IMAGE:latest
  script:
    - ls -l .
    - source ./version
    - docker login -u $CI_TA_ALL -p $CI_TA_ALL_PASS $CI_REGISTRY
    - docker run --rm --privileged  --network zyws-net registry.duopu.cn/zyws/docker/multiarch/qemu-user-static --reset -p yes
    - docker buildx build --platform=linux/amd64,linux/arm64 -t $CI_REGISTRY_IMAGE:$VERSION -t $CONTAINER_RELEASE_IMAGE --push .
    - docker image rm $CI_REGISTRY_IMAGE:$VERSION $CONTAINER_RELEASE_IMAGE
    - docker builder prune -f && docker image prune -f
  # 在Master和EP_前缀的branch提交时也运行此job
  only:
    - master
    - /^EP_.*$/
    - wb_xjbt
    - check
    - f_wb_xjbt_csjc
    - feat_xjbtGM
    - expert_fenpei
    - wb_xjbt_unit_vote
    - wb_hzyy
    - f_wb_xjbt_place
    - f_wb_xjbt_correct_big_data
    - fix_train_import
  except:
    - tags
