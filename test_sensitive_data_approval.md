# 敏感数据上报与审批功能测试指南

## 测试环境准备

### 1. 数据库准备
确保MongoDB中有以下测试数据：

#### SuperUser测试数据
```javascript
// 兵团级用户（可以进行最终审批）
{
  _id: "test_user_bt",
  name: "兵团测试用户",
  regAdd: ["建设兵团"],
  dataReportAuth: true,
  type: 1
}

// 师市级用户（可以进行中级审批）
{
  _id: "test_user_ss",
  name: "师市测试用户", 
  regAdd: ["建设兵团", "建设兵团第一师"],
  dataReportAuth: true,
  type: 1
}

// 团场级用户（可以进行初级审批）
{
  _id: "test_user_tc",
  name: "团场测试用户",
  regAdd: ["建设兵团", "建设兵团第一师", "某团场"],
  dataReportAuth: true,
  type: 1
}

// 无上报权限用户
{
  _id: "test_user_no_auth",
  name: "无权限用户",
  regAdd: ["建设兵团", "建设兵团第一师"],
  dataReportAuth: false,
  type: 1
}
```

#### 测试劳动者数据
```javascript
{
  _id: "test_employee_1",
  name: "张三",
  phoneNum: "13800138001",
  EnterpriseID: "test_enterprise_1",
  enable: true
}
```

#### 测试企业数据
```javascript
{
  _id: "test_enterprise_1",
  cname: "测试企业有限公司",
  workAddress: {
    districts: ["建设兵团", "建设兵团第一师", "某团场"]
  },
  isDelete: false,
  productionStatus: "2"
}
```

## 功能测试步骤

### 1. 权限验证测试

#### 1.1 测试上报权限检查
- 使用无权限用户登录
- 尝试点击"上报"按钮
- 预期结果：显示"您没有敏感数据上报权限"提示

#### 1.2 测试有权限用户上报
- 使用有权限用户登录
- 点击"上报"按钮
- 预期结果：成功打开上报弹窗，显示劳动者/企业列表

### 2. 上报流程测试

#### 2.1 劳动者数据上报
1. 登录有权限用户
2. 进入"劳动者数据审批"页面
3. 点击"上报"按钮
4. 选择劳动者"张三"
5. 填写上报理由："测试上报理由"
6. 点击"确定"
7. 预期结果：
   - 显示"上报成功"提示
   - 列表中新增一条待审批记录
   - 状态为"待审批"
   - 当前审批节点显示审批流程的第一级

#### 2.2 企业数据上报
1. 进入"企业数据审批"页面
2. 重复上述步骤，选择企业进行上报
3. 预期结果：同劳动者上报

### 3. 审批流程测试

#### 3.1 权限匹配测试
1. 使用团场级用户登录
2. 查看上报记录
3. 点击"审批"按钮
4. 预期结果：如果当前审批节点匹配用户级别，可以进行审批

#### 3.2 权限不匹配测试
1. 使用兵团级用户登录
2. 查看刚创建的上报记录（当前应该在团场级审批）
3. 尝试点击"审批"按钮
4. 预期结果：显示"您没有权限审批此记录"

#### 3.3 审批通过流程
1. 使用正确级别的用户登录
2. 选择审批结果"通过"
3. 填写审批意见
4. 提交审批
5. 预期结果：
   - 显示"审批提交成功"
   - 记录状态更新为"审批中"
   - 当前审批节点更新到下一级
   - 审批历史中新增一条记录

#### 3.4 审批拒绝流程
1. 使用有权限的用户登录
2. 选择审批结果"拒绝"
3. 填写审批意见
4. 提交审批
5. 预期结果：
   - 显示"审批提交成功"
   - 记录状态更新为"已拒绝"
   - 重新获取最新审批流程
   - 审批历史中新增拒绝记录

### 4. 流程详情测试

#### 4.1 查看流程详情
1. 点击任意记录的"查看流程"按钮
2. 预期结果：
   - 右侧抽屉打开
   - 显示审批步骤条，正确标识当前进度
   - 显示审批历史表格，包含所有审批记录

### 5. 边界情况测试

#### 5.1 重复上报测试
1. 对同一个劳动者/企业进行重复上报
2. 预期结果：显示"该数据已有待审批记录，请勿重复提交"

#### 5.2 审批流程异常测试
1. 模拟审批流程接口异常
2. 尝试上报
3. 预期结果：显示"当前没有可用的审批流程，无法提交上报"

## 预期结果总结

### 成功场景
1. 有权限用户可以成功上报
2. 审批权限正确匹配
3. 审批流程按步骤进行
4. 状态正确更新
5. 审批历史正确记录

### 异常处理
1. 无权限用户被正确拦截
2. 重复上报被阻止
3. 权限不匹配时审批被拒绝
4. 审批流程异常时给出明确提示

## 注意事项

1. 测试前确保审批流程接口正常工作
2. 确保用户的regAdd字段正确设置
3. 确保dataReportAuth权限正确配置
4. 测试时注意观察浏览器控制台是否有错误信息
5. 测试完成后清理测试数据
